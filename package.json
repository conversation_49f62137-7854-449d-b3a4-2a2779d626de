{"name": "shipvagoo-frontend", "version": "0.1.3", "private": true, "license": "UNLICENSED", "scripts": {"type-check": "vue-tsc --noEmit", "dev": "ENV=LOCAL vite --port 4000", "build": "yarn type-check && vite build", "preview": "vite preview", "postinstall": "rimraf ./node_modules/@types/react", "prebuild": "yarn && rimraf ./dist", "postbuild": "node ./src/scripts/generate-version-uuid.mjs", "prepare": "husky install"}, "dependencies": {"@fawmi/vue-google-maps": "0.9.72", "@fortawesome/fontawesome-pro": "^6.6.0", "@lukeed/uuid": "^2.0.0", "@quasar/extras": "^1.15.5", "@vuelidate/core": "^2.0.0", "@vuelidate/validators": "^2.0.0", "axios": "^1.1.3", "chart.js": "^3.9.1", "dayjs": "^1.11.6", "dotenv": "^16.0.3", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "is-european": "^1.0.4", "jspdf": "^2.5.1", "jspdf-autotable": "^3.5.25", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "modern-normalize": "^1.1.0", "parse-full-name": "^1.2.6", "pdf-lib": "^1.17.1", "quasar": "^2.10.0", "svg2pdf.js": "^2.2.1", "sweetalert": "^2.1.2", "uuid": "^9.0.0", "vue": "^3.2.41", "vue-chart-3": "^3.1.8", "vue-content-loader": "^2.0.1", "vue-i18n": "9", "vue-router": "^4.1.5", "vue-sidebar-menu": "^5.2.4", "vue-the-mask": "^0.11.1", "vuelidate": "^0.7.7", "vuex": "^4.1.0"}, "devDependencies": {"@quasar/vite-plugin": "^1.2.3", "@types/file-saver": "^2.0.5", "@types/lodash-es": "^4.17.6", "@types/parse-full-name": "^1.2.2", "@types/uuid": "^8.3.4", "@types/vue-the-mask": "^0.11.1", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "@vitejs/plugin-vue": "^3.1.2", "@vue/runtime-core": "^3.2.41", "eslint": "8.26.0", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-vue": "^9.6.0", "husky": "^8.0.1", "postcss": "^8.4.18", "postcss-html": "^1.5.0", "postcss-scss": "^4.0.5", "prettier": "^2.7.1", "rimraf": "^3.0.2", "sass": "1.32", "stylelint": "^14.14.0", "stylelint-config-html": "^1.1.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recess-order": "^3.0.0", "stylelint-config-recommended-scss": "^7.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^29.0.0", "stylelint-prettier": "^2.0.0", "typescript": "^4.8.4", "unplugin-vue-components": "^0.22.8", "vite": "^3.1.8", "vite-plugin-compression": "^0.5.1", "vite-plugin-mkcert": "^1.10.1", "vite-plugin-pwa": "^0.13.1", "vite-plugin-stylelint": "^3.0.8", "vue-eslint-parser": "^9.1.0", "vue-loader": "^17.0.0", "vue-tsc": "^1.0.11"}}