{"extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "airbnb-base", "airbnb-typescript", "plugin:vue/vue3-recommended", "prettier"], "env": {"vue/setup-compiler-macros": true}, "parser": "vue-eslint-parser", "parserOptions": {"extraFileExtensions": [".vue"], "project": "./tsconfig.json", "parser": "@typescript-eslint/parser"}, "plugins": ["@typescript-eslint"], "rules": {"arrow-body-style": "off", "@typescript-eslint/no-inferrable-types": "off", "react/jsx-filename-extension": [0], "vue/script-indent": ["error", 2, {"baseIndent": 1}], "import/no-extraneous-dependencies": ["error", {"devDependencies": [".storybook/**", "**/*.stories.ts", "**/*.d.ts", "./vite.config.ts"]}]}, "overrides": [{"files": ["**/*.model.ts"], "rules": {"import/no-cycle": ["off"]}}], "settings": {"import/resolver": {"node": {"extensions": [".js", ".jsx", ".ts", ".tsx"]}}}, "globals": {"$ref": "readonly", "$computed": "readonly", "$shallowRef": "readonly", "$customRef": "readonly", "$toRef": "readonly"}}