{"compilerOptions": {"baseUrl": ".", "module": "ESNext", "target": "ESNext", "lib": ["DOM", "ESNext"], "strict": true, "esModuleInterop": true, "jsx": "preserve", "skipLibCheck": true, "moduleResolution": "node", "resolveJsonModule": true, "noUnusedLocals": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "types": ["vite/client", "vue/ref-macros"], "paths": {"~/*": ["src/*"]}, "allowJs": true, "outDir": "dist"}, "exclude": ["dist", "node_modules", "coverage"], "references": [{"path": "./tsconfig.node.json"}]}