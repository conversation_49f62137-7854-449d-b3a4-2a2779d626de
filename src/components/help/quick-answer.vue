<template>
  <div class="row quick-answer-row">
    <div class="col-12 q-py-sm">
      <div class="flex justify-between q-px-md">
        <div class="text-black text-weight-bold" style="font-size: 16px;">{{faq.question}}</div>
        <div>
          <q-icon
            @click="()=>show=!show"
            class="cursor-pointer" :name="show?'fas fa-times-circle':'fas fa-chevron-down'"
            color="grey" size="xs">
          </q-icon>
        </div>
      </div>
      <Transition name="slide-fade">
        <div class="q-px-md" v-if="show">
          <div style="font-size: 13px; color:#757575">{{faq.answer}}</div>
        </div>
      </Transition>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import {ref} from "vue";

  interface I_FAQ {
    question: string,
    answer: string,
  }

  interface Props {
    faq: I_FAQ,
  }

  const props = defineProps<Props>();
  const faq = ref<I_FAQ>(props.faq);
  const show = ref<boolean>(false);
</script>

<style scoped>
  .quick-answer-row {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 70px;
    background: #FFF url("../../assets/img/intersect_bg.png") no-repeat;
    background-size: auto 100%;
    border: 1px solid #DDD;
    border-radius: 10px;
  }
  
  .slide-fade-enter-active {
    transition: all 0.3s ease-out;
  }

  .slide-fade-leave-active {
    transition: all 0.2s cubic-bezier(1, 0.5, 0.8, 1);
  }

  .slide-fade-enter-from,
  .slide-fade-leave-to {
    opacity: 0;
    transform: translateX(20px);
  }
</style>