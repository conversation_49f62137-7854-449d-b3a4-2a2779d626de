<template>
  <div class="addAddress">
    <div class="addAddress-title">
      <h4>Add new card</h4>
      <div class="close">
        <q-btn v-close-popup icon="close" flat round dense />
      </div>
    </div>
    <div class="addAddress-form">
      <div class="card-wrapper" :class="{ active: transform }">
        <div class="card card-front">
          <img class="rotateCard" src="/assets/img/card.svg" alt="Card" decoding="async" />
          <p class="card-number">{{ form.cardNumber }}</p>
          <p class="card-name">{{ form.cardName }}</p>
          <p v-if="form.cardDay || form.cardYear" class="card-date">
            {{ form.cardDay + '/' + form.cardYear }}
          </p>
        </div>
        <div class="card card-back">
          <img class="rotateCard" src="/assets/img/card-back.svg" alt="Card" decoding="async" />
          <p class="card-cvv">{{ form.cardCvv }}</p>
        </div>
      </div>
      <form @submit="addCard">
        <div class="group">
          <div class="input-group">
            <label>Card Save Name</label>
            <input v-model="form.cardCustomName" type="text" placeholder="" />
            <span v-if="v$.form.cardCustomName.$error" class="error_input">
              {{ v$.form.cardCustomName.$errors[0].$message }}
            </span>
          </div>
          <div class="input-group">
            <label>Card Number</label>
            <input
              v-model="form.cardNumber"
              v-mask="'####-####-####-####'"
              type="text"
              placeholder="**** **** **** ****"
            />
            <span v-if="v$.form.cardNumber.$error" class="error_input">
              {{ v$.form.cardNumber.$errors[0].$message }}
            </span>
          </div>
        </div>
        <div class="group">
          <div class="input-group">
            <label>Card Name</label>
            <input v-model="form.cardName" type="text" placeholder="" />
            <span v-if="v$.form.cardName.$error" class="error_input">
              {{ v$.form.cardName.$errors[0].$message }}
            </span>
          </div>
          <div class="input-group">
            <label>Card Date</label>
            <div class="input-flex">
              <input v-model="form.cardDay" v-mask="'##'" type="text" placeholder="**" />
              <input v-model="form.cardYear" v-mask="'##'" type="text" placeholder="**" />
            </div>
            <div class="error-flex">
              <span v-if="v$.form.cardDay.$error || v$.form.cardYear.$error" class="error_input">
                {{
                  v$.form.cardDay.$error
                    ? v$.form.cardDay.$errors[0].$message
                    : '' || v$.form.cardYear.$error
                    ? v$.form.cardYear.$errors[0].$message
                    : ''
                }}
              </span>
            </div>
          </div>
        </div>
        <div class="group">
          <div class="input-group">
            <label>Cvv</label>
            <input v-model="form.cardCvv" v-mask="'####'" type="text" placeholder="****" />
            <span v-if="v$.form.cardCvv.$error" class="error_input">
              {{ v$.form.cardCvv.$errors[0].$message }}
            </span>
          </div>
          <div class="input-group">
            <div class="check">
              <label for="default">
                <input id="default" v-model="form.setDefault" type="checkbox" />
                <span></span>
                <p>Select to default card.</p>
              </label>
            </div>
          </div>
        </div>
        <div class="input-group">
          <q-btn color="primary" label="Add card" @click="addCard" />
        </div>
      </form>
    </div>
    <Loader :status="loading" />
  </div>
</template>

<script></script>

<style scoped lang="scss">
  .addAddress {
    position: relative;
    width: 100%;
    padding: 15px;
    background: $custom-gray;
    @media screen and (min-width: $laptop) {
      width: 420px;
    }

    .card-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 300px;
      transition: transform 1s;
      transform-style: preserve-3d;

      &.active {
        transform: rotateY(180deg);
      }
    }

    .card {
      position: absolute;
      flex-direction: column;
      justify-content: center;
      max-width: 289px;
      margin: 0 auto;
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;

      &.card-back {
        transform: rotateY(180deg);
      }

      &-date {
        position: absolute;
        bottom: 30px;
        left: 80px;
        font-size: 12px;
      }

      &-cvv {
        position: absolute;
        top: 90px;
        left: 170px;
      }

      &-number {
        position: absolute;
        top: 100px;
        left: 10px;
        font-size: 15px;
        color: #fff;

        &.active {
          display: none;
        }
      }
      &-name {
        position: absolute;
        top: 145px;
        left: 10px;
        font-size: 15px;
        color: #fff;
        &.active {
          display: none;
        }
      }
      &-mastercard {
        position: absolute;
        top: 20px;
        right: 20px;
        width: 40px;
        &.active {
          display: none;
        }
      }
      &-visa {
        position: absolute;
        top: 20px;
        right: 20px;
        width: 40px;
        &.active {
          display: none;
        }
      }
      &-remove {
        position: absolute;
        right: 20px;
        bottom: 20px;
        display: flex;
        align-items: center;

        span {
          font-size: 15px;
          color: #fff;
        }

        .icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 13px;
          height: 13px;
          margin-right: 5px;
          background: $secondary-one;
          border-radius: 50%;
          i {
            color: $custom-gray;
          }
        }
      }
    }

    &-title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      border-bottom: 1px solid $border-color;
      h4 {
        padding: 0;
        margin: 0;
        font-size: 18px;
        color: #fff;
      }
    }

    &-form {
      .group {
        justify-content: center;
        width: 100%;
        @media screen and (min-width: $laptop) {
          display: flex;
        }
      }
      .input-group {
        display: flex;
        flex-direction: column;
        margin-bottom: 15px;

        .error_input {
          margin-top: 5px;
          font-size: 12px;
          color: red;
        }

        .error-flex {
          display: flex;
          flex-direction: column;
        }

        @media screen and (min-width: $laptop) {
          width: 50%;
          padding: 5px;
        }

        label {
          margin-bottom: 5px;
          font-size: 15px;
          font-weight: bold;
          @media screen and (min-width: $laptop) {
            font-size: 16px;
          }
        }

        input {
          padding: 5px;
          color: #fff;
          background: transparent;
          border: 1px solid $border-color;
          border-radius: 5px;
          outline: 0;
        }

        .input-flex {
          display: flex;

          input {
            width: 50px;

            &:first-child {
              margin-right: 10px;
            }
          }
        }
      }
      .check {
        display: flex;
        align-items: end;
        justify-content: end;
        height: 58px;

        input {
          display: none;
        }

        span {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 14px;
          min-width: 14px;
          height: 14px;
          font-size: 12px;
          border: 1px solid $primary-one;
          border-radius: 4px;
        }

        label {
          display: flex;
          align-items: center;
          padding-top: 2px;
          padding-bottom: 0;
          font-size: 15px;
          font-weight: normal;

          span {
            margin-top: 0;
            margin-right: 10px;
          }

          a {
            padding-left: 5px;
          }

          p {
            margin: 0;
            line-height: 16px;
          }

          &:hover {
            cursor: pointer;
          }
        }

        input:checked ~ span:before {
          display: block;
          width: 8px;
          height: 8px;
          content: '';
          background-color: $primary-one;
          border-radius: 1px;
        }
      }
    }
  }
</style>