<template>
  <div
    class="bg-white return-portal-modal">
    <div class="row items-center q-px-md q-py-sm header-bg">
      <div class="text-white" style="font-size:16px;font-weight: 500;">{{printerDetailTitle}}</div>
      <q-space/>
      <slot name="icon-right"/>
      <q-btn v-close-popup icon="close" color="white" flat round dense @click="onClose"/>
    </div>
    <div class="slot-container">
      <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
        <shipvagoo-input v-model="form.name" :label="$t('common.name')"/>
        <shipvagoo-input-group responsive>
          <shipvagoo-select
            v-model="form.computer"
            :options="printerApps"
            :label="$t('common.computer')"
            option-label="hostname"
            option-value="printer_app_code"
            @update:model-value="onUpdateComputer"
          />
          <shipvagoo-select
            v-model="form.printer"
            :label="$t('common.printer')"
            :options="form.computer?.printers || []"
          />
        </shipvagoo-input-group>
        <shipvagoo-select v-model="form.format" :label="$t('common.format')" :options="formats"/>
      </shipvagoo-card-section>
      <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
        <shipvagoo-input-group responsive>
          <shipvagoo-checkbox
            v-model="form.isDefaultPrinterForLabels"
            :label="$t('printers_page.is_default_printer_for_labels')"
          />
          <shipvagoo-checkbox
            v-model="form.isDefaultPrinterForPickDocuments"
            :label="$t('printers_page.is_default_printer_for_pick_documents')"
          />
        </shipvagoo-input-group>
        <shipvagoo-input-group responsive>
          <shipvagoo-checkbox
            v-model="form.isDefaultPrinterForOther"
            :label="$t('printers_page.is_default_printer_for_other')"
          />
          <shipvagoo-checkbox
            v-model="form.isAutoPrintPickList"
            :label="$t('printers_page.is_auto_print_pick_list')"
          />
        </shipvagoo-input-group>
      </shipvagoo-card-section>
      <shipvagoo-separator/>
      <shipvagoo-card-actions>
        <shipvagoo-button
          v-if="isCreate"
          :label="$t('common.create')"
          min-width="120px"
          @click="onCreatePrinterSubmit"
        />
        <shipvagoo-button
          v-if="isEdit"
          :label="$t('common.save')"
          min-width="120px"
          @click="onEditPrinterSubmit"
        />
      </shipvagoo-card-actions>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {onBeforeMount, ref} from 'vue';
  import {IPrinter, IPrinterApp} from '../../model/printer.model';
  import {
    printerService,
    loadingService,
    loggingService,
    notificationService,
  } from '../../services/_singletons';
  import {useStore} from '../../store';

  interface Props {
    printer: IPrinter | null;
  }

  const $store = useStore();
  const props = withDefaults(defineProps<Props>(), {printer: null});
  const emit = defineEmits(['close', 'create', 'update']);
  const printerApps = ref<IPrinterApp[]>([]);

  interface IForm {
    name: string | null;
    computer: IPrinterApp | null;
    printer: string | null;
    format: 'A4' | '10x19' | 'ZPL' | null;
    isDefaultPrinterForLabels: boolean;
    isDefaultPrinterForPickDocuments: boolean;
    isDefaultPrinterForOther: boolean;
    isAutoPrintPickList: boolean;
  }

  const form = $ref<IForm>({
    name: null,
    computer: null,
    printer: null,
    format: null,
    isDefaultPrinterForLabels: false,
    isDefaultPrinterForPickDocuments: false,
    isDefaultPrinterForOther: false,
    isAutoPrintPickList: false,
  });

  // Temporarily disabled ZPL format
  // const formats = ['A4', '10x19', 'ZPL'];
  const formats = ['A4', '10x19'];

  const isCreate = $computed(() => props.printer === null);
  const isEdit = $computed(() => props.printer !== null);

  const printerDetailTitle = $computed(() => {
    if (props.printer) {
      return props.printer.name;
    }

    return 'New printer';
  });

  const onCreatePrinterSubmit = async () => {
    try {
      if (!form.name || !form.computer || !form.printer || !form.format) {
        return;
      }

      loadingService.startLoading('main-loader:create-printer');
      await printerService.createPrinter({
        name: form.name,
        printer_app_code: form.computer?.printer_app_code,
        linked_printer: form.printer,
        format: form.format,
        is_default_label: form.isDefaultPrinterForLabels,
        is_default_other: form.isDefaultPrinterForOther,
        is_default_pick_documents: form.isDefaultPrinterForPickDocuments,
        is_auto_print_pick_list: form.isAutoPrintPickList,
      });
      $store.dispatch('printerStore/syncPrinters');
      emit('create');
      emit('close');
    } catch (error) {
      loggingService.error('Error while creating printer', error);
      notificationService.showError('Error creating printer');
    } finally {
      loadingService.stopLoading('main-loader:create-printer');
    }
  };

  const onEditPrinterSubmit = async () => {
    try {
      if (
        !props.printer?.printer_code ||
        !form.name ||
        !form.computer ||
        !form.printer ||
        !form.format
      ) {
        return;
      }

      loadingService.startLoading('main-loader:edit-printer');
      await printerService.updatePrinter(props.printer.printer_code, {
        name: form.name,
        printer_app_code: form.computer.printer_app_code,
        linked_printer: form.printer,
        format: form.format,
        is_default_label: form.isDefaultPrinterForLabels,
        is_default_other: form.isDefaultPrinterForOther,
        is_default_pick_documents: form.isDefaultPrinterForPickDocuments,
        is_auto_print_pick_list: form.isAutoPrintPickList,
      });
      $store.dispatch('printerStore/syncPrinters');
      emit('update');
      emit('close');
    } catch (error) {
      loggingService.error('Error while updating printer', error);
      notificationService.showError('Error updating printer');
    } finally {
      loadingService.stopLoading('main-loader:edit-printer');
    }
  };

  const onClose = () => {
    emit('close');
  };

  const getPrinterApps = async () => {
    try {
      loadingService.startLoading('main-loader:get-printer-apps');
      const result = await printerService.getPrinterApps();
      printerApps.value = result.map((p) => ({
        ...p,
        hostname: `${p.hostname}${p.instance <= 1 ? '' : `-${p.instance}`}`,
      }));
    } catch (error) {
      loggingService.error('Error getting printer apps', error);
      notificationService.showError('Error getting printer apps');
    } finally {
      loadingService.stopLoading('main-loader:get-printer-apps');
    }
  };

  const onUpdateComputer = () => {
    form.printer = null;
  };

  const hydrate = async () => {
    await getPrinterApps();

    if (props.printer) {
      form.name = props.printer.name;
      form.computer = printerApps.value.find(
        (printerApp) => printerApp.printer_app_code === props.printer?.printer_app_code,
      ) as IPrinterApp;
      form.printer = props.printer.linked_printer;
      form.format = props.printer.format;
      form.isDefaultPrinterForLabels = props.printer.is_default_label;
      form.isDefaultPrinterForPickDocuments = props.printer.is_default_pick_documents;
      form.isDefaultPrinterForOther = props.printer.is_default_other;
      form.isAutoPrintPickList = props.printer.is_auto_print_pick_list;
    }
  };

  onBeforeMount(() => {
    hydrate();
  });
</script>
<style scoped>
  .header-bg {
    background-color: #243E90;
  }

  .btn-style {
    font-size: 15px;
    color: white;
    background: #243E90
  }

  .return-portal-modal {
    width: 650px;
    max-width: 650px !important;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 15px;
  }
</style>
