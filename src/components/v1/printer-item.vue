<template>
  <div class="printer-item-container">
    <div class="printer-header" style="display: flex">
      <q-icon name="fas fa-print" class="text-gray-brand" style="font-size: 17px"></q-icon>
      <div class="text-gray-brand printer-header-name">
        {{ printer.name }}
      </div>
    </div>
    <div class="printer-content">
      <div>
        <div class="text-gray-brand printer-content-title">
          Default printer for printing labels:
        </div>
        <div class="printer-content-subtitle">{{ printer.is_default_label ? 'Yes' : 'No' }}</div>
      </div>
      <div class="q-mt-md">
        <div class="text-gray-brand printer-content-title">
          Default printer for printing picking list:
        </div>
        <div class="printer-content-subtitle">
          {{ printer.is_default_pick_documents ? 'Yes' : 'No' }}
        </div>
      </div>
      <div class="q-mt-md">
        <div class="text-gray-brand printer-content-title">
          Default printer for printing packing lists:
        </div>
        <div class="printer-content-subtitle">{{ printer.is_default_other ? 'Yes' : 'No' }}</div>
      </div>
      <div class="q-mt-md">
        <div class="text-gray-brand printer-content-title">Auto print picking list:</div>
        <div class="printer-content-subtitle">
          {{ printer.is_auto_print_pick_list ? 'Yes' : 'No' }}
        </div>
      </div>
    </div>
    <div class="printer-footer flex justify-around items-center">
      <div @click="onClickUpdate" class="text-brand text-center cursor-pointer" style="width: 49%">
        Update
      </div>
      <div class="printer-footer-horizontal-separator"></div>
      <div @click="onClickDelete" class="text-danger text-center cursor-pointer" style="width: 49%">
        Delete
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { IPrinter } from '../../model/printer.model';

  interface Props {
    printer: IPrinter;
  }

  defineProps<Props>();
  const emit = defineEmits(['delete', 'update']);
  const onClickUpdate = () => {
    emit('update');
  };
  const onClickDelete = () => {
    emit('delete');
  };
</script>

<style scoped lang="scss">
  .printer-item-container {
    width: 100%;
    background: #fff;
    border-radius: 5px;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.25);
  }

  .printer-header {
    height: 50px;
    padding: 15px;
    background: #f5f5f5;
    border-radius: 5px 5px 0px 0px;

    &-name {
      margin-top: -3px;
      margin-left: 10px;
      overflow: hidden;
      font-size: 16px;
      font-weight: 700;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .printer-footer {
    height: 50px;
    font-size: 14px;
    background: #f5f5f5;
    border-radius: 0px 0px 5px 5px;

    &-horizontal-separator {
      width: 1px;
      height: 30px;
      background-color: #ccc;
    }
  }

  .printer-content {
    padding: 15px;

    &-title {
      font-size: 14px;
      font-weight: 700;
    }

    &-subtitle {
      margin-top: 4px;
      font-size: 12px;
      color: #757575;
    }
  }
</style>
