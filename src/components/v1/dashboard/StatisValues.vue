<template>
  <div class="container">
    <div class="row">
      <div class="col-12 heading label-color">
        {{ heading }}
      </div>
    </div>
    <div class="row q-mt-sm">
      <div class="col-12">
        <div class="row q-my-sm" v-for="value in values">
          <div class="col-8 label-color text-weight-medium">
            {{ getLabel(value) }}
          </div>
          <div class="col-4 flex justify-end label-color text-weight-medium">
            {{ value.count }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  interface IStatis {
    /* name?:string | number | '',
     weightClass?: string | number | '' ,
     product?: string | number | '',*/
    [key: string]: string | number | ''
  }

  interface Props {
    heading?: string,
    values?: Array<IStatis>
  }

  const getLabel = (row: IStatis) => {
    if (row.name) {
      return row.name
    } else if (row.weightClass) {
      return row.weightClass
    } else if (row.product) {
      return row.product
    }
    return ''
  }
  withDefaults(defineProps<Props>(), {
    heading: 'Shipping Product'
  })
</script>

<style scoped>
  .heading {
    font-size: 15px;
    font-weight: 700;
    line-height: 19px;
  }

  .label-color {
    color: #313131;
  }
</style>