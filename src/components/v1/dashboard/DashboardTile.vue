<template>
  <ContentLoader viewBox="0 0 250 110" v-if="isLoading">
    <rect y="20" rx="3" ry="3" width="100%" height="90"/>
  </ContentLoader>
  <div v-else class="tile-container bottom-border-radius" :class="`${type}-border`">
    <div class="row tile-box-row">
      <div class="col-3  flex items-center justify-center">
        <div class="icon-box flex justify-center items-center">
          <q-icon :name="icon" size="xs" :class="`${type}-color`"></q-icon>
        </div>
      </div>
      <div class="col-9 flex items-center">
        <div class="row">
          <div class="col-12 tile-label" v-html="label">
          </div>
          <div class="col-12 tile-count">
            {{ count }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {onMounted, ref} from 'vue';
  import {ContentLoader} from 'vue-content-loader'

  type tileType = 'order' | 'return' | 'shipment' | 'sales'

  interface Props {
    type?: tileType;
    icon?: string,
    label?: string,
    count?: string | number
  }

  withDefaults(defineProps<Props>(), {//defaults for testing only
    type: 'order',
    icon: 'fas fa-box',
    label: 'New Orders',
    count: '8'
  })
  const isLoading = ref<boolean>(true);


  onMounted(() => {
    setTimeout(() => {
      isLoading.value = false;
    }, 500)
  })
</script>

<style scoped>
  .tile-container {
    width: 100%;
    height: 95px;
    margin: 15px 0 15px 0;
    background: #FFFFFF;
    border-radius: 5px;
    box-shadow: 0 4px 20px rgba(170, 169, 184, 0.25);

  }

  .tile-box-row {
    height: 100%;
  }

  .icon-box {
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.03);
    border-radius: 5px;
  }

  @media (min-width: 1670px) {
    .tile-container {
      height: 110px;
    }

    .icon-box {
      width: 50px;
      height: 50px;
    }
  }

  .bottom-border-radius {
    border-radius: 0px 0px 5px 5px;
  }

  .order-border {
    border-bottom: 4px solid #FFAA04;
  }

  .return-border {
    border-bottom: 4px solid #53B1FB;
  }

  .shipment-border {
    border-bottom: 4px solid #1467CC;
  }

  .sales-border {
    border-bottom: 4px solid #0A7D5A;
  }

  .order-color {
    color: #FFAA04;
  }

  .return-color {
    color: #53B1FB;
  }

  .shipment-color {
    color: #1467CC;
  }

  .sales-color {
    color: #0A7D5A;
  }

  .tile-label {
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    color: #4A596A;
  }

  .tile-count {
    font-size: 24px;
    font-style: normal;
    font-weight: bold;
    line-height: 33px;
    color: #313131;
  }
</style>