<template>
  <div class="row">
    <div class="col-12">
      <div class="alert-container full-width" :class="`${type}-alert`">
        <div class="flex items-center justify-center alert-container-icon">
          <div class="icon-box flex justify-center items-center" :class="`${type}-box`">
            <q-icon color="white" :name="icon" style="font-size: 11px"></q-icon>
          </div>
        </div>
        <div class="flex items-center q-pl-sm alert-container-text">
          <div class="full-width helvetica-text text-thirteen" :class="`${type}-color`">
            <!--  <div class="text-weight-7" v-if="showHeading">
              {{ type.charAt(0).toUpperCase() + type.slice(1) }}
            </div> -->
            <div v-html="label"></div>
          </div>
        </div>
        <div
          v-if="props.type == 'error' || props.type == 'warning'"
          style="display: flex"
          class="justify-end items-center text-black alert-container-actions"
        >
          <shipvagoo-hyperlink
            v-if="props.type == 'error'"
            :color="color"
            type="button"
            label="Resolve now"
            @click="resolveNow"
          />
          <q-btn
            v-if="props.type == 'warning'"
            icon="close"
            color="grey-8"
            flat
            round
            dense
            @click="onClickClose"
            size="sm"
            style="margin-right: -7px"
          />
          <!--   <shipvagoo-hyperlink
            v-if="props.type == 'warning'"
            :color="color"
            type="button"
            label="Accept"
            @click="accept"
            style="margin-right: 10px"
          /> -->
          <!--
          <shipvagoo-hyperlink
            v-if="props.type == 'warning'"
            :color="color"
            type="button"
            label="Reject"
            @click="deny"
          /> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';

  type ValType = 'error' | 'warning' | 'success' | 'info' | 'general';

  interface Props {
    type?: ValType | string;
    label?: string;
    showHeading?: boolean;
  }

  const emit = defineEmits(['resolve-now', 'accept-now', 'deny-now', 'close']);

  const props = withDefaults(defineProps<Props>(), {
    type: 'error',
    label: 'It is description',
    showHeading: true,
  });

  const onClickClose = () => {
    emit('close');
  };
  const resolveNow = () => {
    emit('resolve-now');
  };

  /*const accept = () => {
    emit('accept-now');
  };

    const deny = () => {
    emit('deny-now');
  }; */

  const color = computed(() => {
    if (props.type == 'error') {
      return '#BE4748 !important';
    } else if (props.type == 'warning') {
      return '#FF5F00 !important';
    }
  });
  const icon = computed(() => {
    if (props.type == 'error') {
      return 'fas fa-times';
    } else if (props.type == 'warning' || props.type == 'info' || props.type == 'general') {
      return 'fas fa fa-exclamation';
    }
  });
</script>

<style scoped lang="scss">
  .alert-container {
    display: flex;
    flex-direction: row;
    width: 100%;
    min-height: 40px;
    padding: 0 5px 0 10px;
    border-radius: 5px;

    .alert-container-icon {
      flex: 0.2;
    }

    .alert-container-text {
      flex: 5;
    }

    .alert-container-actions {
      flex: 1;
    }
  }

  .icon-box {
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }

  .general-box {
    background: #ffcd00;
  }
  .error-box {
    background: #be4748;
  }

  .warning-box {
    background: #007aff;
  }

  .success-box {
    background: #155407;
  }
  .info-box {
    background: #007aff;
  }

  .general-color {
    color: #313131;
  }
  .error-color {
    color: #be4748;
  }

  .warning-color {
    color: #007aff;
  }

  .success-color {
    color: #155407;
  }

  .info-color {
    color: #313131;
  }

  .error-alert {
    background: #ffeff4;
    border: 1px solid #be4748;
  }

  .warning-alert {
    background: #cfe6ff;
    border: 1px solid #007aff;
  }
  .info-alert {
    background: #cfe6ff;
    border: 1px solid #007aff;
  }

  .general-alert {
    background: #fffae4;
    border: 1px solid #ffcd00;
  }
</style>
