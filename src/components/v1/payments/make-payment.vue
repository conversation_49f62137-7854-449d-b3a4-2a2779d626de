<template>
  <div class="make-payment-modal">
    <shipvagoo-card-section class="make-payment-modal-header row items-center">
      <div class="payment-modal-header-title">{{ title }}</div>
      <q-space />
      <slot name="icon-right" />
      <q-btn
        v-close-popup
        icon="close"
        color="black"
        flat
        round
        dense
        @click="onClickClose"
        style="margin-right: -7px"
      />
    </shipvagoo-card-section>
    <shipvagoo-separator class="q-mx-md" />
    <div class="full-width flex justify-center">
      <iframe class="payment-scroll" :src="link" scrolling="auto" frameBorder="0"></iframe>
    </div>
  </div>
</template>

<script lang="ts" setup>
  interface Props {
    link: string;
    title: string;
  }

  defineProps<Props>();
  const emit = defineEmits(['close']);
  const onClickClose = () => {
    emit('close');
  };
</script>

<style scoped>
  .payment-modal-header-title {
    font-family: Helvetica, sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    color: #212121;
  }

  .make-payment-modal {
    width: 590px;
    height: 635px;
    background-color: white;
  }

  .make-payment-modal-header {
    flex-wrap: nowrap;
    height: 60px;
    font-size: 18px;
  }

  /* width */
  .payment-scroll {
    width: 580px;
    height: 630px;
  }

  @media (min-height: 600px) {
    .make-payment-modal {
      height: 640px;
    }

    .payment-scroll {
      height: 640px;
    }
  }

  @media (min-height: 768px) {
    .make-payment-modal {
      height: 760px;
    }

    .payment-scroll {
      height: 690px;
    }
  }

  @media (min-height: 1080px) {
    .make-payment-modal {
      height: 710px;
    }

    .payment-scroll {
      height: 705px;
    }
  }

  @media (min-height: 1440px) {
    .make-payment-modal {
      height: 810px;
    }

    .payment-scroll {
      height: 805px;
    }
  }
</style>
