<template>
  <div class="row cursor-pointer">
    <div class="col-12">
      <div class="payment-method-box" :class="selected?'selected-payment-box':'not-selected-payment-box'">
        <div class="row flex justify-center items-center" style="height: 100%;">
          <div class="col-11 flex justify-between">
            <div style="font-weight: 600;">{{ label }}</div>
            <div>
              <q-icon :name="icon" size="sm" style="color: #96BF47"></q-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {defineProps, computed} from "vue";
import PayWithBalance from '../../../assets/img/payment/pay_with_balance.svg';
import PayWithBalanceActive from '../../../assets/img/payment/pay_with_balance_active.svg';
import PayAndSaveCard from '../../../assets/img/payment/pay_and_save_card.svg';
import PayAndSaveCardActive from '../../../assets/img/payment/pay_and_save_card_active.svg';

import PayWithCard from '../../../assets/img/payment/pay_with_card.svg';
import PayWithCardActive from '../../../assets/img/payment/pay_with_card_active.svg';

type ValType = 'pay_with_balance' | 'pay_and_save_card' | 'pay_with_card';

interface Props {
  type?: ValType | string,
  selected?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'pay_with_balance',
  selected: false
})
const icon = computed(() => {
  let icon = null;
  if (props.type == 'pay_with_balance') {
    icon = props.selected ? PayWithBalanceActive : PayWithBalance;
  } else if (props.type == 'pay_and_save_card') {
    icon = props.selected ? PayAndSaveCardActive : PayAndSaveCard;
  } else if (props.type == 'pay_with_card') {
    icon = props.selected ? PayWithCardActive : PayWithCard;
  }
  return 'img:' + icon;
})
const label = computed(() => {
  let label = 'Pay with balance'
  if (props.type == 'pay_with_balance') {
    label = 'Pay with balance'
  } else if (props.type == 'pay_and_save_card') {
    label = 'Pay with saved card'
  } else if (props.type == 'pay_with_card') {
    label = 'Pay with card'
  }
  return label;
})
</script>

<style scoped>
.payment-method-box {
  height: 56px;
  border-radius: 5px;

}

.not-selected-payment-box {
  color: #7D8398;
  background: #FFFFFF;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
}

.selected-payment-box {
  background-color: #243E90;
  border: 2px solid #243E90;
}
</style>