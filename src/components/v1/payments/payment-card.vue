<template>
  <div class="row">
    <div class="col-12">
      <div class="payment-card-box flex justify-center items-center" :style="`background-image:url(${cardBg}); 
      background-repeat: no-repeat; background-size: 100% 100%;`">
        <div class="internal-detail-box">
          <div class="row">
            <div class="col-6 flex items-center">
              <img :src="cardLogo"/>
            </div>
            <div class="col-6 flex justify-end">
              <q-toggle
                @update:model-value="onChangeStatus"
                style="margin-right: -15px;"
                v-model="cardStatus"
                color="white"
              />
            </div>
          </div>
          <div class="card-user-detail">
            <div class="h3-heading line-height">
              {{card.bin.substring(0,4) + ' - **** - **** - ' + card.last4}}
            </div>
            <div class="h3-heading line-height">
              {{card.holder_name}}
            </div>
            <div class="row">
              <div class="col-10 h4-heading line-height">
                Expiry: {{card.exp_month}}/{{card.exp_year}}
              </div>
              <div class="col-2 flex justify-end ">
                <q-icon
                  @click="onClickDelete"
                  name="fas fa-trash cursor-pointer" size="xs"></q-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import {computed, defineProps, ref, watch} from 'vue';
  import MasterCard from '../../../assets/img/payment/master_card_bg.png';
  import MasterCardInactive from '../../../assets/img/payment/master_card_inactive.png';
  import Visa from '../../../assets/img/payment/visa.png';
  import VisaInactive from '../../../assets/img/payment/visa_inactive.png';
  import Dk from '../../../assets/img/payment/dk.png';
  import DkInactive from '../../../assets/img/payment/dk_inactive.png';
  import MasterCardLogo from '../../../assets/img/payment/master_card_logo.svg';
  import VisaLogo from '../../../assets/img/payment/visa_logo.svg';
  import DkLogo from '../../../assets/img/payment/dk_logo.svg';
  // eslint-disable-next-line import/named
  import {ICardResponse} from "../../../model/payment.model";


  interface Props {
    card: ICardResponse
  }

  const props = defineProps<Props>();

  const cardType = ref<string>(props.card.brand);
  const cardStatus = ref<boolean>(props.card.is_default ? true : false);

  const emit = defineEmits(['delete', 'statusChange']);

  watch(() => props.card, (newVal: ICardResponse) => {
    cardStatus.value = newVal.is_default ? true : false;
    cardType.value = newVal.brand;
  })
  const cardLogo = computed(() => {
    let logo = MasterCardLogo;
    console.log("Brand ", cardType.value)
    if (cardType.value.includes('mastercard')) {
      logo = MasterCardLogo;
    }
    if (cardType.value.includes('visa')) {
      logo = VisaLogo;
    }
    if (cardType.value == 'dk') {
      logo = DkLogo;
    }
    return logo;
  })

  const cardBg = computed(() => {
    let bg = MasterCard;
    if (cardType.value.includes('mastercard')) {
      bg = cardStatus.value ? MasterCard : MasterCardInactive;
    }
    if (cardType.value.includes('visa')) {
      bg = cardStatus.value ? Visa : VisaInactive;
    }
    if (cardType.value == 'dk') {
      bg = cardStatus.value ? Dk : DkInactive;
    }
    return bg;
  })

  const onClickDelete = () => {
    emit('delete')
  }
  const onChangeStatus = (value: boolean) => {
    emit('statusChange', value)
  }

</script>

<style scoped>
  .payment-card-box {
    min-height: 230px;
    padding: 16px 30px 16px 30px;
    background: #A1A1AA;
    border-radius: 5px;
  }

  .internal-detail-box {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    height: 100%;
  }

  .card-user-detail {
    width: 100%;
    height: 55%;
  }

  .line-height {
    line-height: 35px
  }

  @media (max-width: 1366px) {
    .payment-card-box {
      height: 185px;
      padding: 12px 24px 12px 24px;
    }

    .line-height {
      line-height: 30px
    }
  }


</style>