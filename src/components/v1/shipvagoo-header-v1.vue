<template>
  <div>
    <header>
      <div style="display: flex; align-items: center">
        <div class="menu-icon" @click="toggleSidebar">
          <img
            class="menu-icon&#45;&#45;img"
            src="/assets/img/hamburger.svg"
            alt="Hamburger Logo"
            decoding="async"
          />
        </div>
        <router-link to="/" class="logo">
          <img style="width: 140px" :src="Logo" alt="Logo" decoding="async" />
        </router-link>
        <div class="desktop-menu">
          <shipvagoo-tabs
            :options="menuOptions"
            :model-value="menuTabValue"
            @update:model-value="(val) => (menuTabValue = val)"
          />
        </div>
      </div>
      <div style="display: flex; flex-direction: row">
        <div style="display: flex; flex-direction: row" @mouseenter="hideCompanyDropdown">
          <template v-for="list in iconMenus" :key="`icon_menu_${list.value}`">
            <!--              :class="`desktop-menu&#45;&#45;item-icons-${list.value}`"-->
            <button class="desktop-menu--item-icons" @click="onClickMenu1Icon(list)">
              <q-icon v-if="list.icon" :name="list.icon" style="font-size: 20px" />
            </button>
          </template>
        </div>
        <div class="actions">
          <q-btn flat round field="profile" :icon="`img:${UserIcon}`">
            <ul class="menuUserDropDown">
              <li>
                <div class="text-dark text-weight-bold" style="font-size: 16px">
                  {{ companyName }}
                </div>
              </li>
              <li class="p-0">
                <shipvagoo-separator />
              </li>
              <li>
                <div class="text-dark text-fifteen">{{ balanceHeading }}</div>
                <div class="text-brand text-weight-bold" style="font-size: 22px">
                  {{ companyBalance }}
                </div>
              </li>
            </ul>
            <!--            <shipvagoo-menu
              self="top left"
              style="width: 250px;background-color: #F7F7F7">
              <shipvagoo-menu-item>
                <div class="text-dark text-weight-bold" style="font-size: 16px;">{{ companyName }}</div>
              </shipvagoo-menu-item>
              <shipvagoo-separator />
              <shipvagoo-menu-item>
                <div>
                  <div class="text-dark text-fifteen">
                    Balance
                  </div>
                  <div class="q-mt-sm text-brand text-weight-bold" style="font-size: 22px">
                    {{ $store.getters["companyStore/formattedBalance"] }}
                  </div>
                </div>
              </shipvagoo-menu-item>
            </shipvagoo-menu>-->
          </q-btn>
        </div>
      </div>
    </header>
    <div class="header-sub-menu" v-show="subMenuTabsActive">
      <shipvagoo-tabs
        class="header-sub-menu-shipments"
        v-if="menuTabValue == 'shipments'"
        :options="shipmentMenuOptions"
        v-model="shipmentTabValue"
      >
      </shipvagoo-tabs>
      <shipvagoo-tabs
        class="header-sub-menu-orders"
        v-if="menuTabValue == 'orders'"
        :options="orderMenuOptions"
        v-model="ordersTabValue"
      >
      </shipvagoo-tabs>
      <shipvagoo-tabs
        class="header-sub-menu-payments"
        v-if="menuTabValue == 'payments'"
        :options="paymentMenuOptions"
        v-model="paymentTabValue"
      >
      </shipvagoo-tabs>

      <shipvagoo-tabs
        class="header-sub-menu-settings"
        v-if="menuTabValue == 'settings'"
        :options="settingMenuOptions"
        v-model="settingTabValue"
      >
      </shipvagoo-tabs>
    </div>
  </div>
  <shipvagoo-sidebar-menu
    class="shipvagoo-sidebar-menu"
    v-click-outside="onClickOutside"
    :status="isSidebarCollapsed"
  />
  <q-dialog persistent v-model="isAddFundsModalOpen">
    <add-balance @close="onCloseAddFunds" />
  </q-dialog>
  <q-dialog v-model="logoutConfirm">
    <confirm-logout @confirm="logout"></confirm-logout>
  </q-dialog>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { useQuasar } from 'quasar';
  import { useStore } from 'vuex';
  import { authService, loadingService, loggingService } from '../../services/_singletons';
  import { IRootState } from '../../model/store.model';
  import Logo from '../../assets/img/shipvagoo_dark.png';
  import useWidth from '../../hooks/useWidth';

  import shipvagooMenu from '../../components/ui-kit/shipvagoo-menu.vue';
  import { ITabOption } from '../../model/common.model';

  import DashboardIcon from '../../assets/img/header/dashboard.svg';
  import DashboardActiveIcon from '../../assets/img/header/dashboard_active.svg';
  import ShipmentIcon from '../../assets/img/header/shipments.svg';
  import ShipmentActiveIcon from '../../assets/img/header/shipments_active.svg';
  import OrderIcon from '../../assets/img/header/order.svg';
  import OrderActiveIcon from '../../assets/img/header/order_active.svg';
  import ReturnIcon from '../../assets/img/header/returns.svg';
  import ReturnActiveIcon from '../../assets/img/header/returns_active.svg';
  import BillingIcon from '../../assets/img/header/billing.svg';
  import BillingActiveIcon from '../../assets/img/header/billing_active.svg';
  import UserIcon from '/assets/img/user.svg';

  import HelpIcon from '../../assets/img/header/help.svg';
  import SettingIcon from '../../assets/img/header/settings.svg';
  import AddFundsIcon from '../../assets/img/header/add_funds.svg';
  import LogoutIcon from '../../assets/img/header/power_off.svg';
  import HttpService from '../../services/http.service';
  import PriceService from '../../services/price.service';

  const isSidebarCollapsed = ref(true);
  const $router = useRouter();
  const $route = computed(() => $router.currentRoute.value);
  const $store = useStore<IRootState>();
  const $q = useQuasar();

  const isAddFundsModalOpen = ref(false);
  const logoutConfirm = ref(false);
  const company_menu_dropdown = ref<typeof shipvagooMenu | null>(null);
  defineExpose({ $q });
  const width = useWidth();

  const companyName = computed(() => {
    let name = $store.state.companyStore.company_name;
    if (name && name.length > 24) {
      name = name.substring(0, 23) + '..';
    }
    return name ?? '';
  });

  const companyBalance = computed(() => {
    $store.getters['companyStore/formattedBalance'];
    const type = $store.state.companyStore.credit_enabled ? 'credit' : 'balance';
    const _balance = PriceService.formatPrice(Number($store.state.companyStore.balance), 'de-DE');
    if (type == 'credit') {
      return _balance.replace('-', '');
    }
    return _balance;
  });
  const balanceHeading = computed(() => {
    return $store.state.companyStore.credit_enabled ? 'Credit Spend' : 'Balance';
  });

  const hideCompanyDropdown = () => {
    if (company_menu_dropdown.value) {
      company_menu_dropdown.value.hide();
    }
  };

  const subMenuTabsActive = computed(() => {
    const tabValue = menuTabValue.value;
    return (
      tabValue == 'settings' ||
      tabValue == 'shipments' ||
      tabValue == 'orders' ||
      tabValue == 'payments'
    );
  });

  const onClickMenu1Icon = (list: any) => {
    if (list.href) {
      $router.push(list.href);
      menuTabValue.value = null;
    } else if (list.type === 'dialog') {
      if (list.value === 'add_funds') {
        isAddFundsModalOpen.value = true;
      } else if (list.value === 'logout') {
        logoutConfirm.value = true;
      }
    } else if (list.type === 'dropdown') {
      menuTabValue.value = list.value;
      if (list.value === 'settings') {
        $router.push({ path: '/company' });
        settingTabValue.value = 'company';
      }
    }
  };

  const logout = async () => {
    try {
      loadingService.startLoading('main-loader:logout');
      await authService.logout();
      $store.dispatch('userStore/resetUser');
      $router.push('/login');
    } catch (error) {
      loggingService.error('Error while logout', error);
    } finally {
      loadingService.stopLoading('main-loader:logout');
    }
  };

  const onCloseAddFunds = () => {
    isAddFundsModalOpen.value = false;
  };

  const iconMenus = computed(() => {
    const menus = [
      {
        label: 'Help',
        value: 'help',
        icon: `img:${HelpIcon}`,
        type: 'page',
        href: '/help',
      },
      {
        label: 'Settings',
        value: 'settings',
        icon: `img:${SettingIcon}`,
        type: 'dropdown',
        onClick: onClickMenu1Icon,
      },
      {
        label: 'Add Funds',
        value: 'add_funds',
        icon: `img:${AddFundsIcon}`,
        type: 'dialog',
      },
      {
        label: 'Logout',
        value: 'logout',
        icon: `img:${LogoutIcon}`,
        type: 'dialog',
      },
    ];
    if ($store.state.companyStore.credit_enabled) {
      return menus.filter((menu) => menu.value !== 'add_funds');
    }
    return menus;
  });

  const menuTabValue = ref<string | null>(null);

  const menuOptions = ref<ITabOption[]>([
    {
      label: 'Dashboard',
      value: 'dashboard',
      icon: `img:${DashboardIcon}`,
      activeIcon: `img:${DashboardActiveIcon}`,
      stylingClass: 'q-icon-16',
      onClick: () => {
        menuTabValue.value = 'dashboard';
        $router.push({ path: '/' });
      },
    },
    {
      label: 'Shipments',
      icon: `img:${ShipmentIcon}`,
      activeIcon: `img:${ShipmentActiveIcon}`,
      stylingClass: 'q-icon-24',
      value: 'shipments',
      onClick: () => {
        menuTabValue.value = 'shipments';
        $router.push({ path: '/shipped' });
      },
    },
    {
      label: 'Orders',
      value: 'orders',
      icon: `img:${OrderIcon}`,
      activeIcon: `img:${OrderActiveIcon}`,
      stylingClass: 'q-icon-20',
      onClick: () => {
        menuTabValue.value = 'orders';
        $router.push({ path: '/orders' });
      },
    },
    {
      label: 'Returns',
      value: 'returns',
      icon: `img:${ReturnIcon}`,
      activeIcon: `img:${ReturnActiveIcon}`,
      stylingClass: 'q-icon-16',
      onClick: () => {
        menuTabValue.value = 'returns';
        $router.push({ path: '/returns' });
      },
    },
    {
      label: 'Payments',
      value: 'payments',
      icon: `img:${BillingIcon}`,
      activeIcon: `img:${BillingActiveIcon}`,
      stylingClass: 'q-icon-16',
      onClick: () => {
        menuTabValue.value = 'payments';
        $router.push({ path: '/transactions' });
      },
    },
  ]);

  const ordersTabValue = ref<string | null>(null);
  const orderMenuOptions = ref<ITabOption[]>([
    {
      label: 'Orders',
      value: 'orders',
      onClick: () => {
        $router.push({ path: '/orders' });
      },
    },
    {
      label: 'Order Invoice',
      value: 'order-invoices',
      onClick: () => {
        $router.push({ path: '/order-invoices' });
      },
    },
  ]);

  const shipmentTabValue = ref<string | null>(null);
  const shipmentMenuOptions = ref<ITabOption[]>([
    {
      label: 'New Shipment',
      value: 'new-shipment',
      onClick: () => {
        $store.dispatch('shipmentStore/openCreateShipmentModal');
      },
    },
    {
      label: 'Shipped',
      value: 'shipped',
      onClick: () => {
        $router.push({ path: '/shipped' });
      },
    },
    {
      label: 'Drafts',
      value: 'drafts',
      onClick: () => {
        $router.push({ path: '/drafts' });
      },
    },
    {
      label: 'Contacts',
      value: 'contacts',
      onClick: () => {
        $router.push({ path: '/contacts' });
      },
    },
  ]);

  const paymentTabValue = ref<string | null>(null);
  const paymentMenuOptions = ref<ITabOption[]>([
    {
      label: 'Transactions',
      value: 'transactions',
      onClick: () => {
        $router.push({ path: '/transactions' });
      },
    },
    {
      label: 'Billing',
      value: 'billing',
      onClick: () => {
        $router.push({ path: '/billing' });
      },
    },
    {
      label: 'Balance',
      value: 'add-funds',
      onClick: () => {
        $router.push({ path: '/add-funds' });
      },
    },
  ]);

  const settingTabValue = ref<string | null>(null);
  const settingMenuOptions = ref<ITabOption[]>([
    {
      label: 'Account',
      value: 'company',
      onClick: () => {
        menuTabValue.value = 'settings';
        settingTabValue.value = 'company';
        $router.push({ path: '/company' });
      },
    },
    {
      label: 'Integration',
      value: 'order-integration',
      onClick: () => {
        menuTabValue.value = 'settings';
        settingTabValue.value = 'order-integration';
        $router.push({ path: '/order-integration' });
      },
    },
    {
      label: 'Payment',
      value: 'payment-settings',
      onClick: () => {
        menuTabValue.value = 'settings';
        settingTabValue.value = 'payment-settings';
        $router.push({ path: '/payment-settings' });
      },
    },
    {
      label: 'Tracking Messages',
      value: 'personalized-messages',
      onClick: () => {
        menuTabValue.value = 'settings';
        settingTabValue.value = 'personalized-messages';
        $router.push({ path: '/personalized-messages' });
      },
    },
    {
      label: 'Return Portal',
      value: 'return-portals',
      onClick: () => {
        menuTabValue.value = 'settings';
        settingTabValue.value = 'return-portals';
        $router.push({ path: '/return-portals' });
      },
    },
    {
      label: 'Printer',
      value: 'printers',
      onClick: () => {
        menuTabValue.value = 'settings';
        settingTabValue.value = 'printers';
        $router.push({ path: '/printers' });
      },
    },
    {
      label: 'Shipping Rules',
      value: 'templates',
      onClick: () => {
        menuTabValue.value = 'settings';
        settingTabValue.value = 'templates';
        $router.push({ path: '/templates' });
      },
    },
    {
      label: 'Agreement',
      value: 'agreement',
      onClick: () => {
        menuTabValue.value = 'settings';
        settingTabValue.value = 'agreement';
        $router.push({ path: '/agreement' });
      },
    },
  ]);

  $router.beforeEach((to, from, next) => {
    const token = HttpService.getToken();
    if (token) {
      $store.dispatch('companyStore/syncCompanyData');
    }
    next();
  });

  $router.afterEach(() => {
    hydrateMenuValue();
  });

  watch(
    () => width.value,
    (val) => {
      if (val <= 1024) {
        return;
      }

      if (!isSidebarCollapsed.value) {
        isSidebarCollapsed.value = true;
      }
    },
  );

  watch(
    () => menuTabValue.value,
    () => {
      resetSubMenuTab();
    },
  );

  const toggleSidebar = () => {
    isSidebarCollapsed.value = !isSidebarCollapsed.value;
  };
  const resetSubMenuTab = () => {
    ordersTabValue.value = null;
    shipmentTabValue.value = null;
    settingTabValue.value = null;
    paymentTabValue.value = null;
  };

  const hydrateMenuValue = () => {
    const path = $route.value.path.replace('/', '');
    if (path == 'orders' || path == 'order-invoices') {
      menuTabValue.value = 'orders';
      ordersTabValue.value = path;
    } else if (path == 'shipped' || path == 'drafts' || path == 'contacts') {
      menuTabValue.value = 'shipments';
      shipmentTabValue.value = path;
    } else if (path == 'transactions' || path == 'billing' || path == 'add-funds') {
      menuTabValue.value = 'payments';
      paymentTabValue.value = path;
    } else if (
      path == 'company' ||
      path == 'order-integration' ||
      path == 'payment-settings' ||
      path == 'personalized-messages' ||
      path == 'printers' ||
      path == 'templates' ||
      path == 'agreement' ||
      path == 'return-portals'
    ) {
      menuTabValue.value = 'settings';
      settingTabValue.value = path;
    } else if (path == '') {
      menuTabValue.value = 'dashboard';
    } else {
      menuTabValue.value = path;
    }

    // Handle default opening of "Account" tab when "Settings" is clicked
    if (menuTabValue.value === 'settings' && !settingTabValue.value) {
      settingTabValue.value = 'company';
      $router.push({ path: '/company' });
    }
  };

  const onClickOutside = (event: MouseEvent | Event) => {
    if (
      event.target instanceof HTMLElement &&
      (event.target.classList.contains('menu-icon') ||
        event.target.classList.contains('menu-icon--img'))
    ) {
      return;
    }

    if (!isSidebarCollapsed.value) {
      isSidebarCollapsed.value = true;
    }
  };
  onMounted(() => {
    hydrateMenuValue();
  });
</script>

<style lang="scss">
  .actions {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: end;
    width: 60px;
    max-width: 115px;

    .q-btn .q-icon {
      width: 33px !important;
      height: 33px !important;
    }

    button {
      border-radius: 5px;

      &:focus,
      &:hover {
        background: #f2f2f5;
      }
    }

    &:hover {
      .menuUserDropDown {
        display: block;
      }
    }
  }

  .menuUserDropDown {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 999;
    display: none;
    width: 250px;
    padding: 0;
    margin: 0;
    text-align: left;
    list-style: none;
    background: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2), 0 2px 2px rgba(0, 0, 0, 0.14),
      0 3px 1px -2px rgba(0, 0, 0, 0.12);

    li {
      padding: 10px 15px;
      text-transform: none;

      &:hover {
        background-color: #def5ff;
      }
    }

    .p-0 {
      padding: 0;
    }
  }
</style>
<style scoped lang="scss">
  header {
    position: relative;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 59px;
    padding-right: 36px;
    padding-left: 40px;
    background-color: #ffffff;
    border-bottom: 1px solid rgba(125, 131, 152, 0.2);

    @media (max-width: 1024px) {
      padding-right: 10px;
      padding-left: 10px;
    }

    .auth-logo {
      box-sizing: border-box;
      line-height: 0;
    }

    .menu-icon {
      box-sizing: border-box;
      display: none;
      line-height: 0;

      @media (max-width: 1024px) {
        display: block;
      }
    }

    .logo {
      box-sizing: border-box;
      line-height: 0;

      @media (max-width: 1024px) {
        margin-left: 20px;
      }
    }

    .profile-info {
      display: none;
      color: #1f2124;
    }

    @media screen and (min-width: 1025px) {
      // border-color: transparent;

      .contain {
        height: 39px;
        padding: 0 40px;
      }

      .auth-logo {
        display: flex;
        margin-right: 0;
      }

      .profile-info {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-right: 13px;

        p {
          margin-top: 2px;
          font-size: 14px;
        }

        span {
          display: block;
          margin-top: 18px;
          font-size: 13px;
          color: $third-three;
        }
      }

      .balance {
        display: none;
      }
    }
  }

  .desktop-menu--item {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 59px;
    padding: 0 17px;
    font-size: 15px;
    font-weight: 500;
    line-height: 17px;
    color: #313131;
    cursor: default;
    background-color: transparent;
    border: 0;
    transition: background-color 0.3s cubic-bezier(0.25, 0.8, 0.5, 1),
      opacity 0.4s cubic-bezier(0.25, 0.8, 0.5, 1);

    & + .desktop-menu--menu {
      position: absolute;
      display: none;

      &:hover {
        display: flex;
      }
    }

    &:hover {
      color: white;
      background-color: #243e90;
      //background-color: #FFFFFF;
    }

    &:hover + .desktop-menu--menu {
      display: flex;
      margin-left: 10px;
    }
  }

  .desktop-menu--item-icons {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 43px;
    padding: 0 12px;
    margin: 0 1px;
    font-size: 15px;
    font-weight: 500;
    line-height: 17px;
    color: #313131;
    cursor: default;
    background-color: transparent;
    border: 0;
    transition: background-color 0.3s cubic-bezier(0.25, 0.8, 0.5, 1),
      opacity 0.4s cubic-bezier(0.25, 0.8, 0.5, 1);

    & + .desktop-menu--menu {
      position: absolute;
      display: none;

      &:hover {
        display: flex;
        margin-left: 10px;
      }
    }

    &:hover {
      background-color: #f2f2f5;
      border-radius: 5px;
    }

    &:hover + .desktop-menu--menu {
      display: flex;
      margin-left: 10px;
    }
  }

  .desktop-menu--menu {
    position: relative;
    top: 58px;
    left: -10px;
    z-index: 99;
    display: flex;
    flex-direction: column;
    min-width: 190px;
    padding-top: 12px;
    padding-bottom: 15px;
    color: #313131;
    background-color: #ffff;
    // border-top: 1px solid #131393;
    border-bottom-right-radius: 15px;
    border-bottom-left-radius: 15px;
    box-shadow: 0 6px 25px rgba(125, 124, 135, 0.62);

    &:hover {
      display: flex;
      margin-left: 10px;
    }

    .desktop-menu--sub-menu:hover {
      display: flex;
    }
  }

  .desktop-menu--sub-menu--container {
    position: absolute;
    top: 0;
    right: 100%;
    //  left: 100%;
    z-index: 99;
    display: none;
    flex-direction: row;
    // border: 1px solid #e0e0e0;
    border-radius: 7px;
    box-shadow: 0 6px 6px rgba(161, 159, 187, 0.62);

    &:hover {
      display: flex;
    }
  }

  .desktop-menu--sub-menu {
    display: flex;
    flex-direction: column;
    min-width: 185px;
    padding-top: 12px;
    padding-bottom: 15px;
    color: #313131;
    background-color: #ffffff;
    //background-color: #243E90;
    border-radius: 5px;
  }

  button.desktop-menu--item,
  button.desktop-menu--menu-item {
    cursor: pointer;
  }

  .desktop-menu--menu-item {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 13px 25px;
    font-size: 15px;
    line-height: 17px;
    color: #313131;
    text-align: left;
    cursor: default;
    background-color: transparent;
    border: 0;

    &:hover {
      color: white;
      background-color: #243e90;
    }

    &:hover + .desktop-menu--sub-menu--container {
      display: flex;
    }

    &--active {
      color: white;
      background-color: #243e90;
    }
  }

  .desktop-menu--container {
    &:hover .desktop-menu--item {
      color: #ffffff;
      background-color: #243e90;
    }
  }

  .desktop-menu {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 37px;
    padding-left: 24px;
    margin-left: 60px;
    transition: height 150ms ease-in-out, opacity 150ms ease-in-out;
    @media (max-width: 1024px) {
      display: none;
      max-width: 100%;
      height: 0;
      overflow: hidden;
      pointer-events: none;
      opacity: 0;
    }
  }
</style>

<style lang="scss">
  .profile-info-button {
    margin-right: -4px;

    @media (max-width: 1024px) {
      align-items: center;
      justify-content: center;
      margin-right: 21px;
    }
  }

  a {
    text-decoration: none;
  }

  .desktop-menu--menu-item--count {
    display: flex;
    align-items: center;
    height: 18px;
    padding: 5px;
    font-size: 12px;
    font-variation-settings: 'wght' 700;
    color: #389b99;
    background-color: #ffffff;
    border-radius: 9px;
  }

  svg:hover {
    fill: red;
  }

  .header-sub-menu {
    display: flex;
    justify-content: start;
    min-height: 50px;
    padding: 5px 40px;
    overflow-x: auto;
    border-bottom: 1px solid rgba(125, 131, 152, 0.2);
    @media (max-width: 1024px) {
      display: none;
    }
  }

  .shipvagoo-sidebar-menu {
    @media (min-width: 1025px) {
      display: none;
    }
  }

  .desktop-menu--item-icons-settings {
    @media (max-width: 1024px) {
      display: none !important;
    }
  }
</style>
