<template>
  <div class="full-width integration-box flex column justify-between" v-if="ecommerceOrderIntegration">
    <div>
      <q-icon :name="ecommerceOrderIntegration.icon" :size="ecommerceOrderIntegration.size"
              style="color: #96BF47"></q-icon>
    </div>
    <div>
      <div class="text-dark text-weight-7 text-fourteen">
        {{ ecommerceOrderIntegration.name }}
      </div>
      <div class="flex items-center" v-if="ecommerceOrderIntegration.url">
        <shipvagoo-hyperlink
          type="external-link"
          label="How to connect"
          color="#006EFB"
          target="_blank"
          text-decoration="none"
          blank
          :to="ecommerceOrderIntegration.url"></shipvagoo-hyperlink>
        <q-icon name="fas fa-sign-in-alt"
                class="q-ml-sm"
                style="margin-top: 2px;font-size: 12px;color: #006EFB"
        ></q-icon>
      </div>
      <div class="text-dark" v-else>
        Coming soon
      </div>
    </div>
    <div class="full-width ">
      <shipvagoo-button
        height="32px"
        style="min-width: 120px;min-height: 32px !important"
        label="Connect"
        @click="onClickConnect"
        :disable="!ecommerceOrderIntegration.is_available"
        outline/>
    </div>
  </div>
</template>

<script setup lang="ts">
import {PropType} from "vue";
import {
  IOrderEcommerceIntegration
} from '../../../model/order-integration.model';

const emits = defineEmits(['clickConnect'])
defineProps({
  ecommerceOrderIntegration: Object as PropType<IOrderEcommerceIntegration>
})
const onClickConnect = () => {
  emits('clickConnect')
}
</script>

<style scoped>
.integration-box {
  width: 100%;
  min-height: 200px;
  padding: 18px;
  background: #FFF;
  border: 1px solid rgba(125, 131, 152, 0.20);
  border-radius: 8px;
}


</style>