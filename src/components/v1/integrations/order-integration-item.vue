<template>
  <div class="full-width integration-box flex column justify-between" v-if="orderIntegration">
    <div class="full-width flex justify-between">
      <div>
        <q-icon name="fab fa-shopify" size="lg" style="color: #96bf47"></q-icon>
      </div>
      <div>
        <div class="flex items-center gap-10">
          <shipvagoo-status-tag
            :bg-color="status == 'connected' ? '#BEF3D7' : '#FFD4E0'"
            :text="status.charAt(0).toUpperCase() + status.slice(1)"
            :color="status == 'connected' ? '#0C9247' : '#BE4748'"
          />
          <q-icon
            name="fas fa-sync"
            size="xs"
            @click="onClickRefresh"
            class="cursor-pointer"
            color="light-gray"
          ></q-icon>

          <q-icon
            name="fas fa-power-off"
            class="cursor-pointer"
            @click="onClickPowerOff"
            style=" font-size: 20px;color: #be4748"
          >
            <q-tooltip>Disconnect</q-tooltip>
          </q-icon>
        </div>
      </div>
    </div>
    <div class="full-width">
      <div class="dark-text text-weight-7 text-fourteen">
        {{ orderIntegration.name }}
      </div>
      <div class="q-mt-xs">
        <shipvagoo-hyperlink
          type="external-link"
          :label="orderIntegration.api_url"
          color="#006EFB"
          target="_blank"
          blank
          :to="orderIntegration.api_url"
        ></shipvagoo-hyperlink>
      </div>
    </div>
    <ul class="btnList">
      <li>
        <shipvagoo-button
          class="full-width"
          height="32px"
          style="min-height: 32px !important"
          label="My store"
          @click="emit('clickStore')"
          outline
        />
      </li>
      <li>
        <shipvagoo-button
          class="full-width"
          height="32px"
          style="min-height: 32px !important"
          label="Shipping & Delivery"
          @click="emit('clickShippingRule')"
          outline
        />
      </li>
      <li>
        <shipvagoo-button
          class="full-width"
          height="32px"
          style="min-height: 32px !important"
          label="Checkout rule"
          @click="emit('clickCheckoutRule')"
          outline
        />
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
  import { computed, PropType } from 'vue';
  import { IOrderIntegration } from '../../../model/order-integration.model';

  const props = defineProps({
    orderIntegration: Object as PropType<IOrderIntegration>,
  });
  const emit = defineEmits([
    'clickStore',
    'clickShippingRule',
    'clickCheckoutRule',
    'refresh',
    'activate',
    'disconnect',
  ]);
  const status = computed(() => {
    return props.orderIntegration?.status ? 'connected' : 'disconnected';
  });

  const onClickRefresh = () => {
    emit('refresh');
  };
  const onClickPowerOff = () => {
    emit('disconnect');
  };
</script>

<style lang="scss" scoped>
  .integration-box {
    min-height: 200px;
    padding: 18px;
    background: #fff;
    border: 1px solid rgba(125, 131, 152, 0.2);
    border-radius: 8px;
  }

  .btnList {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 0;
    margin: 0;
    list-style: none;

    li {
      flex: 1 0 0;
      padding: 1px;
    }

    button {
      padding: 4px 5px;
    }
  }
</style>
