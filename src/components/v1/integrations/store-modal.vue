<template>
  <shipvagoo-modal :header-separator="false" size="md" style="margin-top: 20px" title="My store">
    <shipvagoo-separator style="margin: 0 20px"></shipvagoo-separator>
    <div class="q-pa-md">
      <div class="flex gap-10 full-width q-py-sm">
        <shipvagoo-input-v1
          class="flex-1"
          v-model="form.name"
          label="Name"
          required
          :error="$v.name.$error"
          :error-message="String($v.name.$errors?.[0]?.$message ?? null)"
        />
        <shipvagoo-input-v1
          class="flex-1"
          v-model="form.email"
          label="Email"
          :error="$v.email.$error"
          :error-message="String($v.email.$errors?.[0]?.$message ?? null)"
        />
      </div>
      <div class="flex gap-10 full-width q-py-sm">
        <shipvagoo-input-v1
          class="flex-1"
          v-model="form.phoneNo"
          label="Phone No"
          :error="$v.phoneNo.$error"
          :error-message="String($v.phoneNo.$errors?.[0]?.$message ?? null)"
        />
        <shipvagoo-input-v1
          class="flex-1"
          v-model="form.address"
          label="Address"
          required
          :error="$v.address.$error"
          :error-message="String($v.address.$errors?.[0]?.$message ?? null)"
        />
      </div>
      <div class="flex gap-10 full-width q-py-sm">
        <shipvagoo-input-v1
          class="flex-1"
          v-model="form.city"
          label="City"
          required
          :error="$v.city.$error"
          :error-message="String($v.city.$errors?.[0]?.$message ?? null)"
        />
        <shipvagoo-select-v1
          class="flex-1"
          v-model="form.country"
          :label="$t('common.country')"
          :options="countries"
          option-label="default_name"
          option-value="country_code"
          :error="$v.country.$error"
          :error-message="String($v.country.$errors?.[0]?.$message ?? null)"
        >
          <template #prepend>
            <shipvagoo-country-icon
              v-if="form.country"
              :country="form.country as ICountry"
            ></shipvagoo-country-icon>
          </template>
          <template #option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section side>
                <shipvagoo-country-icon v-if="scope.opt" :country="scope.opt" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ scope.opt.default_name }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </shipvagoo-select-v1>
      </div>
      <div class="flex q-py-sm">
        <shipvagoo-select-v1
          style="width: 50%"
          v-model="form.currency"
          label="Currency"
          option-label="currency"
          option-value="currency_code"
          :options="currencies"
          :error="$v.currency.$error"
          :error-message="String($v.currency.$errors?.[0]?.$message ?? null)"
        >
          <template #option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section>
                <q-item-label>{{ scope.opt.currency }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </shipvagoo-select-v1>
      </div>
      <div class="flex justify-end gap-15 q-mt-md">
        <shipvagoo-button label="Cancel" style="min-width: 90px" outline v-close-popup />
        <shipvagoo-button
          :label="store ? 'Update' : 'Create'"
          style="min-width: 90px"
          @click="onClickSaveOrderIntegration"
        />
      </div>
    </div>
  </shipvagoo-modal>
</template>
<script setup lang="ts">
  import { IOrderIntegration, IStore } from '../../../model/order-integration.model';
  import { computed, onBeforeMount, ref } from 'vue';
  import { find } from 'lodash-es';
  import { euMember } from 'is-european';
  import { useStore } from 'vuex';
  import { IRootState } from '../../../model/store.model';
  import { useHelper } from '../../../composeable/Helper';
  import useVuelidate from '@vuelidate/core';
  import { helpers, required, email } from '@vuelidate/validators';
  import {
    loadingService,
    loggingService,
    notificationService,
    orderIntegrationService,
  } from '../../../services/_singletons';
  import { ICountry } from '../../../model/country.model';

  interface Props {
    store?: IOrderIntegration | null;
  }

  const props = defineProps<Props>();
  const emit = defineEmits(['close']);
  const { currencies } = useHelper();

  const $store = useStore<IRootState>();
  const countries = computed(() => {
    return $store.state.countryStore.countries.filter((c) => euMember(c.iso));
  });
  const form = ref<IStore>({
    name: '',
    email: '',
    phoneNo: '',
    address: '',
    currency: null,
    country: null,
    city: '',
  });

  const $v = useVuelidate(
    {
      name: {
        required: helpers.withMessage('This field is required.', required),
      },
      country: {
        required: helpers.withMessage('This field is required.', required),
      },
      email: {
        required: helpers.withMessage('This field is required.', required),
        email: helpers.withMessage('Enter a valid email address', email),
      },
      phoneNo: {
        required: helpers.withMessage('This field is required.', required),
      },
      address: {
        required: helpers.withMessage('This field is required.', required),
      },
      city: {
        required: helpers.withMessage('This field is required.', required),
      },
      currency: {
        required: helpers.withMessage('This field is required.', required),
      },
    },
    form,
  );
  const onClickSaveOrderIntegration = async () => {
    await $v.value.$validate();
    if ($v.value.$error) {
      return;
    }
    if (!props.store?.integration_code) {
      return;
    }
    try {
      loadingService.startLoading('main-loader:update-order-integration');
      await orderIntegrationService.updateOrderIntegration(props.store?.integration_code, {
        name: form.value.name,
        company_code: $store.state.companyStore.company_code,
        country_code: form.value.country?.country_code as number,
        currency_code: form.value.currency?.currency_code as number,
        email: form.value.email,
        phone_no: form.value.phoneNo,
        address: form.value.address,
        city: form.value.city,
      });
      notificationService.showSuccess('Updated order integration successfully');
      emit('close');
    } catch (error) {
      loggingService.error('Updating order integration', error);
      notificationService.showError('Error updating integration');
    } finally {
      loadingService.stopLoading('main-loader:update-order-integration');
    }
  };
  const hydrateStore = () => {
    if (props.store) {
      form.value = {
        name: props.store.name,
        email: props.store.email,
        phoneNo: props.store.phone_no,
        address: props.store.address,
        city: props.store.city,
        currency:
          find(currencies, {
            currency_code: props.store.currency_code,
          }) ?? null,
        country:
          find(countries.value, {
            country_code: props.store.country_code,
          }) ?? null,
      };
    }
  };
  onBeforeMount(() => {
    if (props.store) {
      hydrateStore();
    }
  });
</script>
