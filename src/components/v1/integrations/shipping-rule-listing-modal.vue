<template>
  <shipvagoo-modal
    :header-separator="false"
    size="x-lg"
    style="margin-top: 20px"
    title="Shipping and delivery"
  >
    <shipvagoo-separator style="margin: 0 20px" />
    <div class="q-px-md q-pb-lg q-mt-lg">
      <shipvagoo-table-v1
        v-model:pagination="deliveryMethodsPagination"
        :columns="deliveryMethodsColumns"
        :rows="deliveryMethodRows"
        :total="deliveryMethodRows.length"
        :loading="
          loadingService.loadings.has('delivery-methods:get-order-integration-delivery-methods')
        "
        @row-click="onClickDeliveryMethod"
      >
        <template #body-cell-status="props">
          <q-td :props="props" class="more">
            <div style="display: flex; flex-direction: row; gap: 8px; align-items: center">
              <shipvagoo-status-tag
                v-if="
                  (!props.row.third_party_delivered && props.row.delivery_template_code) ||
                  props.row.delivery_template_code
                "
                bg-color="#BEF3D7"
                color="#0C9247"
                text="Manually assigned"
              />

              <shipvagoo-status-tag
                v-else-if="props.row.third_party_delivered && !props.row.delivery_template_code"
                bg-color="#FFD4E0"
                color="#BE4748"
                text="Needs setup"
              />

              <shipvagoo-status-tag
                v-else-if="!props.row.third_party_delivered && !props.row.delivery_template_code"
                bg-color="#BEF3D7"
                color="#0C9247"
                text="Auto Assign"
              />

              <shipvagoo-status-tag v-else bg-color="#FFD4E0" color="#BE4748" text="Needs setup" />
            </div>
          </q-td>
        </template>
        <template #body-cell-carrier="props">
          <q-td :props="props" class="carrier">
            <div class="flex items-center no-wrap">
              <div
                v-if="props.row.delivery_template?.carrier_product_code"
                class="shipvagoo-table-carrier-logo"
                :style="{
                  backgroundImage: `url(${
                    CarrierService.getCarrierFromShippingProductCode(
                      $store.state.courierStore.carriers,
                      $store.state.courierStore.carrierProducts,
                      props.row.delivery_template.carrier_product_code,
                    )?.icon
                  })`,
                }"
              ></div>
              <div
                v-if="props.row.delivery_template?.carrier_product_code"
                class="text-no-wrap q-ml-sm shipped-carrier"
              >
                {{
                  CarrierService.getCarrierFromShippingProductCode(
                    $store.state.courierStore.carriers,
                    $store.state.courierStore.carrierProducts,
                    props.row.delivery_template.carrier_product_code,
                  )?.name
                }}
              </div>
            </div>
          </q-td>
        </template>
        <template #body-cell-service="props">
          <q-td :props="props" class="service">
            <div class="flex items-center no-wrap">
              <div
                v-if="props.row.delivery_template?.carrier_product_code"
                class="text-no-wrap q-ml-sm shipped-carrier"
              >
                {{
                  CarrierService.getCarrierProduct(
                    props.row.delivery_template.carrier_product_code,
                    $store.state.courierStore.carrierProducts,
                  )?.name
                }}
              </div>
            </div>
          </q-td>
        </template>
        <template #body-cell-more="props">
          <q-td :props="props" class="more">
            <q-btn
              :disable="
                loadingService.loadings.has(
                  'delivery-methods:get-order-integration-delivery-methods',
                )
              "
              dense
              flat
              round
              style="color: #1f2124"
              class="more-action"
              field="edit"
              icon="more_horiz"
            >
              <shipvagoo-menu>
                <shipvagoo-menu-item clickable @click="onClickDelete(props.row)">
                  Delete
                </shipvagoo-menu-item>
              </shipvagoo-menu>
            </q-btn>
          </q-td>
        </template>
      </shipvagoo-table-v1>
    </div>
  </shipvagoo-modal>
  <q-dialog v-if="selectedDeliveryMethodForEdit" v-model="isDeliveryMethodFormOpen">
    <shipvagoo-modal :header-separator="false" title="Change shipping rule">
      <shipvagoo-separator style="margin: 0 20px" />
      <!--      <shipvagoo-card-section>
              <shipvagoo-checkbox
                  v-model="deliveryMethodForm.isTransferOrderChecked.value"
                  label="Transfer orders that have this delivery method"
                  size="sm"
              />
            </shipvagoo-card-section>-->
      <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px; width: 100%">
        <div
          style="display: flex; flex-direction: row; gap: 20px; align-items: flex-end; width: 100%"
        >
          <shipvagoo-select-v1
            v-model="deliveryMethodForm.deliveryTemplate.value"
            label="Shipment template:"
            :options="templatesRefs"
            option-label="name"
            option-value="template_code"
            style="flex: 1"
            @filter="onFilterTemplates"
            use-input
            input-debounce="300"
            fill-input
            hide-selected
          >
            <template #prepend>
              <q-icon name="search" size="sm"></q-icon>
            </template>
          </shipvagoo-select-v1>
          <!--          <shipvagoo-button
                      @click="onClickCreateTemplate('delivery')"
                      @close="onCloseCreateTemplate"
                    >
                      Create Template
                    </shipvagoo-button>-->
        </div>

        <!--        <div
                    style="display: flex; flex-direction: row; gap: 20px; align-items: flex-end; width: 100%">
                  <shipvagoo-select
                      v-model="deliveryMethodForm.returnTemplate.value"
                      label="Return shipment template:"
                      :options="templates"
                      option-label="name"
                      option-value="template_code"
                      style="flex: 1"
                  />
                  <shipvagoo-button @click="onClickCreateTemplate('return')">
                    Create Template
                  </shipvagoo-button
                  >
                </div>-->
      </shipvagoo-card-section>
      <!--      <shipvagoo-separator/>-->
      <!--      <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 10px">-->
      <!--        <shipvagoo-checkbox size="sm" label="This delivery method is delivered by a third party"/>-->
      <!--        <shipvagoo-checkbox-->
      <!--          size="sm"-->
      <!--          label="Skip using shipment template for this delivery method"-->
      <!--        />-->
      <!--      </shipvagoo-card-section>-->
      <!--      <shipvagoo-separator/>-->
      <!--      <shipvagoo-card-section>-->
      <!--        <shipvagoo-select-->
      <!--          v-model="deliveryMethodForm.packaging.value"-->
      <!--          label="Packaging:"-->
      <!--          :options="packagings"-->
      <!--          option-label="name"-->
      <!--          option-value="packaging_code"-->
      <!--        />-->
      <!--      </shipvagoo-card-section>-->
      <shipvagoo-card-actions>
        <shipvagoo-button label="Save" min-width="80px" @click="onClickUpdateDeliveryMethod" />
      </shipvagoo-card-actions>
    </shipvagoo-modal>
  </q-dialog>

  <q-dialog v-model="showDeleteConfirm">
    <confirm-popup
      @confirm="onClickDeleteDeliveryMethod"
      confirm-label="Delete"
      confirmation-message="You are about to delete this shipping and delivery. This action 
cannot be undone"
    />
  </q-dialog>
</template>
<script setup lang="ts">
  import {
    loadingService,
    loggingService,
    notificationService,
    orderIntegrationService,
    packagingService,
    templateService,
  } from '../../../services/_singletons';
  import { onBeforeMount, ref, Ref } from 'vue';
  import { Pagination } from '../../../model/common.model';
  import { QTableProps } from 'quasar';
  import { IDeliveryMethod, IOrderIntegration } from '../../../model/order-integration.model';
  import HelperService from '../../../services/helper.service';
  import { ITemplate } from '../../../model/template.model';
  import { IPackaging } from '../../../model/packaging.model';
  import { IRequestParam } from '~/model/http.model';
  import CarrierService from '../../../services/carrier.service';
  import { useStore } from '../../../store';

  interface Props {
    store: IOrderIntegration;
  }

  const $store = useStore();
  const props = defineProps<Props>();
  const deliveryMethodsPagination: Ref<Pagination> = ref({
    page: 1,
    rowsPerPage: 10,
    descending: true,
  });

  const deliveryMethodsColumns: QTableProps['columns'] = [
    { name: 'name', label: 'Title', field: 'name', align: 'left' },
    { name: 'carrier', label: 'Carrier', field: '', align: 'left' },
    { name: 'service', label: 'Service', field: '', align: 'left' },
    {
      name: 'template',
      label: 'Shipping rule',
      field: (row: IDeliveryMethod) => row.delivery_template?.name,
      align: 'left',
    },
    {
      name: 'status',
      label: 'Status',
      field: () => '',
      align: 'left',
    },
    { name: 'more', align: 'right', label: 'Action', field: 'more' },
  ];

  const deliveryMethodForm = {
    isTransferOrderChecked: ref<boolean>(false),
    deliveryTemplate: ref<ITemplate | null>(null),
    returnTemplate: ref<ITemplate | null>(null),
    packaging: ref<IPackaging | null>(null),
    isDeliveredByThirdParty: ref<boolean>(false),
    isSkipUsingShipmentTemplate: ref<boolean>(false),
  };
  const showDeleteConfirm = ref<boolean>(false);
  const isDeliveryMethodFormOpen = ref<boolean>(false);
  const selectedDeliveryMethodForEdit = ref<IDeliveryMethod | null>(null);
  const deliveryMethodRows = ref<IDeliveryMethod[]>([]);
  const templates = ref<ITemplate[]>([]);
  const packagings = ref<IPackaging[]>([]);
  //const isCreateTemplateOpen = ref<boolean>(false);
  //const createTemplateType = ref<'delivery' | 'return' | null>(null);

  const getShippingRules = async () => {
    const response = await orderIntegrationService.getDeliveryMethods(props.store.integration_code);
    deliveryMethodRows.value = response;
  };
  const onClickDelete = (row: IDeliveryMethod) => {
    showDeleteConfirm.value = true;
    selectedDeliveryMethodForEdit.value = row;
  };
  const onClickDeleteDeliveryMethod = async () => {
    if (!selectedDeliveryMethodForEdit.value) {
      return;
    }
    try {
      loadingService.startLoading('main-loader:delete-order-integration-delivery-method');
      await orderIntegrationService.deleteDeliveryMethod(
        selectedDeliveryMethodForEdit.value?.delivery_method_code,
      );
      deliveryMethodRows.value = deliveryMethodRows.value.filter(
        (item) =>
          item.delivery_method_code !== selectedDeliveryMethodForEdit.value?.delivery_method_code,
      );
      notificationService.showSuccess('Deleted delivery method successfully');
    } catch (error) {
      loggingService.error('Deleting order integration delivery method', error);
      notificationService.showError('Error deleting delivery method');
    } finally {
      loadingService.stopLoading('main-loader:delete-order-integration-delivery-method');
    }
  };
  const onClickUpdateDeliveryMethod = async () => {
    if (!selectedDeliveryMethodForEdit.value) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:update-order-integration-delivery-method');
      await orderIntegrationService.updateDeliveryMethod(
        selectedDeliveryMethodForEdit.value.delivery_method_code,
        {
          delivery_template_code: deliveryMethodForm.deliveryTemplate.value?.template_code ?? null,
          return_template_code: deliveryMethodForm.returnTemplate.value?.template_code ?? null,
          packaging_code: deliveryMethodForm.packaging.value?.packaging_code ?? null,
          transfer_orders: deliveryMethodForm.isTransferOrderChecked.value,
          third_party_delivered: deliveryMethodForm.isDeliveredByThirdParty.value,
          skip_shipment: deliveryMethodForm.isSkipUsingShipmentTemplate.value,
        },
      );

      notificationService.showSuccess('Updated delivery method successfully');
      isDeliveryMethodFormOpen.value = false;
      getShippingRules();
    } catch (error) {
      loggingService.error('Updating order integration delivery method', error);
      notificationService.showError('Error updating delivery method');
    } finally {
      loadingService.stopLoading('main-loader:update-order-integration-delivery-method');
    }
  };

  const getTemplates = async () => {
    try {
      loadingService.startLoading('main-loader:get-templates');
      const params: IRequestParam[] = [{ key: 'sorting[created_at]', value: 'desc' }];
      const response = await templateService.getTemplates(1, 1000, params);
      templates.value = response.entities;
    } catch (error) {
      loggingService.error('Error loading templates', error);
      notificationService.showError('Error loading templates');
    } finally {
      loadingService.stopLoading('main-loader:get-templates');
    }
  };
  const onClickDeliveryMethod = async (event: Event, row: IDeliveryMethod) => {
    if (
      loadingService.loadings.has('delivery-methods:get-order-integration-delivery-methods') ||
      HelperService.containsIgnoredRowElements(event)
    )
      return;

    selectedDeliveryMethodForEdit.value = row;
    isDeliveryMethodFormOpen.value = true;

    try {
      loadingService.startLoading('main-loader:get-order-integration-delivery-method');
      await getTemplates();

      const packagingsResponse = await packagingService.getPackagings(1, 1000);
      packagings.value = packagingsResponse.entities;

      deliveryMethodForm.isTransferOrderChecked.value = Boolean(row.transfer_orders);
      deliveryMethodForm.isDeliveredByThirdParty.value = Boolean(row.third_party_delivered);
      deliveryMethodForm.isSkipUsingShipmentTemplate.value = Boolean(row.skip_shipment);

      const deliveryTemplate: ITemplate | undefined = templates.value.find(
        (item) => item.template_code === row.delivery_template_code,
      );

      if (deliveryTemplate) {
        deliveryMethodForm.deliveryTemplate.value = deliveryTemplate;
      }

      const returnTemplate: ITemplate | undefined = templates.value.find(
        (item) => item.template_code === row.return_template_code,
      );

      if (returnTemplate) {
        deliveryMethodForm.returnTemplate.value = returnTemplate;
      }

      const packaging: IPackaging | undefined = packagings.value.find(
        (item) => item.packaging_code === row.packaging_code,
      );

      if (packaging) {
        deliveryMethodForm.packaging.value = packaging;
      }
    } catch (error) {
      loggingService.error('Loading order integration delivery method', error);
      notificationService.showError('Error loading order integration delivery method');
    } finally {
      loadingService.stopLoading('main-loader:get-order-integration-delivery-method');
    }
  };

  const templatesRefs = ref<ITemplate[]>([]);

  const onFilterTemplates = (val: string, update: CallableFunction) => {
    if (val === '') {
      update(() => {
        // Reset to original templates when search term is empty
        templatesRefs.value = templates.value;
      });
      return;
    }

    update(() => {
      const needle = val.toLowerCase();
      // Filter templates based on the search term
      templatesRefs.value = templates.value.filter((template: ITemplate) => {
        return template.name.toLowerCase().indexOf(needle) > -1;
      });
    });
  };

  /*const onCloseCreateTemplate = async (is?: 'create') => {
    isCreateTemplateOpen.value = false;

    if (is !== 'create') {
      return;
    }

    await getTemplates();

    const [template] = templates.value;

    if (createTemplateType.value === 'delivery') {
      deliveryMethodForm.deliveryTemplate.value = template;
    }

    if (createTemplateType.value === 'return') {
      deliveryMethodForm.returnTemplate.value = template;
    }
  };

  const onClickCreateTemplate = (type: 'delivery' | 'return') => {
    createTemplateType.value = type;
    isCreateTemplateOpen.value = true;
  };*/
  onBeforeMount(() => {
    getShippingRules();
  });
</script>
