<template>
  <div class="disconnect-popup-container">
    <shipvagoo-card-section class="ui_kit-shipvagoo-modal-header row items-center" style="padding: 10px 20px 0 20px">
      <q-space/>
      <slot name="icon-right"/>
      <q-btn v-close-popup icon="close" color="grey" flat round dense @click="onClickClose"/>
    </shipvagoo-card-section>
    <div class="text-center text-dark text-sixteen text-weight-7" style="margin-top: -10px">
      Do you want to disconnect?
    </div>
    <div style=" font-size: 13px;color: #7D8398;text-align: center; " class="q-ma-md">
      If you disconnect your Shopify store, Shipvagoo will not be able to import orders or synchronize data. You can
      always reconnect your store when needed.
    </div>
    <div class="q-ma-md flex justify-between gap-10 q-mt-md">
      <div class="flex-1">
        <shipvagoo-button
          height="36px"
          style="min-height: 36px"
          no-caps
          @click="onClickClose"
          v-close-popup
          class="full-width"
          outline
          label="Cancel"/>
      </div>
      <div class="flex-1">
        <shipvagoo-button
          height="36px"
          style="min-height: 36px"
          no-caps
          @click="onClickConfirm"
          v-close-popup
          class="full-width"
          label="Confirm"/>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(['close', 'confirm']);

const onClickClose = () => {
  emit('close');
};
const onClickConfirm = () => {
  emit('confirm')
}
</script>

<style scoped>
.disconnect-popup-container {
  width: 450px;
  max-width: 450px;
  background-color: white;
}


</style>