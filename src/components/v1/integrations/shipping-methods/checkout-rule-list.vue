<template>
  <div class="flex justify-end">
    <shipvagoo-button label="Create checkout rule" @click="onClickCreate" />
  </div>
  <shipvagoo-table-v1 style="margin-top: 20px" v-model:pagination="pagination" :columns="columns" :rows="rows"
    :total="pagination.rowsNumber" :loading="loadingService.loadings.has('main-loader:get-printers')"
    @request="getShippingMethods($event.pagination)" @row-click="onClickShippingMethod">
    <template #body-cell-country_code="props">
      <q-td :props="props">
        <div v-if="$store.state.countryStore.countries.find(
          (c) => c.country_code === props.row.country_code,
        )
        " style="display: flex; gap: 10px; justify-content: flex-start">
          <img decoding="async" :src="`/assets/img/${$store.state.countryStore.countriesIndexedByCountryCode[
            props.row.country_code
          ]?.iso.toLowerCase()}.svg`" style="width: 20px; height: 20px; border-radius: 50%; object-fit: cover" />
          <span>{{
            $store.state.countryStore.countriesIndexedByCountryCode[props.row.country_code]?.default_name
            }}</span>
        </div>
      </q-td>
    </template>
    <!-- code for logo starts here -->
    <template #body-cell-carrier_product_code="props">
      <q-td :props="props">
        <div v-if="$store.state.courierStore.carrierProducts.find(
          (c) => c.carrier_product_code === props.row.carrier_product_code,
        )
        " style="display: flex; gap: 10px; justify-content: flex-start">
          <img decoding="async" :src="`${props.row.carrier.icon}`"
            style="width: 20px; height: 20px; border-radius: 50%; object-fit: cover" />
          <span>{{
            $store.state.courierStore.carrierProducts.find(
              (c) => c.carrier_product_code === props.row.carrier_product_code,
            )?.name
          }}</span>
        </div>
      </q-td>
    </template>
    <!-- code for logo end here -->
    <template #body-cell-conditions="props">
      <q-td :props="props">
        <div v-if="props.row.order_amount_based">
          <span class="text-no-wrap">Amount min: {{
            PriceService.formatPriceTextField(Number(props.row.order_amount_minimum.replace(/,/g, '').replace('.',
              '.')))
          }} {{ integrationCurrency }}</span><br />
          <span class="text-no-wrap">Amount max: {{
            PriceService.formatPriceTextField(Number(props.row.order_amount_maximum.replace(/,/g, '').replace('.',
              '.')))
          }} {{ integrationCurrency }}</span>
        </div>
        <div v-if="props.row.order_weight_based" class="q-mt-sm">
          <span class="text-no-wrap">Cart weight min: {{
            PriceService.shippingPriceFormat(props.row.order_weight_minimum, 'de-DE')
            }} gram</span><br />
          <span class="text-no-wrap">Cart weight max: {{
            PriceService.shippingPriceFormat(props.row.order_weight_maximum, 'de-DE')
            }} gram</span>
        </div>
        <div v-if="props.row.item_quantity_based" class="q-mt-sm">
          <span class="text-no-wrap">Cart value min: {{ props.row.item_quantity_minimum }}</span><br />
          <span class="text-no-wrap">Cart value max: {{ props.row.item_quantity_maximum }}</span>
        </div>
        <div v-if="props.row.zipcode_based" class="q-mt-sm">
          <span class="text-no-wrap">Postal code from: {{ props.row.zipcode_from }}</span><br />
          <span class="text-no-wrap">Postal code to: {{ props.row.zipcode_to }}</span>
        </div>
      </q-td>
    </template>
    <template #body-cell-price="props">
      <q-td :props="props">
        <div style="display: flex; gap: 10px; justify-content: flex-end">
          <span>{{
            PriceService.formatPriceTextField(Number(props.row.price.toString().replace(/,/g, '').replace('.', '.')))
            }} {{ getCurrency(props.row.currency_code) }}</span>
        </div>
      </q-td>
    </template>

    <template #body-cell-more="props">
      <q-td :props="props" class="more">
        <q-btn dense flat round style="color:#1f2124;" class="more-action" field="edit" icon="more_horiz">
          <shipvagoo-menu>
            <shipvagoo-menu-item clickable @click="onClickDeleteShippingMethod(props.row)">
              {{ $t('common.delete') }}
            </shipvagoo-menu-item>
          </shipvagoo-menu>
        </q-btn>
      </q-td>
    </template>
  </shipvagoo-table-v1>
  <!-- <shipvagoo-fab icon="add" @click="onClickCreate"/>-->

  <q-dialog persistent @keydown.esc="() => isShippingMethodDetailOpen = false" v-if="isShippingMethodDetailOpen"
    v-model="isShippingMethodDetailOpen">
    <shipping-method-detail-v1 :integration-code="integration.integration_code.toString()"
      :shipping_method="selectedShippingMethodToEdit" @close="onCloseShippingMethodDetail"
      @create="getShippingMethods(null)" @update="getShippingMethods(pagination)" />
  </q-dialog>
</template>

<script setup lang="ts">
import { QTableProps } from 'quasar';
import { computed, onBeforeMount, Ref, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useStore } from 'vuex';
import { Pagination } from '../../../../model/common.model';
import { IRequestParam } from '../../../../model/http.model';
import { IPrinter } from '../../../../model/printer.model';
import { IRootState } from '../../../../model/store.model';
import HelperService from '../../../../services/helper.service';
import {
  IOrderIntegration,
} from '../../../../model/order-integration.model';
import {
  loadingService,
  loggingService,
  notificationService,
  shippingMethodService,
} from '../../../../services/_singletons';
import { IShippingMethodResponse } from "../../../../model/shipping-method.model";
import PriceService from "../../../../services/price.service";
import { useHelper } from "../../../../composeable/Helper";

const { currencies } = useHelper()

interface Props {
  integration: IOrderIntegration
}

const props = defineProps<Props>();
const $store = useStore<IRootState>();

const { t } = useI18n();
const integrationCurrency = computed(() => {
  return props.integration.currency ?? ''
})
const search = ref<string>('');
const columns = computed<QTableProps['columns']>(() => [
  { field: 'id', name: 'id', label: t('common.id'), align: 'left' },
  { field: 'title', name: 'title', label: 'Checkout title', align: 'left' },
  { field: 'country_code', name: 'country_code', label: 'Destination country', align: 'left' },
  { field: 'carrier_product_code', name: 'carrier_product_code', label: 'Service', align: 'left' },
  { field: 'conditions', name: 'conditions', label: 'Conditions', align: 'left' },
  {
    field: 'price', name: 'price',
    label: 'Checkout price', align: 'right'
  },
  { field: 'more', name: 'more', label: 'Action', align: 'right' },
]);
const rows = ref<IShippingMethodResponse[]>([]);
const pagination: Ref<Pagination> = ref({
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
  descending: true,
});
const isShippingMethodDetailOpen = ref<boolean>(false);
const selectedShippingMethodToEdit = ref<IShippingMethodResponse | null>(null);

const getCurrency = (currency_code: number) => {
  return currencies.find((item) => currency_code == item.currency_code)?.currency
}
const getShippingMethods = async (tablePagination: Pagination | null) => {
  try {
    loadingService.startLoading('main-loader:get-printers');
    const page: number = tablePagination?.page || 1;
    const limit: number = tablePagination?.rowsPerPage || 10;
    const params: IRequestParam[] = [];

    if (search.value && search.value !== '') {
      params.push(
        { key: 'conditions[customer_code:like]', value: search.value },
        { key: 'conditions[email:or_like]', value: search.value },
        { key: 'conditions[first_name:or_like]', value: search.value },
        { key: 'conditions[last_name:or_like]', value: search.value },
      );
    }

    if (tablePagination?.sortBy) {
      const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
      params.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
    } else {
      params.push({ key: 'sorting[created_at]', value: 'asc' });
    }

    const result = await shippingMethodService.getShippingMethods(page, limit, params, props.integration.integration_code.toString());
    rows.value = result.entities;
    pagination.value.rowsNumber = result.total;
    pagination.value.page = page;
    pagination.value.rowsPerPage = limit;
    pagination.value.descending = tablePagination?.descending;
    pagination.value.sortBy = tablePagination?.sortBy;
  } catch (error) {
    loggingService.error('Error getting Shipping Methods', error);
    notificationService.showError('Error getting Shipping Methods');
  } finally {
    loadingService.stopLoading('main-loader:get-printers');
  }
};

const onClickShippingMethod = (event: Event, shipping_method: IShippingMethodResponse) => {
  if (HelperService.containsIgnoredRowElements(event)) {
    return;
  }

  selectedShippingMethodToEdit.value = shipping_method;
  isShippingMethodDetailOpen.value = true;
};

const onClickDeleteShippingMethod = async (row: IPrinter) => {
  try {
    loadingService.startLoading('main-loader:delete-printer');
    await shippingMethodService.deleteShippingMethod(row.id);
    getShippingMethods(pagination.value);
    notificationService.showSuccess('Shipping Method deleted successfully');
  } catch (error) {
    loggingService.error('Error deleting Shipping Method', error);
    notificationService.showError('Error deleting Shipping Method');
  } finally {
    loadingService.stopLoading('main-loader:delete-printer');
  }
};

const onClickCreate = () => {
  selectedShippingMethodToEdit.value = null;
  isShippingMethodDetailOpen.value = true;
};

const onCloseShippingMethodDetail = () => {
  isShippingMethodDetailOpen.value = false;
  selectedShippingMethodToEdit.value = null;
};

onBeforeMount(() => {
  if (props.integration.integration_code) {
    getShippingMethods(null);
  }
});
</script>
