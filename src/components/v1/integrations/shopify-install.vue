<template>
  <div class="disconnect-popup-container">
    <shipvagoo-card-section class="ui_kit-shipvagoo-modal-header row items-center">
      <q-space/>
      <slot name="icon-right"/>
      <q-btn v-close-popup icon="close" color="grey" flat round dense/>
    </shipvagoo-card-section>

    <div class="alert-icon-box">
      <div style="width: auto;">
        <!--<img :src="ShopifyLogo" style="width: 75px;height: 75px;"/>-->
        <q-icon style="font-size: 60px;color: #96BF47;" name="fab fa-shopify"></q-icon>
      </div>
    </div>
    <shipvagoo-separator color="#EEEEEE" class="q-my-lg"></shipvagoo-separator>
    <div class="q-ma-md">
      <shipvagoo-input
        v-model="shopifyUrl"
        @update:modelValue="$emit('update:url', shopifyUrl)"
        placeholder="Please paste link here...">
        <template v-slot:append>
          <shipvagoo-button
            style="width: 90px;margin-right: -4px; ;font-size: 15px;color:white;"
            no-caps
            @click="onClickInstall"
            label="Install"/>
        </template>
      </shipvagoo-input>
      <div/>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {ref} from 'vue';
  // import ShopifyLogo from '../../../assets/img/shopify-logo.svg';

  const props = defineProps(['url'])
  const emit = defineEmits(['update:url', 'install']);
  const shopifyUrl = ref(props.url)
  const onClickInstall = () => {
    emit('install')
  }
</script>

<style scoped>
  .disconnect-popup-container {
    width: 450px;
    max-width: 450px;
    background-color: white;
  }

  .alert-icon-box {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: -40px;
  }

</style>