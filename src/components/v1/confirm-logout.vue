<template>
  <div class="disconnect-popup-container">
    <shipvagoo-card-section class="ui_kit-shipvagoo-modal-header row items-center">
      <q-space/>
      <slot name="icon-right"/>
      <q-btn v-close-popup icon="close" color="grey" flat round dense/>
    </shipvagoo-card-section>

    <div class="alert-icon-box">
      <div style="width: auto;">
        <div class="text-center q-mt-sm"
             style="font-size: 21px;font-weight: 600;color:#313131;">Sign out
        </div>
      </div>
    </div>
    <div style=" font-size: 16px;color: #313131;text-align: center; " class="q-mt-sm">
      Are you sure you want to sign out?
    </div>
    <div class="q-ma-md flex justify-center q-mt-lg">
      <div style="display: flex; justify-content: space-between !important;">
        <q-btn
          style="flex-grow: 1;
         width: 180px;font-size: 16px;
         color: #243E90;background: #243E90"
          no-caps
          size="lg"
          @click="onClickYes"
          v-close-popup
          class="q-mr-sm"
          outline
          label="Yes"/>
        <q-btn
          class="q-ml-sm popup-button-text"
          style="flex-grow: 1;width: 180px;font-size: 15px; color: white;background: #243E90"
          no-caps
          size="lg"
          v-close-popup
          label="No"/>
      </div>
      <div/>
    </div>
  </div>
</template>

<script setup lang="ts">

  const emit = defineEmits(['close', 'confirm']);

  const onClickYes = () => {
    emit('confirm')
  }
</script>

<style scoped>
  .disconnect-popup-container {
    width: 420px;
    max-width: 420px;
    background-color: white;
  }

  .alert-icon-box {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: -40px;
  }

  .popup-button-text {
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 1px;
  }
</style>