<template>
  <div style="height: 0; overflow: hidden">
    <div
      v-if="transactionToPrint"
      id="transactions-pdf-content"
      aria-hidden
      style="position: relative; width: 2480px; height: 3507px"
    >
      <div style="display: flex; justify-content: end; width: 100%">
        <h1 style="font-size: 80px; font-weight: 400; color: #1f2124; letter-spacing: 4px">
          Payment Confirmation
        </h1>
      </div>
      <div style="width: 100%; height: 3px; margin-top: 60px; background-color: #cccccc"></div>
      <div
        style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          padding-top: 100px;
        "
      >
        <div style="font-size: 45px; color: #222234">
          <div style="color: #1f2124">
            {{ $store.state.companyStore.company_name }}
          </div>
          <div>{{ $store.state.companyStore.address }}</div>
          <div>
            {{ $store.state.companyStore.country?.iso }}-{{ $store.state.companyStore.zipcode }}
            {{ $store.state.companyStore.city }}
          </div>
          <div>VAT No:{{ $store.state.companyStore.vat_no }}</div>
        </div>
        <div
          style="
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            min-width: 620px;
          "
        >
          <div>
            <div style="font-size: 40px; font-weight: 700; color: #222234">Date:</div>
            <div style="font-size: 40px; font-weight: 700; color: #222234">Reference No:</div>
            <div style="font-size: 40px; font-weight: 700; color: #222234">Customer No:</div>
          </div>
          <div>
            <div style="font-size: 40px">
              {{ dayjs(transactionToPrint.created_at).format('DD-MM-YYYY') }}
            </div>
            <div style="font-size: 40px">{{ transactionToPrint.payment_id }}</div>
            <div style="font-size: 40px">{{ transactionToPrint.company_code }}</div>
          </div>
        </div>
      </div>
      <table style="width: 100%; margin-top: 135px; border-collapse: collapse">
        <thead>
          <tr style="height: 120px; font-size: 35px; color: #757575">
            <th
              style="
                width: 34%;
                padding-left: 40px;
                font-weight: 500;
                text-align: left;
                background-color: #f5f5f5;
                border-radius: 15px 0 0 15px;
              "
            >
              {{ $t('common.description') }}
            </th>
            <th
              style="
                width: 33%;
                padding-bottom: 16px;
                font-weight: 500;
                text-align: center;
                background-color: #f5f5f5;
              "
            >
              Transaction No
            </th>
            <th
              style="
                width: 33%;
                padding-right: 40px;
                padding-bottom: 16px;
                font-weight: 500;
                text-align: right;
                background-color: #f5f5f5;
                border-radius: 0 15px 15px 0;
              "
            >
              Total (DKK)
            </th>
          </tr>
        </thead>
        <tbody>
          <tr style="font-size: 38px">
            <td style="padding-left: 40px">
              {{ transactionToPrint.description }}
            </td>
            <td style="padding-right: 40px; text-align: center">
              {{ HelperService.formatId(transactionToPrint.id) }}
            </td>
            <td style="padding-right: 40px; text-align: right">
              {{ PriceService.formatPrice(Number(transactionToPrint.amount)) }}
            </td>
          </tr>
        </tbody>
      </table>
      <pdf-footer></pdf-footer>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import type { PropType } from 'vue';
  import { nextTick, onMounted, watch } from 'vue';
  import { ITransaction } from '../../../model/transactions.model';
  import Html2Canvas from 'html2canvas';
  import JsPDF from 'jspdf';
  import HelperService from '../../../services/helper.service';
  import { useStore } from '../../../store';
  import dayjs from 'dayjs';
  import PriceService from '../../../services/price.service';

  const $store = useStore();
  const props = defineProps({
    transactionToPrint: Object as PropType<ITransaction>,
  });
  watch(
    () => props.transactionToPrint as ITransaction,
    (newVal: ITransaction, oldVal) => {
      printPDF();
    },
  );
  const printPDF = async () => {
    await nextTick();

    const el = document.getElementById('transactions-pdf-content');
    const logo = document.querySelector('.pdf-logo');

    if (!props.transactionToPrint || !el || !logo || !(logo instanceof HTMLImageElement)) {
      return;
    }

    const PAGE_WIDTH_MM: number = 210;
    const DESIGN_WIDTH: number = 2480;
    const SCALE_MULTIPLIER: number = PAGE_WIDTH_MM / DESIGN_WIDTH;

    const logoCanvas = await Html2Canvas(logo, {
      useCORS: true,
    });

    const jsPDFInstance = new JsPDF('p', 'mm', 'a4');

    await jsPDFInstance.html(el, {
      width: 210,
      html2canvas: {
        scale: SCALE_MULTIPLIER,
        useCORS: true,
      },
    });

    jsPDFInstance.addImage(
      logoCanvas.toDataURL('image/png'),
      'PNG',
      12,
      //210 - 662.88 * SCALE_MULTIPLIER - 150 * SCALE_MULTIPLIER,
      150 * SCALE_MULTIPLIER,
      662.88 * SCALE_MULTIPLIER,
      106 * SCALE_MULTIPLIER,
    );
    jsPDFInstance.save(`${HelperService.formatId(props.transactionToPrint.id)}.pdf`);
  };
  onMounted(() => {
    if (props.transactionToPrint) {
      printPDF();
    }
  });
</script>

<style scoped lang="scss">
  #transactions-pdf-content {
    padding: 150px;
    color: black !important;
    letter-spacing: 0.01px !important;
    background-color: white;

    h1 {
      margin: 0;
    }
  }
</style>
