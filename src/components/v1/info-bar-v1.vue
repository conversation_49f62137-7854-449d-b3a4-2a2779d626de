<template>
  <div class="shipvagoo_info">
    <div class="shipvagoo_info-head">
      <img v-if="$props.iconType === 'src'" src="../../assets/img/info.svg" decoding="async" />
      <q-icon
        v-if="$props.iconType === 'q-icon'"
        :style="{ color: $props.color }"
        :size="$props.size"
        :name="$props.icon"
      />
    </div>
    <div class="shipvagoo_info-content" :class="{ 'shipvagoo_info-content-dense': $props.dense }">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
  interface Props {
    icon?: string;
    size?: string;
    color?: string;
    dense?: boolean;
    iconType?: 'src' | 'q-icon';
  }

  withDefaults(defineProps<Props>(), {
    color: '#2D60E5',
    dense: false,
    iconType: 'q-icon',
    icon: 'fas fa-info-circle',
    size: '20px',
  });
</script>

<style lang="scss" scoped>
  .shipvagoo_info {
    display: flex;
    overflow: hidden;
    background-color: #ebf0fd;
    border-left: 5px solid #2d60e5;
    border-radius: 6px;

    &-head {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 40px;
      color: #2d60e5;

      img {
        width: 18.59px;
        height: 18.59px;
      }
    }

    &-content {
      padding: 20px 3px 20px 3px;
      margin-top: -2px;
      margin-left: -5px;
      font-size: 11.8px;
      font-weight: 400;

      :deep(p) {
        margin-bottom: 10px;
      }

      :deep(p:last-child) {
        margin-bottom: 0;
      }

      &-dense {
        padding: 13px 2px 13px 0;
        color: #1f2124;
      }
    }
  }
</style>
