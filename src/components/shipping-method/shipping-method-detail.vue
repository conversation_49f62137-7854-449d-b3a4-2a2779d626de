<template>
  <shipvagoo-modal :title="shippingMethodDetailTitle" @close="onClose">
    <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
      <shipvagoo-input v-model="form.title" :label="$t('common.title')"/>
      <shipvagoo-select
        v-model="form.receiveCountry"
        :label="$t('common.country')"
        :options="countries"
        option-label="default_name"
        option-value="country_code"
        @update:model-value="onChangeReceiveCountry"
      >
        <template #prepend>
          <shipvagoo-country-icon
            v-if="form.receiveCountry"
            :country="form.receiveCountry"
          />
        </template>
        <template #option="scope">
          <q-item v-bind="scope.itemProps">
            <q-item-section side>
              <shipvagoo-country-icon v-if="scope.opt" :country="scope.opt"/>
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ scope.opt.default_name }}</q-item-label>
            </q-item-section>
          </q-item>
        </template>
      </shipvagoo-select>
      <shipvagoo-input-group responsive>
        <shipvagoo-select
          v-model="form.courier"
          :options="carriers"
          :label="$t('common.carrier')"
          option-label="name"
          option-value="carrier_code"
          @update:model-value="onChangeCarrier"
        >
          <template #prepend>
            <img
              v-if="form.courier"
              width="17"
              height="17"
              :src="`${form.courier.icon}`"
              alt=""
              decoding="async"
              style="border-radius: 50%"
            />
          </template>
          <template #option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section side>
                <img
                  width="20"
                  height="20"
                  :src="scope.opt.icon"
                  alt=""
                  decoding="async"
                />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ scope.opt.name }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </shipvagoo-select>
        <shipvagoo-select
          v-model="form.shippingProduct"
          :label="$t('common.type')"
          :options="shippingProducts"
          option-label="name"
          option-value="carrier_product_code"
        >
          <template #option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section>
                <q-item-label>{{ scope.opt.name }}</q-item-label>
                <q-item-label v-if="scope.opt.note" style="font-size: 10px">
                  {{ scope.opt.note }}
                </q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </shipvagoo-select>
      </shipvagoo-input-group>
      <shipvagoo-input
        @update:modelValue="onUpdateField('price')"
        :error="$v.price.$error"
        :error-message="String($v.price.$errors?.[0]?.$message ?? null)"
        v-model="form.price"
        :label="$t('common.price')"/>
      <shipvagoo-select
        v-model="form.currency"
        :label="$t('common.currency_code')"
        :options="currencies"
        option-label="currency"
        option-value="currency_code"
      >
      </shipvagoo-select>
      <shipvagoo-input v-model="form.number_of_pickup_points"
                       v-if="form.shippingProduct?.hasMultiParcelShop || form.shippingProduct?.has_multi_parcel_shop"
                       :label="$t('shipping_method_page.number_of_pickup_points')"/>
    </shipvagoo-card-section>
    <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
      <shipvagoo-input-group responsive>
        <shipvagoo-checkbox
          v-model="form.order_amount_based"
          :label="$t('shipping_method_page.order_amount_based')"
        />
        <shipvagoo-checkbox
          v-model="form.order_weight_based"
          :label="$t('shipping_method_page.order_weight_based')"
        />
      </shipvagoo-input-group>
      <shipvagoo-input-group responsive>
        <shipvagoo-checkbox
          v-model="form.item_quantity_based"
          :label="$t('shipping_method_page.item_quantity_based')"
        />
        <shipvagoo-checkbox
          v-model="form.zipcode_based"
          :label="$t('shipping_method_page.zipcode_based')"
        />
      </shipvagoo-input-group>
    </shipvagoo-card-section>
    <div v-if="form.order_amount_based" class="text-dark-gray" style="margin-top: -12px;margin-left: 20px;font-size: 13px;">
      Cart value needs to be set in your store currency
    </div>
    <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
      <shipvagoo-input-group v-if="form.order_amount_based" responsive>
        <shipvagoo-input
          @update:modelValue="onUpdateField('order_amount_minimum')"
          :error="$v.order_amount_minimum.$error"
          :error-message="String($v.order_amount_minimum.$errors?.[0]?.$message ?? null)"
          v-model="form.order_amount_minimum"
          :label="$t('shipping_method_page.order_amount_minimum')"/>
        <shipvagoo-input
          @update:modelValue="onUpdateField('order_amount_maximum')"
          :error="$v.order_amount_maximum.$error"
          :error-message="String($v.order_amount_maximum.$errors?.[0]?.$message ?? null)"
          v-model="form.order_amount_maximum" :label="$t('shipping_method_page.order_amount_maximum')"/>
      </shipvagoo-input-group>
      <shipvagoo-input-group v-if="form.order_weight_based" responsive>
        <shipvagoo-input v-model="form.order_weight_minimum" :label="$t('shipping_method_page.order_weight_minimum')"/>
        <shipvagoo-input v-model="form.order_weight_maximum" :label="$t('shipping_method_page.order_weight_maximum')"/>
      </shipvagoo-input-group>
      <shipvagoo-input-group v-if="form.item_quantity_based" responsive>
        <shipvagoo-input v-model="form.item_quantity_minimum"
                         :label="$t('shipping_method_page.item_quantity_minimum')"/>
        <shipvagoo-input v-model="form.item_quantity_maximum"
                         :label="$t('shipping_method_page.item_quantity_maximum')"/>
      </shipvagoo-input-group>
      <shipvagoo-input-group v-if="form.zipcode_based" responsive>
        <shipvagoo-input v-model="form.zipcode_from" :label="$t('shipping_method_page.zipcode_from')"/>
        <shipvagoo-input v-model="form.zipcode_to" :label="$t('shipping_method_page.zipcode_to')"/>
      </shipvagoo-input-group>
    </shipvagoo-card-section>
    <shipvagoo-card-section>
      <shipvagoo-textarea v-model="form.description" :label="$t('common.description')"/>
    </shipvagoo-card-section>
    <shipvagoo-separator/>
    <shipvagoo-card-actions>
      <shipvagoo-button
        v-if="isCreate"
        :label="$t('common.create')"
        min-width="120px"
        @click="onCreateShippingMethodSubmit"
      />
      <shipvagoo-button
        v-if="isEdit"
        :label="$t('common.save')"
        min-width="120px"
        @click="onEditShippingMethodSubmit"
      />
    </shipvagoo-card-actions>
  </shipvagoo-modal>
</template>

<script setup lang="ts">
import {computed, onBeforeMount, ref} from 'vue';
import {
  shippingMethodService,
  loadingService,
  loggingService,
  notificationService, carrierService,
} from '../../services/_singletons';
import {useStore} from "../../store";
import * as _ from "lodash-es";
import PriceService from "../../services/price.service";
import {ICarrier, IShippingProduct} from "~/model/carrier.model";
import {ICountry} from "~/model/country.model";
import {euMember} from "is-european";
import {ICurrency, IShippingMethodResponse} from "~/model/shipping-method.model";
import {helpers} from "@vuelidate/validators";
import useVuelidate, {ValidationArgs} from '@vuelidate/core';
import {useHelper} from "../../composeable/Helper";

const {denmark, currencies} = useHelper()

interface Props {
  shipping_method: IShippingMethodResponse | null;
  integrationCode: string | null;
}

const $store = useStore();
const props = withDefaults(defineProps<Props>(), {shipping_method: null, integrationCode: null});

const emit = defineEmits(['close', 'create', 'update']);
//const currencies = ref<ICurrency[]>([{"currency": "DKK", "currency_code": 3744202329}]);
const countries = computed(() => {
  return $store.state.countryStore.countries.filter((c) => euMember(c.iso));
});

interface IForm {
  id: number | null;
  currency: ICurrency | null;
  title: string;
  description: string | null;
  price: string;
  order_amount_based: boolean;
  order_amount_minimum: string | null;
  order_amount_maximum: string | null;
  order_weight_based: boolean;
  order_weight_minimum: string | null;
  order_weight_maximum: string | null;
  item_quantity_based: boolean;
  item_quantity_minimum: number | null;
  item_quantity_maximum: number | null;
  zipcode_based: boolean;
  zipcode_from: number | null;
  zipcode_to: number | null;
  sendCountry: ICountry;
  receiveCountry: ICountry | null;
  courier: ICarrier | null;
  shippingProduct: IShippingProduct | null;
  number_of_pickup_points: number;
  integration_code: number | string | null;
}

const form = $ref<IForm>({
  id: null,
  currency: null,
  title: "",
  description: null,
  price: '0,00',
  order_amount_based: false,
  order_amount_minimum: null,
  order_amount_maximum: null,
  order_weight_based: false,
  order_weight_minimum: null,
  order_weight_maximum: null,
  item_quantity_based: false,
  item_quantity_minimum: null,
  item_quantity_maximum: null,
  zipcode_based: false,
  zipcode_from: null,
  zipcode_to: null,
  sendCountry: denmark,
  receiveCountry: null,
  courier: null,
  shippingProduct: null,
  number_of_pickup_points: 0,
  integration_code: props.integrationCode!
});
const mustBeValidInput = helpers.withParams(
  {type: 'validDecimal'},
  (value: any) => {
    const regex = /^(\d{1,3}(\.\d{3})*|\d+)(,\d{2})?$/;
    if (!regex.test(value as string)) {
      return false;
    }
    return true;
  }
)

interface FormatFields {
  [key: string]: any,
}

const validInputMessage = helpers.withMessage('Only valid input is required.', mustBeValidInput);
const $v = useVuelidate<FormatFields, ValidationArgs<FormatFields>>(
  {
    price: {
      mustBeValidInput: validInputMessage
    },
    order_amount_minimum: {
      mustBeValidInput: validInputMessage
    },
    order_amount_maximum: {
      mustBeValidInput: validInputMessage
    }
  },
  form,
);
const carriers = ref<ICarrier[]>([]);
const shippingProducts = ref<IShippingProduct[]>([]);

const isCreate = $computed(() => props.shipping_method === null);
const isEdit = $computed(() => props.shipping_method !== null);

const shippingMethodDetailTitle = $computed(() => {
  if (props.shipping_method) {
    return 'Edit Shipping Method';
  }

  return 'New Shipping Method';
});

const onUpdateField = async (field: string) => {
  await $v.value[field].$validate();
  if ($v.value.$error) {
    return;
  }
}
const onCreateShippingMethodSubmit = async () => {
  try {
    if (!form.title || form.title == "") {
      notificationService.showError('Title is required');
      return;
    }

    if (!form.receiveCountry) {
      notificationService.showError('Fill country required field.');
      return;
    }

    if (!form.courier) {
      notificationService.showError('Fill carrier required field.');
      return;
    }

    if (!form.shippingProduct) {
      notificationService.showError('Fill shipping product required field.');
      return;
    }

    if (!form.currency) {
      notificationService.showError('Fill currency required field.');
      return;
    }

    if (isNaN(form.number_of_pickup_points)) {
      notificationService.showError('Number of pickup points should be integer.');
      return;
    }

    if (form.shippingProduct.hasMultiParcelShop && (form.number_of_pickup_points < 1 || form.number_of_pickup_points > 50)) {
      notificationService.showError('No. of pickup points should in range 1 to 50');
      return;
    }

    if (form.order_amount_based && (form.order_amount_minimum == null || form.order_amount_maximum == null)) {
      notificationService.showError('Fill the order amount conditions correctly.');
      return;
    }

    if (form.order_amount_based && form.order_amount_minimum != null && form.order_amount_maximum != null) {
      const regex = /^(\d{1,3}(\.\d{3})*|\d+)(,\d{2})?$/;

      if (!regex.test(form.order_amount_minimum)) {
        notificationService.showError('Order minimum amount is not in correct format');
        return;
      }
      if (!regex.test(form.order_amount_maximum)) {
        notificationService.showError('Order maximum amount is not in correct format');
        return;
      }

      if (form.order_amount_based && form.order_amount_minimum && form.order_amount_maximum && (Number(form.order_amount_minimum.replace(/[,.]/g, (match) => {
        return match === ',' ? '.' : '';
      })) >= Number(form.order_amount_maximum.replace(/[,.]/g, (match) => {
        return match === ',' ? '.' : '';
      })))) {
        notificationService.showError('Order amount minimum value should less than maximum value.');
        return;
      }
    }

    if (form.price) {
      const regex = /^(\d{1,3}(\.\d{3})*|\d+)(,\d{2})?$/;

      if (!regex.test(form.price)) {
        notificationService.showError('Price is not in correct format');
        return;
      }
    }

    if (form.order_weight_based && (form.order_weight_minimum == null || form.order_weight_maximum == null)) {
      notificationService.showError('Fill the order weight conditions correctly.');
      return;
    }
    if (form.order_weight_based && form.order_weight_minimum && form.order_weight_maximum) {
      const regex = /^(\d{1,3}(\.\d{3})*|\d+)?$/;

      if (!regex.test(form.order_weight_minimum)) {
        notificationService.showError('Order minimum weight is not in correct format');
        return;
      }
      if (!regex.test(form.order_weight_maximum)) {
        notificationService.showError('Order maximum weight is not in correct format');
        return;
      }
    }
    if (form.order_weight_based && form.order_weight_minimum && form.order_weight_maximum && (Number(form.order_weight_minimum.replace(/[,.]/g, (match) => {
      if (match === ',') throw new Error("Can't use decimal value for grams. It should an integer");
      return match === ',' ? '' : '';
    })) >= Number(form.order_weight_maximum.replace(/[,.]/g, (match) => {
      if (match === ',') throw new Error("Can't use decimal value for grams. It should an integer");
      return match === ',' ? '' : '';
    })))) {
      notificationService.showError('Order weight minimum value should less than maximum value.');
      return;
    }

    if (form.item_quantity_based && (form.item_quantity_minimum == null || form.item_quantity_maximum == null)) {
      notificationService.showError('Fill the item quantity conditions correctly.');
      return;
    }
    if (form.item_quantity_based && form.item_quantity_minimum && form.item_quantity_maximum && form.item_quantity_minimum >= form.item_quantity_maximum) {
      notificationService.showError('Item quantity minimum value should less than maximum value.');
      return;
    }

    if (form.zipcode_based && (form.zipcode_to == null || form.zipcode_from == null)) {
      notificationService.showError('Fill the ZipCode conditions correctly.');
      return;
    }
    if (form.zipcode_based && form.zipcode_from && form.zipcode_to && form.zipcode_from >= form.zipcode_to) {
      notificationService.showError('Zipcode minimum value should less than maximum value.');
      return;
    }

    loadingService.startLoading('main-loader:create-shipping-method');
    await shippingMethodService.createShippingMethod({
      currency_code: form.currency?.currency_code,
      title: form.title,
      description: form.description,
      price: PriceService.shippingPriceFormat(Number(form.price.replace(/[,.]/g, (match) => {
        return match === ',' ? '.' : '';
      })), 'en').replace(/,/g, ''),
      order_amount_based: form.order_amount_based,
      order_amount_minimum: form.order_amount_minimum == null ? null : PriceService.shippingPriceFormat(Number(form.order_amount_minimum.replace(/[,.]/g, (match) => {
        return match === ',' ? '.' : '';
      })), 'en').replace(/[,]/g, (match) => {
        return '';
      }),
      order_amount_maximum: form.order_amount_maximum == null ? null : PriceService.shippingPriceFormat(Number(form.order_amount_maximum.replace(/[,.]/g, (match) => {
        return match === ',' ? '.' : '';
      })), 'en').replace(/[,]/g, (match) => {
        return '';
      }),
      order_weight_based: form.order_weight_based,
      order_weight_minimum: form.order_weight_minimum == null ? null : Number(form.order_weight_minimum.replace(/[,.]/g, (match) => {
        return match === ',' ? '' : '';
      })),
      order_weight_maximum: form.order_weight_maximum == null ? null : Number(form.order_weight_maximum.replace(/[,.]/g, (match) => {
        return match === ',' ? '' : '';
      })),
      item_quantity_based: form.item_quantity_based,
      item_quantity_minimum: form.item_quantity_minimum,
      item_quantity_maximum: form.item_quantity_maximum,
      zipcode_based: form.zipcode_based,
      zipcode_from: form.zipcode_from,
      zipcode_to: form.zipcode_to,
      country_code: form.receiveCountry.country_code,
      carrier_code: form.courier.carrier_code,
      carrier_product_code: form.shippingProduct.carrier_product_code,
      number_of_pickup_points: parseInt(form.number_of_pickup_points.toString()),
      integration_code: form.integration_code
    });
    emit('create');
    emit('close');
  } catch (error) {
    loggingService.error('Error while creating printer', error);
    notificationService.showError('Please fill the fields carefully.');
  } finally {
    loadingService.stopLoading('main-loader:create-shipping-method');
  }
};

const onEditShippingMethodSubmit = async () => {
  try {
    if (!form.title || form.title == "") {
      notificationService.showError('Title is required');
      return;
    }

    if (!props.shipping_method?.id) {
      notificationService.showError('Select the shipping method again to whom you wanna edit.');
      return;
    }

    if (!form.receiveCountry) {
      notificationService.showError('Fill country required field.');
      return;
    }

    if (!form.courier) {
      notificationService.showError('Fill carrier required field.');
      return;
    }

    if (!form.shippingProduct) {
      notificationService.showError('Fill shipping product required field.');
      return;
    }

    if (!form.currency) {
      notificationService.showError('Fill currency required field.');
      return;
    }

    if (isNaN(form.number_of_pickup_points)) {
      notificationService.showError('Number of pickup points should be integer.');
      return;
    }

    if (form.shippingProduct.hasMultiParcelShop && (form.number_of_pickup_points < 1 || form.number_of_pickup_points > 50)) {
      notificationService.showError('No. of pickup points should in range 1 to 50');
      return;
    }

    if (form.order_amount_based && (form.order_amount_minimum == null || form.order_amount_maximum == null)) {
      notificationService.showError('Fill the order amount conditions correctly.');
      return;
    }

    if (form.order_amount_based && form.order_amount_minimum != null && form.order_amount_maximum != null) {
      const regex = /^(\d{1,3}(\.\d{3})*|\d+)(,\d{2})?$/;

      if (!regex.test(form.order_amount_minimum)) {
        notificationService.showError('Order minimum amount is not in correct format');
        return;
      }
      if (!regex.test(form.order_amount_maximum)) {
        notificationService.showError('Order maximum amount is not in correct format');
        return;
      }

      if (form.order_amount_based && form.order_amount_minimum && form.order_amount_maximum && (Number(form.order_amount_minimum.replace(/[,.]/g, (match) => {
        return match === ',' ? '.' : '';
      })) >= Number(form.order_amount_maximum.replace(/[,.]/g, (match) => {
        return match === ',' ? '.' : '';
      })))) {
        notificationService.showError('Order amount minimum value should less than maximum value.');
        return;
      }
    }

    if (form.price) {
      const regex = /^(\d{1,3}(\.\d{3})*|\d+)(,\d{2})?$/;

      if (!regex.test(form.price)) {
        notificationService.showError('Price is not in correct format');
        return;
      }
    }

    if (form.order_weight_based && (form.order_weight_minimum == null || form.order_weight_maximum == null)) {
      notificationService.showError('Fill the order weight conditions correctly.');
      return;
    }

    if (form.order_weight_based && form.order_weight_minimum && form.order_weight_maximum) {
      const regex = /^(\d{1,3}(\.\d{3})*|\d+)?$/;

      if (!regex.test(form.order_weight_minimum)) {
        notificationService.showError('Order minimum weight is not in correct format');
        return;
      }
      if (!regex.test(form.order_weight_maximum)) {
        notificationService.showError('Order maximum weight is not in correct format');
        return;
      }
    }
    if (form.order_weight_based && form.order_weight_minimum && form.order_weight_maximum && (Number(form.order_weight_minimum.replace(/[,.]/g, (match) => {
      if (match === ',') throw new Error("Can't use decimal value for grams. It should an integer");
      return match === ',' ? '' : '';
    })) >= Number(form.order_weight_maximum.replace(/[,.]/g, (match) => {
      if (match === ',') throw new Error("Can't use decimal value for grams. It should an integer");
      return match === ',' ? '' : '';
    })))) {
      notificationService.showError('Order weight minimum value should less than maximum value.');
      return;
    }

    if (form.item_quantity_based && (form.item_quantity_minimum == null || form.item_quantity_maximum == null)) {
      notificationService.showError('Fill the item quantity conditions correctly.');
      return;
    }
    if (form.item_quantity_based && form.item_quantity_minimum && form.item_quantity_maximum && form.item_quantity_minimum >= form.item_quantity_maximum) {
      notificationService.showError('Item quantity minimum value should less than maximum value.');
      return;
    }

    if (form.zipcode_based && (form.zipcode_to == null || form.zipcode_from == null)) {
      notificationService.showError('Fill the ZipCode conditions correctly.');
      return;
    }
    if (form.zipcode_based && form.zipcode_from && form.zipcode_to && form.zipcode_from >= form.zipcode_to) {
      notificationService.showError('Zipcode minimum value should less than maximum value.');
      return;
    }

    loadingService.startLoading('main-loader:edit-printer');
    await shippingMethodService.updateShippingMethod(props.shipping_method.id, {
      currency_code: form.currency?.currency_code,
      title: form.title,
      description: form.description,
      price: PriceService.shippingPriceFormat(Number(form.price.replace(/[,.]/g, (match) => {
        return match === ',' ? '.' : '';
      })), 'en').replace(/,/g, ''),
      order_amount_based: form.order_amount_based,
      order_amount_minimum: form.order_amount_minimum == null ? null : PriceService.shippingPriceFormat(Number(form.order_amount_minimum.replace(/[,.]/g, (match) => {
        return match === ',' ? '.' : '';
      })), 'en').replace(/[,]/g, (match) => {
        return '';
      }),
      order_amount_maximum: form.order_amount_maximum == null ? null : PriceService.shippingPriceFormat(Number(form.order_amount_maximum.replace(/[,.]/g, (match) => {
        return match === ',' ? '.' : '';
      })), 'en').replace(/[,]/g, (match) => {
        return '';
      }),
      order_weight_based: form.order_weight_based,
      order_weight_minimum: form.order_weight_minimum == null ? null : Number(form.order_weight_minimum.replace(/[,.]/g, (match) => {
        return match === ',' ? '' : '';
      })),
      order_weight_maximum: form.order_weight_maximum == null ? null : Number(form.order_weight_maximum.replace(/[,.]/g, (match) => {
        return match === ',' ? '' : '';
      })),
      item_quantity_based: form.item_quantity_based,
      item_quantity_minimum: form.item_quantity_minimum,
      item_quantity_maximum: form.item_quantity_maximum,
      zipcode_based: form.zipcode_based,
      zipcode_from: form.zipcode_from,
      zipcode_to: form.zipcode_to,
      country_code: form.receiveCountry.country_code,
      carrier_code: form.courier.carrier_code,
      carrier_product_code: form.shippingProduct.carrier_product_code,
      number_of_pickup_points: parseInt(form.number_of_pickup_points.toString()),
      integration_code: form.integration_code
    });
    emit('update');
    emit('close');
  } catch (error: any) {
    loggingService.error('Error while updating shipping method', error);
    notificationService.showError('Error updating shipping method ' + error.toString());
  } finally {
    loadingService.stopLoading('main-loader:edit-printer');
  }
};

const onClose = () => {
  emit('close');
};

const onChangeCarrier = async (carrier: ICarrier) => {
  form.shippingProduct = null;
  // console.log("Carrier ", carrier)
  if (carrier) {
    shippingProducts.value = carrier.products;
    //console.log("Shipping Products ", shippingProducts.value)
  }
};

const onChangeReceiveCountry = async () => {
  if (!form.sendCountry || !form.receiveCountry) {
    return;
  }

  form.courier = null;
  form.shippingProduct = null;

  try {
    loadingService.startLoading('main-loader:carriers');
    await getCarriers(
      form.sendCountry.country_code,
      form.receiveCountry.country_code,
    );
  } catch (e) {
    notificationService.showError('Error getting carriers');
    loggingService.error('Error getting carriers', e);
  } finally {
    loadingService.stopLoading('main-loader:carriers');
  }
};

const getCarriers = async (senderCountry: number, receiverCountry: number) => {
  try {
    loadingService.startLoading('main-loader:carriers');
    const response = await carrierService.getShippingProducts(senderCountry, receiverCountry);
    carriers.value = _.get(response, 'carriers', []);
  } catch (e) {
    notificationService.showError('Error getting carriers');
    loggingService.error('Error getting carriers', e);
  } finally {
    loadingService.stopLoading('main-loader:carriers');
  }
};

const hydrate = async () => {
  if (props.shipping_method) {
    form.id = props.shipping_method.id;
    form.title = props.shipping_method.title;
    form.description = props.shipping_method.description;
    form.receiveCountry = _.find($store.state.countryStore.countries, {country_code: props.shipping_method.country_code}) as ICountry;
    form.sendCountry = denmark;
    form.currency = _.find(currencies, {currency_code: props.shipping_method.currency_code}) as ICurrency;
    form.price = PriceService.shippingPriceFormat(props.shipping_method.price, 'de-DE');
    await onChangeReceiveCountry();
    form.courier = _.find(carriers.value, {carrier_code: props.shipping_method.carrier_code}) as ICarrier;
    //console.log("Form Carrier ", form.courier)
    await onChangeCarrier(form.courier);
    //console.log("Form Carrier1 ", form.courier)
    form.shippingProduct = _.find(shippingProducts.value, {carrier_product_code: props.shipping_method.carrier_product_code}) as IShippingProduct;
    form.number_of_pickup_points = props.shipping_method.number_of_pickup_points;
    form.order_amount_based = props.shipping_method.order_amount_based ? true : false;
    form.order_amount_minimum = props.shipping_method.order_amount_minimum != null ? PriceService.shippingPriceFormat(props.shipping_method.order_amount_minimum, 'de-DE') : null;
    form.order_amount_maximum = props.shipping_method.order_amount_maximum != null ? PriceService.shippingPriceFormat(props.shipping_method.order_amount_maximum, 'de-DE') : null;
    form.order_weight_based = props.shipping_method.order_weight_based ? true : false;
    form.order_weight_minimum = props.shipping_method.order_weight_minimum != null ? PriceService.shippingPriceFormat(props.shipping_method.order_weight_minimum, 'de-DE').substring(0, PriceService.shippingPriceFormat(props.shipping_method.order_weight_minimum, 'de-DE').indexOf(",")) : null;
    form.order_weight_maximum = props.shipping_method.order_weight_maximum != null ? PriceService.shippingPriceFormat(props.shipping_method.order_weight_maximum, 'de-DE').substring(0, PriceService.shippingPriceFormat(props.shipping_method.order_weight_maximum, 'de-DE').indexOf(",")) : null;
    form.item_quantity_based = props.shipping_method.item_quantity_based ? true : false;
    form.item_quantity_minimum = props.shipping_method.item_quantity_minimum;
    form.item_quantity_maximum = props.shipping_method.item_quantity_maximum;
    form.zipcode_based = props.shipping_method.zipcode_based ? true : false;
    form.zipcode_from = props.shipping_method.zipcode_from;
    form.zipcode_to = props.shipping_method.zipcode_to;
  }
};

onBeforeMount(() => {
  hydrate();
});
</script>
