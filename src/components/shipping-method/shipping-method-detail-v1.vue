<template>
  <shipvagoo-modal size="md" :header-separator="false" :title="shippingMethodDetailTitle" @close="onClose">
    <shipvagoo-separator style="margin:0 20px"></shipvagoo-separator>
    <shipvagoo-stepper ref="stepper" class="create-template-modal-stepper-v2" v-model="step">
      <q-step :name="1" title="General" :done="step > 1">
        <div class="two-columns-responsive gap-10">
          <shipvagoo-input-v1 class="flex-1" v-model="steps.one.title" placeholder="Enter checkout title"
            :error="$v.one.title.$error" :error-message="String($v.one.title.$errors?.[0]?.$message ?? null)"
            label="Checkout title" />


          <shipvagoo-select-v1 placeholder="Select" class="flex-1" v-model="steps.one.receiverCountry"
            label="Destination country" :options="countries" option-label="default_name" option-value="country_code"
            hide-bottom-space @update:model-value="onChangeReceiveCountry" :error="$v.one.receiverCountry.$error"
            :error-message="String($v.one.receiverCountry.$errors?.[0]?.$message ?? null)" use-input input-debounce="0"
            @filter="onFilterReceiverCountries" fill-input hide-selected>
            <template #prepend>
              <shipvagoo-country-icon v-if="steps.one.receiverCountry" :country="steps.one.receiverCountry" />
            </template>
            <template #option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section side>
                  <shipvagoo-country-icon v-if="scope.opt" :country="scope.opt" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ scope.opt.default_name }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>
          </shipvagoo-select-v1>



        </div>
        <div class="two-columns-responsive gap-10 q-mt-md">
          <shipvagoo-select-v1 class="flex-1" v-model="steps.one.carrier" :options="carriers"
            :label="$t('common.carrier')" option-label="name" option-value="carrier_code"
            @update:model-value="onChangeCarrier" placeholder="Select" :error="$v.one.carrier.$error"
            :error-message="String($v.one.carrier.$errors?.[0]?.$message ?? null)">
            <template #prepend>
              <img v-if="steps.one.carrier" width="17" height="17" :src="`${steps.one.carrier.icon}`" alt=""
                decoding="async" style="border-radius: 50%" />
            </template>
            <template #option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section side>
                  <img width="20" height="20" :src="scope.opt.icon" alt="" decoding="async" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ scope.opt.name }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>
          </shipvagoo-select-v1>
          <shipvagoo-select-v1 placeholder="Select" class="flex-1" v-model="steps.one.shippingProduct" label="Service"
            :options="shippingProducts" option-label="name" option-value="carrier_product_code"
            :error="$v.one.shippingProduct.$error"
            :error-message="String($v.one.shippingProduct.$errors?.[0]?.$message ?? null)">
            <template #option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section>
                  <q-item-label>{{ scope.opt.name }}</q-item-label>
                  <q-item-label v-if="scope.opt.note" style="font-size: 10px">
                    {{ scope.opt.note }}
                  </q-item-label>
                </q-item-section>
              </q-item>
            </template>
          </shipvagoo-select-v1>
        </div>
        <div class="two-columns-responsive gap-10 q-mt-md">
          <shipvagoo-input-v1 class="flex-1" v-model="steps.one.description" placeholder="Description"
            label="Description" />
        </div>
      </q-step>

      <q-step :name="2" title="Conditions" :done="step > 2">
        <div class="two-columns-responsive gap-10">
          <shipvagoo-input-v1 class="flex-1" v-model="steps.two.price" placeholder="Enter checkout price"
            :error="$v.two.price.$error" :error-message="String($v.two.price.$errors?.[0]?.$message ?? null)"
            label="Checkout price" />
          <shipvagoo-select-v1 placeholder="Select" class="flex-1" v-model="steps.two.currency" label="Currency"
            :options="currencies" option-label="currency" option-value="currency_code" :error="$v.two.currency.$error"
            :error-message="String($v.two.currency.$errors?.[0]?.$message ?? null)">
          </shipvagoo-select-v1>
        </div>
        <shipvagoo-input-v1 class="q-mt-md" v-model="steps.two.number_of_pickup_points"
          v-if="steps.one.shippingProduct?.hasMultiParcelShop || steps.one.shippingProduct?.has_multi_parcel_shop"
          label="No of parcel shops" :error="$v.two.number_of_pickup_points.$error"
          :error-message="String($v.two.number_of_pickup_points.$errors?.[0]?.$message ?? null)" />
        <div class="q-mt-md">
          <shipvagoo-list>
            <shipvagoo-expansion-item label="Cart value" v-model="steps.two.cartValue.checked">
              <shipvagoo-card-section style="padding-top: 15px">
                <div class="dark-text">
                  Cart value condition needs to be set in your store currency
                </div>
                <div class="flex gap-10 q-pt-md">
                  <shipvagoo-input-v1 class="flex-1" v-model="steps.two.cartValue.minimum"
                    placeholder="Enter cart value min" :error="$v.two.cartValue.minimum.$error"
                    :error-message="String($v.two.cartValue.minimum.$errors?.[0]?.$message ?? null)"
                    label="Cart value min." />
                  <shipvagoo-input-v1 class="flex-1" v-model="steps.two.cartValue.maximum"
                    placeholder="Enter cart value max" :error="$v.two.cartValue.maximum.$error"
                    :error-message="String($v.two.cartValue.maximum.$errors?.[0]?.$message ?? null)"
                    label="Cart value max." />
                </div>
              </shipvagoo-card-section>
            </shipvagoo-expansion-item>
            <shipvagoo-expansion-item label="Cart weight" class="q-mt-md" v-model="steps.two.cartWeight.checked">
              <shipvagoo-card-section style="padding-top: 15px">
                <div class="flex gap-10 ">
                  <shipvagoo-input-v1 class="flex-1" v-model="steps.two.cartWeight.minimum"
                    placeholder="Enter cart weight min" label="Cart weight min. (gram)"
                    :error="$v.two.cartWeight.minimum.$error"
                    :error-message="String($v.two.cartWeight.minimum.$errors?.[0]?.$message ?? null)" />
                  <shipvagoo-input-v1 class="flex-1" v-model="steps.two.cartWeight.maximum"
                    placeholder="Enter cart weight max" :error="$v.two.cartWeight.maximum.$error"
                    :error-message="String($v.two.cartWeight.maximum.$errors?.[0]?.$message ?? null)"
                    label="Cart weight max. (gram)" />
                </div>
              </shipvagoo-card-section>
            </shipvagoo-expansion-item>
            <shipvagoo-expansion-item label="Postal code" class="q-mt-md" v-model="steps.two.postalCode.checked">
              <shipvagoo-card-section style="padding-top: 15px">
                <div class="flex gap-10">
                  <shipvagoo-input-v1 class="flex-1" v-model="steps.two.postalCode.minimum"
                    :error="$v.two.postalCode.minimum.$error"
                    :error-message="String($v.two.postalCode.minimum.$errors?.[0]?.$message ?? null)"
                    placeholder="Enter postal code from" label="Postal code from" />
                  <shipvagoo-input-v1 class="flex-1" v-model="steps.two.postalCode.maximum"
                    placeholder="Enter postal code to" :error="$v.two.postalCode.maximum.$error"
                    :error-message="String($v.two.postalCode.maximum.$errors?.[0]?.$message ?? null)"
                    label="Postal code to" />
                </div>
              </shipvagoo-card-section>
            </shipvagoo-expansion-item>
          </shipvagoo-list>
        </div>
      </q-step>

      <template #navigation>
        <q-stepper-navigation class="create-shipment-stepper-v2-navigation" style="margin-top: -30px;">
          <shipvagoo-button color="brand" class="create-shipment-stepper-v2-navigation-previous" v-if="step > 1"
            label="Back" outline no-caps @click="onClickPrevious" />
          <shipvagoo-button color="brand" v-if="step == 1" class="create-shipment-stepper-v2-navigation-next"
            label="Next" no-caps @click="onClickNext" />
          <shipvagoo-button v-if="step === 2" class="create-shipment-stepper-v2-navigation-next" label="Save"
            @click="onClickSave" no-caps />
        </q-stepper-navigation>
      </template>
    </shipvagoo-stepper>
  </shipvagoo-modal>
</template>

<script setup lang="ts">
import { ComponentPublicInstance, computed, onBeforeMount, ref } from 'vue';
import {
  shippingMethodService,
  loadingService,
  loggingService,
  notificationService, carrierService,
} from '../../services/_singletons';
import { useStore } from "../../store";
import * as _ from "lodash-es";
import PriceService from "../../services/price.service";
import { ICarrier, IShippingProduct } from "~/model/carrier.model";
import { ICountry } from "~/model/country.model";
import { euMember } from "is-european";
import { ICurrency, IShippingMethodResponse } from "~/model/shipping-method.model";
import { helpers, required, requiredIf } from "@vuelidate/validators";
import useVuelidate, { ValidationArgs } from '@vuelidate/core';
import { useHelper } from "../../composeable/Helper";

const { denmark, currencies } = useHelper()

interface IBasedCard {
  checked: boolean;
  minimum: string | null;
  maximum: string | null;
}

interface ICheckoutRuleSteps {
  one: {
    title: string,
    carrier: ICarrier | null,
    shippingProduct: IShippingProduct | null,
    receiverCountry: ICountry | null,
    description: string | null,
    senderCountry: ICountry,
  },
  two: {
    currency: ICurrency | null;
    price: string,
    number_of_pickup_points: number;
    cartValue: IBasedCard,
    cartWeight: IBasedCard,
    postalCode: IBasedCard,
  }
}

interface Props {
  shipping_method: IShippingMethodResponse | null;
  integrationCode: string | null;
}

const $store = useStore();
const props = withDefaults(defineProps<Props>(), { shipping_method: null, integrationCode: null });

const emit = defineEmits(['close', 'create', 'update']);
//const currencies = ref<ICurrency[]>([{"currency": "DKK", "currency_code": 3744202329}]);
// const countries = computed(() => {
//   return $store.state.countryStore.countries.filter((c) => euMember(c.iso));
// });
const stepper = ref<ComponentPublicInstance<{ next: () => void; previous: () => void }> | null>(
  null,
);
const step = ref<number>(1);
const steps = ref<ICheckoutRuleSteps>({
  one: {
    title: '',
    carrier: null,
    shippingProduct: null,
    description: null,
    receiverCountry: null,
    senderCountry: denmark,
  },
  two: {
    currency: null,
    price: '0,00',
    number_of_pickup_points: 0,
    cartValue: {
      checked: false,
      minimum: null,
      maximum: null
    },
    cartWeight: {
      checked: false,
      minimum: null,
      maximum: null
    },
    postalCode: {
      checked: false,
      minimum: null,
      maximum: null
    },
  }
})


const mustBeValidInput = helpers.withParams(
  { type: 'validDecimal' },
  (value: any) => {
    if (value == null || value == '') {
      return true;
    }
    const regex = /^(\d{1,3}(\.\d{3})*|\d+)(,\d{2})?$/;
    if (!regex.test(value as string)) {
      return false;
    }
    return true;
  }
)
const mustBeValidInteger = helpers.withParams(
  { type: 'validInteger' },
  (value: any) => {
    if (value == null || value == '') {
      return true;
    }
    const val = Number(value.replace(/[,.]/g, (match: any) => {
      return match === ',' ? '.' : '';
    }));
    return Number.isInteger(val);
  }
)

/** if isChecked=false, means don't need to apply validations **/
const isValidMinValues = (minValue: string, maxValue: string, isChecked: boolean,) => {
  if (!isChecked) {
    return true;
  }
  const minimum = Number(minValue.replace(/[,.]/g, (match: any) => {
    return match === ',' ? '.' : '';
  }));
  const maximum = Number(maxValue.replace(/[,.]/g, (match: any) => {
    return match === ',' ? '.' : '';
  }));
  return minimum <= maximum;
}

const validAmountMinValue = helpers.withParams(
  { type: 'validMinValue' },
  (value: any) => isValidMinValues(value, String(steps.value.two.cartValue.maximum) ?? '0', steps.value.two.cartValue.checked)
);
const validMinPostalCode = helpers.withParams(
  { type: 'validMinValue' },
  (value: any) => isValidMinValues(value, String(steps.value.two.postalCode.maximum) ?? '0', steps.value.two.postalCode.checked)
);
const validAmountMinWeight = helpers.withParams(
  { type: 'validMinValue' },
  (value: any) => isValidMinValues(value, String(steps.value.two.cartWeight.maximum) ?? '0', steps.value.two.cartWeight.checked)
);
const mustBeValidParcelPoints = helpers.withParams(
  { type: 'mustBeValidParcelPoints' },
  (value: any) => {
    console.log("Value ", typeof value)
    if (!steps.value.one.shippingProduct?.hasMultiParcelShop) {
      return true;
    }
    const numberOfParcelShops = Number(String(value).replace(/[,.]/g, (match: any) => {
      return match === ',' ? '.' : '';
    }));
    console.log("Shops ", numberOfParcelShops)
    return !(numberOfParcelShops < 1 || numberOfParcelShops > 50);
  }
);
const mustBeValidParcelPointsMessage = helpers.withMessage('No. of pickup points should in range 1 to 50', mustBeValidParcelPoints);
const validInputMessage = helpers.withMessage('Only valid input is required.', mustBeValidInput);
const mustBeValidIntegerMessage = helpers.withMessage('It should be an integer value', mustBeValidInteger);
const validMinPostalCodeMessage = helpers.withMessage('From postal code should not be greater than to postal code', validMinPostalCode);
const validMinValueMessage = helpers.withMessage('Min value should not be greater than max value', validAmountMinValue);
const validMinWeightMessage = helpers.withMessage('Min weight should not be greater than max weight', validAmountMinWeight);
const $v = useVuelidate<ICheckoutRuleSteps, ValidationArgs<ICheckoutRuleSteps>>(
  {
    one: {
      title: {
        required: helpers.withMessage('This field is required', required)
      },
      receiverCountry: {
        required: helpers.withMessage('This field is required', required)
      }, senderCountry: {},
      carrier: {
        required: helpers.withMessage('This field is required', required)
      },
      shippingProduct: {
        required: helpers.withMessage('This field is required', required)
      },
      description: {}
    },
    two: {
      number_of_pickup_points: {
        mustBeValidParcelPoints: mustBeValidParcelPointsMessage,
        mustBeValidInput: validInputMessage
      },
      currency: {
        required: helpers.withMessage('This field is required', required)
      },
      price: {
        required: helpers.withMessage('This field is required', required),
        mustBeValidInput: validInputMessage
      },
      cartValue:
      {
        checked: {},
        minimum: {
          required: helpers.withMessage(
            'This field is required',
            requiredIf(() => steps.value.two.cartValue.checked)),
          validMinValue: validMinValueMessage,
          mustBeValidInput: validInputMessage
        },
        maximum: {
          required: helpers.withMessage(
            'This field is required',
            requiredIf(() => steps.value.two.cartValue.checked)),
          mustBeValidInput: validInputMessage
        },
      },
      cartWeight: {
        checked: {},
        minimum: {
          required: helpers.withMessage(
            'This field is required',
            requiredIf(() => steps.value.two.cartWeight.checked)),
          mustBeValidInput: validInputMessage,
          validMinWeight: validMinWeightMessage,
          mustBeValidInteger: mustBeValidIntegerMessage,
        },
        maximum: {
          required: helpers.withMessage(
            'This field is required',
            requiredIf(() => steps.value.two.cartWeight.checked)),
          mustBeValidInput: validInputMessage,
          mustBeValidInteger: mustBeValidIntegerMessage,
        },
      },
      postalCode: {
        checked: {},
        minimum: {
          required: helpers.withMessage(
            'This field is required',
            requiredIf(() => steps.value.two.postalCode.checked)),
          validMinPostalCode: validMinPostalCodeMessage,
          // mustBeValidInput: validInputMessage
        },
        maximum: {
          required: helpers.withMessage(
            'This field is required',
            requiredIf(() => steps.value.two.postalCode.checked)),
          //  mustBeValidInput: validInputMessage
        },
      },
    }
  },
  steps,
);
const carriers = ref<ICarrier[]>([]);
const shippingProducts = ref<IShippingProduct[]>([]);

const shippingMethodDetailTitle = computed(() => {
  if (props.shipping_method) {
    return 'Edit checkout rule';
  }
  return 'New checkout rule';
});

const onClickPrevious = () => {
  step.value -= 1;
};
const onClickNext = async () => {
  if (step.value === 1) {
    await $v.value.one.$validate();
    if ($v.value.one.$error) {
      return;
    }
  }
  stepper.value?.next();
}
const onClickSave = async () => {
  await $v.value.two.$validate();
  if ($v.value.$error) {
    console.log($v.value.one.$errors)
    console.log($v.value.two.$errors)
    return;
  }
  if (props.shipping_method) {
    onEditShippingMethodSubmit()
  } else {
    onCreateShippingMethodSubmit();
  }
}

const onCreateShippingMethodSubmit = async () => {
  try {
    const stepOne = steps.value.one;
    const stepTwo = steps.value.two;
    if (stepOne.receiverCountry == null || stepOne.carrier == null || stepOne.shippingProduct == null) {
      return;
    }
    if (stepTwo.currency == null || (stepTwo.postalCode.checked && (stepTwo.postalCode.minimum == null || stepTwo.postalCode.maximum == null))) {
      return;
    }
    loadingService.startLoading('main-loader:create-shipping-method');
    await shippingMethodService.createShippingMethod({
      currency_code: stepTwo.currency.currency_code,
      title: stepOne.title,
      description: stepOne.description,
      price: PriceService.shippingPriceFormat(Number(steps.value.two.price.replace(/[,.]/g, (match) => {
        return match === ',' ? '.' : '';
      })), 'en').replace(/,/g, ''),
      order_amount_based: stepTwo.cartValue.checked,
      order_amount_minimum: !stepTwo.cartValue.checked ? null : PriceService.shippingPriceFormat(Number(stepTwo.cartValue.minimum?.replace(/[,.]/g, (match) => {
        return match === ',' ? '.' : '';
      })), 'en').replace(/[,]/g, (match) => {
        return '';
      }),
      order_amount_maximum: !stepTwo.cartValue.checked ? null : PriceService.shippingPriceFormat(Number(stepTwo.cartValue.maximum?.replace(/[,.]/g, (match) => {
        return match === ',' ? '.' : '';
      })), 'en').replace(/[,]/g, (match) => {
        return '';
      }),
      order_weight_based: stepTwo.cartWeight.checked,
      order_weight_minimum: !stepTwo.cartWeight.checked ? null : Number(stepTwo.cartWeight.minimum?.replace(/[,.]/g, (match) => {
        return match === ',' ? '' : '';
      })),
      order_weight_maximum: !stepTwo.cartWeight.checked ? null : Number(stepTwo.cartWeight.maximum?.replace(/[,.]/g, (match) => {
        return match === ',' ? '' : '';
      })),
      item_quantity_based: false,
      item_quantity_minimum: 0,
      item_quantity_maximum: 0,
      zipcode_based: stepTwo.postalCode.checked,
      zipcode_from: Number(stepTwo.postalCode.minimum),
      zipcode_to: Number(stepTwo.postalCode.maximum),
      country_code: stepOne.receiverCountry?.country_code,
      carrier_code: stepOne.carrier.carrier_code,
      carrier_product_code: stepOne.shippingProduct.carrier_product_code,
      number_of_pickup_points: parseInt(stepTwo.number_of_pickup_points.toString()),
      integration_code: props.integrationCode
    });
    emit('create');
    emit('close');
  } catch (error) {
    console.log("Error ", error)
    loggingService.error('Error while creating printer', error);
    notificationService.showError('Please fill the fields carefully.');
  } finally {
    loadingService.stopLoading('main-loader:create-shipping-method');
  }
};

const onEditShippingMethodSubmit = async () => {
  try {
    const stepOne = steps.value.one;
    const stepTwo = steps.value.two;
    if (stepOne.receiverCountry == null || stepOne.carrier == null || stepOne.shippingProduct == null) {
      return;
    }
    if (stepTwo.currency == null || (stepTwo.postalCode.checked && (stepTwo.postalCode.minimum == null || stepTwo.postalCode.maximum == null))) {
      return;
    }
    if (props.shipping_method == null) {
      return;
    }
    loadingService.startLoading('main-loader:edit-printer');
    await shippingMethodService.updateShippingMethod(props.shipping_method.id, {
      currency_code: stepTwo.currency?.currency_code,
      title: stepOne.title,
      description: stepOne.description,
      price: PriceService.shippingPriceFormat(Number(stepTwo.price.replace(/[,.]/g, (match) => {
        return match === ',' ? '.' : '';
      })), 'en').replace(/,/g, ''),
      order_amount_based: stepTwo.cartValue.checked,
      order_amount_minimum: !stepTwo.cartValue.checked ? null : PriceService.shippingPriceFormat(Number(stepTwo.cartValue.minimum?.replace(/[,.]/g, (match) => {
        return match === ',' ? '.' : '';
      })), 'en').replace(/[,]/g, (match) => {
        return '';
      }),
      order_amount_maximum: !stepTwo.cartValue.checked ? null : PriceService.shippingPriceFormat(Number(stepTwo.cartValue.maximum?.replace(/[,.]/g, (match) => {
        return match === ',' ? '.' : '';
      })), 'en').replace(/[,]/g, (match) => {
        return '';
      }),
      order_weight_based: stepTwo.cartWeight.checked,
      order_weight_minimum: !stepTwo.cartWeight.checked ? null : Number(stepTwo.cartWeight.minimum?.replace(/[,.]/g, (match) => {
        return match === ',' ? '' : '';
      })),
      order_weight_maximum: !stepTwo.cartWeight.checked ? null : Number(stepTwo.cartWeight.maximum?.replace(/[,.]/g, (match) => {
        return match === ',' ? '' : '';
      })),
      item_quantity_based: false,
      item_quantity_minimum: 0,
      item_quantity_maximum: 0,
      zipcode_based: stepTwo.postalCode.checked,
      zipcode_from: Number(stepTwo.postalCode.minimum),
      zipcode_to: Number(stepTwo.postalCode.maximum),
      country_code: stepOne.receiverCountry.country_code,
      carrier_code: stepOne.carrier.carrier_code,
      carrier_product_code: stepOne.shippingProduct.carrier_product_code,
      number_of_pickup_points: parseInt(stepTwo.number_of_pickup_points.toString()),
      integration_code: props.integrationCode
    });
    emit('update');
    emit('close');
  } catch (error: any) {
    loggingService.error('Error while updating shipping method', error);
    notificationService.showError('Error updating shipping method ' + error.toString());
  } finally {
    loadingService.stopLoading('main-loader:edit-printer');
  }
};

const onClose = () => {
  emit('close');
};

// HMZ code
const receiverCountries = computed(() => {
  return $store.state.countryStore.countries;
});
const countries = ref<ICountry[]>($store.state.countryStore.countries.filter((c) => euMember(c.iso)));


const onFilterReceiverCountries = (val: string, update: CallableFunction) => {
  if (val === "") {
    update(() => {
      countries.value = receiverCountries.value;
    });
    return;
  }
  update(() => {
    const needle = val.toLowerCase();
    countries.value = receiverCountries.value.filter((country: ICountry) => {
      return country.name.toLowerCase().indexOf(needle) > -1;
    });
  });
};

const onChangeCarrier = async (carrier: ICarrier) => {
  steps.value.one.shippingProduct = null;
  // console.log("Carrier ", carrier)
  if (carrier) {
    shippingProducts.value = carrier.products;
    //console.log("Shipping Products ", shippingProducts.value)
  }
};

const onChangeReceiveCountry = async () => {
  if (!steps.value.one.senderCountry || !steps.value.one.receiverCountry) {
    return;
  }

  steps.value.one.carrier = null;
  steps.value.one.shippingProduct = null;

  try {
    loadingService.startLoading('main-loader:carriers');
    await getCarriers(
      steps.value.one.senderCountry.country_code,
      steps.value.one.receiverCountry.country_code,
    );
  } catch (e) {
    notificationService.showError('Error getting carriers');
    loggingService.error('Error getting carriers', e);
  } finally {
    loadingService.stopLoading('main-loader:carriers');
  }
};

const getCarriers = async (senderCountry: number, receiverCountry: number) => {
  try {
    loadingService.startLoading('main-loader:carriers');
    const response = await carrierService.getShippingProducts(senderCountry, receiverCountry);
    carriers.value = _.get(response, 'carriers', []);
  } catch (e) {
    notificationService.showError('Error getting carriers');
    loggingService.error('Error getting carriers', e);
  } finally {
    loadingService.stopLoading('main-loader:carriers');
  }
};

const hydrate = async () => {
  if (props.shipping_method) {
    //  form.id = props.shipping_method.id;
    steps.value.one.title = props.shipping_method.title;
    steps.value.one.description = props.shipping_method.description;
    steps.value.one.receiverCountry = _.find($store.state.countryStore.countries, { country_code: props.shipping_method.country_code }) as ICountry;
    steps.value.one.senderCountry = denmark;
    steps.value.two.currency = _.find(currencies, { currency_code: props.shipping_method.currency_code }) as ICurrency;
    steps.value.two.price = PriceService.shippingPriceFormat(props.shipping_method.price, 'de-DE');
    await onChangeReceiveCountry();
    steps.value.one.carrier = _.find(carriers.value, { carrier_code: props.shipping_method.carrier_code }) as ICarrier;
    //console.log("Form Carrier ", form.courier)
    await onChangeCarrier(steps.value.one.carrier);
    //console.log("Form Carrier1 ", form.courier)
    steps.value.one.shippingProduct = _.find(shippingProducts.value, { carrier_product_code: props.shipping_method.carrier_product_code }) as IShippingProduct;
    steps.value.two.number_of_pickup_points = props.shipping_method.number_of_pickup_points;
    steps.value.two.cartValue = {
      checked: props.shipping_method.order_amount_based ? true : false,
      minimum: props.shipping_method.order_amount_minimum != null ? PriceService.shippingPriceFormat(props.shipping_method.order_amount_minimum, 'de-DE') : null,
      maximum: props.shipping_method.order_amount_maximum != null ? PriceService.shippingPriceFormat(props.shipping_method.order_amount_maximum, 'de-DE') : null
    };
    steps.value.two.cartWeight = {
      checked: props.shipping_method.order_weight_based ? true : false,
      minimum: props.shipping_method.order_weight_minimum != null ? PriceService.shippingPriceFormat(props.shipping_method.order_weight_minimum, 'de-DE').substring(0, PriceService.shippingPriceFormat(props.shipping_method.order_weight_minimum, 'de-DE').indexOf(",")) : null,
      maximum: props.shipping_method.order_weight_maximum != null ? PriceService.shippingPriceFormat(props.shipping_method.order_weight_maximum, 'de-DE').substring(0, PriceService.shippingPriceFormat(props.shipping_method.order_weight_maximum, 'de-DE').indexOf(",")) : null
    }
    steps.value.two.postalCode = {
      checked: props.shipping_method.zipcode_based ? true : false,
      minimum: String(props.shipping_method.zipcode_from),
      maximum: String(props.shipping_method.zipcode_to)
    }
  }
};

onBeforeMount(() => {
  hydrate();
});
</script>
