<template>
  <div ref="tooltip" :data-tooltip="tooltipText" class="tooltip" :style="{fontSize:`${fontSize} !important`}">
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";

const props = defineProps({
  tooltipText: String,
  fontSize: {
    type: String,
    default: "13px",
    required: false
  }
});

const tooltip = ref<any>(null);

const updateTooltipPosition = () => {
  const tooltipElement = tooltip.value;
  if (tooltipElement) {
    const tooltipWidth = tooltipElement.offsetWidth;
    const arrowMargin = tooltipWidth / 2 - 15;
    tooltipElement.style.setProperty("--arrow-margin-left", `-${arrowMargin}px`);
  }
};

onMounted(() => {
  updateTooltipPosition();
});

watch(() => props.tooltipText, () => {
  updateTooltipPosition();
});
</script>

<style lang="scss">
[data-tooltip],
.tooltip {
  position: relative;
  cursor: pointer;
}

[data-tooltip]:before,
[data-tooltip]:after,
.tooltip:before,
.tooltip:after {
  position: absolute;
  pointer-events: none;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out, transform 0.2s cubic-bezier(0.71, 1.7, 0.77, 1.24);
  transform: translate3d(0, 0, 0);
}

/* Show the entire tooltip on hover and focus */
[data-tooltip]:hover:before,
[data-tooltip]:hover:after,
[data-tooltip]:focus:before,
[data-tooltip]:focus:after,
.tooltip:hover:before,
.tooltip:hover:after,
.tooltip:focus:before,
.tooltip:focus:after {
  visibility: visible;
  opacity: 1;
}

/* Base styles for the tooltip's directional arrow */
.tooltip:before,
[data-tooltip]:before {
  z-index: 1001;
  content: "";
  background: transparent;
  border: 6px solid transparent;
}

.tooltip:after,
[data-tooltip]:after {
  z-index: 1000;
  min-width: 20px;
  padding: 5px 8px;
  font-family: Helvetica, sans-serif !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 1.2;
  color: #212121;
  white-space: nowrap; /* Ensure the text is on one line */
  content: attr(data-tooltip);
  background-color: #fff;
  border: 1px solid #d2d0d0;
  border-radius: 5px;
}

/* Directions */

/* Top (default) */
[data-tooltip]:before,
[data-tooltip]:after,
.tooltip:before,
.tooltip:after,
.tooltip-top:before,
.tooltip-top:after {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%); /* Center the tooltip */
}

[data-tooltip]:before,
.tooltip:before,
.tooltip-top:before {
  margin-bottom: -12px;
  border-top-color: #000;
  border-top-color: hsla(0, 0%, 20%, 0.9);
}

[data-tooltip]:after,
.tooltip:after,
.tooltip-top:after {
  margin-left: 0; /* No need for margin adjustment as we're centering with transform */
}

[data-tooltip]:hover:before,
[data-tooltip]:hover:after,
[data-tooltip]:focus:before,
[data-tooltip]:focus:after,
.tooltip:hover:before,
.tooltip:hover:after,
.tooltip:focus:before,
.tooltip:focus:after,
.tooltip-top:hover:before,
.tooltip-top:hover:after,
.tooltip-top:focus:before,
.tooltip-top:focus:after {
  transform: translate(-50%, -12px); /* Adjust transform to keep centered */
}
</style>
