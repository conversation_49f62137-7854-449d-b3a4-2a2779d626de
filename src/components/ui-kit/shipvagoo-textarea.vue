<template>
  <q-input
    v-model="modelValue"
    class="ui_kit-shipvagoo_textarea"
    :class="{ 'ui_kit-shipvagoo_textarea--top-padding': label }"
    stack-label
    dense
    outlined
    hide-bottom-space
    no-error-icon
    type="textarea"
    :placeholder="placeholder ?? `${label}...`"
    :label="required ? addStar(label) : label"
    v-bind="$attrs"
  >
    <template v-for="(_index, name) in $slots" #[name]>
      <slot :name="name"/>
    </template>
  </q-input>
</template>

<script setup lang="ts">
  import {QInput, QInputProps} from 'quasar';

  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  interface Props extends QInputProps {
    placeholder?: string;
    required?: boolean;
    label?: string;
  }

  withDefaults(defineProps<Props>(), {
    placeholder: '',
    label: '',
    required: false,
  });

  const addStar = (label?: string) => {
    if (!label) {
      return undefined;
    }

    return `${label}*`;
  };
</script>

<style lang="scss">
  .ui_kit-shipvagoo_textarea {
    &--top-padding {
      padding-top: 17px;
    }

    &.q-field--outlined .q-field__control {
      padding: 0 4px 0 12px !important;
      border-radius: 5px !important;

      &:hover,
      &:focus-within {
        &::before,
        &::after {
          border: 1px solid #4e4e4e !important;
        }
      }

      &:focus-within {
        &::before,
        &::after {
          border: 1px solid #4e4e4e !important;
          outline: 0 none;
          outline-offset: 0;
          box-shadow: 0 0 0 0.2rem rgba(107, 107, 137, 0.31) !important;
        }
      }

      &::before,
      &::after {
        border-color: $secondary-one !important;
        border-style: solid !important;
        border-width: 1px !important;
        box-shadow: 0 0 0 0rem rgba(95, 95, 104, 0.31) !important;
        transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
      }
    }

    &.q-field--outlined.q-field--disabled .q-field__control {
      &:before {
        border: 1px solid #465157 !important;
      }
    }

    .q-field__native[type='number']::-webkit-inner-spin-button,
    .q-field__native[type='number']::-webkit-outer-spin-button {
      -webkit-appearance: none;
      appearance: none;
      margin: 0;
    }

    .q-field__native[type='number'] {
      -moz-appearance: textfield;
      appearance: textfield;
    }

    .q-field__native,
    .q-field__prefix,
    .q-field__suffix,
    .q-field__input,
    .q-icon {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
      line-height: 17px !important;
      //color: #ffffff !important;
      color: #313131 !important;
    }

    .q-field__suffix {
      margin-right: 10px;
    }

    &.q-field--auto-height.q-field--labeled .q-field__control-container,
    &.q-field--labeled.q-field--dense .q-field__native {
      padding-top: unset !important;
    }

    .q-field__inner .q-field__label {
      top: -11px !important;
      left: -13px !important;
      font-size: 15px !important;
      line-height: 16px !important;
      color: $secondary-one;
    }

    .q-field__append .q-icon {
      padding-right: 12px;
    }
  }
</style>
