<template>
  <q-list class="ui_kit-shipvagoo_list" v-bind="$attrs">
    <slot />
  </q-list>
</template>

<script setup lang="ts">
import { QListProps } from "quasar";

// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface Props extends QListProps {
}

defineProps<Props>();
</script>

<style scoped lang="scss">
.ui_kit-shipvagoo_list {
  color: #1f2124;
  // background-color: #151526 !important;
  background-color: #FFFFFF !important;
  
}
</style>
