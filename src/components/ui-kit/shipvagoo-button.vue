<template>
  <q-btn
    class="ui_kit-shipvagoo_button"
    no-caps
    size="15px"
    color="brand"
    :style="{
      height: height,
      width: width,
      minHeight: minHeight,
      minWidth: minWidth,
      padding: padding,
    }"
    v-bind="$attrs"
  >
    <slot />
  </q-btn>
</template>

<script setup lang="ts">
  import { dom, QBtnProps } from 'quasar';
  import height = dom.height;

  interface Props extends QBtnProps {
    height?: string;
    width?: string;
    minHeight?: string;
    minWidth?: string;
    fontSize?: string;
    padding?: string;
  }

  withDefaults(defineProps<Props>(), {
    height: '37px',
  });
</script>

<style lang="scss">
  .ui_kit-shipvagoo_button {
    font-family: Helvetica, sans-serif !important;
    font-size: 12px !important;
    font-style: normal !important;
    font-weight: 400 !important;
    .q-btn__content {
      line-height: normal !important;
      text-align: center !important;

      .q-icon,
      .on-left {
        font-size: 14px !important;
      }
    }

    &.q-btn--outline {
      background: #f8f9ff !important;

      &:hover {
        color: #fff !important;
        background: #243e90 !important;
      }
    }

    &:hover {
      //background-color: #DEF5FF !important;
      opacity: 0.8 !important;
    }
  }
</style>
