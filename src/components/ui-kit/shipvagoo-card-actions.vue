<template>
  <q-card-actions
    class="ui_kit-shipvagoo_card_actions"
    :class="{
      'ui_kit-shipvagoo_card_actions--left': align === 'left',
      'ui_kit-shipvagoo_card_actions--right': align === 'right',
    }"
    v-bind="$attrs"
  >
    <slot />
  </q-card-actions>
</template>

<script setup lang="ts">
  import { QCardActionsProps } from 'quasar';

  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  interface Props extends QCardActionsProps {
    align?: QCardActionsProps['align'];
  }

  withDefaults(defineProps<Props>(), { align: 'right' });
</script>

<style lang="scss">
  .ui_kit-shipvagoo_card_actions {
    padding: 20px !important;

    &--right {
      justify-content: flex-end !important;
    }
  }
</style>
