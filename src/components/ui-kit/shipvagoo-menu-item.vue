<template>
  <q-item class="ui_kit-shipvagoo-menu_item"  v-bind="$attrs">
    <slot />
    <q-icon v-if="hasCaret" name="ion-ios-arrow-down rotate-270" />
  </q-item>
</template>

<script setup lang="ts">
  import { QItemProps } from 'quasar';

  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  interface Props extends QItemProps {
    hasCaret?: boolean;
  }

  withDefaults(defineProps<Props>(), {
    hasCaret: false,
  });
</script>

<style lang="scss">
  .ui_kit-shipvagoo-menu_item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: Helvetica, sans-serif !important;
    font-size: 13px !important;
    font-style: normal !important;
    font-weight: 400 !important;
    line-height: normal !important;
    color: #313131 !important;
  }

  .ui_kit-shipvagoo-menu_item:hover {
    background-color: #def5ff;
  }
</style>
