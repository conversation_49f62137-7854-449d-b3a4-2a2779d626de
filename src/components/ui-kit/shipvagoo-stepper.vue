<template>
  <q-stepper
    class="ui_kit-shipvagoo-stepper"
    v-model="stepperValue"
    ref="stepper"
    alternative-labels
    inactive-color="dark-brand"
    done-color="brand"
    :icon="icon"
    active-icon="far fa-dot-circle"
    active-color="brand"
    done-icon="far fa-dot-circle"
    :inactive-icon="icon"
    v-bind="$attrs"
    animated
  >
    <template v-for="(_index, name) in $slots" #[name]>
      <slot :name="name" />
    </template>
  </q-stepper>
</template>

<script setup lang="ts">
  import { ComponentPublicInstance, computed, ref } from 'vue';
  import InactiveIcon from '../../assets/img/stepper-inactive.svg';
  import { QStepper } from 'quasar';

  const stepper = ref<ComponentPublicInstance<{ next: () => void; previous: () => void }> | null>(
    null,
  );
  const next = () => {
    stepper.value?.next();
  };
  const previous = () => {
    stepper.value?.previous();
  };
  defineExpose({
    next,
    previous,
  });

  interface Props<T> {
    modelValue: T;
  }

  const props = defineProps<Props<any>>();
  const emit = defineEmits(['update:modelValue']);
  const stepperValue = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emit('update:modelValue', value);
    },
  });

  const icon = ref('img:' + InactiveIcon);
</script>
<style lang="scss">
  .ui_kit-shipvagoo-stepper {
    &.q-stepper {
      border: none !important;
      outline: none !important;
      box-shadow: none !important;

      .q-stepper__header--border {
        border-bottom: none !important;
      }
    }

    .q-stepper__dot {

      order: 2;
      width: 20px;
      height: 20px;
      margin-top: 10px;
      background-color: transparent;

      .q-icon{
        &:before {
          font-size: 21px;
          color: #243e90;
        }
        
       /* &:after{
          position: absolute;
          content: "";
          width: 8px;
          height: 8px;
          display: block;
          background: #243e90;
          border-radius: 50%;
          top: 3px;
          left: 3px;
        }*/
      }

      .q-icon img {
        width: 22px;
        height: 22px;
      }

      &:after,
      &:before {
        height: 1px;
        margin-right: -2px !important;
        margin-left: -2px !important;
      }
    }

    .q-stepper__label {
      order: 1;
    }

    .q-stepper__tab {
      .q-stepper__title {
        font-family: Helvetica, sans-serif;
        font-size: 14px;
        font-style: normal;
        line-height: normal;
        text-align: center;
      }

      &--active,
      &--done {
        .q-stepper__title {
          font-weight: 700;
        }
      }

      &--active {
        &.q-stepper__dot,
        .q-stepper__line:before {
          background: #243e90 !important;
        }
      }

      &--done {
        &.q-stepper__dot,
        .q-stepper__line:before {
          background: #243e90 !important;
        }

        &.q-stepper__dot,
        .q-stepper__line:after {
          background: #243e90 !important;
        }
      }
    }
  }
</style>
