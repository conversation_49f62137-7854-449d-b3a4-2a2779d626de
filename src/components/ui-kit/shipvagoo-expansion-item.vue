<template>
  <q-expansion-item
    class="shipvagoo-expansion-item-ui-kit"
    dense
    hide-expand-icon
    v-model="isChecked"
    ref="expansionItem"
  >
    <template #header>
      <div class="shipvagoo-expansion-item-ui-kit--header">
        <shipvagoo-checkbox-v1 v-model="isChecked"
          ><strong>
            {{ label }}
          </strong></shipvagoo-checkbox-v1
        >
      </div>
    </template>
    <template #default>
      <slot></slot>
    </template>
  </q-expansion-item>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { QExpansionItem } from 'quasar';

  interface Props {
    modelValue: boolean;
    label: string;
  }

  const props = defineProps<Props>();
  const emit = defineEmits(['update:modelValue']);
  const expansionItem = ref<QExpansionItem | null>(null);
  const isChecked = computed({
    get() {
      return props.modelValue;
    },
    set(value: boolean) {
      emit('update:modelValue', value);
    },
  });
</script>
<style lang="scss">
  .shipvagoo-expansion-item-ui-kit {
    border: 1px solid #e1e1e1;
    border-radius: 4px;

    .q-item,
    .q-item--dense {
      padding: 0 !important;
      margin: 0 !important;
    }

    &--header {
      display: flex;
      align-items: center;
      width: 100%;
      height: 40px;
      padding: 10px 15px;
      background-color: rgba(208, 211, 218, 0.2) !important;
    }
  }
</style>
