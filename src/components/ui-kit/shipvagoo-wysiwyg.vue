<template>
  <q-editor v-model="modelValue" class="ui_kit-shipvagoo_wysiwyg" v-bind="$attrs" />
</template>

<script setup lang="ts">
import { QEditor, QEditorProps } from 'quasar';

// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface Props extends QEditorProps {}

defineProps<Props>();
</script>

<style lang="scss">
.ui_kit-shipvagoo_wysiwyg {
  background-color: transparent;
  border: 1px solid $secondary-one;

  .q-editor__toolbar {
    border-bottom: 1px solid $secondary-one;
  }
}
</style>