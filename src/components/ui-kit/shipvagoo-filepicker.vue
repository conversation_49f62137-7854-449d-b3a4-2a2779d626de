<template>
  <q-file
    ref="refFilePicker"
    class="ui-kit-shipvagoo-filepikcer"
    stack-label
    filled
    dense
    hide-bottom-space
    v-model="modelValue"
    :label="label"
    v-bind="$attrs">
    <template v-slot:prepend>
      <q-icon name="fas fa-upload" size="xs" color="grey-7" />
    </template>
    <!--    <template v-for="(_index, name) in $slots" #[name]>
          <slot :name="name" />
        </template>-->
    <div v-if="!modelValue" class="text-grey-7 shipvagoo-filepicker-placeholder">
      {{ placeholder }}
    </div>
  </q-file>
</template>
<script setup lang="ts">
import { QFileProps } from "quasar";
import { ref } from "vue";

interface Props extends QFileProps {
  label: string,
  placeholder: string,
}

withDefaults(defineProps<Props>(), {
  label: "Upload file",
  placeholder: "Select file"
});
const refFilePicker = ref<any>();
const removeFileAtIndex = (index: number) => {
  if (refFilePicker.value) {
    refFilePicker.value.removeAtIndex(0);
  }
};
defineExpose({
  removeFileAtIndex
});
</script>


<style lang="scss">
.ui-kit-shipvagoo-filepikcer {
  .q-field__inner .q-field__label {
    margin-left: -25px;
    font-family: Helvetica, sans-serif;
    font-size: 14px !important;
    font-style: normal;
    font-weight: 400;
    line-height: normal !important;

    color: #212121;
  }

  &.q-field.q-field--filled {
    .q-field__inner > :not(.q-field__bottom) {
      background-color: #F9F9F9 !important;
      border-radius: 4px 4px 0 0;
    }

    .q-field__control {
      height: 55px !important;

    }
  }

  .q-field__prepend {
    .q-icon {
      margin-top: 24px !important;
    }
  }

  .q-field__append {
    .q-icon {
      margin-top: 20px !important;
      font-size: 16px !important;
    }
  }

  .shipvagoo-filepicker-placeholder {
    position: absolute;
    top: 22px;
    left: 3px;
  }
}
</style>