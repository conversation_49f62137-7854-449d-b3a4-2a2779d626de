<template>
  <q-separator
    class="ui_kit-shipvagoo_separator"
    v-bind="$attrs"
    :style="{
      background: `${color} !important`,
    }"
  />
</template>

<script setup lang="ts">
import {QSeparatorProps} from 'quasar';

// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface Props extends QSeparatorProps {
  color?: string;
}

withDefaults(defineProps<Props>(), {
  color: '#7D839833',
});
</script>
