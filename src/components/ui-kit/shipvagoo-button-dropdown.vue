<template>
  <q-btn-dropdown
    no-caps
    class="ui_kit-shipvagoo_button_dropdown"
    style="height: 40px;color: white !important;background:#243E90  !important; border-radius: 8px;"
    v-bind="$attrs">
    <slot/>
  </q-btn-dropdown>
</template>

<script setup lang="ts">
  import {QBtnDropdown, QBtnDropdownProps} from 'quasar';

  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  interface Props extends QBtnDropdownProps {
  }

  defineProps<Props>();
</script>

<style lang="scss">
  .ui_kit-shipvagoo_button_dropdown {
    &.q-btn-dropdown--split .q-btn-dropdown__arrow-container {
      width: 40px !important;
      color: white;
      //background-color: #2929c8 !important;
      background-color: #7287C8 !important;
    }

    &.q-btn-dropdown--split .q-btn-dropdown__arrow-container.q-btn--outline {
      border-left: unset !important;

      .q-btn__content {
        color: white;

        .block {
          color: white !important;
        }
      }
    }

    &.q-btn-dropdown--split {
      border: 1px solid #CFDDE4 !important;

      .q-btn--outline:before {
        border: unset !important;
      }

      .q-btn-dropdown--current {
        margin-right: 1px;
        // color: #2929c8 !important;
        color: white !important;
        /* background-color: #2929c8 !important; */
      }
    }
  }


</style>