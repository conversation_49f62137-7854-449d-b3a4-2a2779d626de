<template>
  <button v-ripple class="ui_kit-shipvagoo_fab" @click="onClickFab">
    <q-icon :name="icon" :size="size" />
  </button>
</template>

<script setup lang="ts">
  import { useQuasar } from 'quasar';

  const $q = useQuasar();
  defineExpose({ $q });
  const emit = defineEmits(['click']);

  interface Props {
    icon: string;
    size?: `${string}px`;
  }

  withDefaults(defineProps<Props>(), {
    size: '24px',
  });

  const onClickFab = () => {
    emit('click');
  };
</script>

<style scoped lang="scss">
  .ui_kit-shipvagoo_fab {
    position: fixed;
    right: 40px;
    bottom: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    color: #ffffff;
    cursor: pointer;
    background-color: #1f2124;
    border: none;
    border-radius: 50%;
    transition: background-color 0.3s cubic-bezier(0.25, 0.8, 0.5, 1);

    &:hover {
      background-color: #424242;
    }
  }
</style>
