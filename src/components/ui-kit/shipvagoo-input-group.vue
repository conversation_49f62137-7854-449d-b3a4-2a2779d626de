<template>
  <div
    class="ui_kit-shipvagoo_input_group"
    :class="{
      'ui_kit-shipvagoo_input_group--grid-1': grid === 1,
      'ui_kit-shipvagoo_input_group--grid-2': grid === 2,
      'ui_kit-shipvagoo_input_group--grid-3': grid === 3,
      'ui_kit-shipvagoo_input_group--responsive': responsive,
    }"
    :style="{ gap: gap }"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
  interface Props {
    responsive?: boolean;
    gap?: `${number}px`;
    grid?: 1 | 2 | 3;
  }

  withDefaults(defineProps<Props>(), {
    responsive: false,
    gap: '20px',
    grid: 2,
  });
</script>

<style scoped lang="scss">
  .ui_kit-shipvagoo_input_group {
    display: grid;
  }

  .ui_kit-shipvagoo_input_group--grid-1 {
    grid-template-columns: 1fr;
  }

  .ui_kit-shipvagoo_input_group--grid-2 {
    grid-template-columns: 1fr 1fr;
  }

  .ui_kit-shipvagoo_input_group--grid-3 {
    grid-template-columns: 1fr 1fr 1fr;
  }

  .ui_kit-shipvagoo_input_group--responsive {
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
</style>
