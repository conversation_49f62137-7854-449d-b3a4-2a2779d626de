<template>
  <q-card
    class="ui_kit-shipvagoo-modal"
    :class="{
      'ui_kit-shipvagoo-modal--xs': size === 'xs',
      'ui_kit-shipvagoo-modal--sm': size === 'sm',
      'ui_kit-shipvagoo-modal--md': size === 'md',
      'ui_kit-shipvagoo-modal--x-md': size === 'x-md',
      'ui_kit-shipvagoo-modal--lg': size === 'lg',
      'ui_kit-shipvagoo-modal--s-lg': size === 's-lg',
      'ui_kit-shipvagoo-modal--x-lg': size === 'x-lg',
    }"
  >
    <div>
      <shipvagoo-card-section class="ui_kit-shipvagoo-modal-header row items-center">
        <div class="ui_kit-shipvagoo-modal-header-title">{{ title }}</div>
        <q-space />
        <slot name="icon-right" />
        <q-btn
          v-close-popup
          icon="close"
          color="black"
          flat
          round
          dense
          @click="onClickClose"
          style="margin-right: -7px"
        />
      </shipvagoo-card-section>
    </div>
    <shipvagoo-separator v-if="headerSeparator" :class="separatorClass" />
    <!--    this slot for fixed container -->
    <slot name="under-header" />
    <div class="slot-container">
      <slot />
    </div>
  </q-card>
</template>

<script setup lang="ts">
  interface Props {
    title?: string;
    size?: 'xs' | 'sm' | 'md' | 'lg' | 'x-md' | 's-lg' | 'x-lg';
    headerSeparator: boolean;
    separatorClass?: string | null;
  }

  withDefaults(defineProps<Props>(), {
    title: '',
    size: 'md',
    headerSeparator: true,
    separatorClass: null,
  });

  const emit = defineEmits(['close']);

  const onClickClose = () => {
    emit('close');
  };
</script>

<style scoped lang="scss">
  .ui_kit-shipvagoo-modal {
    min-width: 310px;
    overflow: hidden;
    background: #ffffff;
    border-radius: 6px;

    &-header {
      flex-wrap: nowrap;
      height: 60px;
      font-size: 18px;

      &-title {
        overflow: hidden;
        font-family: Helvetica, sans-serif;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        color: #212121;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    &--xs {
      width: 470px;
      max-width: 470px;
    }

    &--sm {
      width: 560px;
      max-width: 560px;
    }

    &--md {
      width: 635px;
      max-width: 635px;
    }

    &--x-md {
      width: 800px;
      max-width: 800px;
    }

    &--lg {
      width: 1038px;
      max-width: 1038px;
    }

    &--x-lg {
      width: 1200px;
      max-width: 1200px;
    }

    &--s-lg {
      width: 900px;
      max-width: 900px;
    }

    .slot-container {
      max-height: calc(100vh - 200px);
      overflow: auto;

      scrollbar-gutter: stable;
      scrollbar-color: #ccc transparent;
      scrollbar-width: thin;

      *::-webkit-scrollbar {
        width: 5px;
      }

      *::-webkit-scrollbar-track {
        background: transparent;
      }

      *::-webkit-scrollbar-thumb {
        background-color: #ccc;
        border: 0 solid transparent;
        border-radius: 0;
      }
    }
  }

  .q-dialog__inner--top:not(.q-dialog__inner--animating) > div {
    border-top-left-radius: 8px !important;
    border-top-right-radius: 8px !important;
  }
</style>
