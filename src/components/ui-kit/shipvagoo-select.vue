<template>
  <q-select
    v-model="modelValue"
    :class="{
      'ui-kit-shipvagoo_select--top-padding':
        ($attrs.label !== undefined && $attrs.label !== '') ||
        (label !== undefined && label !== ''),
    }"
    class="ui-kit-shipvagoo_select"
    popup-content-class="ui-kit-shipvagoo_select__popup"
    dense
    outlined
    hide-bottom-space
    stack-label
    no-error-icon
    :label="required ? HelperService.addStar(label) : label"
    v-bind="$attrs"
  >
    <template #prepend>
      <slot name="prepend"/>
    </template>

    <template v-for="(_index, name) in slots" #[name]="scope">
      <slot :name="name" v-bind="scope"/>
    </template>
  </q-select>
</template>

<script setup lang="ts">
  import * as _ from 'lodash-es';
  import {QSelect, QSelectProps} from 'quasar';
  import {useSlots} from 'vue';
  import HelperService from '../../services/helper.service';

  interface Props extends QSelectProps {
    required?: boolean;
    label?: string;
  }

  const slots = _.omit(useSlots(), ['prepend']);

  defineProps<Props>();
</script>

<style lang="scss">
  .ui-kit-shipvagoo_select {
    &--top-padding {
      padding-top: 17px;
    }

    &.q-field--outlined .q-field__control {
      padding: 0 4px 0 12px !important;
      border-radius: 5px !important;

      &:hover,
      &:focus-within {
        &::before,
        &::after {
          border: 1px solid #a3a3a3 !important;
        }
      }

      &:focus-within {
        &::before,
        &::after {
          //  border: 1px solid #DEE2EF !important;
          outline: 0 none;
          outline-offset: 0;
          /* box-shadow: 0 0 0 0.05rem #1f2124 !important;*/
        }
      }

      &::before,
      &::after {
        //    border-color: $secondary-one !important;
        border-color: #DEE2EF !important;
        border-style: solid !important;
        border-width: 1px !important;
        box-shadow: 0 0 0 0rem rgba(167, 167, 190, 0.31) !important;
        transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
      }
    }

    &.q-field--outlined.q-field--disabled .q-field__control {
      &:before {
        border: 1px solid #DEE2EF !important;
      }
    }

    .q-field__native,
    .q-field__prefix,
    .q-field__suffix,
    .q-field__input,
    .q-icon {
      padding: 0 !important;
      line-height: 17px !important;
      color: #000000 !important;
    }

    &.q-field--auto-height.q-field--dense.q-field--labeled .q-field__control-container {
      position: static !important;
      padding-top: unset !important;
    }

    .q-field__inner .q-field__label {
      top: -13px !important;
      left: 3px !important;
      font-size: 16px !important;
      //color: #777777;
      font-weight: 500;
      line-height: 16px !important;
      color: $secondary-one;
    }

    .ellipsis {
      overflow: visible !important;;
    }

    .q-field__append .q-icon {
      padding-right: 12px;
    }
  }

  .ui-kit-shipvagoo_select__popup {
    max-height: 200px !important;

    &.q-menu {
      // background-color: #151526 !important;
      background-color: #FFFFFF !important;
      border-radius: 5px !important;
      //  box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.5) !important;
    }

    .q-virtual-scroll__content {
      .q-item {
        //color: #ffffff !important;
        color: #1f2124 !important;
        background-color: white !important;

        &:hover {
          background-color: #DEF5FF !important;
        }
      }
    }
  }

  .q-manual-focusable--focused > .q-focus-helper, body.desktop .q-hoverable:hover > .q-focus-helper {
    background: none !important;
  }
</style>
