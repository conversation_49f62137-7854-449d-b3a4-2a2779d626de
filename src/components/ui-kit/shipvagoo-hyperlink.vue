<template>
  <div class="ui_kit-shipvagoo-hyperlink">
    <a v-if="type === 'external-link'" :style="{color:color ,textDecoration:textDecoration}"
       :href="to" :target="target" :rel="rel">{{ label }}</a>
    <router-link v-if="type === 'router-link'" :to="to" :style="{color:color}">{{ label }}</router-link>
    <button v-if="type === 'button'" @click="emit('click')" class="text-no-wrap" :style="{color:color}">{{ label }}</button>
  </div>
</template>

<script setup lang="ts">
import {computed} from 'vue';

interface Props {
  type: 'router-link' | 'external-link' | 'button';
  label: string;
  to?: string;
  blank?: boolean;
  noreferrer?: boolean;
  noopener?: boolean;
  color?: string
  textDecoration: string
}

const emit = defineEmits(['click']);

const props = withDefaults(defineProps<Props>(), {
  blank: false,
  noreferrer: false,
  noopener: false,
  to: '#',
  textDecoration: 'underline'
});

const target = computed(() => {
  if (props.blank) {
    return '_blank';
  }

  return '_self';
});

const rel = computed(() => {
  const rels = [];

  if (props.noreferrer) {
    rels.push('noreferrer');
  }

  if (props.noopener) {
    rels.push('noopener');
  }

  return rels.join(' ');
});
</script>

<style scoped lang="scss">
.ui_kit-shipvagoo-hyperlink {
  display: inline-block;

  a {
    color: $primary-one;
    text-decoration: underline;

    &:hover {
      text-decoration: underline !important;
    }
  }

  p {
    margin: 0;
  }

  button {
    display: inline-block;
    padding: 0;
    font-weight: 500;
    color: $primary-one;
    text-decoration: underline;
    cursor: pointer;
    background: none;
    border: none;
  }
}
</style>
