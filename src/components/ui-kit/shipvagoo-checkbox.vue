<template>
  <div
    class="ui_kit-shipvagoo_checkbox"
    :class="{
      'ui_kit-shipvagoo_checkbox--sm': size === 'sm',
      'ui_kit-shipvagoo_checkbox--md': size === 'md',
      'ui_kit-shipvagoo_checkbox--lg': size === 'lg',
      'ui_kit-shipvagoo_checkbox--disabled': disabled,
    }"
  >
    <label>
      <input v-model="checkedValue" type="checkbox" :disabled="disabled" @click="onClick"/>
      <span class="ui_kit-shipvagoo_checkbox--box"></span>
      <p v-if="$props.label && !$slots.default">{{ label }}</p>
      <p v-if="$slots.default"></p>
      <slot/>
    </label>
  </div>
</template>

<script setup lang="ts">
  import {computed, WritableComputedRef} from 'vue';

  interface Props {
    label?: string;
    size?: 'sm' | 'md' | 'lg'
    modelValue: boolean;
    disabled?: boolean;
  }

  const props: Readonly<Props> = withDefaults(defineProps<Props>(), {
    label: undefined,
    modelValue: false,
    size: 'md',
    disabled: false,
  });

  const emit = defineEmits(['update:modelValue']);

  const checkedValue: WritableComputedRef<boolean> = computed({
    get: () => props.modelValue,
    set: (value: boolean) => {
      emit('update:modelValue', value);
    },
  });

  const onClick = (e: MouseEvent) => {
    e.stopPropagation();
  };
</script>

<style scoped lang="scss">
  @use '../../assets/scss/variables';

  .ui_kit-shipvagoo_checkbox {
    $self: &;

    display: flex;
    align-items: center;

    &#{$self}--sm {
      span {
        width: 14px;
        min-width: 14px;
        height: 14px;
      }

      input:checked ~ span:before {
        width: 8px;
        min-width: 8px;
        height: 8px;
      }
    }

    &#{$self}--md {
      span {
        width: 18px;
        min-width: 18px;
        height: 18px;
      }

      input:checked ~ span:before {
        width: 12px;
        min-width: 12px;
        height: 12px;
      }
    }

    &#{$self}--lg {
      span {
        width: 20px;
        min-width: 20px;
        height: 20px;
      }

      input:checked ~ span:before {
        width: 14px;
        min-width: 14px;
        height: 14px;
      }
    }

    &#{$self}--disabled {
      opacity: 0.7;
    }

    input {
      display: none;
    }

    span {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      border: 1px solid $primary-one;
      border-radius: 4px;
    }

    label {
      display: flex;
      align-items: center;
      padding-bottom: 0;

      p {
        margin: 0;
        margin-left: 10px;
        font-size: 12px;
        line-height: 14px;
      }

      &:hover {
        cursor: pointer;
      }
    }

    input:checked ~ span:before {
      display: block;
      content: '';
      background-color: $primary-one;
      border-radius: 2px;
    }
  }

  p {
    color: #1f2124
  }

  label {
    color: #1f2124;
  }
</style>
