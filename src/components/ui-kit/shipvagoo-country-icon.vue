<template>
  <img
    v-if="country"
    width="15"
    height="15"
    :src="`/assets/img/${iso}.svg`"
    alt=""
    decoding="async"
    style="border-radius: 50%"
  />
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { ICountry } from '../../model/country.model';

  interface Props {
    country: ICountry;
  }

  const props = defineProps<Props>();

  const iso = computed(() => props.country.iso.toLowerCase());
</script>
