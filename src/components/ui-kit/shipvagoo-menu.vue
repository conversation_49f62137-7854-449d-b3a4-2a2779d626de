<template>
  <q-menu
    class="ui_kit-shipvagoo_menu"
    anchor="bottom left"
    v-bind="$attrs"
    :style="{ 'border-radius': borderRadiusStyle }"
    ref="shipvagoo_menu"
  >
    <slot />
  </q-menu>
</template>

<script setup lang="ts">
  import { QMenuProps, QMenu } from 'quasar';
  import { computed, ref } from 'vue';

  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  interface Props extends QMenuProps {
    borderRadius?: string;
  }

  const props = defineProps<Props>();
  const shipvagoo_menu = ref<QMenu | null>(null);
  const borderRadiusStyle = computed(() => {
    if (!props.borderRadius) {
      return props.borderRadius;
    }

    return `${props.borderRadius} !important`;
  });
  const show = () => {
    if (shipvagoo_menu.value) {
      shipvagoo_menu.value.show();
    }
  };
  const hide = () => {
    if (shipvagoo_menu.value) {
      shipvagoo_menu.value.hide();
    }
  };

  defineExpose({
    show,
    hide,
  });
</script>

<style lang="scss">
  .ui_kit-shipvagoo_menu {
    z-index: 99999999;
    overflow: hidden !important;
    //  background: #151526 !important;
    background: #ffffff !important;
    border-radius: 5px !important;
  }
</style>
