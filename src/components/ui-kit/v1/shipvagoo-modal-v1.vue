<template>
  <q-card
    class="ui_kit-shipvagoo-modal"
    :class="{
      'ui_kit-shipvagoo-modal--xs': size === 'xs',
      'ui_kit-shipvagoo-modal--sm': size === 'sm',
      'ui_kit-shipvagoo-modal--md': size === 'md',
      'ui_kit-shipvagoo-modal--lg': size === 'lg',
    }"
  >
    <shipvagoo-card-section no-padding class="ui_kit-shipvagoo-modal-header row items-center">
      <div class="ui_kit-shipvagoo-modal-header-title">{{ title }}</div>
      <q-space />
      <slot name="icon-right" />
      <q-btn v-close-popup icon="close" color="white" flat round dense @click="onClickClose" />
    </shipvagoo-card-section>
    <shipvagoo-separator />
    <div class="slot-container">
      <slot />
    </div>
  </q-card>
</template>

<script setup lang="ts">
  interface Props {
    title?: string;
    size?: 'xs' | 'sm' | 'md' | 'lg';
  }

  withDefaults(defineProps<Props>(), {
    title: '',
    size: 'md',
  });

  const emit = defineEmits(['close']);

  const onClickClose = () => {
    emit('close');
  };
</script>

<style scoped lang="scss">
  .ui_kit-shipvagoo-modal {
    min-width: 310px;
    overflow: hidden;
    background: #fff;
    border-radius: 10px;

    &-header {
      flex-wrap: nowrap;
      padding: 11px 13px 11px 13px;
      font-size: 16px;
      background: #243e90;

      &-title {
        overflow: hidden;
        font-weight: 500;
        font-weight: bold;
        color: white;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    &--xs {
      width: 470px;
      max-width: 470px;
    }

    &--sm {
      width: 560px;
      max-width: 560px;
    }

    &--md {
      width: 600px;
      max-width: 600px;
    }

    &--lg {
      width: 1038px;
      max-width: 1038px;
    }

    .slot-container {
      max-height: calc(100vh - 48px - 67px);
      overflow: auto;
    }
  }
</style>
