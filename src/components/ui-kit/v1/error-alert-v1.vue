<template>
<div class="error-alert">
    <div class="icon">
        <svg class="alert-icon" width="30" height="30" viewBox="0 0 24 24" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
                d="M17.9718 20.3999H6.02938C5.41692 20.4263 4.80844 20.2892 4.26641 20.0028C3.72438 19.7164 3.26829 19.2911 2.94489 18.7704C2.62148 18.2496 2.4424 17.6521 2.42604 17.0393C2.40968 16.4264 2.55662 15.8204 2.85178 15.2831L8.82296 4.90545C9.15398 4.35901 9.62037 3.90719 10.177 3.59359C10.7336 3.28 11.3616 3.11523 12.0005 3.11523C12.6394 3.11523 13.2675 3.28 13.8241 3.59359C14.3808 3.90719 14.8472 4.35901 15.1782 4.90545L21.1494 15.2831C21.4445 15.8204 21.5915 16.4264 21.5751 17.0393C21.5588 17.6521 21.3797 18.2496 21.0563 18.7704C20.7329 19.2911 20.2767 19.7164 19.7347 20.0028C19.1927 20.2892 18.5842 20.4263 17.9718 20.3999Z"
                stroke="#BE4748" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round" />
            <path
                d="M10.9258 16.4639C10.9258 16.3219 10.9539 16.1813 11.0085 16.0502C11.0632 15.9191 11.1432 15.8002 11.2441 15.7002C11.3449 15.6003 11.4646 15.5213 11.5961 15.4678C11.7278 15.4143 11.8685 15.3875 12.0106 15.3887C12.2201 15.3925 12.4238 15.4579 12.5963 15.5769C12.7687 15.6958 12.9023 15.863 12.9804 16.0574C13.0583 16.2519 13.0772 16.4651 13.0348 16.6702C12.9923 16.8754 12.8902 17.0634 12.7414 17.211C12.5926 17.3584 12.4036 17.4588 12.1981 17.4995C11.9925 17.5402 11.7796 17.5193 11.5859 17.4396C11.3921 17.3599 11.2262 17.2248 11.1088 17.0512C10.9913 16.8778 10.9277 16.6735 10.9258 16.4639ZM11.2714 13.8431L11.137 8.83191C11.1242 8.71145 11.1369 8.58966 11.1743 8.47444C11.2117 8.35921 11.2728 8.25312 11.3538 8.16307C11.4349 8.07301 11.5339 8.00101 11.6445 7.95172C11.7552 7.90243 11.875 7.87695 11.9962 7.87695C12.1173 7.87695 12.2371 7.90243 12.3477 7.95172C12.4584 8.00101 12.5574 8.07301 12.6384 8.16307C12.7194 8.25312 12.7807 8.35921 12.818 8.47444C12.8554 8.58966 12.8681 8.71145 12.8554 8.83191L12.7306 13.8431C12.7306 14.0365 12.6537 14.2222 12.5169 14.359C12.38 14.4958 12.1944 14.5727 12.001 14.5727C11.8074 14.5727 11.6219 14.4958 11.4851 14.359C11.3483 14.2222 11.2714 14.0365 11.2714 13.8431Z"
                fill="#BE4748" />
        </svg>

    </div>
    <div class="alret-error-v1">{{ text }}</div>
</div>
</template>
  
<script>
export default {
    props: {
        text: {
            type: String,
            required: true
        }
    }
}
</script>
  
<style scoped lang="scss">
.error-alert {
    display: flex;
    align-items: center;
    padding: 1vw;
    border: 1px solid #7D839833;
    border-radius: 5px;

    .icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;

        .alert-icon {
            width: 24px;
            height: 24px;
            object-fit: contain;
        }
    }

    .alret-error-v1 {
        padding-left: 5px;
        margin: 0;
        font-size: 15px;
        color: #212121;
    }


}
</style>
  