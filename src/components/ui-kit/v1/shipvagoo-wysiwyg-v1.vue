<template>
  <div class="ui_kit-shipvagoo_wysiwyg-v1">
    <div>
      <div class="dark-text text-eleven" style="padding:8px 12px">{{ label }}</div>
      <q-editor
        dense
        min-height="5em"
        v-model="modelValue"
        toolbar-color="dark"
        :placeholder="placeholder"
        v-bind="$attrs" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { QEditor, QEditorProps } from "quasar";

// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface Props extends QEditorProps {
  label: string,
  placeholder: string
}

withDefaults(defineProps<Props>(), {
  label: "Content",
  placeholder: "To find your order, please provide details about your purchase"
});
</script>

<style lang="scss">
.ui_kit-shipvagoo_wysiwyg-v1 {
  //background-color: #F9F9F9 !important;
  background-color: #F9F9F9 !important;
  border-bottom: 2px solid #7D839833 !important;
  border-radius: 4px 4px 0 0;

  .q-editor {
    background: transparent !important;
    border: none !important;;

    .q-editor__toolbars-container {
      margin: 0 11px 0 11px;

      .q-editor__toolbar {
        border-top: 1px solid #7D839833;
        border-bottom: 1px solid #7D839833;
        border-radius: 0;
      }
    }

    .q-editor__content {
      padding: 3px 12px 3px 12px;
      color: black !important;
    }
  }
}
</style>
