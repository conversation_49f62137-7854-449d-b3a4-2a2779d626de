<template>
  <q-input
    v-model="modelValue"
    class="ui_kit-shipvagoo_input-v1"
    :class="{ 'ui_kit-shipvagoo_input-v1--top-padding': label }"
    stack-label
    dense
    hide-bottom-space
    no-error-icon
    :placeholder="_placeholder"
    :label="required ? HelperService.addStar(label) : label"
    v-bind="$attrs"
    :style="{ backgroundColor:  bgColor }"
    autocomplete="off"
  >
    <template v-for="(_index, name) in $slots" #[name]>
      <slot :name="name" />
    </template>
  </q-input>
</template>

<script setup lang="ts">
import { QInput, QInputProps } from "quasar";
import { computed } from "vue";
import HelperService from "../../../services/helper.service";

interface Props extends QInputProps {
  placeholder?: string;
  required?: boolean;
  label?: string;
  bgColor?: string;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: "",
  label: "",
  required: false,
  bgColor: "#F9F9F9"
});

// eslint-disable-next-line @typescript-eslint/naming-convention, no-underscore-dangle
const _placeholder = computed<string>(() => {
  if (!props.placeholder && props.placeholder === "" && props.label && props.label !== "") {
    return `${props.label}...`;
  }

  return props.placeholder;
});
</script>

<style lang="scss">
.ui_kit-shipvagoo_input-v1 {

  &--top-padding {
    //padding-top: 5px;
  }


  &.q-field--standard .q-field__control {
    padding: 0 4px 0 12px !important;
    //border-radius: 5px !important;

    &:hover,
    &:focus-within {
      &::before,
      &::after {
        //border: 1px solid #a3a3a3 !important;
        border-bottom: 1px solid #243E90 !important;
      }
    }

    &:focus-within {
      &::before,
      &::after {
        // border: 1px solid #1f2124 !important;
        outline: 0 none;
        outline-offset: 0;
        /*box-shadow: 0 0 0 0.05rem #1f2124 !important;*/
      }
    }

    &::before,
    &::after {
      // border-color: #dedede; //$secondary-one !important;
      // border-style: solid !important;
      // border-width: 1px !important;
      //  box-shadow: 0 0 0 0 #EEEEEE !important;
      // transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
      border-bottom: 2px solid #7D839833 !important;
    }
  }

  &.q-field--standard .q-field--disabled .q-field__control {
    &:before {
      background-color: #F9F9F9 !important;
      border: none !important;
      outline: none !important;
    }
  }


  .q-field__native[type='number']::-webkit-inner-spin-button,
  .q-field__native[type='number']::-webkit-outer-spin-button {
    -webkit-appearance: none;
    appearance: none;
    margin: 0;
  }

  .q-field__native[type='number'] {
    -moz-appearance: textfield;
    appearance: textfield;
  }

  .q-field__native,
  .q-field__prefix,
  .q-field__suffix,
  .q-field__input,
  .q-icon {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    line-height: 17px !important;
    // color: #ffffff !important;
    color: #000000 !important;
  }

  .q-field__suffix {
    margin-right: 10px;
  }

  &.q-field--auto-height.q-field--labeled .q-field__control-container,
  &.q-field--labeled.q-field--dense .q-field__native {
    padding-top: unset !important;
  }

  .q-field__inner .q-field__label {
    //top: -2px !important;
    margin-top: 0 !important;
    font-family: Helvetica, sans-serif;
    font-size: 14px !important;
    font-style: normal;
    font-weight: 400;
    line-height: normal !important;
    color: #212121;
  }

  .ellipsis {
    overflow: visible !important;;
  }

  .q-field__append .q-icon {
    padding-right: 12px;
  }

  /** new **/


  .q-field__native, .q-placeholder {
    font-family: Helvetica, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    color: #7D8398;
  }

  /** new **/

  &.q-field--standard, .q-field__control {
    .text-negative {
      color: #BE4748 !important;
      border-top: 1px solid #BE4748;
      border-right: 1px solid #BE4748;
      border-bottom: 2px solid #BE4748;
      border-left: 1px solid #BE4748;
      // border: 1px solid #ff0724 !important;
      border-radius: 4px 4px 0 0;
    }
  }

  &.q-field.q-field--standard {
    .q-field__inner > :not(.q-field__bottom) {
      // background-color: #F9F9F9 !important;
      // background-color: #CCCCCC !important;
      border-radius: 4px 4px 0 0;
    }

    .q-field__control {
      height: 55px !important;

      .q-field__control-container {
        padding-top: 15px !important;
      }
    }
  }


  .q-field__bottom {
    // background-color: #ffffff !important;
    .q-field__messages {
      padding-left: 2px !important;
      margin-top: -2px !important;
      color: #BE4748 !important;
    }
  }
}

</style>
