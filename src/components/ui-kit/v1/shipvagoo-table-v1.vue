<template>
  <div class="table-container">
    <q-table
      class="ui_kit-shipvagoo_table-v1"
      :loading-label="`${$t('common.loading')}...`"
      :no-data-label="$t('common.no_data')"
      v-bind="$attrs"
    >
      <template #header-selection="scope">
        <shipvagoo-checkbox v-model="scope.selected" />
      </template>
      <template #body-selection="scope">
        <shipvagoo-checkbox v-model="scope.selected" />
      </template>
      <template #no-data>
        <div class="flex items-center">
          <q-icon name="ion-alert" size="sm" style="color: #007aff" />
          <div class="dark-text q-ml-sm text-thirteen">{{ noData }}</div>
        </div>
      </template>
      <template #bottom="scope">
        <div v-if="!hidePagination" class="ui_kit-shipvagoo_table-v1-bottom">
          <div class="ui_kit-shipvagoo_table-v1-bottom-pagination_info">
            {{
              $t('common.entry_counts', {
                start:
                  scope.pagination.page === 1
                    ? 1
                    : scope.pagination.page * scope.pagination.rowsPerPage - 20 + 1,
                end: scope.pagination.page * scope.pagination.rowsPerPage,
                total: total,
              })
            }}
          </div>

          <div class="flex items-center">
            <slot name="sync-pagination"></slot>
            <q-btn
              class="rotate-90 q-mr-sm q-ml-sm pagination-next-prev-btn"
              size="sm"
              :disable="scope.isFirstPage"
              round
              icon="ion-ios-arrow-down"
              @click="scope.prevPage()"
            />

            <q-btn
              class="rotate-270 pagination-next-prev-btn"
              size="sm"
              :disable="scope.isLastPage"
              round
              icon="ion-ios-arrow-down"
              @click="scope.nextPage()"
            />
          </div>
        </div>
      </template>

      <template v-for="(_index, name) in $slots" #[name]="scope">
        <slot :name="name" v-bind="scope" />
      </template>
    </q-table>
  </div>
</template>

<script setup lang="ts">
  import { QTable, QTableProps } from 'quasar';
  import { watch, ref } from 'vue';

  interface Props extends QTableProps {
    total: number;
    hidePagination?: boolean;
    noDataLabel?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    total: 0,
    hidePagination: false,
    noDataLabel: 'Data not found',
  });

  const noData = ref<string>(props.noDataLabel);
  watch(
    () => props.noDataLabel as string,
    (currentVal) => {
      noData.value = currentVal ?? 'Data not found';
    },
  );
</script>

<style lang="scss">
  .table-container {
    position: relative;
    margin-top: 10px;
  }
  .ui_kit-shipvagoo_table-v1 {
    position: relative;

    .q-table {
      //  border-top: 1px solid #2929c8;
      border-top: 1px solid #dee2ef;
    }

    /* .q-table tbody td:before {
       //  background: rgba(113, 4, 213, 0.13);
       background-color: rgba(122, 230, 250, 0.13);
     }*/
    .q-table tbody tr:nth-child(even) {
      background-color: #f7f7f7;
    }

    .q-table tbody tr:nth-child(odd) {
      background-color: #ffffff;
    }

    .q-table tbody tr:hover {
      background-color: #def5ff;
    }

    .q-table thead tr:first-child {
      background-color: #f7f7f7;
    }

    .q-table th,
    .q-table td {
      background: none;
    }

    &.q-table__card {
      //  color: #ffffff;
      color: #1f2124;
      background-color: transparent;
    }

    .q-table__sort-icon {
      opacity: 1;

      img {
        width: 10px;
        height: 10px;
      }
    }

    .q-table__sort-icon--right {
      padding-right: 50px;
      margin-left: -10px;
    }

    &.q-table th {
      padding: 7px 10px !important;
      font-size: 15px;
      font-weight: bold;
    }

    &.q-table td {
      padding: 7px 10px !important;
      font-size: 15px;
    }

    .q-table thead,
    .q-table tr,
    .q-table th,
    .q-table td {
      border-color: #e1e1e1;
    }

    .q-table td {
      font-family: Helvetica, sans-serif;
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
      color: #212121;
    }

    .q-table th {
      position: relative;
      font-family: Helvetica, sans-serif;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      color: #212121;

      span {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    &.q-table__bottom {
      padding: 0;
    }

    &-header_border {
      position: absolute;
      top: 48px;
      right: 0;
      left: 0;
      z-index: 2;
      height: 1px;
      //  border-top: 1px solid #2929c8;
      border-top: 1px solid #dee2ef;
    }

    &-bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      &-pagination_info {
        font-family: Helvetica, sans-serif;
        font-size: 13px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        color: #7d8398;
      }
    }

    .pagination-next-prev-btn {
      width: 32px;
      height: 32px;
    }
  }
</style>
