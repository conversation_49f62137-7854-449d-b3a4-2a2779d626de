<template>
  <q-checkbox class="ui_kit-shipvagoo_checkbox-v1" v-model="checkedValue" dense v-bind="$attrs">
    <template v-for="(_index, name) in $slots" #[name]>
      <slot :name="name" />
    </template>
  </q-checkbox>
</template>

<script setup lang="ts">
  import { computed, WritableComputedRef } from 'vue';
  import { QCheckbox, QCheckboxProps } from 'quasar';

  interface Props extends QCheckboxProps {
    modelValue: boolean;
  }

  const props = defineProps<Props>();

  const emit = defineEmits(['update:modelValue']);

  const checkedValue: WritableComputedRef<boolean> = computed({
    get: () => props.modelValue,
    set: (value: boolean) => {
      emit('update:modelValue', value);
    },
  });

  /*const onClick = (e: MouseEvent) => {
  e.stopPropagation();
};*/
</script>

<style lang="scss">
  .ui_kit-shipvagoo_checkbox-v1 {
    .q-checkbox__label {
      font-family: Helvetica, sans-serif !important;
      font-size: 13px !important;
      font-style: normal !important;
      font-weight: 400 !important;
      line-height: normal !important;
      color: #212121 !important;
      text-align: center !important;
      margin-left: 5px;
    }

    .q-checkbox__inner--truthy .q-checkbox__bg {
      background: #243e90 !important;
      border: 4px solid #243e90 !important;
      border-radius: 5px !important;
    }

    .q-checkbox__bg {
      border: 1px solid #212121 !important;
      border-radius: 6px !important;
    }

    &.disabled {
      opacity: 1 !important;

      .q-checkbox__inner--truthy .q-checkbox__bg {
        background-color: #7d8398 !important;
        border: 4px solid #7d8398 !important;
        border-radius: 5px !important;
      }
    }
  }
</style>
