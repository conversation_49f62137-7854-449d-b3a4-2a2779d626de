<template>
  <div :style="{ backgroundColor: bgColor, color: color }" class="status-tag">
    <div class="text text-no-wrap">
      {{ text }}
    </div>
  </div>
</template>
<script setup lang="ts">
  interface Props {
    text: string;
    color: string;
    bgColor: string;
  }

  defineProps<Props>();
</script>
<style scoped lang="scss">
  .status-tag {
    display: inline-flex;
    gap: 4px;
    align-items: center;
    justify-content: center;
    padding: 4px 10px;
    border-radius: 12px;

    .text {
      font-family: Helvetica, sans-serif;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      letter-spacing: 0.6px;
    }
  }
</style>
