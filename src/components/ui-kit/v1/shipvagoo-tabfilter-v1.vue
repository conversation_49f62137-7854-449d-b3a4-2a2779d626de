<template>
  <div
    class="ui_kit-shipvagoo-tabfilter-filters"
    :class="{
      'ui_kit-shipvagoo-tabfilter-filters--align-left': align === 'left',
      'ui_kit-shipvagoo-tabfilter-filters--align-right': align === 'right',
      'ui_kit-shipvagoo-tabfilter-filters--align-center': align === 'center',
    }"
  >
    <q-tabs
      active-color="dark-gray"
      indicator-color="brand"
      v-model="currentFilter"
      no-caps
      @update:model-value="onChangeTab"
      class="text-dark-gray">
      <q-tab
        style="z-index: 2"
        class="shipvagoo-tab"
        v-for="option in options"
        :name="option.value" :label="option.label"/>

    </q-tabs>
    <div v-if="options?.length > 0" class="ui_kit-shipvagoo-tabfilter-filters-border" style="z-index: 1"></div>
  </div>
</template>

<script setup lang="ts">
import {computed, WritableComputedRef} from 'vue';
import {ITabOption} from '../../../model/common.model';

interface Props {
  options: ITabOption[];
  modelValue: string;
  align?: 'left' | 'center' | 'right';
}

const props: Readonly<Props> = withDefaults(defineProps<Props>(), {
  align: 'left',
});

const emit = defineEmits(['update:modelValue']);

const currentFilter: WritableComputedRef<string> = computed({
  get: () => props.modelValue,
  set: (value: string) => {
    emit('update:modelValue', value);
  },
});
const onChangeTab = (selectedTab: string) => {
  const option: any = props.options.find((item) => item.value == selectedTab);
  if (option.onClick) {
    option.onClick(selectedTab);
  }
}
</script>

<style scoped lang="scss">
.ui_kit-shipvagoo-tabfilter {
  &-filters {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;
    margin-bottom: 0;

    &--align-left {
      margin-right: auto;
    }

    &--align-center {
      margin-right: auto;
      margin-left: auto;
    }

    &--align-right {
      margin-left: auto;
    }

    &-border {
      position: absolute;
      z-index: -1;
      width: 100%;
      height: 2px;
      margin-top: 46px;
      background-color: #EEEEEE;
    }
  }
}


</style>
<style>
.shipvagoo-tab .q-tab__indicator {
  border-radius: 10px !important;
}
</style>
