<template>
  <q-stepper
    class="ui_kit-shipvagoo-stepper-v1"
    v-model="stepperValue"
    ref="stepper"
    alternative-labels
    inactive-color="gray"
    done-color="brand"
    :icon="inactiveIcon"
    :active-icon="activeIcon"
    active-color="brand"
    :done-icon="activeIcon"
    :inactive-icon="inactiveIcon"
    v-bind="$attrs"
    animated>
    <template v-for="(_index, name) in $slots" #[name]>
      <slot :name="name"/>
    </template>
  </q-stepper>
</template>

<script setup lang="ts">
import {ComponentPublicInstance, computed, ref} from 'vue'
import InactiveIcon from '../../../assets/img/stepper-inactive-v1.svg';
import ActiveIcon from '../../../assets/img/stepper-active-v1.svg';
import {QStepper} from 'quasar';

const stepper = ref<ComponentPublicInstance<{ next: () => void; previous: () => void }> | null>(
  null,
);
const next = () => {
  stepper.value?.next();
}
const previous = () => {
  stepper.value?.previous();
}
defineExpose({
  next,
  previous
})

interface Props<T> {
  modelValue: T
}

const props = defineProps<Props<any>>()
const emit = defineEmits(['update:modelValue']);
const stepperValue = computed({
  get() {
    return props.modelValue;
  }, set(value) {
    emit('update:modelValue', value);
  }
})

const inactiveIcon = ref('img:' + InactiveIcon)
const activeIcon = ref('img:' + ActiveIcon)
</script>
<style lang="scss">
.ui_kit-shipvagoo-stepper-v1 {
  &.q-stepper {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;

    .q-stepper__header--border {
      border-bottom: none !important;
    }
  }

  .q-stepper__dot {
    width: 20px;
    height: 20px;
    margin-top: 10px;
    background-color: transparent;

    .q-icon:before {
      font-size: 21px;
      color: #243E90;
    }

    .q-icon img {
      width: 30px;
      height: 30px;
    }

    &:after, &:before {
      height: 1px;
      margin-right: -2px !important;
      margin-left: -2px !important;
    }
  }

  .q-stepper__label {
    order: 1;
  }

  .q-stepper__tab {

    .q-stepper__title {
      margin-top: 5px;
      font-family: Helvetica, sans-serif;
      font-size: 14px;
      font-style: normal;
      line-height: normal;
      text-align: center
    }

    &--active, &--done {
      .q-stepper__title {
        font-weight: 500;
      }
    }

    &--active {
      &.q-stepper__dot, .q-stepper__line:before {
        background: #243E90 !important;
      }
    }

    &--done {
      &.q-stepper__dot, .q-stepper__line:before {
        background: #243E90 !important;
      }

      &.q-stepper__dot, .q-stepper__line:after {
        background: #243E90 !important;
      }
    }
  }

}
</style>

