<template>
  <q-btn-dropdown
    no-caps
    class="ui_kit-shipvagoo_button_dropdown-v1"
    :style="{
      background: `${bgColor} !important`,
      color: `${color} !important`,
      minHeight: minHeight,
      minWidth: minWidth,
    }"
    style=" height: 40px;border-radius: 5px"
    v-bind="$attrs"
  >
    <slot />
  </q-btn-dropdown>
</template>

<script setup lang="ts">
  import { QBtnDropdown, QBtnDropdownProps } from 'quasar';

  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  interface Props extends QBtnDropdownProps {
    color: string;
    bgColor: string;
    minHeight?: string;
    minWidth?: string;
  }

  withDefaults(defineProps<Props>(), {
    color: 'white',
    bgColor: '#243E90',
  });
</script>

<style lang="scss">
  .ui_kit-shipvagoo_button_dropdown-v1 {
    &.q-btn-dropdown--split .q-btn-dropdown__arrow-container {
      width: 40px !important;
      //background-color: #2929c8 !important;
      // background-color: #7287C8 !important;
      //background-color: #243e90 !important;
      //color: white;
    }

    .q-btn__content {
      font-family: Helvetica, sans-serif;
      font-size: 12px !important;
      font-style: normal;
      font-feature-settings: 'clig' off, 'liga' off;
      line-height: normal;
    }

    &.q-btn-dropdown--split .q-btn-dropdown__arrow-container.q-btn--outline {
      border-left: unset !important;

      .q-btn__content {
        //color: #fff;

        .block {
          //   color: white !important;
        }
      }
    }

    .q-btn__content .q-icon {
      font-size: 13px;

      &.q-btn-dropdown__arrow {
        &.rotate-180 {
          transform: rotate(90deg);
        }
      }
    }

    &.q-btn-dropdown--split {
      border: 1px solid #243e90 !important;

      .q-btn--outline:before {
        border: unset !important;
      }

      .q-btn-dropdown--current {
        // color: #2929c8 !important;
        //    color: white !important;
        margin-right: 1px;
        /* background-color: #2929c8 !important; */
      }
    }
  }
</style>
