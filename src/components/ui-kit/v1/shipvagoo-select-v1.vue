<template>
  <q-select
    v-model="modelValue"
    :class="{
      'ui-kit-shipvagoo_select-v1--top-padding':
        ($attrs.label !== undefined && $attrs.label !== '') ||
        (label !== undefined && label !== ''),
    }"
    class="ui-kit-shipvagoo_select-v1"
    popup-content-class="ui-kit-shipvagoo_select__popup-v1"
    dense
    hide-bottom-space
    stack-label
    no-error-icon
    :label="required ? HelperService.addStar(label) : label"
    v-bind="$attrs"
  >
    <template #prepend>
      <slot name="prepend"/>
    </template> 
    
    <template v-for="(_index, name) in slots" #[name]="scope">
      <slot :name="name" v-bind="scope"/>
    </template>
  </q-select>
</template>

<script setup lang="ts">
import * as _ from 'lodash-es';
import {QSelect, QSelectProps} from 'quasar';
import {useSlots} from 'vue';
import HelperService from '../../../services/helper.service';

interface Props extends QSelectProps {
  required?: boolean;
  label?: string;
}

const slots = _.omit(useSlots(), ['prepend','selected']);

defineProps<Props>();
</script>

<style lang="scss">
.ui-kit-shipvagoo_select-v1 {
  &--top-padding {
    // padding-top: 17px;
  }

  &.q-field--standard .q-field__control {
    padding: 0 4px 0 12px !important;
    //  border-radius: 5px !important;

    &:hover,
    &:focus-within {
      &::before,
      &::after {
        // border: 1px solid #a3a3a3 !important;
        border-bottom: 1px solid #243E90 !important;
      }
    }

    &:focus-within {
      &::before,
      &::after {
        //  border: 1px solid #DEE2EF !important;
        outline: 0 none;
        outline-offset: 0;
        /* box-shadow: 0 0 0 0.05rem #1f2124 !important;*/
      }
    }

    &::before,
    &::after {
      //    border-color: $secondary-one !important;
      /*border-color: #DEE2EF !important;
      border-style: solid !important;
      border-width: 1px !important;
      box-shadow: 0 0 0 0rem rgba(167, 167, 190, 0.31) !important;
      transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;*/
      border-bottom: 2px solid rgba(125, 131, 152, 0.20);
    }
  }

  &.q-field--standard.q-field--disabled .q-field__control {
    &:before {
      background-color: #F9F9F9 !important;
      border: none !important;
      outline: none !important;
    }
  }

  .q-field__native,
  .q-field__prefix,
  .q-field__suffix,
  .q-field__input,
  .q-icon {
    padding: 0 !important;
    line-height: 17px !important;
    color: #000000 !important;
  }

  &.q-field--auto-height.q-field--dense.q-field--labeled .q-field__control-container {
    position: static !important;
    padding-top: unset !important;
  }

  .q-field__inner .q-field__label {
    left: 12px !important;
    font-family: Helvetica, sans-serif;
    font-size: 14px !important;
    font-style: normal;
    font-weight: 400;
    line-height: normal !important;
    color: #212121;
  }

  .ellipsis {
    overflow: visible !important;;
  }

  .q-field__append .q-icon {
    padding-right: 12px;
  }


  /** new **/
  .q-field__native, .q-placeholder {
    font-family: Helvetica, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    color: #7D8398;
  }

  &.q-field--standard, .q-field__control {
    .text-negative {
      color: #BE4748 !important;
      border-top: 1px solid #BE4748;
      border-right: 1px solid #BE4748;
      border-bottom: 2px solid #BE4748;
      border-left: 1px solid #BE4748;
      // border: 1px solid #ff0724 !important;
      border-radius: 4px 4px 0 0;
    }
  }

  &.q-field.q-field--standard {
    .q-field__inner > :not(.q-field__bottom) {
      background-color: #F9F9F9 !important;
      //background-color: #CCCCCC !important;
      border-radius: 4px 4px 0 0;
    }

    .q-field__control {
      height: 55px !important;

      .q-field__control-container {
        padding-top: 15px !important;
      }

      .q-field__prepend {
        margin-top: 15px !important;
      }

      .q-field__append {
        margin-top: 13px !important
      }
    }

  }


  .q-field__bottom {
    // background-color: #ffffff !important;
    .q-field__messages {
      padding-left: 2px !important;
      margin-top: -2px !important;
      color: #BE4748 !important;
    }
  }
}

.ui-kit-shipvagoo_select__popup-v1 {
  max-height: 200px !important;

  &.q-menu {
    // background-color: #151526 !important;
    background-color: #FFFFFF !important;
    border-radius: 5px !important;
    //  box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.5) !important;
  }

  .q-virtual-scroll__content {
    .q-item {
      //color: #ffffff !important;
      color: #1f2124 !important;
      background-color: white !important;

      &:hover {
        background-color: #DEF5FF !important;
      }
    }
  }
}

.q-manual-focusable--focused > .q-focus-helper, body.desktop .q-hoverable:hover > .q-focus-helper {
  background: none !important;
}

</style>
