<template>
  <div class="shipvagoo-card-checkbox">
    <div class="shipvagoo-card-checkbox-data flex items-center">
      <div class="shipvagoo-card-checkbox-data-labels">
        <div class="text-twelve" 
        :class="{
          'dark-text':!disable,
          'dark-subtitle-text':disable
        }"
        >
          {{ title }}
        </div>
        <div class="dark-subtitle-text q-mt-xs">
          {{ subtitle }}
        </div>
      </div>
      <div class="shipvagoo-card-checkbox-data-checkbox flex justify-end">
        <shipvagoo-checkbox-v1
          :disable="disable"
          v-model="value">

        </shipvagoo-checkbox-v1>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import {computed} from 'vue';

interface Props {
  title: string,
  subtitle: string,
  modelValue?: boolean,
  disable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disable: false,
  modelValue: false
})

const emit = defineEmits(['update:modelValue'])
const value = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit('update:modelValue', value);
  }
})
</script>

<style scoped lang="scss">
.shipvagoo-card-checkbox {
  width: 100%;
  height: 72px;
  padding: 11px 16px;
  background: #F9F9F9;
  border-bottom: 2px solid rgba(125, 131, 152, 0.20);
  border-radius: 4px 4px 0px 0px;

  &-data {
    display: flex;
    height: 100%;

    &-labels {
      display: flex;
      flex: 9;
      flex-direction: column;
      justify-content: center;
      height: 100%;
    }

    &-checkbox {
      flex: 1
    }
  }
}
</style>