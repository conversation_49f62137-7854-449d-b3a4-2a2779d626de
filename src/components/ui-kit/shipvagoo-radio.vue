<template>
  <q-radio
    v-bind="$attrs"
    class="shipvagoo-ui-kit-radio"
    dense
    :val="radioValue"
    v-model="modelVal"
  >
    <slot name="default" />
  </q-radio>
</template>
<script setup lang="ts">
  import { computed } from 'vue';

  interface Props {
    radioValue: number | string | null | undefined;
    modelValue: number | string | null | undefined;
  }

  const props = defineProps<Props>();
  const emit = defineEmits(['update:modelValue']);
  const modelVal = computed({
    get() {
      return props.modelValue;
    },
    set(val) {
      emit('update:modelValue', val);
    },
  });
</script>

<style lang="scss">
  .shipvagoo-ui-kit-radio {
    .q-radio__inner--truthy {
      color: #243e90 !important;
    }
  }
</style>
