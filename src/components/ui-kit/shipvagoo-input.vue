<template>
  <q-input
    v-model="modelValue"
    class="ui_kit-shipvagoo_input"
    :class="{ 'ui_kit-shipvagoo_input--top-padding': label }"
    stack-label
    dense
    outlined
    hide-bottom-space
    no-error-icon
    :placeholder="_placeholder"
    :label="required ? HelperService.addStar(label) : label"
    v-bind="$attrs"
  >
    <template v-for="(_index, name) in $slots" #[name]>
      <slot :name="name"/>
    </template>
  </q-input>
</template>

<script setup lang="ts">
  import {QInput, QInputProps} from 'quasar';
  import {computed} from 'vue';
  import HelperService from '../../services/helper.service';

  interface Props extends QInputProps {
    placeholder?: string;
    required?: boolean;
    label?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '',
    label: '',
    required: false,
  });

  // eslint-disable-next-line @typescript-eslint/naming-convention, no-underscore-dangle
  const _placeholder = computed<string>(() => {
    if (!props.placeholder && props.placeholder === '' && props.label && props.label !== '') {
      return `${props.label}...`;
    }

    return props.placeholder;
  });
</script>

<style lang="scss">
  .ui_kit-shipvagoo_input {
    .q-field__bottom .q-field__messages {
      //color: #ffffff !important;
      color: #d32424 !important;
    }

    &--top-padding {
      padding-top: 17px;
    }

    &.q-field--outlined .q-field__control {
      padding: 0 4px 0 12px !important;
      border-radius: 5px !important;

      &:hover,
      &:focus-within {
        &::before,
        &::after {
          border: 1px solid #a3a3a3 !important;
        }
      }

      &:focus-within {
        &::before,
        &::after {
          // border: 1px solid #1f2124 !important;
          outline: 0 none;
          outline-offset: 0;
          /*box-shadow: 0 0 0 0.05rem #1f2124 !important;*/
        }
      }

      &::before,
      &::after {
        border-color: #dedede; //$secondary-one !important;
        border-style: solid !important;
        border-width: 1px !important;
        box-shadow: 0 0 0 0 #EEEEEE !important;
        transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
      }
    }

    &.q-field--outlined.q-field--disabled .q-field__control {
      &:before {
        border: 1px solid #EEEEEE !important;
      }
    }

    .q-field__native[type='number']::-webkit-inner-spin-button,
    .q-field__native[type='number']::-webkit-outer-spin-button {
      -webkit-appearance: none;
      appearance: none;
      margin: 0;
    }

    .q-field__native[type='number'] {
      -moz-appearance: textfield;
      appearance: textfield;
    }

    .q-field__native,
    .q-field__prefix,
    .q-field__suffix,
    .q-field__input,
    .q-icon {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
      line-height: 17px !important;
      // color: #ffffff !important;
      color: #000000 !important;
    }

    .q-field__suffix {
      margin-right: 10px;
    }

    &.q-field--auto-height.q-field--labeled .q-field__control-container,
    &.q-field--labeled.q-field--dense .q-field__native {
      padding-top: unset !important;
    }

    .q-field__inner .q-field__label {
      top: -12px !important;
      left: -13px !important;
      font-size: 16px !important;
      font-weight: 500;
      line-height: 16px !important;
      color: $secondary-one;
    }

    .ellipsis {
      overflow: visible !important;;
    }

    .q-field__append .q-icon {
      padding-right: 12px;
    }
  }
</style>
