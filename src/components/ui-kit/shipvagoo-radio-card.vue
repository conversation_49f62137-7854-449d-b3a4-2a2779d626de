<template>
  <div
    @click="onClickCard"
    class="shipvagoo-ui-kit-radio-card"
    :class="{
      'shipvagoo-ui-kit-radio-card--active': isActive,
    }"
  >
    <div style="flex: 1.5" class="flex items-center justify-center">
      <shipvagoo-radio v-model="value" :radio-value="radioValue"></shipvagoo-radio>
    </div>
    <div v-if="imgSrc" style="flex: 1.5" class="flex items-center justify-center">
      <img
        width="24"
        height="24"
        :src="imgSrc"
        alt=""
        decoding="async"
        style="border-radius: 50%"
      />
    </div>
    <div
      :style="{ flex: textFlex }"
      :class="{
        'dark-text': isActive,
        'dark-subtitle-text': !isActive,
      }"
      class="text-weight-4 text-fourteen"
    >
      {{ label }}
    </div>
  </div>
</template>
<script setup lang="ts">
  import { computed } from 'vue';
  interface Props {
    modelValue: number | string | null | undefined;
    radioValue: number | string | null | undefined;
    label?: string;
    imgSrc?: string | null;
  }

  const props = withDefaults(defineProps<Props>(), {
    label: '',
    imgSrc: null,
  });
  const emit = defineEmits(['update:modelValue']);
  const value = computed({
    get() {
      return props.modelValue;
    },
    set(val) {
      emit('update:modelValue', val);
    },
  });
  const isActive = computed(() => {
    return value.value == props.radioValue;
  });
  const textFlex = computed(() => {
    return props.imgSrc ? 7 : 8.5;
  });
  const onClickCard = () => {
    value.value = props.radioValue;
  };
</script>
<style scoped lang="scss">
  .shipvagoo-ui-kit-radio-card {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    height: 48px;
    padding: 0 7px;
    cursor: pointer;
    background: #fff;
    border: 1px solid rgba(125, 131, 152, 0.2);
    border-radius: 4px;

    &--active {
      background: #f8f9ff !important;
      border: 1px solid #243e90 !important;
    }
  }
</style>
