<template>
  <div style="position: relative; margin-top: 20px;">
    <div class="ui_kit-shipvagoo_table-header_border"></div>
    <q-table class="ui_kit-shipvagoo_table" :loading-label="`${$t('common.loading')}...`"
             :no-data-label="$t('common.no_data')" v-bind="$attrs">
      <template #header-selection="scope">
        <shipvagoo-checkbox v-model="scope.selected"/>
      </template>
      <template #body-selection="scope">
        <shipvagoo-checkbox v-model="scope.selected"/>
      </template>

      <template #bottom="scope">
        <div v-if="!hidePagination" class="ui_kit-shipvagoo_table-bottom">
          <div class="ui_kit-shipvagoo_table-bottom-pagination_info">
            {{
              $t('common.entry_counts', {
                start:
                  scope.pagination.page === 1
                    ? 1
                    : scope.pagination.page * scope.pagination.rowsPerPage - 10 + 1,
                end: scope.pagination.page * scope.pagination.rowsPerPage,
                total: total,
              })
            }}
          </div>
          <div>
            <q-btn class="rotate-90 q-mr-sm" size="sm" :disable="scope.isFirstPage" round icon="ion-ios-arrow-down"
                   @click="scope.prevPage()"/>
            <q-btn class="rotate-270" size="sm" :disable="scope.isLastPage" round icon="ion-ios-arrow-down"
                   @click="scope.nextPage()"/>
          </div>
        </div>
      </template>

      <template v-for="(_index, name) in $slots" #[name]="scope">
        <slot :name="name" v-bind="scope"/>
      </template>
    </q-table>
  </div>
</template>

<script setup lang="ts">
import {QTable, QTableProps} from 'quasar';

interface Props extends QTableProps {
  total: number;
  hidePagination?: boolean;
}

withDefaults(defineProps<Props>(), {
  total: 0,
  hidePagination: false,
});
</script>

<style lang="scss">
.ui_kit-shipvagoo_table {
  position: relative;

  .q-table {
    //  border-top: 1px solid #2929c8;
    border-top: 1px solid #DEE2EF;
  }

  /* .q-table tbody td:before {
       //  background: rgba(113, 4, 213, 0.13);
       background-color: rgba(122, 230, 250, 0.13);
     }*/
  .q-table tbody tr:hover {
    background-color: #DEF5FF;
  }

  .q-table thead tr:first-child {
    background-color: #F5F5F5;
  }

  &.q-table__card {
    //  color: #ffffff;
    color: #1f2124;
    background-color: transparent;
  }

  .q-table__sort-icon {
    opacity: 1;

    img {
      width: 10px;
      height: 10px;
    }
  }

  .q-table__sort-icon--right {
    padding-right: 50px;
    margin-left: -10px;
  }

  &.q-table th {
    padding: 7px 10px !important;
    font-size: 15px;
    font-weight: bold;
  }

  &.q-table td {
    padding: 7px 10px;
    font-size: 15px;
  }

  .q-table thead,
  .q-table tr,
  .q-table th,
  .q-table td {
    color: #393939;
    border-color: #e1e1e1;
    //  border-color: #1f2124;
  }

  .q-table td {
    font-size: 1em;
    color: #1f2124;
  }

  .q-table th {
    position: relative;
    font-size: 1em;
    font-weight: bold;

    span {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  &.q-table__bottom {
    padding: 0;
  }

  &-header_border {
    position: absolute;
    top: 48px;
    right: 0;
    left: 0;
    z-index: 2;
    height: 1px;
    //  border-top: 1px solid #2929c8;
    border-top: 1px solid #DEE2EF;
  }

  &-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    &-pagination_info {
      font-size: 12px;
      line-height: 14px;
      //  color: #9eb2c6;
      color: #1f2124;
    }
  }
}
</style>
