<template>
  <div class="shipvagoo-ui-kit-placeholder flex justify-between items-center">
    <div class="flex items-center">
      <q-icon :name="icon" size="sm" style="color: #007AFF" />
      <div class="dark-text q-ml-sm text-thirteen">{{ text }}</div>
    </div>
    <slot name="actions" />
  </div>
</template>
<script setup lang="ts">

interface Props {
  icon?: string,
  text: string
}

withDefaults(defineProps<Props>(), {
  icon: `ion-alert` //fas fa-exclamation-circle
});
</script>


<style scoped lang="scss">
.shipvagoo-ui-kit-placeholder {
  min-height: 55px;
  padding: 7px 17px;
  background: #FFF;
  border: 1px solid rgba(125, 131, 152, 0.20);
  border-radius: 8px;
}
</style>