<template>
  <div
    class="multi-select-dropdown"
    :style="{
      border: modelValue && modelValue.length ? '1px solid rgb(36, 62, 144)' : '',
    }"
  >
    <q-btn-dropdown
      :label="dropdownLabel"
      class="dropdown-btn full-width text-left"
      flat
      no-caps
      dropdown-icon="fas fa-caret-down"
      hide-bottom-space
    >
      <q-list>
        <q-item
          v-for="option in options"
          :key="option.value"
          clickable
          @click.stop="toggleOption(option.value)"
          v-ripple
        >
          <q-item-section avatar>
            <shipvagoo-checkbox-v1
              :model-value="isSelected(option.value)"
              @update:model-value="() => toggleOption(option.value)"
              :label="option.label"
            />
          </q-item-section>
        </q-item>
      </q-list>
    </q-btn-dropdown>
  </div>
</template>
<script setup lang="ts">
  import { computed } from 'vue';
  import { IOption } from '../../model/common.model';

  const props = defineProps<{
    options: IOption<string, string>[];
    label?: string;
    modelValue: string[];
  }>();

  const emit = defineEmits<{
    (e: 'update:modelValue', value: string[]): void;
  }>();

  const dropdownLabel = computed(() => {
    return props.label || 'Country';
  });

  const toggleOption = (value: string) => {
    const selected = [...props.modelValue];
    const index = selected.indexOf(value);

    if (index === -1) {
      selected.push(value);
    } else {
      selected.splice(index, 1);
    }

    emit('update:modelValue', selected);
  };

  const isSelected = (value: string): boolean => {
    return props.modelValue?.includes(value);
  };
</script>
<style lang="scss">
  .multi-select-dropdown {
    height: 40px;
    padding: 0px !important;
    position: relative;
    color: #7d8398;
    font-family: Helvetica, sans-serif;
    font-size: 14px !important;
    font-style: normal;
    font-weight: 400;
    border-radius: 4px;
    .dropdown-btn {
      border: 1px solid #75757533 !important;
      border-radius: 4px;
      height: 40px;
    }
    .q-btn__content .q-icon {
      font-size: 14px !important;
    }
  }

  .dropdown-label {
    color: #212121 !important;
  }
</style>
