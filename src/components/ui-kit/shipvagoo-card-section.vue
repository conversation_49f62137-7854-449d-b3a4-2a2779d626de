<template>
  <q-card-section
    class="ui_kit-shipvagoo_card_section"
    v-bind="$attrs"
    :class="{ 'ui_kit-shipvagoo_card_section__padding': !noPadding }"
  >
    <slot />
  </q-card-section>
</template>

<script setup lang="ts">
  import { QCardSectionProps } from 'quasar';

  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  interface Props extends QCardSectionProps {
    noPadding?: boolean;
  }

  withDefaults(defineProps<Props>(), {
    noPadding: false,
  });
</script>

<style lang="scss">
  .ui_kit-shipvagoo_card_section.q-card__section,
  .ui_kit-shipvagoo_card_section.q-card__section--vert {
    padding: 0;
    color: #1f2124;
  }

  .ui_kit-shipvagoo_card_section__padding.q-card__section,
  .ui_kit-shipvagoo_card_section__padding.q-card__section--vert {
    padding: 20px;
  }
</style>
