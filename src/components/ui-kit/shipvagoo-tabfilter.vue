<template>
  <div
    class="ui_kit-shipvagoo-tabfilter-filters"
    :class="{
      'ui_kit-shipvagoo-tabfilter-filters--align-left': align === 'left',
      'ui_kit-shipvagoo-tabfilter-filters--align-right': align === 'right',
      'ui_kit-shipvagoo-tabfilter-filters--align-center': align === 'center',
    }"
  >
    <button
      v-for="option in options"
      :key="`${option.label}-${option.value}`"
      class="ui_kit-shipvagoo-tabfilter-filters-item"
      :class="{ selected: currentFilter === option.value }"
      @click="
        currentFilter = option.value;
        option.onClick?.();
      "
    >
      <div>{{ option.label }}</div>
      <div class="ui_kit-shipvagoo-tabfilter-filters-item-border"></div>
    </button>
    <div v-if="options?.length > 0" class="ui_kit-shipvagoo-tabfilter-filters-border"></div>
  </div>
</template>

<script setup lang="ts">
  import {computed, WritableComputedRef} from 'vue';
  import {ITabOption} from '../../model/common.model';

  interface Props {
    options: ITabOption[];
    modelValue: string;
    align?: 'left' | 'center' | 'right';
  }

  const props: Readonly<Props> = withDefaults(defineProps<Props>(), {
    align: 'center',
  });

  const emit = defineEmits(['update:modelValue']);

  const currentFilter: WritableComputedRef<string> = computed({
    get: () => props.modelValue,
    set: (value: string) => {
      emit('update:modelValue', value);
    },
  });
</script>

<style scoped lang="scss">
  .ui_kit-shipvagoo-tabfilter {
    &-filters {
      position: relative;

      display: flex;
      align-items: center;
      justify-content: space-between;
      max-width: 808px;
      margin-top: 20px;
      margin-bottom: 0;

      &--align-left {
        margin-right: auto;
      }

      &--align-center {
        margin-right: auto;
        margin-left: auto;
      }

      &--align-right {
        margin-left: auto;
      }

      &-item {
        position: relative;
        z-index: 2;
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;
        padding-bottom: 17px;
        font-size: 15px;
        line-height: 17px;
        //  color: #1f2124;
        color: #1f2124;
        cursor: pointer;
        background-color: transparent;
        border: 0;

        &-border {
          position: absolute;
          bottom: 0;
          left: 0;
          display: none;
          width: 100%;
          height: 3px;
          background-color: #1f2124;
          //  background-color: #1f2124;
          border-radius: 10px;
        }

        &.selected {
          font-weight: bold;

          .ui_kit-shipvagoo-tabfilter-filters-item-border {
            display: block;
          }
        }
      }

      &-border {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: 3px;
        background-color: #DEE2EF;
        border-radius: 10px;
      }

      &-border :hover {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: 3px;
        // background-color: #1f2124;
        background-color: #1f2124;
        border-radius: 10px;
      }

      .ui_kit-shipvagoo-tabfilter-filters-item:hover:not(.selected) {
        font-weight: bold;
        // border-bottom: 2px solid #1f2124;
        border-bottom: 2px solid #1f2124;
      }
    }
  }


</style>
