<template>
  <q-tabs
      stretch
      v-model="tabValue"
      no-caps
      indicator-color="transparent"
      class="shipvagoo-tabs no-padding"
      dense
      inline-label
      outside-arrows
      mobile-arrows
      v-bind="$attrs"
  >
    <q-tab v-for="(option,index) in options"
           :key="`tab_${index}`"
           :tabindex="index"
           :icon='option.value==tabValue?option.activeIcon:option.icon'
           @click="option.onClick"
           :name="option.value"
           :class="option.stylingClass"
           :label="option.label"/>
  </q-tabs>
</template>

<script setup lang="ts">
import {computed} from 'vue';
import {QTabsProps} from "quasar";
import {ITabOption} from '../../model/common.model';

interface Props extends QTabsProps {
  options: ITabOption[];
  modelValue: string | number | null | undefined
//  stylingClass?: IQIcon
}

const props = defineProps<Props>();
const emit = defineEmits(['update:modelValue']);
const tabValue = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit('update:modelValue', value);
  },
});
</script>
<style lang="scss">
.shipvagoo-tabs {
  .q-tab {
    min-width: 110px !important;
    padding: 3px 14px !important;
    font-family: Helvetica, sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: normal;
    color: #7D8398;

    .q-tab__label {
      font-weight: 400 !important;
      color: #7D8398 !important;
    }

    .q-icon {
      color: #212121 !important;
    }
  }

  .q-tab--active {
    font-style: normal;
    line-height: normal;
    background: rgba(125, 131, 152, 0.10);
    border-radius: 4px;

    .q-tab__label {
      font-weight: 700 !important;
      color: #212121 !important;
    }
  }

  .q-icon-24 {
    .q-icon {
      width: 24px !important;
      height: 24px !important;
    }
  }

  .q-icon-20 {
    .q-icon {
      width: 20px !important;
      height: 20px !important;
    }
  }

  .q-icon-16 {
    .q-icon {
      width: 16px !important;
      height: 16px !important;
    }
  }
}
</style>