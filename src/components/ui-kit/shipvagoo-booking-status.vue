<template>
  <div v-if="!isStopShippingMonitoring" class="ui_kit-shipvagoo_booking_status">
    <!--    <div
          class="ui_kit-shipvagoo_booking_status-indicator"
          :class="{
            'ui_kit-shipvagoo_booking_status-indicator&#45;&#45;error': isError,
            'ui_kit-shipvagoo_booking_status-indicator&#45;&#45;is_registered': isRegistered,
            'ui_kit-shipvagoo_booking_status-indicator&#45;&#45;in_transit': isInTransit,
            'ui_kit-shipvagoo_booking_status-indicator&#45;&#45;draft': isDraft,
            'ui_kit-shipvagoo_booking_status-indicator&#45;&#45;unknown': isUnknown,
            'ui_kit-shipvagoo_booking_status-indicator&#45;&#45;failed_to_register': isFailedToRegister,
            'ui_kit-shipvagoo_booking_status-indicator&#45;&#45;delivered': isDelivered,
            'ui_kit-shipvagoo_booking_status-indicator&#45;&#45;delivery_attempted': isDeliveryAttempted,
          }"
        />-->
    <!--    <div v-if="isDraft">{{ $t('common.shipment_status.draft') }}</div>
        <div v-else-if="isRegistered">{{ $t('common.shipment_status.registered') }}</div>
        <div v-else-if="isError">{{ $t('common.shipment_status.error') }}</div>
        <div v-else-if="isInTransit">{{ $t('common.shipment_status.in_transit') }}</div>
        <div v-else-if="isUnknown">{{ $t('common.shipment_status.unknown') }}</div>
        <div v-else-if="isFailedToRegister">{{ $t('common.shipment_status.failed_to_register') }}</div>
        <div v-else-if="isDelivered">{{ $t('common.shipment_status.delivered') }}</div>
        <div v-else-if="isDeliveryAttempted">{{ $t('common.shipment_status.delivery_attempted') }}</div>-->
    <shipvagoo-status-tag v-if="statusTag"
                          :color="statusTag.color"
                          :bg-color="statusTag.bgColor"
                          :text="statusTag.text"
    />
  </div>
</template>

<script setup lang="ts">
import {computed} from 'vue';
import * as _ from 'lodash-es';
import {EShipmentStatus, IShipment} from '../../model/shipment.model';

interface Props {
  shipment: IShipment;
  isStopShippingMonitoring: boolean;
  isMarkedAsDelivered: boolean;
}

const props = defineProps<Props>();

const latestEventByCreatedAt = computed(() => {
  if (!props.shipment.shipment_events || props.shipment.shipment_events.length === 0) {
    return null;
  }

  return _.maxBy(props.shipment.shipment_events, (event) => {
    return new Date(event.event_created_at).getTime();
  });
});

const status = computed(() => {
  return latestEventByCreatedAt.value?.state ?? props.shipment.status;
});

const isError = computed(() => {
  return status.value === EShipmentStatus.ERROR;
});

const isRegistered = computed(() => {
  return status.value === EShipmentStatus.REGISTERED;
});

const isDraft = computed(() => {
  return status.value === EShipmentStatus.DRAFT;
});

const isInTransit = computed(() => {
  return status.value === EShipmentStatus.IN_TRANSIT;
});

const isUnknown = computed(() => {
  return status.value === EShipmentStatus.UNKNOWN;
});

const isFailedToRegister = computed(() => {
  return status.value === EShipmentStatus.FAILED_TO_REGISTER
});
const isDeliveryAttempted = computed(() => {
  return status.value === EShipmentStatus.DELIVERY_ATTEMPTED
});

const isDelivered = computed(() => {
  return status.value === EShipmentStatus.DELIVERED;
});

const statusTag = computed(() => {
  let response = null;
  if (isRegistered.value) {
    response = {
      text: 'Registered',
      color: '#2384FF',
      bgColor: '#D9EAFF'
    }
  } else if (isInTransit.value) {
    response = {
      text: 'In transit',
      color: '#735C00',
      bgColor: '#FEECA1'
    }
  } else if (isDeliveryAttempted.value) {
    response = {
      text: 'Delivery attempted',
      color: '#735C00',
      bgColor: '#FEECA1'
    }
  } else if (isDelivered.value) {
    response = {
      text: 'Delivered',
      color: '#0C9247',
      bgColor: '#BEF3D7'
    }
  } else if (isError.value) {
    response = {
      text: 'Error',
      color: '#BE4748',
      bgColor: '#FFD4E0'
    }
  } else if (isUnknown.value) {
    response = {
      text: 'Unknown',
      color: '#BE4748',
      bgColor: '#FFD4E0'
    }
  } else if (isDraft.value) {
    response = {
      text: 'Draft',
      color: '#702FA0',
      bgColor: '#F2E1FF'
    }
  } else if (isFailedToRegister.value) {
    response = {
      text: 'Failed to register',
      color: '#702FA0',
      bgColor: '#F2E1FF'
    }
  }  /*else {
    response = {
      text: convertStatusToMessage(status.value),
      color: '#565656',
      bgColor: '#cccccc'
    }
  }*/
  return response;
})

/*function convertStatusToMessage(status: string) {
  let formattedStatus = status.toLowerCase().replace(/_/g, ' ');
  formattedStatus = formattedStatus.charAt(0).toUpperCase() + formattedStatus.slice(1).toLowerCase();
  return formattedStatus;
}*/
</script>

<style scoped lang="scss">
.ui_kit-shipvagoo_booking_status {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;

  &-indicator {
    width: 12px;
    height: 12px;
    border-radius: 6px;
  }

  &-indicator--is_registered {
    background-color: #62A6FC;
  }

  &-indicator--in_transit {
    background-color: #FECB00;
  }

  &-indicator--delivery_attempted {
    background-color: #FECB00;
  }

  &-indicator--error {
    background-color: #E44257;
  }

  &-indicator--unknown {
    background-color: #E44257;
  }

  &-indicator--delivered {
    background-color: #1DCA71;
  }

  &-indicator--draft {
    background-color: #7030A0;
  }

  &-indicator--failed_to_register {
    background-color: #7030A0;
  }
}
</style>
