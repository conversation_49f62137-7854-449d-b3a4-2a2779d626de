<template>
  <div>
    <button ref="chatBtn" class="chat-btn" @click="OpenChatBot">
      <img
        :src="baseOrigin + '/src/assets/icons/comment.png'"
        class="material-icon"
      />
    </button>

    <div
      ref="chatPopup"
      class="chat-popup"
      style=" z-index: 999;display: none; height: unset !important"
    >
      <div class="chat-header">
        <!-- <div class="chatbot-img">
          <img src="${botLogoPath}" alt="Chat Bot image" class="bot-img" />
        </div> -->
        <h3 class="bot-title">Zendesk</h3>
        <button
          ref="expandWindow"
          class="expand-chat-window"
          @click="startExpandWindow()"
        >
          <ion-icon name="close-circle-outline"></ion-icon>
          <!-- <img src="../assets/icons/open_fullscreen.png" class="material-icon" /> -->
        </button>
      </div>
      <form @submit.prevent="chatSubmit">
        <shipvagoo-input
          v-model="email"
          label="Email"
          type="text"
          style="background: #1c1d29"
          readonly
        >
        </shipvagoo-input>
        <shipvagoo-input
          v-model="subject"
          label="Subject"
          type="text"
          style="background: #1c1d29"
        >
        </shipvagoo-input>

        <shipvagoo-input
          v-model="description"
          label="Description"
          type="textarea"
          style="background: #1c1d29"
        >
        </shipvagoo-input>
        <shipvagoo-button
          class="login_page-login_button"
          type="submit"
          label="Submit"
        ></shipvagoo-button>
      </form>
    </div>
  </div>
</template>




<script  setup lang="ts">
  import "../assets/demo-bot/chatbot-ui.css";
  import { onMounted, onBeforeMount, ref } from "vue";
  import axios from "axios";
  import {useStore} from "vuex";
  import {
    loadingService,
    loggingService,
    notificationService,
  } from "../services/_singletons";
  import {IRootState} from "~/model/store.model";

  const isChatBotOpen = ref<boolean>(false);
  const chatPopup =  ref<HTMLDivElement | null>(null);
  const chatBtn = ref<HTMLButtonElement | null>(null);
  // const chatInput = <Element | null>document.querySelector(".chat-input");
  const expandWindow = ref<HTMLButtonElement | null>(null);
  const root = document.documentElement;
  const store = useStore<IRootState>();

  const email = ref<string>('');
  const subject = ref<string>(  "");
  const description = ref<string>("");
  const baseOrigin = window.location.origin;
  // const chatBtn = document.querySelector('.chat-btn');
  // const chatHeader = document.querySelector('.chat-header');
  // const chatPopup = document.querySelector('.chat-popup');
  // const chatArea = document.querySelector('.chat-area');
  // if (chatPopup) chatPopup.style.display = 'none';
  // const expandWindow = document.querySelector('.expand-chat-window');
  // const host = '';

  // const chatPopupComputed = computed(() => chatPopup.value);
  // const expandWindowComputed = computed(() => expandWindow.value);
  // console.log(chatPopupComputed,expandWindowComputed);
  const mobileView = async () => {
    try {
      // $('.chat-popup').width($(window).width());
      if (chatPopup.value && chatPopup.value.style.display === "none") {
        chatPopup.value.style.display = "flex";
        // chatInput.focus();
        chatBtn.value ? chatBtn.value.style.display = "none" : '';
        chatPopup.value.style.bottom = "0";
        chatPopup.value.style.right = "0";
        // chatPopup.style.transition = "none"
        expandWindow.value ? expandWindow.value.innerHTML = `<img src = "${baseOrigin}/src/assets/icons/close.png" class = "material-icon" >` : '';
      } else {
        chatPopup.value ? chatPopup.value.style.display = "none" : null;
      }
    } catch (e) {
      notificationService.showError("Error loading shipment statistics");
      loggingService.error("Error loading shipment statistics", e);
    } finally {
      loadingService.stopLoading("main-loader:shipment-statistics");
    }
  };

  const detectMob = async () => {
    return window.innerHeight <= 800 && window.innerWidth <= 600;
  };

  const OpenChatBot = () => {
    email.value = store.state.userStore.email
    isChatBotOpen.value === false
      ? (isChatBotOpen.value = true)
      : (isChatBotOpen.value = false);
    const mobileDevice = !detectMob();
    if (chatPopup.value && chatPopup.value.style.display === "none" && mobileDevice) {
      chatPopup.value.style.display = "flex";
      // chatInput.value.focus();
      chatBtn.value!.innerHTML = `<img src = "${baseOrigin}/src/assets/icons/close.png" class = "material-icon" >`;
    } else if (mobileDevice) {
      chatPopup.value ? chatPopup.value.style.display = "none" : null;
      chatBtn.value ? chatBtn.value.innerHTML = `<img src = "${baseOrigin}/src/assets/icons/comment.png" class = "material-icon" >` : '';
    } else {
      mobileView();
    }
  };

  const chatSubmit = async () => {
    const data = JSON.stringify({
      ticket: { subject, comment: { body: description } },
    });
    axios
      .post(
        "https://delaneyandknappinc.zendesk.com/api/v2/tickets.json",
        data,
        {
          headers: {
            "Access-Control-Allow-Method": "*",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Request-Headers": "*",
            "Content-Type": "application/json",
          },
        },
      )

      .then((res) => {
        alert("success");
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const startExpandWindow = () => {
    // console.log(expandWindow.innerHTML)
    if (
      expandWindow.value && expandWindow.value.innerHTML ===
        `<img src="${baseOrigin}/src/assets/icons/close_fullscreen.png" class="material-icon">`
    ) {
      expandWindow.value.innerHTML = `<img src = "${baseOrigin}/src/assets/icons/close_fullscreen.png" class = 'material-icon'>`;
      root.style.setProperty("--chat-window-height", `${80  }%`);
      root.style.setProperty("--chat-window-total-width", `${85  }%`);
    // chatHeader.style.width = '100%';
    } else if (
      expandWindow.value && expandWindow.value.innerHTML ===
        `<img src="${baseOrigin}/src/assets/icons/close.png" class="material-icon">`
    ) {
      chatPopup.value ? chatPopup.value.style.display = "none" : '';
      chatBtn.value ? chatBtn.value.style.display = "block" : '';
    } else {
      expandWindow.value ? expandWindow.value.innerHTML = `<img src = "${baseOrigin}/src/assets/icons/close_fullscreen.png" class = "material-icon" >` : '';
      root.style.setProperty("--chat-window-height", `${500  }px`);
      root.style.setProperty("--chat-window-total-width", `${380  }px`);
    }
  };

  // const scrollToBottomOfResults = async () => {
  //   chatArea.scrollTop = chatArea.scrollHeight;
  // };

  // // ------------------------------------ Set bot response -------------------------------------
  // const setBotResponse = async (val) => {
  //   setTimeout(function () {
  //     if (val.length < 1) {
  //       //if there is no response from Rasa
  //       // msg = 'I couldn\'t get that. Let\' try something else!';
  //       msg = inactiveMessage;

  //       var BotResponse = `<div class='bot-msg'><img class='bot-img' src ='${botLogoPath}' /><span class='msg'> ${msg} </span></div>`;
  //       $(BotResponse).appendTo('.chat-area').hide().fadeIn(1000);
  //       scrollToBottomOfResults();
  //       chatInput.focus();
  //     } else {
  //       //if we get response from Rasa
  //       for (let i = 0; i < val.length; i++) {
  //         //check if there is text message
  //         if (val[i].hasOwnProperty('text')) {
  //           var BotResponse = `<div class='bot-msg'><img class='bot-img' src ='${botLogoPath}' /><span class='msg'>${val[i].text}</span></div>`;
  //           $(BotResponse).appendTo('.chat-area').hide().fadeIn(1000);
  //         }

  //         //check if there is image
  //         if (val[i].hasOwnProperty('image')) {
  //           var BotResponse =
  //             "<div class='bot-msg'>" + "<img class='bot-img' src ='${botLogoPath}' />";
  //           '<img class="msg-image" src="' + val[i].image + '">' + '</div>';
  //           $(BotResponse).appendTo('.chat-area').hide().fadeIn(1000);
  //         }
  //       }
  //       scrollToBottomOfResults();
  //       chatInput.focus();
  //     }
  //   }, 500);
  // };

  // const  createChatBot = async (hostURL, botLogo, title, welcomeMessage, inactiveMsg, theme = 'blue')=> {
  //   host = hostURL;
  //   botLogoPath = botLogo;
  //   inactiveMessage = inactiveMsg;
  //   init(botLogoPath);
  //   const msg = document.querySelector('.msg');
  //   msg.innerText = welcomeMessage;

  //   const botTitle = document.querySelector('.bot-title');
  //   botTitle.innerText = title;

  //   // chatbotTheme(theme);
  // };
  onBeforeMount(async () => {
  // observer.disconnect();
  // const recaptchaScript = document.createElement('script');
  // recaptchaScript.setAttribute('src', '../assets/demo-bot/chatbot-ui.js');
  // document.head.appendChild(recaptchaScript);
  });
  onMounted(() => {
    // observer.observe(target);
    // Logs: `Headline`
    // console.log(chatPopupComputed);
    console.log(chatPopup.value);
  });
</script>

