<template>
  <div class="shipvagoo-title">
    <div>
      <div class="title">{{ title }}</div>
      <div v-if="subtitle" class="sub-title">{{ subtitle }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
  interface Props {
    title: string;
    subtitle?: string | null;
  }

  withDefaults(defineProps<Props>(), {
    subtitle: null,
  });
</script>

<style scoped lang="scss">
  .shipvagoo-title {
    display: flex;
    align-items: center;
    height: 70px;
    transition: height 150ms ease-in-out;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      object-position: right;
      pointer-events: none;

      @media (max-width: 1281px) {
        object-fit: cover;
      }
    }

    @media (max-width: 1024px) {
      height: 57px;
    }

    .title {
      padding: 0;
      font-family: Helvetica, sans-serif;
      font-size: 24px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      color: #212121;
      transition: padding 150ms ease-in-out;

      &[role='h1'] {
        margin: 0;
        font-size: 28px;
        font-weight: 600;
        line-height: initial;

        @media (max-width: 1024px) {
          font-size: 25px;
        }
      }
    }

    .sub-title {
      font-family: Helvetica, sans-serif;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%;
      color: #7d8398;
    }
  }
</style>
