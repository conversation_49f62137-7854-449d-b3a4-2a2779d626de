<template>
  <div id="app">
    <div class="chart-information">
      <ul>
        <li><span class="radius shipments-circle" />{{ $t('common.shipments') }}</li>
        <li>
          <span class="radius imported-orders-circle" />
          {{ $t('dashboard_page.chart_legend.imported_orders') }}
        </li>
        <li>
          <span class="radius dispatched-orders-circle" />
          {{ $t('dashboard_page.chart_legend.dispatched_orders') }}
        </li>
        <li>
          <span class="radius orders-picked-up-circle" />
          {{ $t('dashboard_page.chart_legend.orders_picked_up') }}
        </li>
      </ul>
    </div>
    <shipvagoo-select
      v-model="selectedRange"
      class="range-select"
      :options="selectRangeOptions"
      :outlined="false"
    />
    <BarChart v-bind="barChartProps" />
  </div>
</template>

<script setup lang="ts">
  import { Chart, ChartData, ChartOptions, registerables } from 'chart.js';
  import { BarChart, useBarChart } from 'vue-chart-3';
  import { computed, watch } from 'vue';
  import dayjs, { Dayjs } from 'dayjs';
  import { useI18n } from 'vue-i18n';
  import { loadingService, loggingService, notificationService } from '../services/_singletons';
  import { IDashboardOverview } from '../model/dashboard.model';
  import { useStore } from '../store';
  import { IOption } from '../model/common.model';

  const { t } = useI18n();

  Chart.register(...registerables);

  const $store = useStore();
  const ranges = [7, 14, 21, 28];

  const selectRangeOptions = computed(() => {
    return ranges.map((range) => {
      return {
        value: range,
        label: t('common.last_count_days', { count: range }),
      };
    });
  });

  const selectedRange = $ref<IOption<string, number>>(selectRangeOptions.value[0]);
  const lastSelectedRange = $ref<IOption<string, number>>(selectRangeOptions.value[0]);
  const dateFrom = computed(() => {
    return dayjs().subtract(selectedRange.value - 1, 'day');
  });
  const shipments = computed<IDashboardOverview['shipments']>(
    () => $store.state.dashboardStore.overviewInfo.shipments,
  );
  const importedOrders = computed<IDashboardOverview['imported_orders']>(
    () => $store.state.dashboardStore.overviewInfo.imported_orders,
  );

  const getDaysBetweenDates = (startDate: Dayjs, endDate: Dayjs) => {
    const days: Dayjs[] = [];

    for (let i = 0; i <= endDate.diff(startDate, 'day'); i += 1) {
      days.push(startDate.add(i, 'day'));
    }

    return days;
  };

  const daysBetweenDates = computed((): Dayjs[] => {
    return getDaysBetweenDates(dateFrom.value, dayjs());
  });

  const dayNamesOfDates = computed((): string[] => {
    return daysBetweenDates.value.map((day) => day.format('MMM DD'));
  });

  const shipmentData = computed((): number[] => {
    return daysBetweenDates.value.map((day) => {
      const date = shipments.value.find(
        (shipment) => shipment.theDate === day.format('YYYY-MM-DD'),
      );

      return date ? date.shipmentCount : 0;
    });
  });

  const importedOrdersData = computed((): number[] => {
    return daysBetweenDates.value.map((day) => {
      const date = importedOrders.value.find((order) => order.theDate === day.format('YYYY-MM-DD'));

      return date ? date.orderCount : 0;
    });
  });

  const options = computed(
    (): ChartOptions<'bar'> => ({
      scales: {
        y: {
          beginAtZero: true,
          grace: 20,
        },
      },
      plugins: {
        legend: {
          display: false,
        },
      },
    }),
  );

  const chartData = computed(
    (): ChartData<'bar'> => ({
      labels: [...dayNamesOfDates.value],
      datasets: [
        {
          data: [...shipmentData.value],
          backgroundColor: '#389b99',
          borderRadius: 3,
          barPercentage: 1,
          categoryPercentage: 0.5,
        },
        {
          data: [...importedOrdersData.value],
          backgroundColor: '#e1e1e1',
          borderRadius: 3,
          barPercentage: 1,
          categoryPercentage: 0.5,
        },
        {
          data: [],
          backgroundColor: '#ffbc01',
          borderRadius: 3,
          barPercentage: 1,
          categoryPercentage: 0.5,
        },
        {
          data: [],
          backgroundColor: '#2929c8',
          borderRadius: 3,
          barPercentage: 1,
          categoryPercentage: 0.5,
        },
      ],
    }),
  );

  const { barChartProps } = useBarChart({
    chartData,
    options,
  });

  const onRangeChanged = async (newRange: IOption<string, number>) => {
    if (newRange.value === lastSelectedRange.value) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:bar-chart');
      await $store.dispatch('dashboardStore/getDashboardOverview', selectedRange.value);
    } catch (e) {
      notificationService.showError('Error loading dashboard overview');
      loggingService.error('Error loading dashboard overview', e);
    } finally {
      loadingService.stopLoading('main-loader:bar-chart');
    }
  };

  watch(selectedRange, onRangeChanged, { immediate: true });
</script>

<style scoped lang="scss">
  .range-select {
    width: max-content;
    margin: 0 auto;
  }

  .chart-information {
    margin-bottom: 30px;

    .shipments-circle {
      background-color: #389b99;
    }

    .imported-orders-circle {
      background-color: #e1e1e1;
    }

    .dispatched-orders-circle {
      background-color: #ffbc01;
    }

    .orders-picked-up-circle {
      background-color: #2929c8;
    }

    ul {
      display: flex;
      gap: 50px;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 0;

      @media (max-width: 1024px) {
        flex-direction: column;
        gap: 10px;
      }

      li {
        display: flex;
        align-items: center;
        font-size: 12px;

        .radius {
          display: block;
          width: 12px;
          height: 12px;
          margin-right: 6px;
          border-radius: 50%;
        }
      }
    }
  }
</style>
