<template>
  <shipvagoo-modal :title="`SKU: ${product.sku || product.title || ''}`">
    <shipvagoo-card-section style="display: grid; grid-template-columns: 1fr; gap: 20px">
      <shipvagoo-input v-model="form.name" :label="$t('common.name')" />

      <shipvagoo-input-group responsive>
        <shipvagoo-input v-model="form.variantCode" :label="$t('common.variant_code')" />
        <shipvagoo-input v-model="form.barcode" :label="$t('common.barcode')" />
      </shipvagoo-input-group>

      <shipvagoo-input-group responsive>
        <shipvagoo-input v-model="form.bin" label="Bin" />
        <shipvagoo-input v-model="form.image" :label="$t('common.image')" />
      </shipvagoo-input-group>

      <shipvagoo-input-group responsive>
        <shipvagoo-input v-model="form.selling_price" :label="$t('product_detail.selling_price')" />
        <div />
      </shipvagoo-input-group>

      <shipvagoo-checkbox v-model="form.isVirtualItem" size="sm" :label="$t('common.virtual')" />

      <div style="padding-top: 5px; font-size: 16px; line-height: 19px">
        {{ $t('product_detail.customs_information') }}
      </div>
      <shipvagoo-input-group responsive>
        <shipvagoo-select
          v-model="form.country"
          :label="$t('common.country')"
          :options="countries"
          option-label="default_name"
          option-value="country_code"
        >
          <!-- :error="$v.one.senderCountry.$error"
          :error-message="$v.one.senderCountry.$errors?.[0]?.$message" -->
          <template #prepend>
            <shipvagoo-country-icon v-if="form.country" :country="form.country" />
          </template>
          <template #option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section side>
                <shipvagoo-country-icon v-if="scope.opt" :country="scope.opt" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ scope.opt.default_name }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </shipvagoo-select>
        <shipvagoo-input
          v-model="form.content"
          :label="`${$t('common.content')} (${$t('common.in_english')})`"
        />
      </shipvagoo-input-group>
      <shipvagoo-input-group>
        <shipvagoo-input v-model="form.commodityCode" :label="$t('common.commodity_code')" />
        <shipvagoo-input v-model="form.weight" :label="$t('common.weight')" />
      </shipvagoo-input-group>
    </shipvagoo-card-section>
    <shipvagoo-separator />
    <shipvagoo-card-actions>
      <shipvagoo-button
        :label="$t('common.save')"
        class="save-button"
        min-width="120px"
        @click="onClickSave"
      />
    </shipvagoo-card-actions>
  </shipvagoo-modal>
</template>

<script setup lang="ts">
  import useVuelidate, { ValidationArgs } from '@vuelidate/core';
  import { helpers, required } from '@vuelidate/validators';
  import { computed, onBeforeMount, ref } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { ICountry } from '../../model/country.model';
  import { IProduct } from '../../model/product.model';
  import {
    loadingService,
    loggingService,
    notificationService,
    productService,
  } from '../../services/_singletons';
  import { useStore } from '../../store';

  const $store = useStore();
  const { t } = useI18n();

  interface Props {
    product: IProduct;
  }

  const props = defineProps<Props>();
  const emit = defineEmits(['close', 'refresh']);

  const defaultCountryValue = {
    country_code: -1,
    default_name: t('common.select_country'),
    iso: 'XX',
  };

  const countries = computed(() => [defaultCountryValue, ...$store.state.countryStore.countries]);

  interface IProductsForm {
    name: string | null;
    variantCode: string | null;
    barcode: string | null;
    bin: string | null;
    image: string | null;
    isVirtualItem: boolean;
    country: ICountry | null;
    commodityCode: string | null;
    weight: string | null;
    content: string | null;
    selling_price: string | null;
  }

  const form = ref<IProductsForm>({
    name: null,
    variantCode: null,
    barcode: null,
    bin: null,
    image: null,
    isVirtualItem: false,
    country: null,
    commodityCode: null,
    weight: null,
    content: null,
    selling_price: null,
  });

  const $v = useVuelidate<IProductsForm, ValidationArgs<IProductsForm>>(
    {
      name: {
        required: helpers.withMessage('This field is required', required),
      },
      variantCode: {},
      barcode: {},
      bin: {},
      image: {},
      isVirtualItem: {},
      country: {},
      commodityCode: {},
      weight: {},
      content: {},
      selling_price: {},
    },
    form,
  );

  const onClickSave = async () => {
    await $v.value.$validate();

    if ($v.value.$error || !form.value.name) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:product-save');
      await productService.updateProduct(props.product.product_code, {
        title: form.value.name,
        variant_code: form.value.variantCode,
        barcode: form.value.barcode,
        bin: form.value.bin,
        image: form.value.image,
        is_virtual: form.value.isVirtualItem ? 1 : 0,
        country_code:
          form.value.country?.country_code !== -1 && form.value.country?.country_code
            ? form.value.country.country_code
            : null,
        commodity_code: form.value.commodityCode,
        weight: form.value.weight ? parseFloat(form.value.weight) : 0,
        content: form.value.content,
        selling_price: form.value.selling_price ? parseFloat(form.value.selling_price) : 0,
      });
      notificationService.showSuccess('Product updated successfully');
      emit('close');
      emit('refresh');
    } catch (e) {
      notificationService.showError('Error saving product');
      loggingService.error('Error saving product', e);
    } finally {
      loadingService.stopLoading('main-loader:product-save');
    }
  };

  onBeforeMount(() => {
    form.value.name = props.product.title;
    form.value.variantCode = props.product.variant_code;
    form.value.barcode = props.product.barcode;
    form.value.bin = props.product.bin;
    form.value.image = props.product.image_url;
    form.value.isVirtualItem = Boolean(props.product.is_virtual);
    if (props.product.country_code) {
      const country =
        $store.state.countryStore.countriesIndexedByCountryCode[props.product.country_code];
      form.value.country = country;
    } else {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      form.value.country = defaultCountryValue;
    }
    form.value.commodityCode = props.product.commodity_code;
    form.value.weight = props.product.weight;
    form.value.selling_price = props.product.selling_price;
  });
</script>
