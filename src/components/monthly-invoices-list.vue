<template>
  <div class="invoices_page">
    <shipvagoo-placeholder v-if="!rows.length" text="No monthly invoices are available">
    </shipvagoo-placeholder>
    <div class="invoices_page-table" v-if="rows.length">
      <shipvagoo-table-v1
        v-model:pagination="pagination"
        :rows="rows"
        :columns="columns"
        :total="pagination.rowsNumber"
        :loading="loadingService.loadings.has('main-loader:loading-invoices')"
        @request="getMonthlyInvoices($event.pagination)"
        class="monthly-invoices-page-table"
      >
        <!--        <template #body-cell-country="props">
                  <q-td :props="props">
                    <div v-if="props" style="display: flex; gap: 10px; justify-content: flex-start">
                      <img
                        decoding="async"
                        :src="`/assets/img/${props.row.recipient?.country?.toLowerCase?.() ?? ''}.svg`"
                        style="width: 20px; height: 20px; border-radius: 50%; object-fit: cover"
                      />
                      {{ props.row.recipient.city }}
                    </div>
                  </q-td>
                </template>-->
        <template #body-cell-created_at="props">
          <q-td :props="props">
            <span class="text-no-wrap">{{
              dayjs(String(props.row.created_at)).format('DD/MM/YYYY')
            }}</span
            ><br />
            <span class="text-no-wrap">{{
              dayjs(String(props.row.created_at)).format('HH:mm')
            }}</span>
          </q-td> </template
        >props.row.created_at
        <template #body-cell-month="props">
          <q-td :props="props">
            {{ dayjs(props.row.created_at).subtract(1, 'month').format('MMMM') }}
          </q-td>
        </template>
        <template #body-cell-year="props">
          <q-td :props="props">
            {{ dayjs(props.row.created_at).subtract(1, 'month').format('YYYY') }}
          </q-td>
        </template>

        <template #body-cell-more="props">
          <q-td :props="props" class="more">
            <q-btn
              dense
              flat
              round
              style="color: #1f2124"
              class="more-action"
              field="edit"
              icon="more_horiz"
            >
              <shipvagoo-menu>
                <shipvagoo-menu-item clickable @click="onClickDownloadInvoice(null, props.row)">
                  Download
                </shipvagoo-menu-item>
                <shipvagoo-menu-item clickable @click="onClickViewSpecifications(null, props.row)">
                  View specifications
                </shipvagoo-menu-item>
              </shipvagoo-menu>
            </q-btn>
          </q-td>
        </template>
      </shipvagoo-table-v1>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, onBeforeMount, Ref, ref, watch } from 'vue';
  import { QTableProps } from 'quasar';
  import dayjs from 'dayjs';
  import * as _ from 'lodash-es';
  import { useI18n } from 'vue-i18n';
  import {
    invoiceService,
    loadingService,
    loggingService,
    notificationService,
  } from '../services/_singletons';
  import { Pagination } from '../model/common.model';
  import HelperService from '../services/helper.service';
  import { IInvoice } from '../model/invoice.model';
  import { IRequestParam } from '../model/http.model';
  import { PDFDocument } from 'pdf-lib';
  import WindowService from '../services/window.service';
  import PriceService from '../services/price.service';
  import { ITransaction } from '../model/transactions.model';

  const props = defineProps({
    searchParam: {
      type: String,
      default: '',
    },
  });
  watch(
    () => props.searchParam,
    (currentVal) => {
      search.value = currentVal;
      getMonthlyInvoices(null);
    },
  );
  const { t } = useI18n();

  const search: Ref<string> = ref(props.searchParam);
  const columns = computed<QTableProps['columns']>(() => [
    {
      name: 'created_at',
      align: 'left',
      label: 'Date',
      field: (row: IInvoice) => dayjs(String(row.created_at)).format('DD/MM/YYYY HH:mm:ss'),
      sortable: false,
    },
    {
      name: 'id',
      align: 'left',
      label: 'Invoice number',
      field: (row: IInvoice) => HelperService.formatId(row.id),
      sortable: false,
    },
    {
      name: 'month',
      align: 'left',
      label: t('common.month'),
      field: 'month',
      sortable: false,
    },
    {
      name: 'year',
      align: 'left',
      label: t('common.year'),
      field: 'year',
      sortable: false,
    },
    {
      name: 'shipments',
      align: 'left',
      label: 'Shipments',
      field: (row: ITransaction) => {
        return row.meta?.total_shippings;
      },
      sortable: false,
    },
    {
      name: 'amount',
      align: 'right',
      label: 'Total amount',
      field: (row: ITransaction) => PriceService.formatPrice(Number(row.amount), 'de-DE'),
      sortable: false,
    },
    { name: 'more', label: 'Action', field: 'more', sortable: false },
  ]);

  const rows = ref<IInvoice[]>([]);
  const pagination: Ref<Pagination> = ref({
    page: 1,
    rowsPerPage: 10,
    rowsNumber: 0,
    descending: true,
  });

  const getMonthlyInvoices = async (tablePagination: Pagination | null) => {
    try {
      loadingService.startLoading('main-loader:loading-invoices');
      const page = tablePagination?.page ?? 1;
      const limit = tablePagination?.rowsPerPage ?? 10;

      const params: IRequestParam[] = [];

      if (search.value && search.value !== '') {
        params.push({ key: 'conditions[invoice_code:like]', value: search.value });
      }

      if (tablePagination?.sortBy) {
        const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
        params.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
      } else {
        params.push({ key: 'sorting[created_at]', value: 'desc' });
      }

      const invoices = await invoiceService.getMonthlyInvoices(page, limit, params);

      rows.value = invoices.entities;
      pagination.value.rowsNumber = invoices.total;
      pagination.value.page = page;
      pagination.value.rowsPerPage = limit;
      pagination.value.descending = tablePagination?.descending;
      pagination.value.sortBy = tablePagination?.sortBy;
    } catch (e) {
      loggingService.error('Error loading invoices', e);
      notificationService.showError('Error loading invoices');
    } finally {
      loadingService.stopLoading('main-loader:loading-invoices');
    }
  };

  const onClickViewSpecifications = async (event: Event | null, row: IInvoice) => {
    const newTab = window.open(
      `/monthly-invoice/${row.id}/specifications?created_at=${row.created_at}&total=${row.amount}`,
      '_blank',
    );
    if (newTab) {
      newTab.focus();
    }
  };
  const onClickDownloadInvoice = async (event: Event | null, row: IInvoice) => {
    if (event && HelperService.containsIgnoredRowElements(event)) {
      return;
    }
    try {
      loadingService.startLoading('main-loader:print-sales-invoice-receipt');
      const response = await invoiceService.downloadMonthlyInvoice(row.id);
      const filePaths = [response.file, response.file1];

      await (async () => {
        const mergedPdf = await PDFDocument.create();
        for (const filePath of filePaths) {
          if (filePath != undefined) {
            const pdfDoc = await PDFDocument.load(filePath as string);
            const pages = await mergedPdf.copyPages(pdfDoc, pdfDoc.getPageIndices());
            pages.forEach((page) => mergedPdf.addPage(page));
          }
        }
        const url = URL.createObjectURL(
          await HelperService.toBlob(await mergedPdf.saveAsBase64({ dataUri: true })),
        );
        const windowRef = WindowService.openWindow('Monthly Invoice Preview');
        windowRef.location = url;
      })();
    } catch (e) {
      loggingService.error('Error while printing receipt', e);
      notificationService.showError('Error while printing receipt');
    } finally {
      loadingService.stopLoading('main-loader:print-sales-invoice-receipt');
    }
  };

  onBeforeMount(() => {
    getMonthlyInvoices(null);
  });
</script>

<style>
  .monthly-invoices-page-table th:nth-child(-n + 5),
  .monthly-invoices-page-table td:nth-child(-n + 5) {
    width: 15%;
  }

  .monthly-invoices-page-table th:nth-child(6),
  .monthly-invoices-page-table td:nth-child(6) {
    width: 8%;
  }

  .monthly-invoices-page-table th:nth-child(7),
  .monthly-invoices-page-table td:nth-child(7) {
    width: 17%;
  }
</style>
