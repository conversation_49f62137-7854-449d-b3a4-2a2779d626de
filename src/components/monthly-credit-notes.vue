<template>
  <div class="credit_notes_page">
    <div class="credit_notes_page-actions">
      <div class="big_search-rows_per_page-group row">
        <div class="col-12">
          <big-search
            v-model="search" class="booked_page-actions-big_search"/>
        </div>
      </div>
    </div>
    <div class="credit_notes_page-table">
      <shipvagoo-table
        class="monthly-credit-notes-table"
        v-model:selected="selected"
        v-model:pagination="pagination"
        :rows="rows"
        :columns="columns"
        :total="pagination.rowsNumber"
        :loading="loadingService.loadings.has('main-loader:loading-credit-notes')"
      >

      </shipvagoo-table>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {Ref, ref} from 'vue';
  import {QTableProps} from 'quasar';
  import dayjs from 'dayjs';
  import {
    loadingService
  } from '../services/_singletons';
  import {useI18n} from 'vue-i18n';

  import {Pagination} from '../model/common.model';
  import {ICreditNote} from '../model/credit-note.model';
  import HelperService from '../services/helper.service';

  const {t} = useI18n();
  const search: Ref<string> = ref('');

  const columns: QTableProps['columns'] = [
    {
      name: 'created_at',
      align: 'left',
      label: t('common.created_at'),
      field: (row: ICreditNote) => dayjs(String(row.created_at)).format('DD/MM/YYYY HH:mm:ss'),
      sortable: true,
    },
    {
      name: 'id',
      align: 'left',
      label: t('credit_notes_page.credit_note_no'),
      field: (row: ICreditNote) => HelperService.formatId(row.id),
      sortable: true,
    },
    {
      name: 'month',
      align: 'left',
      label: 'Month',
      field: '',
      sortable: false,
    }, {
      name: 'year',
      align: 'left',
      label: 'Year',
      field: '',
      sortable: false,
    },

    {
      name: 'amount',
      align: 'right',
      label: 'Total Amount incl. VAT',
      field: '',
      sortable: false,
    },
    {name: 'more', align: 'right', label: t('common.more'), field: 'more', sortable: false},
  ];

  const rows = ref<ICreditNote[]>([]);
  const pagination: Ref<Pagination> = ref({
    page: 1,
    rowsPerPage: 10,
    rowsNumber: 0,
    descending: true,
  });
  const selected = ref([]);
</script>

<style scoped lang="scss">
  .credit_notes_page {
    margin-top: 15px;

    &-actions {
      display: flex;
      flex-direction: row;

      & > *:not(:first-child) {
        margin-left: 10px;

        @media (max-width: 600px) {
          margin-left: unset;
        }
      }

      .big_search-rows_per_page-group {
        display: flex;
        flex: 1;
        flex-direction: row;

        & > *:not(:first-child) {
          margin-left: 10px;
        }

        .rows_per_page {
          display: none;

          @media (max-width: $tablet) {
            display: block;
          }
        }
      }

      &-big_search {
        flex: 1;
      }

      @media (max-width: 600px) {
        flex-direction: column;

        &-big_search {
          margin-bottom: 10px;
        }
      }
    }
  }
</style>
<style>
  .monthly-credit-notes-table th:nth-child(1),
  .monthly-credit-notes-table td:nth-child(1) {
    width: 17%;
  }

  .monthly-credit-notes-table th:nth-child(2) {
    width: 17%;
  }

  .monthly-credit-notes-table td:nth-child(2) {
    width: 17%;
    padding-left: 38px;
  }

  .monthly-credit-notes-table th:nth-child(3),
  .monthly-credit-notes-table td:nth-child(3) {
    width: 17%;
  }

  .monthly-credit-notes-table th:nth-child(4),
  .monthly-credit-notes-table td:nth-child(4) {
    width: 24%;
  }

  .monthly-credit-notes-table th:nth-child(5),
  .monthly-credit-notes-table td:nth-child(5) {
    width: 15%;
  }

  .monthly-credit-notes-table th:nth-child(6),
  .monthly-credit-notes-table td:nth-child(6) {
    width: 10%;
    padding-right: 25px;
  }
</style>