<template>
  <div class="credit_notes_page">
    <shipvagoo-placeholder v-if="!rows.length" text="No credit notes are available">
    </shipvagoo-placeholder>
    <div class="credit_notes_page-table" v-if="rows.length">
      <shipvagoo-table-v1
        v-model:pagination="pagination"
        :rows="rows"
        :columns="columns"
        :total="pagination.rowsNumber"
        :loading="loadingService.loadings.has('main-loader:loading-invoices')"
        @request="getCreditNotes($event.pagination)"
        @row-click="onClickDownloadInvoice"
        class="credit-notes-table"
      >
        <template #body-cell-created_at="props">
          <q-td :props="props">
            <span class="text-no-wrap">{{
              dayjs(String(props.row.created_at)).format('DD/MM/YYYY')
            }}</span
            ><br />
            <span class="text-no-wrap">{{
              dayjs(String(props.row.created_at)).format('HH:mm')
            }}</span>
          </q-td>
        </template>
        <!--        <template #body-cell-country="props">
                  <q-td :props="props">
                    <div v-if="props" style="display: flex; gap: 10px; justify-content: flex-start">
                      <img
                        decoding="async"
                        :src="`/assets/img/${props.row.recipient?.country?.toLowerCase?.() ?? ''}.svg`"
                        style="width: 20px; height: 20px; border-radius: 50%; object-fit: cover"
                      />
                      {{ props.row.recipient.city }}
                    </div>
                  </q-td>
                </template>
                
                <template #body-cell-month="props">
                  <q-td :props="props">
                    {{ dayjs(props.row.created_at).format('MMMM') }}
                  </q-td>
                </template>
                <template #body-cell-year="props">
                  <q-td :props="props">
                    {{ dayjs(props.row.created_at).format('YYYY') }}
                  </q-td>
                </template>
                -->
        <template #body-cell-carrier="props">
          <q-td :props="props">
            <!--            <div
                          class="flex items-center no-wrap"
                          v-if="
                                            CarrierService.getCarrierFromShippingProductCode(
                                              $store.state.courierStore.carriers,
                                              $store.state.courierStore.carrierProducts,
                                              asInvoice(props.row).shipment?.carrier_product_code,
                                            )?.carrier_id?.toLowerCase()
                                          ">
                          <div
                            class="shipvagoo-table-carrier-logo"
                            :style="{
                                backgroundImage: 'url(' + CarrierService.getCarrierFromShippingProductCode(
                                          $store.state.courierStore.carriers,
                                          $store.state.courierStore.carrierProducts,
                                          asInvoice(props.row).shipment?.carrier_product_code,
                                        )?.icon + ')'
                              }"
                          />
                          <div class="text-no-wrap q-ml-sm shipped-carrier">
                            {{
                              CarrierService.getCarrierFromShippingProductCode(
                                $store.state.courierStore.carriers,
                                $store.state.courierStore.carrierProducts,
                                asInvoice(props.row).shipment?.carrier_product_code,
                              )?.name
                            }}
                          </div>
                        </div>-->
          </q-td>
        </template>
        <template #body-cell-service="props">
          <q-td :props="props">
            <!--            <div
                          class="flex items-center no-wrap"
                          v-if="
                                            CarrierService.getCarrierFromShippingProductCode(
                                              $store.state.courierStore.carriers,
                                              $store.state.courierStore.carrierProducts,
                                              asInvoice(props.row).shipment?.carrier_product_code,
                                            )?.carrier_id?.toLowerCase()
                                          ">
                          <div class="text-no-wrap q-ml-sm shipped-carrier">
                            {{
                              $store.state.courierStore.carrierProducts.find((item: IShippingProduct) => item.carrier_product_code == asInvoice(props.row).shipment?.carrier_product_code)?.name
                            }}
                          </div>
                        </div>-->
          </q-td>
        </template>
        <template #body-cell-more="props">
          <q-td :props="props" class="more">
            <q-btn
              dense
              flat
              round
              style="color: #1f2124"
              class="more-action"
              field="edit"
              icon="more_horiz"
            >
              <shipvagoo-menu>
                <shipvagoo-menu-item clickable @click="onClickDownloadInvoice(null, props.row)">
                  {{ $t('common.download') }}
                </shipvagoo-menu-item>
              </shipvagoo-menu>
            </q-btn>
          </q-td>
        </template>
      </shipvagoo-table-v1>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { nextTick, onBeforeMount, Ref, ref, watch } from 'vue';
  import { QTableProps } from 'quasar';
  import dayjs from 'dayjs';
  import * as _ from 'lodash-es';
  import Html2Canvas from 'html2canvas';
  import JsPDF from 'jspdf';
  //import {useI18n} from 'vue-i18n';
  import {
    creditNoteService,
    loadingService,
    loggingService,
    notificationService,
  } from '../services/_singletons';
  import { Pagination } from '../model/common.model';
  import { IRequestParam } from '../model/http.model';
  import { ICreditNote } from '../model/credit-note.model';
  import HelperService from '../services/helper.service';
  import { ITransaction } from '~/model/transactions.model';

  //const {t} = useI18n();

  const props = defineProps({
    searchParam: {
      type: String,
      default: '',
    },
  });
  const search: Ref<string> = ref('');
  watch(
    () => props.searchParam,
    (currentVal) => {
      search.value = currentVal;
      getCreditNotes(null);
    },
  );

  const columns: QTableProps['columns'] = [
    {
      name: 'created_at',
      align: 'left',
      label: 'Transaction date',
      field: 'created_at',
      sortable: false,
    },
    {
      name: 'transaction_type',
      align: 'left',
      label: 'Transaction type',
      field: (row: ICreditNote) => `Shipment ID: ${row.shipment_code}`,
      sortable: false,
    },
    {
      name: 'id',
      align: 'left',
      label: 'Invoice number',
      field: (row: ICreditNote) => HelperService.formatId(row.id),
      sortable: false,
    },
    {
      name: 'carrier',
      field: '',
      required: true,
      label: 'Carrier',
      align: 'left',
      sortable: false,
    },
    {
      name: 'service',
      field: '',
      required: true,
      label: 'Service',
      align: 'left',
      sortable: false,
    },
    {
      name: 'payment_method',
      field: (row: ITransaction) => {
        const payment_method = row.payment_method;
        if (payment_method) {
          const type = payment_method.type;
          const details = payment_method.details;
          if (type == 'card') {
            if (details) {
              return `${
                details.brand.charAt(0).toUpperCase() + details.brand.slice(1)
              } (${details.bin.substring(0, 4)} **** **** ${details.last4})`;
            } else {
              return type.charAt(0).toUpperCase() + type.slice(1);
            }
          } else {
            return type.charAt(0).toUpperCase() + type.slice(1);
          }
        } else {
          return 'Balance';
        }
      },
      required: true,
      label: 'Payment method',
      align: 'left',
      sortable: false,
    },
    {
      name: 'amount',
      align: 'right',
      label: 'Total amount',
      field: '',
      sortable: false,
    },
    {
      name: 'more',
      align: 'right',
      label: 'Action',
      field: 'more',
      sortable: false,
    },
  ];

  const rows = ref<ICreditNote[]>([]);
  const pagination: Ref<Pagination> = ref({
    page: 1,
    rowsPerPage: 10,
    rowsNumber: 0,
    descending: true,
  });
  //const selected = ref([]);
  const creditNoteToPrint = ref<ICreditNote | null>(null);

  const getCreditNotes = async (tablePagination: Pagination | null) => {
    try {
      loadingService.startLoading('main-loader:loading-credit-notes');
      const page = tablePagination?.page ?? 1;
      const limit = tablePagination?.rowsPerPage ?? 10;

      const params: IRequestParam[] = [];

      if (search.value && search.value !== '') {
        params.push({ key: 'conditions[credit_note_code:like]', value: search.value });
      }

      if (tablePagination?.sortBy) {
        const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
        params.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
      } else {
        params.push({ key: 'sorting[created_at]', value: 'desc' });
      }

      const creditNotes = await creditNoteService.getCreditNotes(page, limit, params);

      rows.value = creditNotes.entities;
      pagination.value.rowsNumber = creditNotes.total;
      pagination.value.page = page;
      pagination.value.rowsPerPage = limit;
      pagination.value.descending = tablePagination?.descending;
      pagination.value.sortBy = tablePagination?.sortBy;
    } catch (e) {
      loggingService.error('Error loading credit notes', e);
      notificationService.showError('Error loading credit notes');
    } finally {
      loadingService.stopLoading('main-loader:loading-credit-notes');
    }
  };

  const onClickDownloadInvoice = async (event: Event | null, row: ICreditNote) => {
    if (event && HelperService.containsIgnoredRowElements(event)) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:print-sales-invoice-receipt');

      creditNoteToPrint.value = row;

      await nextTick();

      const el = document.getElementById('credit-note-pdf-content');
      const logo = document.querySelector('.pdf-logo');

      if (!creditNoteToPrint.value || !el || !logo || !(logo instanceof HTMLImageElement)) {
        return;
      }

      const PAGE_WIDTH_MM: number = 210;
      const DESIGN_WIDTH: number = 2480;
      const SCALE_MULTIPLIER: number = PAGE_WIDTH_MM / DESIGN_WIDTH;

      const logoCanvas = await Html2Canvas(logo, {
        useCORS: true,
      });

      const jsPDFInstance = new JsPDF('p', 'mm', 'a4');

      await jsPDFInstance.html(el, {
        width: 210,
        html2canvas: {
          scale: SCALE_MULTIPLIER,
          useCORS: true,
        },
      });

      jsPDFInstance.addImage(
        logoCanvas.toDataURL('image/png'),
        'PNG',
        210 - 662.88 * SCALE_MULTIPLIER - 150 * SCALE_MULTIPLIER,
        150 * SCALE_MULTIPLIER,
        662.88 * SCALE_MULTIPLIER,
        106 * SCALE_MULTIPLIER,
      );

      jsPDFInstance.save(`${HelperService.formatId(creditNoteToPrint.value.id)}.pdf`);
    } catch (e) {
      loggingService.error('Error while printing receipt', e);
      notificationService.showError('Error while printing receipt');
    } finally {
      loadingService.stopLoading('main-loader:print-sales-invoice-receipt');
    }
  };

  onBeforeMount(() => {
    getCreditNotes(null);
  });
</script>

<style>
  .credit-notes-table th:nth-child(-n + 6),
  .credit-notes-table td:nth-child(-n + 6) {
    width: 14%;
  }

  .credit-notes-table th:nth-child(7),
  .credit-notes-table td:nth-child(7) {
    width: 6%;
  }

  .credit-notes-table th:nth-child(8),
  .credit-notes-table td:nth-child(8) {
    width: 10%;
  }
</style>
