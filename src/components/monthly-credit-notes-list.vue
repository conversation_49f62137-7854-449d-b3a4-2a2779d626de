<template>
  <div class="credit_notes_page">
    <div class="credit_notes_page-table">
      <shipvagoo-table-v1
        v-model:pagination="pagination"
        :rows="rows"
        :columns="columns"
        :total="pagination.rowsNumber"
        :loading="loadingService.loadings.has('main-loader:loading-credit-notes')"
        class="monthly-credit-notes-table"
      >
        <!--        <template #body-cell-country="props">
                  <q-td :props="props">
                    <div v-if="props" style="display: flex; gap: 10px; justify-content: flex-start">
                      <img
                        decoding="async"
                        :src="`/assets/img/${props.row.recipient?.country?.toLowerCase?.() ?? ''}.svg`"
                        style="width: 20px; height: 20px; border-radius: 50%; object-fit: cover"
                      />
                      {{ props.row.recipient.city }}
                    </div>
                  </q-td>
                </template>-->
        <template #body-cell-created_at="props">
          <q-td :props="props">
            <span class="text-no-wrap">{{ dayjs(String(props.row.created_at)).format('DD/MM/YYYY') }}</span><br/>
            <span class="text-no-wrap">{{ dayjs(String(props.row.created_at)).format('HH:mm') }}</span>
          </q-td>
        </template>
        <template #body-cell-month="props">
          <q-td :props="props">
            {{ dayjs(props.row.created_at).format('MMMM') }}
          </q-td>
        </template>
        <template #body-cell-year="props">
          <q-td :props="props">
            {{ dayjs(props.row.created_at).format('YYYY') }}
          </q-td>
        </template>
        <template #body-cell-more="props">
          <q-td :props="props" class="more">
            <q-btn
              dense
              flat
              round
              style="color:#1f2124;"
              class="more-action"
              field="edit"
              icon="more_horiz"
            >
              <shipvagoo-menu>
                <shipvagoo-menu-item clickable >
                  {{ $t('common.download') }}
                </shipvagoo-menu-item>
              </shipvagoo-menu>
            </q-btn>
          </q-td>
        </template>
      </shipvagoo-table-v1>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, Ref, ref, watch} from 'vue';
import {QTableProps} from 'quasar';
import dayjs from 'dayjs';
import {
  loadingService
} from '../services/_singletons';
import {useI18n} from 'vue-i18n';

import {Pagination} from '../model/common.model';
import {ICreditNote} from '../model/credit-note.model';
import HelperService from '../services/helper.service';

import PriceService from "../services/price.service";

const {t} = useI18n();
const props = defineProps({
  searchParam: {
    type: String,
    default: ''
  }
})
watch(() => props.searchParam, currentVal => {
  search.value = currentVal;
  ;
})
const search: Ref<string> = ref('');

const columns = computed<QTableProps['columns']>(() => [
  {
    name: 'created_at',
    align: 'left',
    label: 'Date',
    field: '',
    sortable: false,
  },
  {
    name: 'id',
    align: 'left',
    label: 'Invoice number',
    field: (row: ICreditNote) => HelperService.formatId(row.id),
    sortable: false,
  },
  {
    name: 'month',
    align: 'left',
    label: t('common.month'),
    field: 'month',
    sortable: false,
  },
  {
    name: 'year',
    align: 'left',
    label: t('common.year'),
    field: 'year',
    sortable: false,
  },
  {
    name: 'shipments',
    align: 'left',
    label: 'Shipments',
    field: () => {
      return '150'
    },
    sortable: false,
  },
  {
    name: 'amount',
    align: 'right',
    label: 'Total amount',
    field: (row: ICreditNote) => PriceService.formatPrice(Number(row.amount), 'de-DE'),
    sortable: false,
  },
  {name: 'more', label: 'Action', field: 'more', sortable: false},
]);

const rows = ref<ICreditNote[]>([]);
const pagination: Ref<Pagination> = ref({
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
  descending: true,
});
//const selected = ref([]);
</script>

<style>
.monthly-credit-notes-table th:nth-child(1),
.monthly-credit-notes-table td:nth-child(1) {
  width: 15%;
}

.monthly-credit-notes-table th:nth-child(2),
.monthly-credit-notes-table td:nth-child(2) {
  width: 15%;
}


.monthly-credit-notes-table th:nth-child(3),
.monthly-credit-notes-table td:nth-child(3) {
  width: 15%;
}

.monthly-credit-notes-table th:nth-child(4),
.monthly-credit-notes-table td:nth-child(4) {
  width: 15%;
}

.monthly-credit-notes-table th:nth-child(5),
.monthly-credit-notes-table td:nth-child(5) {
  width: 15%;
}

.monthly-credit-notes-table th:nth-child(6),
.monthly-credit-notes-table td:nth-child(6) {
  width: 8%;
}

.monthly-credit-notes-table th:nth-child(7),
.monthly-credit-notes-table td:nth-child(7) {
  width: 17%;
}
</style>