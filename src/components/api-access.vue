<template>
  <shipvagoo-modal size="xs" title="API" :header-separator="false">
    <shipvagoo-separator color="#DDDDDD" class="q-mx-md" />
    <shipvagoo-card-section>
      <div>
        <div class="flex items-center">
          <shipvagoo-input-v1 v-model="form.user"
                              placeholder="Enter API key"
                              class="full-width"
                              readonly label="User">
            <q-btn class="cursor-pointer" :icon="'img:' + CopyIcn" @click="onClickCopyUser" flat
                   style="background-color: transparent;" />
          </shipvagoo-input-v1>
        </div>
        <div class="flex items-center q-mt-lg">
          <shipvagoo-input-v1 v-model="form.key"
                              placeholder="Enter API secret key"
                              class="full-width" readonly label="Key">
            <q-btn class="cursor-pointer" :icon="'img:' + CopyIcn" @click="onClickCopyKey" flat
                   style="background-color: transparent; " />
          </shipvagoo-input-v1>
        </div>
      </div>
      <div class="flex justify-center ">
        <shipvagoo-button
          label="Generate"
          class="full-width q-mt-lg"
          @click="onClickGenerate"
        />
      </div>
    </shipvagoo-card-section>
  </shipvagoo-modal>

</template>

<script setup lang="ts">
import { onBeforeMount, ref } from "vue";
import {
  apiAccessService,
  loadingService,
  loggingService,
  notificationService
} from "../services/_singletons";

import CopyIcn from "../assets/img/copy-n-icon.svg";

interface IForm {
  user: string | null;
  key: string | null;
}


const form = ref<IForm>({
  user: null,
  key: null
});

const getApiAccess = async () => {
  try {
    loadingService.startLoading("main-loader:get-api-access");
    const response = await apiAccessService.getApiAccess();

    if (response !== "") {
      form.value.user = response.user;
      form.value.key = response.key;
    }
  } catch (e) {
    loggingService.error("Error while getting api access", e);
    notificationService.showError("Error while getting api access");
  } finally {
    loadingService.stopLoading("main-loader:get-api-access");
  }
};

const onClickGenerate = async () => {
  try {
    loadingService.startLoading("main-loader:generate-api-access");
    const response = await apiAccessService.generateApiAccess();

    form.value.user = response.user;
    form.value.key = response.key;

    notificationService.showSuccess("Api access generated successfully");
  } catch (e) {
    loggingService.error("Error while generating api access", e);
    notificationService.showError("Error while generating api access");
  } finally {
    loadingService.stopLoading("main-loader:generate-api-access");
  }
};

const onClickCopyUser = () => {
  if (form.value.user) {
    navigator.clipboard.writeText(form.value.user);
    notificationService.showSuccess("User copied to clipboard");
  }
};

const onClickCopyKey = () => {
  if (form.value.key) {
    navigator.clipboard.writeText(form.value.key);
    notificationService.showSuccess("User copied to clipboard");
  }
};

onBeforeMount(() => {
  getApiAccess();
});
</script>
