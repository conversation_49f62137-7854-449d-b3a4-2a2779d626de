<template>
  <GMapMap :zoom="10" :center="centerPoint">
    <GMapMarker
      v-for="(servicePoint, index) in servicePoints"
      :key="index"
      :clickable="true"
      :draggable="false"
      :icon="{
        url: markerIconUrl,
        scaledSize: { width: 30, height: 30 },
      }"
      :position="{ lat: servicePoint.latitude, lng: servicePoint.longitude }"
      @click="openServicePointInfo(index)"
    >
      <GMapInfoWindow
        :opened="openedServicePointInfo === index"
        :closeclick="true"
        @closeclick="openServicePointInfo(-1)"
      >
        <div class="infoBox">
          <div class="line">
            <p>Address:</p>
            <span>{{ servicePoint.addressLine1 }}</span>
          </div>
          <div class="line">
            <p>City:</p>
            <span>{{ servicePoint.city }}</span>
          </div>
          <div class="line">
            <p>Name:</p>
            <span>{{ servicePoint.name }}</span>
          </div>
          <div class="wrapper">
            <p>Opening Times:</p>
            <table>
              <tr>
                <th>Open Days</th>
                <th>Start Time</th>
                <th>End Time</th>
              </tr>
              <tr
                v-for="opening in servicePoint.openingTimes"
                :key="`${opening.from}-${opening.to}`"
              >
                <td>
                  <strong>{{ opening.day }}. Day</strong>
                </td>
                <td>{{ opening.from }}</td>
                <td>{{ opening.to }}</td>
              </tr>
            </table>
          </div>
        </div>
      </GMapInfoWindow>
    </GMapMarker>
  </GMapMap>
</template>

<script setup lang="ts">
  import { computed, onBeforeMount, ref } from 'vue';
  import { ICarrier } from '../model/carrier.model';
  import { ICountry } from '../model/country.model';
  import { IParcelShop } from '../model/shipment.model';
  import { loadingService, notificationService, shipmentService } from '../services/_singletons';

  interface Props {
    carrier: ICarrier;
    address: string;
    zipcode: string;
    city: string;
    country: ICountry;
  }

  const props = defineProps<Props>();

  const openedServicePointInfo = ref();
  const servicePoints = ref<IParcelShop[]>([]);

  const centerPoint = computed(() => {
    if (servicePoints.value.length > 0) {
      return {
        lat: servicePoints.value[0].latitude,
        lng: servicePoints.value[0].longitude,
      };
    }

    return {
      lat: 0,
      lng: 0,
    };
  });

  const markerIconUrl = computed(()=>{
    if (props.carrier.carrier_id === 'GLS_DK') {
      return '/assets/img/gls_dk.png';
    }

    if (props.carrier.carrier_id === 'DAO') {
      return '/assets/img/dao.png';
    }

    return undefined;
  })

  const openServicePointInfo = (value: number) => {
    openedServicePointInfo.value = value;
  };

  const searchServicePoint = async () => {
    if (!props.country || !props.address || !props.zipcode || !props.carrier) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:service-points');
      const response = await shipmentService.getParcelShops(
        props.carrier.carrier_id,
        props.country.iso,
        `${props.address} ${props.city}`,
        props.zipcode,
      );
      servicePoints.value = response.map((p) => p.parcelShop);
    } catch (e) {
      notificationService.showError('No service points found');
    } finally {
      loadingService.stopLoading('main-loader:service-points');
    }
  };

  onBeforeMount(() => {
    searchServicePoint();
  });
</script>

<style lang="scss">
  .vue-map-container {
    width: 100%;
    height: 100%;
  }

  .google-map {
    width: 100%;
    height: 500px;
    padding: 10px;
    background-color: #151526;

    .close {
      text-align: right;
    }

    .map {
      height: 90%;
    }
  }

  .infoBox {
    p {
      margin: 0 5px 0 0;
      font-size: 15px;
      font-weight: bold;
      color: #000;
    }

    span {
      margin: 0;
      font-size: 12px;
      color: #000;
    }

    .line {
      display: flex;
      align-items: center;
    }

    table {
      td {
        color: #000;
        border: 1px solid #000;
      }

      th {
        color: #000;
        border: 1px solid #000;
      }
    }
  }
</style>
