<template>
  <div v-if="view" class="alert" :class="type">
    <div class="icon">
      <span class="icon-alert" :class="type"></span>
    </div>
    <div class="message">
      <p>{{ message }}</p>
    </div>
  </div>
</template>
<script>
  import { ref, watch } from 'vue';

  export default {
    props: {
      message: String,
      type: String,
    },
    setup(props) {
      const view = ref(false);

      watch(
        () => props.message,
        () => {
          if (props.message != null) {
            view.value = true;
          } else {
            view.value = false;
          }
        },
      );

      return {
        view,
      };
    },
  };
</script>
<style lang="scss" scoped>
  .alert {
    display: flex;
    align-items: center;
    border: 1px solid $third-one;
    border-radius: 5px;

    .icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background-color: $third-one;
      border-top-right-radius: 5px;
      border-bottom-right-radius: 5px;

      &-alert {
        display: block;
        width: 19px;
        height: 19px;
        background-image: url('/assets/img/info.svg');
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
      }
    }

    .message {
      padding-left: 15px;

      p {
        margin: 0;
        font-size: 15px;
        color: #fff;
      }
    }

    &.success {
      border-color: $primary-one;

      .icon {
        background-color: $primary-one;

        &-alert {
          &.success {
            background-image: url('/assets/img/success.svg');
          }
        }
      }
    }
  }
</style>
