<template>
  <aside class="custom-sidebar">
    <sidebar-menu
      :menu="_menu"
      :status="!props.status"
      :collapsed="props.status"
      :hide-toggle="true"
      theme="white-theme"
      @item-click="onItemClick"
    />
  </aside>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { SidebarMenu } from "vue-sidebar-menu";
import { _my_menu, IMenu } from "../menu/menu";


interface Props {
  status: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  status: false
});

// eslint-disable-next-line no-underscore-dangle, @typescript-eslint/naming-convention
const _menu = computed(() => {
  return _my_menu.map((m) => ({ ...m, title: m.title }));
});


const onItemClick = (_: Event, item: IMenu) => {
  if (item.onClick) {
    item.onClick();
  }
  /* if (item.title === "Create") {
     emit("modal-open", true);
   }*/
};
</script>

<style lang="scss">
.custom-sidebar {
  .v-sidebar-menu .vsm--toggle-btn {
    display: none;
  }

  .v-sidebar-menu .vsm--scroll-wrapper{
    width: 100% !important;
  }

  .v-sidebar-menu {
    top: 59px;
    right: auto;
    left: 0;
    background-color: #fff;
    
  }

  .v-sidebar-menu .vsm--link_level-1 .vsm--icon {
    width: 15px;
    height: 15px;
    background-color: transparent !important;
  }

  .v-sidebar-menu .vsm--title > span:first-child {
    font-family: Helvetica, sans-serif;
    font-size: 15px;
    font-style: normal;
    line-height: normal;
    color: #212121 !important;
  }

  .v-sidebar-menu .vsm--link_level-1.vsm--link_active {
    background-color: #ececec;
    box-shadow: none;
  }

  .vsm--child .vsm--child {
    padding-left: 20px;
  }

  .v-sidebar-menu.vsm_expanded .vsm--link_level-1.vsm--link_open {
    background-color: #ececec;
  }

  .v-sidebar-menu.vsm_expanded .vsm--link_level-1.vsm--link_open .vsm--icon {
    background-color: white;
  }

  .v-sidebar-menu .vsm--dropdown {
    background-color: white;
  }

  .v-sidebar-menu .vsm--link_active {
    font-weight: 400;
  }

  .v-sidebar-menu.vsm_expanded {
    width: 70% !important;
    max-width: 300px !important;
  }

  .v-sidebar-menu .vsm--scroll > .vsm--menu {
    width: 100% !important;

    > li{
      &:nth-child(2){
        > a {
          img{
            width: 20px;
            height: 20px;
            margin: 0 12px 0 -2px;
          }
        }
      }
    }
  }

  .v-sidebar-menu.vsm_collapsed {
    max-width: 0 !important;
  }

  .vsm--arrow_default {
    color: #212121
  }

  .v-sidebar-menu .vsm--link_hover {
    background-color: transparent !important;
  }

  .vsm--link_level-2 {
    margin-left: 25px !important;
  }
}
</style>
