<template>
  <div class="credit_notes_page">
    <div class="credit_notes_page-actions">
      <div class="big_search-rows_per_page-group row">
        <div class="col-12">
          <big-search
            @update:model-value="getCreditNotes"
            v-model="search" class="booked_page-actions-big_search"/>
        </div>
      </div>
      <!-- <q-btn-dropdown outline no-caps split label="Download"></q-btn-dropdown> -->
    </div>
    <div class="credit_notes_page-table">
      <shipvagoo-table
        v-model:selected="selected"
        v-model:pagination="pagination"
        :rows="rows"
        :columns="columns"
        :total="pagination.rowsNumber"
        :loading="loadingService.loadings.has('main-loader:loading-credit-notes')"
        @request="getCreditNotes($event.pagination)"
        @row-click="onClickDownloadInvoice"
        class="credit-notes-table"
      >
        <template #body-cell-country="props">
          <q-td :props="props">
            <div v-if="props" style="display: flex; gap: 10px; justify-content: flex-start">
              <img
                decoding="async"
                :src="`/assets/img/${props.row.recipient?.country?.toLowerCase?.() ?? ''}.svg`"
                style="width: 20px; height: 20px; border-radius: 50%; object-fit: cover"
              />
              {{ props.row.recipient.city }}
            </div>
          </q-td>
        </template>
        <template #body-cell-more="props">
          <q-td :props="props" class="more">
            <q-btn
              dense
              flat
              round
              style="color:#1f2124;"
              class="more-action"
              field="edit"
              icon="more_horiz"
            >
              <shipvagoo-menu>
                <shipvagoo-menu-item clickable @click="() => {}">{{
                  $t('common.download')
                  }}
                </shipvagoo-menu-item>
              </shipvagoo-menu>
            </q-btn>
          </q-td>
        </template>
      </shipvagoo-table>
    </div>
  </div>
  <div class="pdf-assets" aria-hidden>
    <img class="pdf-logo" height="106" width="662.88" :src="LogoDark"/>\
  </div>
  <div style="height: 0; overflow: hidden">
    <div
      v-if="creditNoteToPrint"
      id="credit-note-pdf-content"
      aria-hidden
      style="position: relative; width: 2480px; height: 3507px"
    >
      <div style="display: flex; justify-content: space-between; width: 100%">
        <h1 style="font-size: 80px; color: black">{{ $t('common.credit_note') }}</h1>
      </div>
      <div
        style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          padding-top: 160px;
        "
      >
        <div style="font-size: 40px; color: #000000">
          <div style="font-weight: 700">
            {{ $store.state.companyStore.company_name }}
          </div>
          <div>{{ $store.state.companyStore.address }}</div>
          <div>
            {{ $store.state.companyStore.country?.iso }}-{{ $store.state.companyStore.zipcode }}
            {{ $store.state.companyStore.city }}
          </div>
        </div>
      </div>
      <div
        style="
          display: flex;
          justify-content: space-between;
          padding-top: 170px;
          font-size: 40px;
          color: #000000;
        "
      >
        <div style="font-weight: 700">
          {{ $t('common.number') }}: {{ HelperService.formatId(creditNoteToPrint.id) }}
        </div>
        <div>
          {{ $t('common.date') }}: {{ dayjs(creditNoteToPrint.created_at).format('DD-MM-YYYY') }}
        </div>
      </div>

      <table style="width: 100%; margin-top: 135px; border-collapse: collapse">
        <thead>
        <tr style="font-size: 40px">
          <th
            style="
                width: 40%;
                padding-bottom: 16px;
                padding-left: 20px;
                text-align: left;
                border-bottom: 8px solid #151526;
              "
          >
            {{ $t('common.description') }}
          </th>
          <th
            style="
                width: 20%;
                padding-bottom: 16px;
                text-align: right;
                border-bottom: 8px solid #151526;
              "
          >
            {{ $t('common.quantity') }}
          </th>
          <th
            style="
                width: 20%;
                padding-bottom: 16px;
                text-align: right;
                border-bottom: 8px solid #151526;
              "
          >
            {{ $t('common.unit_price') }}
          </th>
          <th
            style="
                width: 20%;
                padding-right: 20px;
                padding-bottom: 16px;
                text-align: right;
                border-bottom: 8px solid #151526;
              "
          >
            {{ $t('common.amount') }}
          </th>
        </tr>
        </thead>
        <tbody
          v-if="
            creditNoteToPrint.credit_note_items && creditNoteToPrint.credit_note_items.length > 0
          "
        >
        <tr>
          <td style="padding-left: 20px; font-size: 40px; font-weight: 700">Shipments</td>
          <td></td>
          <td></td>
          <td></td>
        </tr>
        <template
          v-for="[key, invoiceItems] in Object.entries(
              _.groupBy(
                creditNoteToPrint.credit_note_items.filter((i) => i.type === 'Shipments'),
                'country_pair',
              ),
            )"
          :key="key"
        >
          <tr>
            <td style="padding-left: 40px; font-size: 40px">{{ key }}</td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
          <template v-for="invoiceItem in invoiceItems">
            <tr
              v-for="item in invoiceItem.items"
              :key="`${item.weight_class}-${item.count}-${item.unit_price}`"
              style="font-size: 40px"
            >
              <td style="padding-left: 60px">{{ item.weight_class }}</td>
              <td style="text-align: right">{{ item.count }}</td>
              <td style="text-align: right">
                {{
                new Intl.NumberFormat('dk-DK', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
                }).format(Number(item.unit_price))
                }}
              </td>
              <td style="padding-right: 20px; text-align: right">
                {{ DecimalService.toDecimal(Number(item.unit_price) * Number(item.count)) }}
              </td>
            </tr>
          </template>
        </template>
        <template
          v-for="additionalServices in creditNoteToPrint.credit_note_items.filter(
              (i) => i.type === 'Additional Services',
            )"
          :key="additionalServices.items"
        >
          <tr v-if="additionalServices.items.some((i) => parseFloat(i.unit_price) > 0)">
            <td style="padding-left: 20px; font-size: 40px; font-weight: 700">
              {{ $t('common.additional_services') }}
            </td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
          <tr
            v-for="item in additionalServices.items.filter((i) => parseFloat(i.unit_price) > 0)"
            :key="`${item.weight_class}-${item.count}-${item.unit_price}`"
            style="font-size: 40px"
          >
            <td style="padding-left: 40px">{{ item.weight_class }}</td>
            <td style="text-align: right">{{ item.count }}</td>
            <td style="text-align: right">
              {{ DecimalService.toDecimal(Number(item.unit_price)) }}
            </td>
            <td style="padding-right: 20px; text-align: right">
              {{ DecimalService.toDecimal(Number(item.unit_price) * Number(item.count)) }}
            </td>
          </tr>
        </template>
        </tbody>
      </table>

      <table style="width: 100%; margin-top: 40px; border-collapse: collapse">
        <thead>
        <tr style="font-size: 40px">
          <th
            style="
                width: 20%;
                padding-bottom: 16px;
                padding-left: 20px;
                text-align: left;
                border-bottom: 8px solid #151526;
              "
          >
            {{ $t('common.this_includes') }}
          </th>
          <th
            style="
                width: 20%;
                padding-bottom: 16px;
                text-align: right;
                border-bottom: 8px solid #151526;
              "
          >
            {{ $t('common.amount') }}
          </th>
          <th
            style="
                width: 20%;
                padding-bottom: 16px;
                text-align: right;
                border-bottom: 8px solid #151526;
              "
          >
            {{ $t('common.vat') }}
          </th>
          <th
            style="
                width: 20%;
                padding-bottom: 16px;
                text-align: right;
                border-bottom: 8px solid #151526;
              "
          >
            {{ $t('common.vat_amount') }}
          </th>
          <th
            style="
                width: 20%;
                padding-right: 20px;
                padding-bottom: 16px;
                text-align: right;
                border-bottom: 8px solid #151526;
              "
          >
            {{ $t('common.total') }}
          </th>
        </tr>
        </thead>
        <tbody>
        <tr style="font-size: 40px; text-align: right">
          <td></td>
          <td>
            {{ DecimalService.toDecimal(CreditNoteService.getTotalExclVAT(creditNoteToPrint)) }}
          </td>
          <td>
            {{
            new Intl.NumberFormat('dk-DK', {
            style: 'percent',
            }).format(0.25)
            }}
          </td>
          <td>
            {{
            DecimalService.toDecimal(
            CreditNoteService.getTotalExclVAT(creditNoteToPrint) * 0.25,
            )
            }}
          </td>
          <td style="padding-right: 20px">
            {{ DecimalService.toDecimal(getTotalInclVAT()) }}
          </td>
        </tr>
        </tbody>
      </table>

      <div
        style="
          display: flex;
          justify-content: space-between;
          font-size: 40px;
          font-weight: 700;
          border-top: 8px solid #151526;
        "
      >
        <div style="padding-left: 20px">{{ $t('common.total_incl_vat') }} (DKK)</div>
        <div style="padding-right: 20px">
          {{ DecimalService.toDecimal(getTotalInclVAT()) }}
        </div>
      </div>

      <div
        style="
          position: absolute;
          bottom: 0;
          left: 50%;
          padding-bottom: 150px;
          font-size: 40px;
          text-align: center;
          transform: translateX(-50%);
        "
      >
        <div>ShipVagoo ApS • Laplandsgade 4A • DK-2300 København S</div>
        <div>DK12345678 • <EMAIL> • +45 12 345 678</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {nextTick, onBeforeMount, Ref, ref} from 'vue';
  import {QTableProps} from 'quasar';
  import dayjs from 'dayjs';
  import * as _ from 'lodash-es';
  import Html2Canvas from 'html2canvas';
  import JsPDF from 'jspdf';
  import {useI18n} from 'vue-i18n';
  import {
    creditNoteService,
    loadingService,
    loggingService,
    notificationService,
  } from '../services/_singletons';
  import {Pagination} from '../model/common.model';
  import {IRequestParam} from '../model/http.model';
  import {ICreditNote} from '../model/credit-note.model';
  import DecimalService from '../services/decimal.service';
  import {useStore} from '../store';
  import LogoDark from '../assets/img/logo-dark.svg';
  import HelperService from '../services/helper.service';
  import CreditNoteService from '../services/credit-note.service';

  const $store = useStore();
  const {t} = useI18n();
  const search: Ref<string> = ref('');

  const columns: QTableProps['columns'] = [
    {
      name: 'created_at',
      align: 'left',
      label: t('common.created_at'),
      field: (row: ICreditNote) => dayjs(String(row.created_at)).format('DD/MM/YYYY HH:mm:ss'),
      sortable: true,
    },
    {
      name: 'id',
      align: 'left',
      label: t('credit_notes_page.credit_note_no'),
      field: (row: ICreditNote) => HelperService.formatId(row.id),
      sortable: true,
    }, {
      name: 'description',
      align: 'left',
      label: 'Description',
      field: '',
      sortable: false,
    },
    {
      name: 'amount',
      align: 'right',
      label: 'Total Amount incl. VAT',
      field: '',
      sortable: false,
    },
    {
      name: 'more',
      align: 'right', label: t('common.more'), field: 'more', sortable: false
    },
  ];

  const rows = ref<ICreditNote[]>([]);
  const pagination: Ref<Pagination> = ref({
    page: 1,
    rowsPerPage: 10,
    rowsNumber: 0,
    descending: true,
  });
  const selected = ref([]);
  const creditNoteToPrint = ref<ICreditNote | null>(null);

  const getCreditNotes = async (tablePagination: Pagination | null) => {
    try {
      loadingService.startLoading('main-loader:loading-credit-notes');
      const page = tablePagination?.page ?? 1;
      const limit = tablePagination?.rowsPerPage ?? 10;

      const params: IRequestParam[] = [];

      if (search.value && search.value !== '') {
        params.push({key: 'conditions[credit_note_code:like]', value: search.value});
      }

      if (tablePagination?.sortBy) {
        const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
        params.push({key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder});
      } else {
        params.push({key: 'sorting[created_at]', value: 'desc'});
      }

      const creditNotes = await creditNoteService.getCreditNotes(page, limit, params);

      rows.value = creditNotes.entities;
      pagination.value.rowsNumber = creditNotes.total;
      pagination.value.page = page;
      pagination.value.rowsPerPage = limit;
      pagination.value.descending = tablePagination?.descending;
      pagination.value.sortBy = tablePagination?.sortBy;
    } catch (e) {
      loggingService.error('Error loading credit notes', e);
      notificationService.showError('Error loading credit notes');
    } finally {
      loadingService.stopLoading('main-loader:loading-credit-notes');
    }
  };

  const onClickDownloadInvoice = async (event: Event | null, row: ICreditNote) => {
    if (event && HelperService.containsIgnoredRowElements(event)) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:print-sales-invoice-receipt');

      creditNoteToPrint.value = row;

      await nextTick();

      const el = document.getElementById('credit-note-pdf-content');
      const logo = document.querySelector('.pdf-logo');

      if (!creditNoteToPrint.value || !el || !logo || !(logo instanceof HTMLImageElement)) {
        return;
      }

      const PAGE_WIDTH_MM: number = 210;
      const DESIGN_WIDTH: number = 2480;
      const SCALE_MULTIPLIER: number = PAGE_WIDTH_MM / DESIGN_WIDTH;

      const logoCanvas = await Html2Canvas(logo, {
        useCORS: true,
      });

      const jsPDFInstance = new JsPDF('p', 'mm', 'a4');

      await jsPDFInstance.html(el, {
        width: 210,
        html2canvas: {
          scale: SCALE_MULTIPLIER,
          useCORS: true,
        },
      });

      jsPDFInstance.addImage(
        logoCanvas.toDataURL('image/png'),
        'PNG',
        210 - 662.88 * SCALE_MULTIPLIER - 150 * SCALE_MULTIPLIER,
        150 * SCALE_MULTIPLIER,
        662.88 * SCALE_MULTIPLIER,
        106 * SCALE_MULTIPLIER,
      );

      jsPDFInstance.save(`${HelperService.formatId(creditNoteToPrint.value.id)}.pdf`);
    } catch (e) {
      loggingService.error('Error while printing receipt', e);
      notificationService.showError('Error while printing receipt');
    } finally {
      loadingService.stopLoading('main-loader:print-sales-invoice-receipt');
    }
  };

  const getTotalInclVAT = (): number => {
    if (!creditNoteToPrint.value) {
      return 0;
    }

    const totalExclVAT: number = CreditNoteService.getTotalExclVAT(creditNoteToPrint.value);
    const vat: number = totalExclVAT * 0.25;

    return totalExclVAT + vat;
  };

  onBeforeMount(() => {
    getCreditNotes(null);
  });
</script>

<style scoped lang="scss">
  .pdf-assets {
    height: 0;
    overflow: hidden;
  }

  #credit-note-pdf-content {
    padding: 150px;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu,
    Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: black !important;
    letter-spacing: 0.01px !important;
    background-color: white;

    h1 {
      margin: 0;
    }
  }

  .credit_notes_page {
    margin-top: 15px;

    &-actions {
      display: flex;
      flex-direction: row;

      & > *:not(:first-child) {
        margin-left: 10px;

        @media (max-width: 600px) {
          margin-left: unset;
        }
      }

      .big_search-rows_per_page-group {
        display: flex;
        flex: 1;
        flex-direction: row;

        & > *:not(:first-child) {
          margin-left: 10px;
        }

        .rows_per_page {
          display: none;

          @media (max-width: $tablet) {
            display: block;
          }
        }
      }

      &-big_search {
        flex: 1;
      }

      @media (max-width: 600px) {
        flex-direction: column;

        &-big_search {
          margin-bottom: 10px;
        }
      }
    }
  }
</style>
<style>
  .credit-notes-table th:nth-child(1),
  .credit-notes-table td:nth-child(1) {
    width: 17%;
  }

  .credit-notes-table th:nth-child(2) {
    width: 17%;
  }

  .credit-notes-table td:nth-child(2) {
    width: 17%;
    padding-left: 38px;
  }

  .credit-notes-table th:nth-child(3),
  .credit-notes-table td:nth-child(3) {
    width: 41%;
  }

  .credit-notes-table th:nth-child(4),
  .credit-notes-table td:nth-child(4) {
    width: 15%;
  }

  .credit-notes-table th:nth-child(5),
  .credit-notes-table td:nth-child(5) {
    width: 10%;
    padding-right: 25px;
  }
</style>