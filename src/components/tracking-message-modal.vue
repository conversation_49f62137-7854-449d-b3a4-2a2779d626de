<template>
  <shipvagoo-modal
    :title="trackingMessage ? 'Update tracking message' : 'New tracking message'"
    :header-separator="false"
    size="md"
    style="margin-top: 40px"
  >
    <shipvagoo-separator style="margin: 0 20px"></shipvagoo-separator>
    <shipvagoo-stepper ref="stepper" class="create-template-modal-stepper-v2" v-model="step">
      <q-step :name="1" title="General" :done="step > 1">
        <shipvagoo-card-section class="flex column gap-15 no-padding">
          <shipvagoo-select-v1
            v-model="form.one.trigger"
            label="Trigger"
            :options="messageHooks"
            option-label="content"
            option-value="message_hook_code"
            :error="$v.one.trigger.$error"
            :error-message="$v.one.trigger.$errors?.[0]?.$message.toString()"
            @update:model-value="onChangeTrigger"
            use-input
            input-debounce="0"
            placeholder="Select trigger"
            fill-input
            hide-selected
          />
          <shipvagoo-input-group v-if="form.one.trigger" responsive>
            <shipvagoo-select-v1
              v-model="form.one.isReceiverCountrySpecific"
              label="Destination country specific"
              :options="[
                { label: 'Yes', value: true },
                { label: 'No', value: false },
              ]"
              emit-value
              option-label="label"
              option-value="value"
              map-options
              :error="$v.one.isReceiverCountrySpecific.$error"
              :error-message="$v.one.isReceiverCountrySpecific.$errors?.[0]?.$message.toString()"
              @update:model-value="onChangeIsReceiverCountrySpecific"
            />
            <shipvagoo-select-v1
              v-model="form.one.receiverCountry"
              :disable="!form.one.isReceiverCountrySpecific"
              label="Destination country"
              :options="countries"
              option-label="default_name"
              option-value="country_code"
              :error="$v.one.receiverCountry.$error"
              :error-message="$v.one.receiverCountry.$errors?.[0]?.$message.toString()"
              use-input
              input-debounce="0"
              placeholder="Select"
              fill-input
              hide-selected
            >
              <template #prepend>
                <shipvagoo-country-icon
                  v-if="form.one.receiverCountry"
                  :country="form.one.receiverCountry"
                />
              </template>
              <template #option="scope">
                <q-item v-bind="scope.itemProps">
                  <q-item-section side>
                    <shipvagoo-country-icon v-if="scope.opt" :country="scope.opt" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ scope.opt.default_name }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
            </shipvagoo-select-v1>
          </shipvagoo-input-group>
          <shipvagoo-input-group v-if="form.one.trigger" responsive>
            <shipvagoo-select-v1
              v-model="form.one.isIntegrationSpecific"
              label="E-commerce specific"
              :options="yesNoOptions"
              emit-value
              option-label="label"
              option-value="value"
              map-options
              :disable="form.one.trigger.message_id == 'fulfillment_created'"
              :error="$v.one.isIntegrationSpecific.$error"
              :error-message="$v.one.isIntegrationSpecific.$errors?.[0]?.$message.toString()"
              @update:model-value="onChangeIsIntegrationSpecific"
            />
            <shipvagoo-select-v1
              v-model="form.one.integration"
              :disable="!form.one.isIntegrationSpecific"
              label="E-commerce"
              :options="orderIntegrations"
              option-label="name"
              option-value="integration_code"
              :error="$v.one.integration.$error"
              :error-message="$v.one.integration.$errors?.[0]?.$message.toString()"
              use-input
              input-debounce="0"
              placeholder="Select"
              fill-input
              hide-selected
            />
          </shipvagoo-input-group>
        </shipvagoo-card-section>
      </q-step>
      <q-step :name="2" title="Message" :done="step > 2">
        <shipvagoo-card-section v-if="form.one.trigger" class="flex column gap-15 no-padding">
          <shipvagoo-input-v1
            v-if="selectedMessageType === 'EMAIL'"
            v-model="form.two.subject"
            label="Subject"
            :error="$v.two.subject.$error"
            :error-message="$v.two.subject.$errors?.[0]?.$message.toString()"
          />
          <div>
            <shipvagoo-wysiwyg-v1
              v-if="selectedMessageType === 'EMAIL'"
              label="Message content"
              placeholder="Enter message content"
              v-model="form.two.messageContent"
              :error="$v.two.messageContent.$error"
              :error-message="$v.two.messageContent.$errors?.[0]?.$message.toString()"
            />
          </div>
        </shipvagoo-card-section>
      </q-step>
      <template #navigation>
        <q-stepper-navigation
          class="create-shipment-stepper-v2-navigation"
          style="margin-top: -30px"
        >
          <shipvagoo-button
            color="brand"
            class="create-shipment-stepper-v2-navigation-previous"
            v-if="step > 1"
            label="Back"
            outline
            no-caps
            @click="onClickPrevious"
          />
          <shipvagoo-button
            color="brand"
            v-if="step == 1"
            class="create-shipment-stepper-v2-navigation-next"
            label="Next"
            no-caps
            @click="onClickNext"
          />
          <shipvagoo-button
            v-if="step === 2"
            class="create-shipment-stepper-v2-navigation-next"
            :label="trackingMessage ? 'Save' : 'Create'"
            @click="trackingMessage ? onClickSave() : onClickCreate()"
            no-caps
          />
        </q-stepper-navigation>
      </template>
    </shipvagoo-stepper>
  </shipvagoo-modal>
</template>
<script setup lang="ts">
  import { ref, computed, onBeforeMount, Ref, ComponentPublicInstance } from 'vue';
  import {
    IMessageHook,
    IPersonalizedMessage,
    ITrackingMessageForm,
    TMessagingType,
  } from '../model/personalized-message.model';
  import { useStore } from '../store';
  import useVuelidate, { ValidationArgs } from '@vuelidate/core';
  import { helpers, required, requiredIf } from '@vuelidate/validators';
  import {
    loadingService,
    loggingService,
    notificationService,
    orderIntegrationService,
    personalizedMessageService,
  } from '../services/_singletons';
  import { IOrderIntegration } from '../model/order-integration.model';
  import { ICountry } from '../model/country.model';

  interface IProps {
    trackingMessage: IPersonalizedMessage | null;
  }

  const props = defineProps<IProps>();
  const emit = defineEmits(['close']);
  const $store = useStore();

  /** ref properties **/
  interface YesNo {
    label: string;
    value: boolean;
  }

  const selectedMessageType = ref<TMessagingType | null>('EMAIL');
  const stepper = ref<ComponentPublicInstance<{ next: () => void; previous: () => void }> | null>(
    null,
  );
  const step = ref<number>(1);
  const form = ref<ITrackingMessageForm>({
    one: {
      trigger: null,
      isReceiverCountrySpecific: false,
      receiverCountry: null,
      isIntegrationSpecific: false,
      integration: null,
      when: 'IMMEDIATELY',
    },
    two: {
      subject: null,
      messageContent: '',
    },
  });

  const orderIntegrations = ref<IOrderIntegration[]>([]);
  const messageHooks: Ref<IMessageHook[]> = ref<IMessageHook[]>([]);
  const yesNoOptions = ref<YesNo[]>([
    { label: 'Yes', value: true },
    { label: 'No', value: false },
  ]);

  const $v = useVuelidate<ITrackingMessageForm, ValidationArgs<ITrackingMessageForm>>(
    {
      one: {
        trigger: {
          required: helpers.withMessage('This field is required', required),
        },
        isReceiverCountrySpecific: {
          required: helpers.withMessage('This field is required', required),
        },
        receiverCountry: {
          required: helpers.withMessage(
            'This field is required',
            requiredIf(() => form.value.one.isReceiverCountrySpecific),
          ),
        },
        isIntegrationSpecific: {
          required: helpers.withMessage('This field is required', required),
        },
        integration: {
          required: helpers.withMessage(
            'This field is required',
            requiredIf(() => form.value.one.isIntegrationSpecific),
          ),
        },
        when: {
          required: helpers.withMessage('This field is required', required),
        },
      },
      two: {
        subject: {
          required: helpers.withMessage(
            'This field is required',
            requiredIf(() => selectedMessageType.value === 'EMAIL'),
          ),
        },
        messageContent: {
          required: helpers.withMessage('This field is required', required),
        },
      },
    },
    form,
  );
  /** computed properties **/
  const countries = computed(() => {
    return $store.state.countryStore.countries;
  });

  /** functions **/
  const onClickPrevious = () => {
    step.value -= 1;
  };
  const onClickNext = async () => {
    if (step.value === 1) {
      await $v.value.one.$validate();
    }
    if (step.value === 1 && $v.value.one.$error) {
      return;
    }
    stepper.value?.next();
  };

  const onClickCreate = async () => {
    await $v.value.$validate();

    if (
      $v.value.one.$error ||
      !form.value.one.trigger ||
      !selectedMessageType.value ||
      $v.value.two.$error
    ) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:save-personalized-message');

      await personalizedMessageService
        .createPersonalizedMessage({
          message_hook_code: form.value.one.trigger.message_hook_code,
          type: selectedMessageType.value,
          is_receiver_country_specific: form.value.one.isReceiverCountrySpecific,
          receiver_country_code: form.value.one.receiverCountry?.country_code ?? undefined,
          is_integration_specific: form.value.one.isIntegrationSpecific,
          integration_code: form.value.one.integration?.integration_code ?? undefined,
          send_option: form.value.one.when,
          send_time: null,
          subject: form.value.two.subject,
          bcc: null,
          message_content: form.value.two.messageContent,
        })
        .then((response) => {
          notificationService.showSuccess('Tracking message saved successfully.');
          emit('close');
        })
        .catch((error) => {
          throw new Error(error.response.data?.error);
        });
    } catch (e) {
      if (typeof e === 'string') {
        notificationService.showError(e);
      } else if (e instanceof Error) {
        notificationService.showError(e.message);
      } else {
        notificationService.showError('Error while saving personalized message');
      }
      loggingService.error('Error while saving personalized message', e);
    } finally {
      loadingService.stopLoading('main-loader:save-personalized-message');
    }
  };

  const onClickSave = async () => {
    await $v.value.$validate();

    if (
      $v.value.$error ||
      !form.value.one.trigger ||
      !selectedMessageType.value ||
      !props.trackingMessage ||
      $v.value.two.$error
    ) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:save-personalized-message');

      await personalizedMessageService
        .updatePersonalizedMessage(props.trackingMessage.message_code, {
          message_hook_code: form.value.one.trigger.message_hook_code,
          type: selectedMessageType.value,
          is_receiver_country_specific: form.value.one.isReceiverCountrySpecific,
          receiver_country_code: form.value.one.receiverCountry?.country_code ?? undefined,
          is_integration_specific: form.value.one.isIntegrationSpecific,
          integration_code: form.value.one.integration?.integration_code ?? undefined,
          send_option: form.value.one.when,
          send_time: null,
          subject: form.value.two.subject,
          bcc: null,
          message_content: form.value.two.messageContent,
        })
        .then((response) => {
          notificationService.showSuccess('Tracking message saved successfully.');
        })
        .catch((error) => {
          throw new Error(error.response.data?.error);
        });

      emit('close');
    } catch (e) {
      if (typeof e === 'string') {
        notificationService.showError(e);
      } else if (e instanceof Error) {
        notificationService.showError(e.message);
      } else {
        notificationService.showError('Error while saving personalized message');
      }
      loggingService.error('Error while saving personalized message', e);
    } finally {
      loadingService.stopLoading('main-loader:save-personalized-message');
    }
  };

  const onChangeTrigger = async (messageHook: IMessageHook | null) => {
    if (!messageHook || !selectedMessageType.value) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:get-message-templates');
      const messageTemplate = await personalizedMessageService.getPersonalizedMessageTemplate(
        selectedMessageType.value,
        messageHook.message_hook_code,
      );

      form.value.one.isReceiverCountrySpecific = Boolean(
        messageTemplate.is_receiver_country_specific,
      );

      if (form.value.one.isReceiverCountrySpecific) {
        form.value.one.receiverCountry = $store.state.countryStore.countries.find(
          (country) => country.country_code === messageTemplate.receiver_country_code,
        ) as ICountry;
      }
      if (messageHook.message_id == 'fulfillment_created') {
        yesNoOptions.value = [{ label: 'Yes', value: true }];
        form.value.one.isIntegrationSpecific = true;
      } else {
        yesNoOptions.value = [
          { label: 'Yes', value: true },
          { label: 'No', value: false },
        ];
        form.value.one.isIntegrationSpecific = Boolean(messageTemplate.is_integration_specific);
      }

      if (form.value.one.isIntegrationSpecific) {
        form.value.one.integration = orderIntegrations.value.find(
          (o) => o.integration_code === messageTemplate.integration_code,
        ) as IOrderIntegration;
      }

      form.value.one.when = messageTemplate.send_option;

      form.value.two.subject = messageTemplate.subject;

      form.value.two.messageContent = messageTemplate.message_content.replaceAll('\n', '<br>');
    } catch (e) {
      notificationService.showError('Error while getting message templates');
      loggingService.error('Error while getting message templates', e);
    } finally {
      loadingService.stopLoading('main-loader:get-message-templates');
    }
  };

  const onChangeIsReceiverCountrySpecific = (value: boolean) => {
    if (value) {
      return;
    }

    form.value.one.receiverCountry = null;
  };
  const onChangeIsIntegrationSpecific = (value: boolean) => {
    if (value) {
      return;
    }

    form.value.one.integration = null;
  };

  const getMessageHooks = async () => {
    try {
      loadingService.startLoading('main-loader:get-message-hooks');
      const response = await personalizedMessageService.getMessageHooks();
      messageHooks.value = response.slice(0, 2);
    } catch (e) {
      notificationService.showError('Error while getting message hooks');
      loggingService.error('Error while getting message hooks', e);
    } finally {
      loadingService.stopLoading('main-loader:get-message-hooks');
    }
  };

  const getOrderIntegrations = async () => {
    try {
      loadingService.startLoading('main-loader:get-order-integrations');
      const response = await orderIntegrationService.getOrderIntegrations(1, 1000);
      orderIntegrations.value = response.entities;
    } catch (e) {
      notificationService.showError('Error while getting order integrations');
      loggingService.error('Error while getting order integrations', e);
    } finally {
      loadingService.stopLoading('main-loader:get-order-integrations');
    }
  };

  const hydrateTrackingMessage = () => {
    const trackingMessage = props.trackingMessage;
    if (trackingMessage == null) {
      return;
    }

    form.value.one.trigger = messageHooks.value.find(
      (messageHook) => messageHook.message_hook_code === trackingMessage.message_hook_code,
    ) as IMessageHook;
    form.value.one.isReceiverCountrySpecific = Boolean(
      trackingMessage.is_receiver_country_specific,
    );

    if (form.value.one.isReceiverCountrySpecific) {
      form.value.one.receiverCountry = $store.state.countryStore.countries.find(
        (country) => country.country_code === trackingMessage.receiver_country_code,
      ) as ICountry;
    }

    form.value.one.isIntegrationSpecific = Boolean(trackingMessage.is_integration_specific);

    if (form.value.one.isIntegrationSpecific) {
      form.value.one.integration = orderIntegrations.value.find(
        (o) => o.integration_code === trackingMessage.integration_code,
      ) as IOrderIntegration;
    }

    form.value.one.when = trackingMessage.send_option;

    /*if (form.value.one.when === "SPECIFIC_TIME") {
    form.value.one.specificTime = trackingMessage.send_time;
  }*/

    if (trackingMessage.type === 'EMAIL') {
      form.value.two.subject = trackingMessage.subject;
    }

    form.value.two.messageContent = trackingMessage.message_content;
  };
  const hydratePredefinedFields = async () => {
    await getMessageHooks();
    await getOrderIntegrations();
    if (props.trackingMessage != null) {
      hydrateTrackingMessage();
    }
  };
  onBeforeMount(() => {
    hydratePredefinedFields();
  });
</script>
