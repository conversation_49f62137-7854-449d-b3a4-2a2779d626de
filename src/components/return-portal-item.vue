<template>
  <div
    v-if="returnPortalItem"
    :style="{ backgroundColor: status === 'active' ? '#fff' : '#F8F9FF' }"
    class="full-width integration-box flex column justify-between"
  >
    <div class="full-width" style="display: flex;justify-content: space-between">
      <div>
        <div class="dark-text text-weight-7 text-fifteen">
          {{ returnPortalItem.portal_name }}
        </div>
      </div>
      <div>
        <shipvagoo-status-tag
          :bg-color="status === 'active' ? '#BEF3D7' : '#FFD4E0'"
          :text="capitalize(status)"
          :color="status === 'active' ? '#0C9247' : '#BE4748'"
        />
      </div>
    </div>
    <div class="full-width q-mt-sm">
      <div>
        <div class="dark-text text-weight-7 text-fourteen">Return country:</div>
        <div v-if="props" class="flex items-center no-wrap q-mt-xs" style="gap: 7px;">
          <img
            decoding="async"
            :src="`/assets/img/${returnPortalItem.country?.iso.toLowerCase()}.svg`"
            style="width: 20px; height: 20px; border-radius: 50%; object-fit: cover"
          />
          <div class="dark-text text-fourteen full-height text-weight-4">
            {{ capitalize(returnPortalItem.country?.name.toLowerCase()) }}
          </div>
        </div>
      </div>
      <div class="q-mt-sm">
        <div class="dark-text text-weight-7 text-fourteen">Return portal webpage</div>
        <div class="q-mt-xs">
          <shipvagoo-hyperlink
            type="external-link"
            :label="`${baseUrl}/return/${returnPortalItem.portal_name}`"
            color="#006EFB"
            target="_blank"
            blank
            :to="`${baseUrl}/return/${returnPortalItem.portal_name}`"
          />
          <q-icon
            @click="onClickCopyUrl(`${baseUrl}/return/${returnPortalItem.portal_name}`)"
            class="cursor-pointer q-ml-xs" style="color:#7D8398 !important;" size="xs" :name="'img:'+CopyIcon">
            <q-tooltip>Copy url</q-tooltip>
          </q-icon>
        </div>
      </div>
    </div>
    <div class="flex row justify-between gap-10 q-mt-sm">
      <shipvagoo-button
        v-for="action in actions"
        :key="action.label"
        class="full-width"
        height="32px"
        style="flex:1;min-height: 32px !important"
        :label="action.label"
        @click="action.handler"
        outline
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits } from "vue";
import { IReturnPortal } from "../model/return-portal.model";
import CopyIcon from "../assets/img/copy.svg";
import { copyToClipboard } from "quasar";
import { notificationService } from "../services/_singletons";
import { capitalize } from "lodash-es";

/*const props = defineProps({
  returnPortalItem: Object as PropType<IReturnPortal>
});*/
interface IProps {
  returnPortalItem: IReturnPortal;
}

const props = defineProps<IProps>();

const emit = defineEmits(["edit", "activate", "delete", "deactivate"]);

const status = computed(() => (props.returnPortalItem?.is_active ? "active" : "inactive"));

const baseUrl = import.meta.env.VITE_appUrl;


const actions = computed(() => [
  {
    label: "Edit",
    handler: () => emit("edit")
  },
  {
    label: status.value === "active" ? "Deactivate" : "Activate",
    handler: () => (status.value === "active" ? emit("deactivate") : emit("activate"))
  },
  {
    label: "Delete",
    handler: () => emit("delete")
  }
]);
const onClickCopyUrl = (text: string) => {
  copyToClipboard(text)
    .then(() => {
      notificationService.showSuccess("Return portal url copied on clipboard.");
    })
    .catch(() => {
      // fail
    });
};
</script>

<style scoped>
.integration-box {
  padding: 18px;
  border: 1px solid rgba(125, 131, 152, 0.20);
  border-radius: 8px;
}
</style>
