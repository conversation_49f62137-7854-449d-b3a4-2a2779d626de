<template>
  <div v-if="status" class="loader">
    <img src="/assets/img/loader.svg" alt="Loader" decoding="async" />
    <div v-if="loadingMessage!=null" class="loader-text">{{ loadingMessage }}</div>
  </div>
</template>

<script>
export default {
  props: {
    status: <PERSON><PERSON><PERSON>,
    message: {
      type: [String, null],
      required: false,
      default: null
    }
  },
  data() {
    return {
      loadingMessage: null
    };
  },
  watch: {
    message: function(val) {
      this.loadingMessage = val;
    }
  },
  mounted() {
    this.loadingMessage = this.message;
  }
};
</script>

<style scoped>
.loader {
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
}

.loader-text {
  font-size: 18px;
  font-weight: bold;
  color: #69a3f5;
}
</style>
