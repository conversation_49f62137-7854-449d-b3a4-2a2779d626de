<template>
  <shipvagoo-modal
    title="New Shipment"
    :header-separator="false"
    size="x-md"
    style="margin-top: 40px"
    @close="onCloseCreateShipment"
  >
    <shipvagoo-separator style="margin: 0 20px"></shipvagoo-separator>
    <shipvagoo-stepper ref="stepper" class="create-shipment-stepper-v2" v-model="step">
      <q-step :name="1" title="Recipient" :done="step > 1">
        <div class="full-width flex justify-center">
          <shipvagoo-tabs
            dense
            style="width: 250px"
            :options="stepOneTabOptions"
            v-model="stepOneTabValue"
          />
        </div>
        <div class="q-my-lg">
          <div v-if="stepOneTabValue == 'sender'">
            <div class="two-columns-responsive gap-20">
              <shipvagoo-select-v1
                class="flex-1"
                disable
                v-model="steps.one.sender.senderCountry"
                placeholder="Select"
                label="Origin Country"
                :options="$store.state.countryStore.countries"
                option-label="default_name"
                option-value="country_code"
                @update:model-value="onChangeCountryOnStep1"
              >
                <template #prepend>
                  <shipvagoo-country-icon
                    v-if="steps.one.sender.senderCountry"
                    :country="steps.one.sender.senderCountry"
                  />
                </template>
                <template #option="scope">
                  <q-item v-bind="scope.itemProps">
                    <q-item-section side>
                      <shipvagoo-country-icon v-if="scope.opt" :country="scope.opt" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ scope.opt.default_name }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
              </shipvagoo-select-v1>
              <shipvagoo-select-v1
                class="flex-1"
                placeholder="Enter name"
                v-model="steps.one.sender.name"
                :label="$t('common.name')"
                :error="$v.one.sender.name.$error"
                use-input
                hide-selected
                fill-input
                input-debounce="0"
                :options="
                  addressBookRecords.filter(
                    (record) =>
                      record.country_code === steps.one.sender.senderCountry?.country_code,
                  )
                "
                option-label="name"
                option-value="address_book_code"
                @input-value="onChangeSenderName"
                hide-dropdown-icon
                :error-message="$v.one.sender.name.$errors?.[0]?.$message"
              >
                <template #option="scope">
                  <q-item v-bind="scope.itemProps" clickable @click="onSelectSender(scope.opt)">
                    <q-item-label style="display: flex; align-items: center">
                      {{ scope.opt.name }}
                    </q-item-label>
                  </q-item>
                </template>
              </shipvagoo-select-v1>
            </div>
            <div class="two-columns-responsive gap-20 q-mt-lg">
              <shipvagoo-input-v1
                class="flex-1"
                placeholder="Enter email"
                v-model="steps.one.sender.email"
                :label="$t('common.email')"
                :error="$v.one.sender.email.$error"
                :error-message="$v.one.sender.email.$errors?.[0]?.$message"
              />
              <shipvagoo-input-v1
                placeholder="Enter mobile"
                v-model="steps.one.sender.phone"
                style="flex: 1"
                :label="`${$t('common.mobile')} ${getDialCode(
                  steps.one.sender.senderCountry?.iso,
                )}`"
                type="tel"
                :error="$v.one.sender.phone.$error"
                :error-message="$v.one.sender.phone.$errors?.[0]?.$message"
              />
            </div>
            <div class="two-columns-responsive gap-20 q-mt-lg">
              <shipvagoo-input-v1
                placeholder="Enter address"
                class="flex-1"
                v-model="steps.one.sender.address1"
                :label="$t('common.address')"
                :error="$v.one.sender.address1.$error"
                :error-message="$v.one.sender.address1.$errors?.[0]?.$message"
              />
              <shipvagoo-input-v1
                placeholder="Enter address 2"
                class="flex-1"
                v-model="steps.one.sender.address2"
                label="Address 2 (optional)"
                :error="$v.one.sender.address2.$error"
                :error-message="$v.one.sender.address2.$errors?.[0]?.$message"
              />
            </div>
            <div class="two-columns-responsive gap-20 q-mt-lg">
              <shipvagoo-input-v1
                class="flex-1"
                placeholder="Enter postal code"
                v-model="steps.one.sender.zipCode"
                :label="`Postal code`"
                type="tel"
                :error="$v.one.sender.zipCode.$error"
                :error-message="$v.one.sender.zipCode.$errors?.[0]?.$message"
                :debounce="500"
                @update:model-value="onChangeZipCode('sender', $event)"
              />
              <shipvagoo-input-v1
                class="flex-1"
                v-model="steps.one.sender.city"
                :label="$t('common.city')"
                :error="$v.one.sender.city.$error"
                :error-message="$v.one.sender.city.$errors?.[0]?.$message"
              />
            </div>
            <!--            <div class="q-mt-sm">
                          <shipvagoo-checkbox-v1
                            @update:model-value="(value)=>onSaveContact(value,'sender')"
                            v-model="saveRecipientContactCheckbox">Save contact
                          </shipvagoo-checkbox-v1>
                        </div>-->
          </div>
          <div v-if="stepOneTabValue == 'recipient'">
            <div class="two-columns-responsive gap-20">
              <shipvagoo-select-v1
                class="flex-1"
                v-model="steps.one.recipient.receiverCountry"
                label="Destination Country"
                :options="receiverCountriesOptions"
                option-label="default_name"
                :error="$v.one.recipient.receiverCountry.$error"
                :error-message="$v.one.recipient.receiverCountry.$errors?.[0]?.$message"
                @update:model-value="onChangeCountryOnStep1"
                use-input
                input-debounce="0"
                placeholder="Select"
                @filter="onFilterReceiverCountries"
                fill-input
                hide-selected
                option-value="country_code"
              >
                <template v-slot:no-option>
                  <q-item>
                    <q-item-section class="text-grey"> No results </q-item-section>
                  </q-item>
                </template>
                <template #prepend>
                  <shipvagoo-country-icon
                    v-if="steps.one.recipient.receiverCountry"
                    :country="steps.one.recipient.receiverCountry"
                  />
                </template>
                <template #option="scope">
                  <q-item v-bind="scope.itemProps">
                    <q-item-section side>
                      <shipvagoo-country-icon v-if="scope.opt" :country="scope.opt" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ scope.opt.default_name }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
              </shipvagoo-select-v1>
              <shipvagoo-select-v1
                class="flex-1"
                placeholder="Enter first and last name"
                v-model="steps.one.recipient.name"
                :label="$t('common.name')"
                :error="$v.one.recipient.name.$error"
                use-input
                hide-selected
                fill-input
                input-debounce="0"
                :options="
                  addressBookRecords.filter(
                    (record) =>
                      record.country_code === steps.one.recipient.receiverCountry?.country_code,
                  )
                "
                option-label="name"
                option-value="address_book_code"
                @input-value="onChangeReceiverName"
                hide-dropdown-icon
                :error-message="$v.one.recipient.name.$errors?.[0]?.$message"
              >
                <template #option="scope">
                  <q-item v-bind="scope.itemProps" clickable @click="onSelectReceiver(scope.opt)">
                    <q-item-label style="display: flex; align-items: center">
                      {{ scope.opt.name }}
                    </q-item-label>
                  </q-item>
                </template>
              </shipvagoo-select-v1>
            </div>
            <div class="two-columns-responsive gap-20 q-mt-lg">
              <shipvagoo-input-v1
                class="flex-1"
                placeholder="Email"
                type="email"
                autocomplete="off"
                v-model="steps.one.recipient.email"
                :label="$t('common.email')"
                :error="$v.one.recipient.email.$error"
                :error-message="$v.one.recipient.email.$errors?.[0]?.$message"
              />

              <shipvagoo-input-v1
                placeholder="Enter mobile"
                v-model="steps.one.recipient.phone"
                style="flex: 1"
                :label="`${$t('common.mobile')} ${getDialCode(
                  steps.one.recipient.receiverCountry?.iso,
                )}`"
                type="tel"
                :error="$v.one.recipient.phone.$error"
                :error-message="$v.one.recipient.phone.$errors?.[0]?.$message"
              />
            </div>
            <div class="two-columns-responsive gap-20 q-mt-lg">
              <shipvagoo-input-v1
                placeholder="Enter address"
                class="flex-1"
                v-model="steps.one.recipient.address1"
                :label="$t('common.address')"
                :error="$v.one.recipient.address1.$error"
                :error-message="$v.one.recipient.address1.$errors?.[0]?.$message"
              />
              <shipvagoo-input-v1
                placeholder="Enter address 2"
                class="flex-1"
                v-model="steps.one.recipient.address2"
                label="Address 2 (optional)"
                :error="$v.one.recipient.address2.$error"
                :error-message="$v.one.recipient.address2.$errors?.[0]?.$message"
              />
            </div>
            <div class="two-columns-responsive gap-20 q-mt-lg">
              <shipvagoo-input-v1
                placeholder="Enter postal code"
                class="flex-1"
                v-model="steps.one.recipient.zipCode"
                :label="`Postal code`"
                type="tel"
                :error="$v.one.recipient.zipCode.$error"
                :error-message="$v.one.recipient.zipCode.$errors?.[0]?.$message"
                :debounce="500"
                @update:model-value="onChangeZipCode('recipient', $event)"
              />
              <shipvagoo-input-v1
                placeholder="Enter city"
                class="flex-1"
                v-model="steps.one.recipient.city"
                :label="$t('common.city')"
                :error="$v.one.recipient.city.$error"
                :error-message="$v.one.recipient.city.$errors?.[0]?.$message"
              />
            </div>
            <div class="q-mt-sm">
              <shipvagoo-checkbox-v1
                @update:model-value="(value:boolean) => onSaveContact(value, 'recipient')"
                v-model="saveRecipientContactCheckbox"
                >Save contact
              </shipvagoo-checkbox-v1>
            </div>
          </div>
        </div>
      </q-step>
      <q-step :name="2" title="Shipment" :done="step > 2">
        <div class="dark-text text-thirteen text-weight-7">Shipment details:</div>
        <shipvagoo-separator class="q-my-md" />
        <div
          class="gap-10 create-shipment-stepper-v2-shipments-details dark-text text-thirteen text-weight-7"
        >
          <div class="flex-1">
            <div>Carrier</div>
            <shipvagoo-radio-card
              class="create-shipment-stepper-v2-shipments-details-radio-card"
              v-for="(carrier, index) in carriers"
              :key="`shipment_create_carrier${index}`"
              v-model="carrierCode"
              :radio-value="carrier.carrier_code"
              :img-src="carrier.icon"
              :label="carrier.name"
              @update:model-value="onChangeCarrier"
            >
            </shipvagoo-radio-card>
          </div>
          <div class="flex-1">
            <div>Service</div>
            <shipvagoo-radio-card
              class="create-shipment-stepper-v2-shipments-details-radio-card"
              v-for="(shippingProduct, index) in shippingProducts"
              :key="`shipment_create_shipping_product${index}`"
              v-model="shippingProductCode"
              :radio-value="shippingProduct.carrier_product_code"
              :label="shippingProduct.name"
              @update:model-value="onChangeShippingProduct"
            >
            </shipvagoo-radio-card>
            <!--            <div v-if="!shippingProducts.length" class="dark-subtitle-text q-mt-md text-weight-4">Services not found</div>-->
          </div>
          <div class="flex-1 q-pr-sm">
            <div>Parcel weight</div>
            <shipvagoo-radio-card
              class="create-shipment-stepper-v2-shipments-details-radio-card"
              v-for="(weightClass, index) in weightClasses"
              :key="`shipment_create_weight_class${index}`"
              v-model="weightClassCode"
              :radio-value="weightClass.weight_class_code"
              :label="`${parseFloat(weightClass.min_weight.toString())}-${parseFloat(
                weightClass.max_weight.toString(),
              )}
                          ${weightClass.unit.toLowerCase()}`"
            >
            </shipvagoo-radio-card>
          </div>
        </div>
        <div v-if="!carriers.length" class="dark-subtitle-text text-center q-mt-md">
          No carriers found
        </div>

        <div
          class="q-mt-md"
          v-if="actualServicePoints.length && steps.two.shippingProduct?.hasMultiParcelShop"
        >
          <shipvagoo-input-v1
            disable
            placeholder="Enter address"
            class="flex-1"
            v-model="servicePointSearch.address"
            label="Address *"
          />
          <div class="flex gap-20 q-mt-md">
            <shipvagoo-input-v1
              disable
              placeholder="Enter postal code"
              class="flex-1"
              v-model="servicePointSearch.zipCode"
              label="Postal code *"
              type="tel"
            />
            <shipvagoo-input-v1
              disable
              placeholder="Enter city"
              class="flex-1"
              label="City *"
              v-model="servicePointSearch.city"
            />
          </div>
          <!-- <shipvagoo-button
            :disabled="
              !servicePointSearch.address || !servicePointSearch.zipCode || !servicePointSearch.city
            "
            no-caps
            @click="onClickSearchServicePoint"
            class="q-mt-md full-width"
          >
            Search
          </shipvagoo-button> -->
          <shipvagoo-select-v1
            placeholder="Select"
            class="q-mt-md"
            v-if="actualServicePoints.length > 0"
            v-model="steps.two.servicePoint"
            :options="servicePoints"
            label="Parcel shop"
            option-label="label"
            option-value="id"
            use-input
            hide-selected
            fill-input
            input-debounce="0"
            @filter="filterParcelShops"
          >
            <template #prepend>
              <q-icon name="search" size="sm"></q-icon>
            </template>
          </shipvagoo-select-v1>
        </div>
      </q-step>

      <q-step :name="3" :done="step > 3" title="Tracking">
        <div>
          <div class="dark-text text-thirteen text-weight-7">Tracking:</div>
          <shipvagoo-separator class="q-my-md" />
          <div class="two-columns-responsive gap-10">
            <template v-for="(additionalServiceCharge, index) in additionalServiceCharges">
              <shipvagoo-checkbox-card
                :key="`as_charges${additionalServiceCharge.additional_service_code}_${index}`"
                v-model="steps.three.additionalServices[additionalServiceCharge.as_charge_code]"
                class="flex-1"
                v-if="additionalServiceCharge.additional_service_code == tracking_email_as_code"
                :title="`Tracking email ${ShipmentService.formatAdditionalServicePrice(
                  parseFloat(additionalServiceCharge.price),
                )}`"
                subtitle="Send customized, automated and activity based tracking emails to your customers based"
              >
              </shipvagoo-checkbox-card>
              <shipvagoo-checkbox-card
                :key="`as_charges${additionalServiceCharge.additional_service_code}_${index}`"
                v-model="steps.three.additionalServices[additionalServiceCharge.as_charge_code]"
                class="flex-1"
                v-if="additionalServiceCharge.additional_service_code == tracking_sms_as_code"
                disable
                :title="`Tracking SMS (0,00 DKK)`"
                subtitle="Send customized, automated and activity based tracking SMS to your customers based"
              >
              </shipvagoo-checkbox-card>
            </template>
            <!--            <shipvagoo-checkbox-card
                          class="flex-1"
                          v-model="steps.three.additionalServices[3333128889]"
                          title="SMS notification"
                          subtitle="Email from the carrier sent to the recipient to provide updates on the shipment status">
                        </shipvagoo-checkbox-card>-->
            <!--            <shipvagoo-checkbox-card
                          class="flex-1"
                          v-for="(additionalServiceCharge, index) in additionalServiceCharges"
                          :key="`as_charges${additionalServiceCharge.additional_service_code}_${index}`"
                          v-model="steps.three.additionalServices[additionalServiceCharge.as_charge_code]"
                          title="Email notification"
                          subtitle="Email from the carrier sent to the recipient to provide updates on the shipment status">
                        </shipvagoo-checkbox-card>-->
          </div>
          <div class="q-mt-md q-px-md">
            <div class="dark-text text-weight-7 text-fourteen">What are tracking messages?</div>
            <div class="dark-text text-thirteen q-mt-xs">
              Tracking messages are automated tracking emails sent tor your customers to keep them
              informed about the delivery status of their parcels. You can set up tracking messages
              under Settings > Tracking messages
            </div>
          </div>
        </div>
      </q-step>
      <q-step :name="4" :done="step > 4" title="Payment">
        <div>
          <div class="two-columns-responsive gap-10">
            <div
              class="flex-1 create-shipment-stepper-v2-shipment create-shipment-stepper-v2-shipment-carrier"
            >
              <div class="dark-text text-weight-7 text-thirteen">Your selected carrier</div>
              <shipvagoo-separator class="create-shipment-stepper-v2-shipment-separator" />
              <div
                class="full-width flex justify-center items-center"
                style="height: calc(100% - 35px); min-height: 200px"
              >
                <div>
                  <img
                    width="80"
                    height="80"
                    :src="steps.two.carrier?.icon"
                    alt=""
                    decoding="async"
                    style="border-radius: 50%"
                  />
                  <div class="dark-text text-thirteen q-mt-sm text-center">
                    {{ steps.two.carrier?.name }}
                  </div>
                </div>
              </div>
            </div>
            <div class="flex-1">
              <mini-shipment-charge-table
                v-if="createdShipment"
                height="206px"
                :shipment="createdShipment"
                :charges="calculatedCharge"
              ></mini-shipment-charge-table>
            </div>
          </div>
          <div style="display: flex; gap: 8px" class="q-mt-sm">
            <div>
              <shipvagoo-checkbox-v1 v-model="steps.four.isTermsAndConditionsChecked" size="md">
              </shipvagoo-checkbox-v1>
            </div>
            <div
              @click="
                () =>
                  (steps.four.isTermsAndConditionsChecked = !steps.four.isTermsAndConditionsChecked)
              "
              class="dark-text text-thirteen cursor-pointer"
              style="margin-top: 4px"
            >
              <div>
                By proceeding with this shipment, you accecpt that you have read and understood our
                <strong>
                  <a
                    href="http://shipvagoo.com/virksomhed/vilkaar-og-betingelser/"
                    class="blue-text"
                    @click.stop
                    target="_blank"
                    >terms of use and conditions.</a
                  >
                </strong>
              </div>
              <div class="q-mt-xs" v-if="steps.two.carrier?.carrier_id">
                Read more about {{ steps.two.carrier?.name }} general information, surcharges and
                more
                <strong>
                  <a
                    :href="carrierInfoLink(steps.two.carrier?.carrier_id)"
                    class="blue-text"
                    @click.stop
                    target="_blank"
                    >click here.</a
                  >
                </strong>
              </div>
            </div>
          </div>
        </div>
      </q-step>
      <q-step :name="5" :done="step > 5" title="Confirmation">
        <div class="two-columns-responsive gap-10">
          <div
            class="flex-1 create-shipment-stepper-v2-shipment create-shipment-stepper-v2-shipment-carrier"
          >
            <div class="dark-text text-weight-7">Payment status:</div>
            <shipvagoo-separator class="create-shipment-stepper-v2-shipment-separator" />
            <div
              class="full-width flex justify-center items-center"
              style="height: calc(100% - 64px); min-height: 150px"
            >
              <div style="display: flex; flex-direction: column; align-items: center">
                <q-icon name="fas fa-check-circle" size="xl" color="green-brand "></q-icon>
                <div
                  class="text-green-brand text-fourteen text-weight-7 q-mt-sm text-center q-mt-md"
                >
                  Your payment has been completed
                </div>
              </div>
            </div>
            <shipvagoo-separator class="create-shipment-stepper-v2-shipment-separator" />
            <div class="flex justify-between" style="padding: 12px 0">
              <div class="dark-text text-weight-7">Tracking ID:</div>
              <div class="blue-text text-weight-7 cursor-pointer">
                <shipvagoo-hyperlink
                  v-if="carrierReference"
                  type="external-link"
                  color="#243E90"
                  text-decoration="none"
                  :to="steps.two.carrier?.tracking_url + carrierReference"
                  :label="carrierReference"
                  blank
                  noopener
                  noreferer
                />
                <div v-else>-</div>
              </div>
            </div>
          </div>
          <div class="flex-1">
            <mini-shipment-charge-table
              v-if="createdShipment"
              height="206px"
              :shipment="createdShipment"
              :charges="calculatedCharge"
            ></mini-shipment-charge-table>
          </div>
        </div>
      </q-step>

      <template #navigation>
        <q-stepper-navigation class="create-shipment-stepper-v2-navigation">
          <shipvagoo-button
            color="brand"
            class="create-shipment-stepper-v2-navigation-previous"
            v-if="step > 1 && step < 5"
            label="Back"
            outline
            no-caps
            @click="onClickPrevious"
          />
          <shipvagoo-button
            color="brand"
            v-if="step <= 3"
            class="create-shipment-stepper-v2-navigation-next"
            :label="$t('common.next')"
            no-caps
            @click="onClickNext"
            :disable="disableNextButton"
          />
          <shipvagoo-button
            v-if="step === 4"
            @click="onClickPay"
            class="create-shipment-stepper-v2-navigation-next"
            label="Pay"
            :disable="!steps.four.isTermsAndConditionsChecked"
            no-caps
          />
          <shipvagoo-button
            v-if="step === 5"
            outline
            class="create-shipment-stepper-v2-navigation-next"
            icon="fas fa-download"
            label="Download label"
            @click="onClickDownloadLabel(false)"
            :disable="!generatedLabel || paymentStatus != 'success'"
            no-caps
          />
          <shipvagoo-button
            v-if="step === 5"
            @click="onClickDefaultPrintLabel"
            outline
            class="create-shipment-stepper-v2-navigation-next"
            icon="fas fa-print"
            label="Print label"
            :disable="!generatedLabel || paymentStatus != 'success'"
            no-caps
          />

          <shipvagoo-button
            v-if="step === 5"
            class="create-shipment-stepper-v2-navigation-next"
            label="Done"
            v-close-popup
            no-caps
          />
        </q-stepper-navigation>
      </template>
    </shipvagoo-stepper>
  </shipvagoo-modal>

  <q-dialog v-model="isGlsRequirementsOpen">
    <gls-requirements />
  </q-dialog>

  <q-dialog v-model="isDaoRequirementsOpen">
    <dao-requirements />
  </q-dialog>
  <q-dialog v-model="showAdditionalServicesDialog">
    <additional-services />
  </q-dialog>
  <q-dialog persistent no-backdrop-dismiss no-esc-dismiss v-model="isAddFundModalOpen">
    <add-balance @close="onCloseAddFunds" />
  </q-dialog>
</template>

<script setup lang="ts">
  import useVuelidate, { ValidationArgs } from '@vuelidate/core';
  import { helpers, required, requiredIf } from '@vuelidate/validators';
  import {
    ComponentPublicInstance,
    computed,
    onBeforeMount,
    onBeforeUnmount,
    ref,
    watch,
  } from 'vue';
  import * as _ from 'lodash-es';
  import { euMember } from 'is-european';
  //import useWidth from '../hooks/useWidth';
  import { ICarrier, IShippingProduct, IWeightClass } from '../model/carrier.model';
  import { ICountry, ISOCountries } from '../model/country.model';
  import { IServicePointSearch } from '../model/common.model';
  import {
    IAdditionalServiceCharge,
    ICalculateChargeResponse,
    IParcelShop,
    IParcelShopSingleResponse,
    IShipment,
    IShipmentUpdateRequest,
    ICreateNewShipmentForm,
  } from '../model/shipment.model';
  import {
    addressBookService,
    carrierService,
    loadingService,
    loggingService,
    mapService,
    notificationService,
    printJobService,
    shipmentService,
  } from '../services/_singletons';
  import { useStore } from '../store';
  import { IAddressBookCreate, IAddressBookRecord } from '../model/address-book.model';
  import WindowService from '../services/window.service';
  import HelperService from '../services/helper.service';
  import PriceService from '../services/price.service';
  import postCodes from '../assets/json/postcodes.json';
  import { useHelper } from '../composeable/Helper';
  import { ITabOption } from '../model/common.model';
  import ShipmentService from '../services/shipment.service';

  const {
    handleBeforeUnload,
    denmark,
    carrierInfoLink,
    getDialCode,
    nineDigitsMobileForMontyProducts,
    phoneRequiredForMontyProducts,
    minFourAndMaxFourteenDigitsMobileForMontyProducts,
    mustBeValidName,
    mustBeValidEmail,
    mustBeValidMobileLength,
  } = useHelper();
  const $store = useStore();
  //const windowWidth = useWidth();
  let windowRef: Window | null;
  const tracking_email_as_code = 3730866037;
  const tracking_sms_as_code = 3333128889;
  //const carrier_email_as_code = 1097535283;
  const carrier_sms_as_code = 270943235;

  /** ref properties **/
  const getLabelInterval = ref<ReturnType<typeof setInterval> | null>(null);
  const generatedLabel = ref<string | null>(null);
  const paymentStatus = ref<string>('pending');
  const stepOneTabOptions = ref<ITabOption[]>([
    {
      label: 'Sender',
      value: 'sender',
    },
    {
      label: 'Recipient',
      value: 'recipient',
    },
  ]);
  const stepOneTabValue = ref<string>('recipient');
  const step = ref<number>(1);
  const steps = ref<ICreateNewShipmentForm>({
    one: {
      sender: {
        name: null,
        email: null,
        phone: null,
        zipCode: null,
        address1: null,
        address2: null,
        city: null,
        senderCountry: null,
      },
      recipient: {
        name: null,
        email: null,
        phone: null,
        zipCode: null,
        address1: null,
        address2: null,
        city: null,
        receiverCountry: null,
      },
    },
    two: {
      carrier: null,
      shippingProduct: null,
      weightClass: null,
      isChooseClosestServicePointChecked: false,
      servicePoint: null,
      productWeight: null,
    },
    three: {
      additionalServices: {},
    },
    four: {
      payWith: 0,
      isTermsAndConditionsChecked: false,
    },
  });
  const isGlsRequirementsOpen = ref<boolean>(false);
  const isDaoRequirementsOpen = ref<boolean>(false);
  const isAddFundModalOpen = ref<boolean>(false);
  const showAdditionalServicesDialog = ref<boolean>(false);
  const createdShipment = ref<IShipment | null>(null);
  const addressBookRecords = ref<IAddressBookRecord[]>([]);
  const additionalServiceCharges = ref<IAdditionalServiceCharge[]>([]);
  const carriers = ref<ICarrier[]>([]);
  const productWithClientIds = ref<string>('{}');
  const shippingProducts = ref<IShippingProduct[]>([]);
  const stepper = ref<ComponentPublicInstance<{ next: () => void; previous: () => void }> | null>(
    null,
  );
  const saveRecipientContactCheckbox = ref<boolean>(false);
  //const saveRecipientContactCheckbox = ref<boolean>(false);
  const weightClasses = ref<IWeightClass[]>([]);
  const servicePoints = ref<(IParcelShop & { label: string })[]>([]);
  const actualServicePoints = ref<(IParcelShop & { label: string })[]>([]);
  /** important, commented now **/
  const servicePointSearch = ref<IServicePointSearch>({
    address: null,
    zipCode: null,
    city: null,
  });
  const calculatedCharge = ref<ICalculateChargeResponse | null>(null);
  const bookedShipment = ref<IShipment | null>(null);
  const carrierCode = ref<number | null>();
  const shippingProductCode = ref<number | null>();
  const weightClassCode = ref<number | null>();
  const receiverCountriesOptions = ref(
    $store.state.countryStore.countries.filter((c) => euMember(c.iso)),
  );
  //const isTrackingMessageSet = ref<boolean>(true); //means checkbox is unchecked first time
  /** ref properties end **/

  /** watchers **/

  /** this watcher is implemented to fix the issue when select saved name with arrow down button. it stored the whole object in name**/
  watch(
    () => steps.value.one.recipient.name,
    (currentVal: any) => {
      if (typeof currentVal == 'object') {
        steps.value.one.recipient.name = currentVal?.name ?? '';
      }
    },
  );
  watch(
    () => steps.value.one.sender.name,
    (currentVal: any) => {
      if (typeof currentVal == 'object') {
        steps.value.one.sender.name = currentVal?.name ?? '';
      }
    },
  );
  watch(
    () => steps.value.one.recipient.phone,
    (currentValue) => {
      const asCharge = additionalServiceCharges.value.find(
        (item) => item.additional_service_code == carrier_sms_as_code,
      );
      if (asCharge && !$v.value.one.recipient.phone.$error) {
        steps.value.three.additionalServices[asCharge.as_charge_code] = !!currentValue;
      } else if (asCharge) {
        steps.value.three.additionalServices[asCharge.as_charge_code] = false;
      }
    },
  );
  /** these are self defined due to limit types of radios card **/
  watch(
    () => carrierCode.value,
    (currentVal) => {
      if (currentVal) {
        steps.value.two.carrier = getCarrier(currentVal);
      } else {
        steps.value.two.carrier = null;
      }
    },
  );
  watch(
    () => shippingProductCode.value,
    (currentVal) => {
      if (currentVal) {
        steps.value.two.shippingProduct = getShippingProduct(currentVal);
      } else {
        steps.value.two.shippingProduct = null;
      }
    },
  );
  watch(
    () => weightClassCode.value,
    (currentVal) => {
      if (currentVal) {
        steps.value.two.weightClass = getWeightClass(currentVal);
      } else {
        steps.value.two.weightClass = null;
      }
    },
  );

  /** computed properties start **/
  const disableNextButton = computed<boolean>(() => {
    if (loadingService.loadings.size > 0) return true;

    if (step.value === 2) {
      const weightClass = steps.value.two?.weightClass;
      console.log('Weight Class: ', weightClass);
      console.log('Product Weight: ', steps.value.two?.productWeight);
      console.log('Max Weight: ', weightClass?.max_weight);
      return (
        !!weightClass && (steps.value.two?.productWeight ?? 0) > Number(weightClass.max_weight)
      );
    }

    return false;
  });
  const carrierReference = computed(() => {
    return bookedShipment.value?.parcels[0].carrierReference;
  });
  const receiverCountries = computed(() => {
    return $store.state.countryStore.countries.filter((c) => euMember(c.iso));
  });

  /** important, commented for now **/
  /*const senderPostCodeLabel = computed(() => {
    if (!senderPostCode.value) {
      return null;
    }
    return `(${senderPostCode.value.format})`;
  });
  const senderPostCode = computed(() => {
    if (!steps.value.one.sender.senderCountry) {
      return null;
    }
    const postcodeCountry = _.get(postCodes, steps.value.one.sender.senderCountry.iso, null);
    if (!postcodeCountry) {
      return null;
    }
    return postCodes[steps.value.one.sender.senderCountry.iso];
  });*/
  /** computed properties end **/

  /** validations start **/
  const mustBeValidInput = helpers.withParams({ type: 'validNumber' }, (value: any) => {
    if (value && value.length > 0) {
      const regex = /^[0-9]*$/;
      return regex.test(value as string);
    } else {
      return true;
    }
  });

  const $v = useVuelidate<ICreateNewShipmentForm, ValidationArgs<ICreateNewShipmentForm>>(
    {
      one: {
        sender: {
          name: {
            required: helpers.withMessage('This field is required.', required),
            mustBeValidName: helpers.withMessage(
              'First and last name is required',
              mustBeValidName,
            ),
          },
          email: {
            required: helpers.withMessage('This field is required.', required),
            email: helpers.withMessage('This field must be a valid email.', (value: any) =>
              mustBeValidEmail(value, steps.value.two?.carrier?.carrier_id),
            ), // email: helpers.withMessage('This field must be a valid email.', email),
          },
          phone: {
            required: helpers.withMessage('This field is required.', required),

            /*  mustBeValidMobileLength: helpers.withMessage(
              () => {
                const montyProduct = steps.value.two?.shippingProduct?.product_id;
                if (nineDigitsMobileForMontyProducts[montyProduct ?? '']) {
                  return 'Mobile number must consist of 9 digits';
                }

                if (minFourAndMaxFourteenDigitsMobileForMontyProducts[montyProduct ?? '']) {
                  return 'Mobile number must consist of 4 to 40 digits.';
                }

                return 'Invalid mobile number length.';
              },
              (value: any) =>
                mustBeValidMobileLength(value, steps.value.two?.shippingProduct?.product_id),
            ), */
            /* Commented on Abdul QA Request */
          },
          zipCode: {
            required: helpers.withMessage('This field is required.', required),
          },
          address1: {
            required: helpers.withMessage('This field is required.', required),
          },
          address2: {},
          city: {
            required: helpers.withMessage('This field is required.', required),
          },
          senderCountry: {
            required: helpers.withMessage('This field is required.', required),
          },
        },
        recipient: {
          name: {
            required: helpers.withMessage('This field is required.', required),
            mustBeValidName: helpers.withMessage(
              'First and last name is required.',
              mustBeValidName,
            ),
          },
          email: {
            required: helpers.withMessage('This field is required.', required),
            email: helpers.withMessage('This field must be a valid email.', (value: any) =>
              mustBeValidEmail(value, steps.value.two?.carrier?.carrier_id),
            ),
          },
          phone: {
            required: helpers.withMessage(
              'Mobile number is mandatory.',
              requiredIf(() => {
                return isRequiredReceiverPhone();
              }),
            ),
            mustBeValidInput: helpers.withMessage(
              'Enter a valid mobile number only with digits.',
              mustBeValidInput,
            ),
            mustBeValidMobileLength: helpers.withMessage(
              () => {
                const montyProduct = steps.value.two?.shippingProduct?.product_id;
                if (nineDigitsMobileForMontyProducts[montyProduct ?? '']) {
                  return 'Mobile number must consist of 9 digits';
                }

                if (minFourAndMaxFourteenDigitsMobileForMontyProducts[montyProduct ?? '']) {
                  return 'Mobile number must consist of 4 to 40 digits.';
                }

                return 'Invalid mobile number length.';
              },
              (value: any) =>
                mustBeValidMobileLength(value, steps.value.two?.shippingProduct?.product_id),
            ),
          },
          zipCode: {
            required: helpers.withMessage('This field is required.', required),
          },
          address1: {
            required: helpers.withMessage('This field is required.', required),
          },
          address2: {},
          city: {
            required: helpers.withMessage('This field is required.', required),
          },
          receiverCountry: {
            required: helpers.withMessage('This field is required.', required),
          },
        },
      },
      two: {
        carrier: {
          required: helpers.withMessage('This field is required.', required),
        },
        shippingProduct: {
          required: helpers.withMessage('This field is required.', required),
        },
        weightClass: {
          required: helpers.withMessage('This field is required.', required),
        },
        isChooseClosestServicePointChecked: {},
        servicePoint: {
          required: helpers.withMessage(
            'This field is required.',
            requiredIf(() => steps.value.two.shippingProduct?.hasMultiParcelShop ?? false),
          ),
          /*required: helpers.withMessage(
            'This field is required.',
            requiredIf(() => !steps.value.two.isChooseClosestServicePointChecked),
          ),*/
        },
      },
      three: {
        additionalServices: {},
      },
      four: {
        payWith: {},
        isTermsAndConditionsChecked: {},
      },
    },
    steps,
  );
  /** validations end **/

  /** functions start here **/

  /**
   * @input-value="onInputReceiverCountry"
   **/
  /*const onInputReceiverCountry = (val: string) => {
    const needle = val.toLowerCase()
    steps.value.one.recipient.receiverCountry = receiverCountries.value.find((country: ICountry) => {
      return country.name.toLowerCase().indexOf(needle) > -1
    }) ?? null
    if (steps.value.one.recipient.receiverCountry) {
      onChangeCountryOnStep1()
    }
  }*/
  /*const onChangeTrackingEmail = async (value: boolean) => {
    if (!createdShipment.value) {
      return;
    }
    if (!value) {
      isTrackingMessageSet.value = true;
    }
    if (value) {
      try {
        loadingService.startLoading('main-loader:check-tracking-email');
        await shipmentService.checkTrackingEmail(
          createdShipment.value?.shipment_code,
          {
            additional_services: Object.entries(steps.value.three.additionalServices)
              .filter(([, value]) => value)
              .map(([key]) => Number(key))
          }
        )
      } catch (error: any) {
        if (error.response.data?.error) {
          isTrackingMessageSet.value = false;
          notificationService.showError(error.response.data?.error)
        }
      } finally {
        loadingService.stopLoading('main-loader:check-tracking-email');
      }
    }
  }*/

  const getCarrier = (carrier_code: number): ICarrier | null => {
    return carriers.value.find((carrier: ICarrier) => carrier.carrier_code == carrier_code) ?? null;
  };
  const getShippingProduct = (shipping_product_code: number): IShippingProduct | null => {
    return (
      shippingProducts.value.find(
        (shipping_product: IShippingProduct) =>
          shipping_product.carrier_product_code == shipping_product_code,
      ) ?? null
    );
  };
  const getWeightClass = (weight_class_code: number): IWeightClass | null => {
    return (
      weightClasses.value.find(
        (weightClass: IWeightClass) => weightClass.weight_class_code == weight_class_code,
      ) ?? null
    );
  };
  const onUpdateShipment = async () => {
    if (
      !createdShipment.value ||
      !steps.value.two.shippingProduct ||
      !steps.value.one.sender.senderCountry ||
      !steps.value.one.recipient.receiverCountry ||
      !steps.value.one.sender.name ||
      !steps.value.one.sender.address1 ||
      !steps.value.one.sender.city ||
      !steps.value.one.sender.zipCode ||
      !steps.value.one.sender.phone ||
      !steps.value.one.sender.email ||
      !steps.value.one.recipient.name ||
      !steps.value.one.recipient.address1 ||
      !steps.value.one.recipient.city ||
      !steps.value.one.recipient.zipCode ||
      !steps.value.one.recipient.email
    ) {
      return;
    }

    const senderName = HelperService.parseFullName(steps.value.one.sender.name);
    const receiverName = HelperService.parseFullName(steps.value.one.recipient.name);
    if (!senderName.first || !senderName.last) {
      notificationService.showError(
        `Sender name ${steps.value.one.sender.name} is not valid. Please enter a valid name for sender name.`,
      );
      return;
    }

    if (!receiverName.first || !receiverName.last) {
      notificationService.showError(
        `Receiver name ${steps.value.one.recipient.name} is not valid. Please enter a valid name for receiver name.`,
      );
      return;
    }
    if (
      steps.value.two.servicePoint == null &&
      steps.value.two.shippingProduct.hasMultiParcelShop
    ) {
      notificationService.showError(`Parcel shop is not available, please contact Shipvagoo.`);
      return;
    }

    try {
      loadingService.startLoading('main-loader:update-shipment');

      let payload: IShipmentUpdateRequest = _.omit(createdShipment.value, [
        'shipment_code',
        'created_at',
        'updated_at',
      ]);

      if (createdShipment.value.status == 'DRAFT' && createdShipment.value.order_code) {
        payload = {
          ...payload,
          delivery_template_code: null,
        };
      }
      const product_with_client_ids = JSON.parse(JSON.stringify(productWithClientIds.value));
      const ubsend_client_id =
        product_with_client_ids[steps.value.two.shippingProduct.product_id] !== undefined
          ? product_with_client_ids[steps.value.two.shippingProduct.product_id]
          : '';
      console.log('Payload ', payload);
      payload = {
        ...payload,
        ubsend_client_id: ubsend_client_id,
        carrier_product_code: steps.value.two.shippingProduct.carrier_product_code,
        sender_country_code: steps.value.one.sender.senderCountry.country_code,
        receiver_country_code: steps.value.one.recipient.receiverCountry.country_code,
        sender: {
          firstName: `${senderName.first} ${senderName.middle}`,
          lastName: senderName.last,
          addressLine1: steps.value.one.sender.address1,
          addressLine2: steps.value.one.sender.address2,
          postCode: steps.value.one.sender.zipCode,
          city: steps.value.one.sender.city,
          country: steps.value.one.sender.senderCountry.iso,
          contactInfo: {
            dialCode: getDialCode(steps.value.one.sender.senderCountry.iso, false),
            telephone: steps.value.one.sender.phone,
            email: steps.value.one.sender.email,
          },
        },
        recipient: {
          parcelShopId: steps.value.two.servicePoint?.id,
          firstName: `${receiverName.first} ${receiverName.middle}`,
          lastName: receiverName.last,
          addressLine1: steps.value.one.recipient.address1,
          addressLine2: steps.value.one.recipient.address2,
          postCode: steps.value.one.recipient.zipCode,
          city: steps.value.one.recipient.city,
          country: steps.value.one.recipient.receiverCountry.iso,
          contactInfo: {
            dialCode: getDialCode(steps.value.one.recipient.receiverCountry.iso, false),
            telephone: steps.value.one.recipient.phone ? steps.value.one.recipient.phone : '',
            email: steps.value.one.recipient.email,
          },
          parcelShopTitle: steps.value.two.servicePoint
            ? `${steps.value.two.servicePoint.carrier} PakkeShop ${steps.value.two.servicePoint.id} - ${steps.value.two.servicePoint.name}`
            : undefined,
        },
        parcels: [
          {
            count: 1,
            weight: parseFloat(steps.value.two.weightClass?.max_weight?.toString() || '0'),
            height: 1,
            width: 1,
            length: 1,
            description: `Parcel ${steps.value.two.weightClass?.min_weight} - ${steps.value.two.weightClass?.max_weight} ${steps.value.two.weightClass?.unit}`,
          },
        ],
        additional_services: Object.entries(steps.value.three.additionalServices)
          .filter(([, value]) => value)
          .map(([key]) => Number(key)),
      };

      const shipment: IShipment = await shipmentService.updateShipment(
        createdShipment.value.shipment_code,
        payload,
      );

      createdShipment.value = shipment;

      notificationService.showSuccess('Shipment draft updated successfully.');
      $store.dispatch('shipmentStore/refreshDrafts');
      const charge = await shipmentService.calculateShipmentCharge(shipment.shipment_code);
      calculatedCharge.value = charge;
      stepper.value?.next();
    } catch (error: any) {
      if (error.response?.data?.error) {
        const e = error.response?.data?.error;
        if (e['sender.addressLine1']) {
          notificationService.showError(e['sender.addressLine1'][0]);
        } else if (e['sender.addressLine2']) {
          notificationService.showError(e['sender.addressLine2'][0]);
        } else if (e['recipient.addressLine1']) {
          notificationService.showError(e['recipient.addressLine1'][0]);
        } else if (e['recipient.addressLine2']) {
          notificationService.showError(e['recipient.addressLine2'][0]);
        } else {
          notificationService.showError(e);
        }
      } else {
        notificationService.showError('Error updating shipment');
      }
      console.log('ERROR:: ', error);
      loggingService.error('Error updating shipment', error);
    } finally {
      loadingService.stopLoading('main-loader:update-shipment');
    }
  };

  const onCreateShipment = async () => {
    if (
      !steps.value.two.shippingProduct ||
      !steps.value.one.sender.senderCountry ||
      !steps.value.one.sender.name ||
      !steps.value.one.sender.address1 ||
      !steps.value.one.sender.city ||
      !steps.value.one.sender.zipCode ||
      !steps.value.one.sender.phone ||
      !steps.value.one.sender.email ||
      !steps.value.one.recipient.receiverCountry ||
      !steps.value.one.recipient.name ||
      !steps.value.one.recipient.address1 ||
      !steps.value.one.recipient.city ||
      !steps.value.one.recipient.zipCode ||
      !steps.value.one.recipient.email
    ) {
      return;
    }

    const senderName = HelperService.parseFullName(steps.value.one.sender.name);
    const receiverName = HelperService.parseFullName(steps.value.one.recipient.name);
    if (!senderName.first || !senderName.last) {
      notificationService.showError(
        `Sender name ${steps.value.one.sender.name} is not valid. Please enter a valid name for sender name.`,
      );
      return;
    }

    if (!receiverName.first || !receiverName.last) {
      notificationService.showError(
        `Receiver name ${steps.value.one.recipient.name} is not valid. Please enter a valid name for receiver name.`,
      );
      return;
    }
    if (
      steps.value.two.servicePoint == null &&
      steps.value.two.shippingProduct.hasMultiParcelShop
    ) {
      notificationService.showError(`Parcel shop is not available, please contact Shipvagoo.`);
      return;
    }
    try {
      loadingService.startLoading('main-loader:create-shipment');
      const product_with_client_ids = JSON.parse(JSON.stringify(productWithClientIds.value));
      const ubsend_client_id =
        product_with_client_ids[steps.value.two.shippingProduct.product_id] !== undefined
          ? product_with_client_ids[steps.value.two.shippingProduct.product_id]
          : '';
      await shipmentService
        .createShipment({
          ubsend_client_id,
          carrier_product_code: steps.value.two.shippingProduct.carrier_product_code,
          sender_country_code: steps.value.one.sender.senderCountry.country_code,
          receiver_country_code: steps.value.one.recipient.receiverCountry.country_code,
          sender: {
            firstName: `${senderName.first} ${senderName.middle}`,
            lastName: senderName.last,
            addressLine1: steps.value.one.sender.address1,
            addressLine2: steps.value.one.sender.address2,
            postCode: steps.value.one.sender.zipCode,
            city: steps.value.one.sender.city,
            country: steps.value.one.sender.senderCountry.iso,
            contactInfo: {
              dialCode: getDialCode(steps.value.one.sender.senderCountry.iso, false),
              telephone: steps.value.one.sender.phone,
              email: steps.value.one.sender.email,
            },
          },
          recipient: {
            parcelShopId: steps.value.two.servicePoint?.id,
            firstName: `${receiverName.first} ${receiverName.middle}`,
            lastName: receiverName.last,
            addressLine1: steps.value.one.recipient.address1,
            addressLine2: steps.value.one.recipient.address2,
            postCode: steps.value.one.recipient.zipCode,
            city: steps.value.one.recipient.city,
            country: steps.value.one.recipient.receiverCountry.iso,
            contactInfo: {
              dialCode: getDialCode(steps.value.one.recipient.receiverCountry.iso, false),
              telephone: steps.value.one.recipient.phone ? steps.value.one.recipient.phone : '',
              email: steps.value.one.recipient.email,
            },
            parcelShopTitle: steps.value.two.servicePoint
              ? `${steps.value.two.servicePoint.carrier} PakkeShop ${steps.value.two.servicePoint.id} -${steps.value.two.servicePoint.name}`
              : null,
          },
          parcels: [
            {
              count: 1,
              weight: parseFloat(steps.value.two.weightClass?.max_weight?.toString() || '0'),
              height: 1,
              width: 1,
              length: 1,
              description: `Parcel ${steps.value.two.weightClass?.min_weight} - ${steps.value.two.weightClass?.max_weight} ${steps.value.two.weightClass?.unit}`,
            },
          ],
          /** commented now **/
          /* parcels: [
           ...steps.value.one.parcels.map(
             (p: IFormParcel): IShipmentParcel => ({
               count: p.quantity as number,
               weight: parseFloat(p.weightClass?.max_weight || '0'),
               height: 1,
               width: 1,
               length: 1,
               description: `Parcel ${p.weightClass?.min_weight} - ${p.weightClass?.max_weight} ${p.weightClass?.unit}`,
             }),
           ),
         ],*/
          additional_services: Object.entries(steps.value.three.additionalServices)
            .filter(([, value]) => value)
            .map(([key]) => Number(key)),
          fulfillment_code: $store.state.shipmentStore.fulfillmentCode,
        })
        .then(async (response) => {
          createdShipment.value = response;
          notificationService.showSuccess('Shipment saved as a draft');
          $store.dispatch('shipmentStore/refreshDrafts');
          const charge = await shipmentService.calculateShipmentCharge(response.shipment_code);
          calculatedCharge.value = charge;
          stepper.value?.next();
        })
        .catch((error) => {
          if (error.response.data?.error) {
            const e = error.response.data?.error;
            if (e['sender.addressLine1']) {
              notificationService.showError(e['sender.addressLine1'][0]);
            } else if (e['sender.addressLine2']) {
              notificationService.showError(e['sender.addressLine2'][0]);
            } else if (e['recipient.addressLine1']) {
              notificationService.showError(e['recipient.addressLine1'][0]);
            } else if (e['recipient.addressLine2']) {
              notificationService.showError(e['recipient.addressLine2'][0]);
            } else {
              notificationService.showError(e);
            }
          }
        });
    } catch (e) {
      console.log('Error ', e);
      notificationService.showError('Error creating shipment');
      loggingService.error('Error creating shipment', e);
    } finally {
      loadingService.stopLoading('main-loader:create-shipment');
    }
  };

  /** important, commented for now **/
  /* const onClickSearchServicePoint = async () => {
      if (
        !steps.value.one.recipient.receiverCountry ||
        !servicePointSearch.value.address ||
        !servicePointSearch.value.zipCode ||
        !steps.value.two.carrier
      ) {
        return;
      }

      try {
        loadingService.startLoading('main-loader:service-points');
        await getNearestServicePoints(
          steps.value.two.carrier!.carrier_id,
          steps.value.one.recipient.receiverCountry!.iso,
          servicePointSearch.value.address,
          servicePointSearch.value.zipCode,
          servicePointSearch.value.city,
        );
        //steps.value.one.recipient.city
      } catch (e) {
        servicePoints.value = [];
        notificationService.showError('No service points found');
      } finally {
        loadingService.stopLoading('main-loader:service-points');
      }
    }; */
  const extractDistance = (distance: any) => {
    return `(${Number(distance / 1000).toFixed(1)} km)`;
  };

  const filterList = (list: any, value: string, key: string) => {
    if (value === '') return list;
    const needle = value.toLowerCase();
    return list.filter((item: any) => item[key].toLowerCase().includes(needle));
  };
  const filterParcelShops = (val: any, update: CallableFunction) => {
    update(() => {
      servicePoints.value = filterList(actualServicePoints.value, val, 'label');
    });
  };
  const onFilterReceiverCountries = (val: string, update: CallableFunction) => {
    update(() => {
      receiverCountriesOptions.value = filterList(receiverCountries.value, val, 'name');
    });
  };
  const setFirstServicePoint = () => {
    const [firstServicePoint] = servicePoints.value;
    if (firstServicePoint) {
      steps.value.two.servicePoint = firstServicePoint;
    } else {
      steps.value.two.servicePoint = null;
    }
  };
  const getNearestServicePoints = async (
    carrier_id: any,
    country_code: string,
    address: any,
    zip_code: any,
    city: any,
  ) => {
    try {
      loadingService.startLoading('main-loader:service-points');
      servicePointSearch.value = {
        address: address,
        zipCode: zip_code,
        city: city,
      };
      const response = await shipmentService.getParcelShops(
        carrier_id,
        country_code,
        address,
        zip_code,
        city,
      );
      servicePoints.value = [];
      servicePoints.value = response.map((parcelShop: IParcelShopSingleResponse) => ({
        ...parcelShop.parcelShop,
        distance: extractDistance(parcelShop.distance),
        label: `${parcelShop.parcelShop.name} - ${parcelShop.parcelShop.addressLine1}, Pakkeshop:${
          parcelShop.parcelShop.id
        }, ${parcelShop.parcelShop.country}-${parcelShop.parcelShop.postCode}, ${
          parcelShop.parcelShop.city
        } (${(parcelShop.distance / 1000).toFixed(1)} km)`,
      }));
      actualServicePoints.value = servicePoints.value;
      if (steps.value.two.servicePoint != null) {
        const sp = servicePoints.value.find((item) => item.id == steps.value.two.servicePoint?.id);
        if (sp) {
          steps.value.two.servicePoint = sp;
        } else {
          setFirstServicePoint();
        }
      } else {
        setFirstServicePoint();
      }
    } finally {
      loadingService.stopLoading('main-loader:service-points');
    }
  };
  const getWeightClasses = async (shippingProduct: IShippingProduct | null) => {
    if (
      !shippingProduct ||
      !steps.value.one.sender.senderCountry ||
      !steps.value.one.recipient.receiverCountry
    ) {
      return;
    }
    weightClasses.value = [];
    try {
      const product_with_client_ids = JSON.parse(JSON.stringify(productWithClientIds.value));
      const ubsend_client_id =
        product_with_client_ids[shippingProduct.product_id] !== undefined
          ? product_with_client_ids[shippingProduct.product_id]
          : '';
      loadingService.startLoading('main-loader:get-weight-classes');
      const response = await shipmentService.getWeightClasses(
        shippingProduct.carrier_product_code,
        steps.value.one.sender.senderCountry.country_code,
        steps.value.one.recipient.receiverCountry.country_code,
        ubsend_client_id,
      );
      weightClasses.value = response;
    } catch (e) {
      console.log('Error ', e);
      notificationService.showError('Error getting weight classes');
      loggingService.error('Error getting weight classes', e);
    } finally {
      loadingService.stopLoading('main-loader:get-weight-classes');
    }
  };
  const getAdditionalServices = async (shippingProduct: IShippingProduct | null) => {
    if (
      !steps.value.one.sender.senderCountry ||
      !steps.value.one.recipient.receiverCountry ||
      !shippingProduct
    ) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:get-additional-services');
      additionalServiceCharges.value = await shipmentService.getAdditionalServiceCharges(
        steps.value.one.sender.senderCountry.country_code,
        steps.value.one.recipient.receiverCountry.country_code,
        shippingProduct.carrier_product_code,
      );
      /** removed the splice asCharge for fourth row,previously we hiding tracking sms, now it will be disabled **/

      /** Tracking email and tracking sms shows only**/
      /* additionalServiceCharges.value = additionalServiceCharges.value.filter(asCharges => {
         return (asCharges.additional_service_code == tracking_email || asCharges.additional_service_code == tracking_sms)
       })*/
      steps.value.three.additionalServices = {};
      hydrateAdditionalServices();
      // }
    } catch (e) {
      loggingService.error('Error getting additional services', e);
      notificationService.showError('Error getting additional services');
    } finally {
      loadingService.stopLoading('main-loader:get-additional-services');
    }
  };
  const hydrateAdditionalServices = () => {
    const [firstAdditionalService] = additionalServiceCharges.value || [];

    /** fulfillmentCode come from return fulfillment from orders. **/
    /*if ((!$store.state.shipmentStore.createShipmentPredefinedValues || $store.state.shipmentStore.fulfillmentCode)
      && firstAdditionalService) {*/
    steps.value.three.additionalServices[firstAdditionalService.as_charge_code] = true; //carrier email
    if (steps.value.one.recipient.phone) {
      const asCharge = additionalServiceCharges.value.find(
        (item) => item.additional_service_code == carrier_sms_as_code,
      );
      if (asCharge) {
        steps.value.three.additionalServices[asCharge.as_charge_code] = true;
      }
    }

    /** this is related to checkbox of tracking email **/
    /*const trackingMessageAS: IAdditionalServiceCharge | null | undefined = additionalServiceCharges.value?.find(item => item.additional_service_code == 3730866037);
    if (trackingMessageAS) {
      if (steps.value.three.additionalServices[trackingMessageAS.as_charge_code]) {
        console.log("Tracking Message ", steps.value.three.additionalServices)
        onChangeTrackingEmail(true);
      }
    }*/
  };
  const onChangeShippingProduct = async (shipping_product_code: number | null) => {
    if (!shipping_product_code) {
      return;
    }
    const shippingProduct = await getShippingProduct(shipping_product_code);
    steps.value.two.weightClass = null;
    weightClassCode.value = null;
    await Promise.all([getAdditionalServices(shippingProduct), getWeightClasses(shippingProduct)]);
    /** commented now **/
    //seems useless, because parcel quantity will be 1 hardcode
    /*steps.value.one.parcels = steps.value.one.parcels.map((p) => {
      return {
        ...p,
        weightClass: null,
      };
    });
    if (steps.value.one.parcels.length <= 0) {
      addParcels();
    }*/
  };

  const onChangeCarrier = (carrier_code: number) => {
    const carrier = getCarrier(carrier_code);
    steps.value.two.shippingProduct = null;
    steps.value.two.weightClass = null;
    shippingProductCode.value = null;
    weightClassCode.value = null;
    weightClasses.value = [];
    if (carrier == null) {
      return;
    }
    shippingProducts.value = carrier?.products ?? [];

    /** implemented now **/
    getNearestServicePoints(
      carrier.carrier_id as any,
      steps.value.one.recipient.receiverCountry!.iso,
      steps.value.one.recipient.address1,
      steps.value.one.recipient.zipCode,
      steps.value.one.recipient.city,
    );
  };

  const onSaveContact = async (value: boolean, type: 'sender' | 'recipient') => {
    if (!type) {
      return;
    }

    if ((type == 'sender' && !value) || (type == 'recipient' && !value)) {
      return;
    }

    let address: IAddressBookCreate | null = null;
    if (type === 'sender') {
      await $v.value.one.sender.$validate();

      if (
        $v.value.one.sender.$error ||
        !steps.value.one.sender.name ||
        !steps.value.one.sender.address1 ||
        !steps.value.one.sender.city ||
        !steps.value.one.sender.zipCode ||
        //!steps.value.two.phone ||
        !steps.value.one.sender.email ||
        !steps.value.one.sender.senderCountry
      ) {
        return;
      }

      const country = $store.state.countryStore.countries.find(
        (c: ICountry) => c.iso === steps.value.one.sender.senderCountry?.iso,
      ) as ICountry;

      address = {
        name: steps.value.one.sender.name,
        attn: '',
        address: steps.value.one.sender.address1,
        address2: steps.value.one.sender.address2,
        city: steps.value.one.sender.city,
        zipcode: steps.value.one.sender.zipCode,
        mobile: steps.value.one.sender.phone,
        email: steps.value.one.sender.email,
        country_code: country.country_code,
        is_default: false,
      };
    }

    if (type === 'recipient') {
      await $v.value.one.recipient.$validate();

      if (
        $v.value.one.recipient.$error ||
        !steps.value.one.recipient.name ||
        !steps.value.one.recipient.address1 ||
        !steps.value.one.recipient.city ||
        !steps.value.one.recipient.zipCode ||
        // !steps.value.three.phone ||
        !steps.value.one.recipient.email ||
        !steps.value.one.recipient.receiverCountry
      ) {
        return;
      }

      const country = $store.state.countryStore.countries.find(
        (c: ICountry) => c.iso === steps.value.one.recipient.receiverCountry?.iso,
      ) as ICountry;

      address = {
        name: steps.value.one.recipient.name,
        attn: '',
        address: steps.value.one.recipient.address1,
        address2: steps.value.one.recipient.address2,
        city: steps.value.one.recipient.city,
        zipcode: steps.value.one.recipient.zipCode,
        mobile: steps.value.one.recipient.phone,
        email: steps.value.one.recipient.email,
        country_code: country.country_code,
        is_default: false,
      };
    }

    if (!address) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:save-address');
      await addressBookService.createAddressBookRecord(address);
      notificationService.showSuccess('Address saved successfully');
      getAddressBookRecords();
    } catch (e) {
      notificationService.showError('Error saving address');
      loggingService.error('Error saving address', e);
    } finally {
      loadingService.stopLoading('main-loader:save-address');
    }
  };

  const onSelectReceiver = async (addressBookRecord: IAddressBookRecord) => {
    resetReceiver();
    $v.value.one.recipient.$reset();

    steps.value.one.recipient.name = addressBookRecord.name || null;
    steps.value.one.recipient.email = addressBookRecord.email || null;
    steps.value.one.recipient.phone = addressBookRecord.mobile || null;
    steps.value.one.recipient.zipCode = addressBookRecord.zipcode || null;
    steps.value.one.recipient.address1 = addressBookRecord.address || null;
    steps.value.one.recipient.address2 = addressBookRecord.address2 || null;
    steps.value.one.recipient.city = addressBookRecord.city || null;
    steps.value.one.recipient.receiverCountry = addressBookRecord.country || null;

    servicePointSearch.value = {
      address: addressBookRecord.address || null,
      city: addressBookRecord.city || null,
      zipCode: addressBookRecord.zipcode || null,
    };
    /** commented now **/
    /* if (!steps.value.two.isChooseClosestServicePointChecked) {
        servicePointSearch.value = {
          address: addressBookRecord.address || null,
          city: addressBookRecord.city || null,
          zipCode: addressBookRecord.zipcode || null,
        };
        await onClickSearchServicePoint();
      } else {
        await getNearestServicePoints(
          steps.value.two.carrier!.carrier_id as any,
          steps.value.one.recipient.receiverCountry!.iso as any,
          steps.value.one.recipient.address1 as any,
          steps.value.one.recipient.zipCode as any,
          steps.value.one.recipient.city as any,
        );
      } */
  };
  const resetReceiver = () => {
    steps.value.one.recipient.name = null;
    steps.value.one.recipient.email = null;
    steps.value.one.recipient.phone = null;
    steps.value.one.recipient.zipCode = null;
    steps.value.one.recipient.address1 = null;
    steps.value.one.recipient.address2 = null;
    steps.value.one.recipient.city = null;
  };
  const onChangeReceiverName = (value: string) => {
    steps.value.one.recipient.name = value;
  };

  const resetSender = () => {
    steps.value.one.sender.name = null;
    steps.value.one.sender.email = null;
    steps.value.one.sender.phone = null;
    steps.value.one.sender.zipCode = null;
    steps.value.one.sender.address1 = null;
    steps.value.one.sender.address2 = null;
    steps.value.one.sender.city = null;
  };
  const onChangeSenderName = (value: string) => {
    steps.value.one.sender.name = value;
  };
  const onSelectSender = async (addressBookRecord: IAddressBookRecord) => {
    resetSender();
    $v.value.two.$reset();

    steps.value.one.sender.name = addressBookRecord.name || null;
    steps.value.one.sender.email = addressBookRecord.email || null;
    steps.value.one.sender.phone = addressBookRecord.mobile || null;
    steps.value.one.sender.zipCode = addressBookRecord.zipcode || null;
    steps.value.one.sender.address1 = addressBookRecord.address || null;
    steps.value.one.sender.address2 = addressBookRecord.address2 || null;
    steps.value.one.sender.city = addressBookRecord.city || null;
  };
  const onClickDownloadLabel = async (close: boolean) => {
    windowRef = WindowService.openWindow('Shipment Label Preview');

    downloadLabel();

    if (close) {
      onCloseCreateShipment();
    }
  };
  const onClickDefaultPrintLabel = async () => {
    if (!bookedShipment.value || !bookedShipment.value.label) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:print-label');

      const defaultPrinter = $store.state.printerStore.defaultPrinterForLabel;

      if (!defaultPrinter) {
        notificationService.showError('No default printer found');
        return;
      }

      /* const senderCountryIso =
         $store.state.countryStore.countriesIndexedByCountryCode[
           bookedShipment.value.sender_country_code
           ]?.iso;
       const receiverCountryIso =
         $store.state.countryStore.countriesIndexedByCountryCode[
           bookedShipment.value.receiver_country_code
           ]?.iso;*/
      let label: string | null = null;

      if (defaultPrinter.format === 'A4') {
        label = bookedShipment.value.label;
      }

      if (defaultPrinter.format === '10x19') {
        label = bookedShipment.value.label_a6;
      }

      if (defaultPrinter.format === 'ZPL') {
        label = bookedShipment.value.label_zpl;
      }

      if (!label) {
        throw new Error('No label found');
      }

      await printJobService.queuePrintJob({
        company_code: $store.state.companyStore.company_code,
        document_base64: label,
        format: defaultPrinter.format,
        reference: `Shipment #${bookedShipment.value.shipment_code}`,
        printer: defaultPrinter.linked_printer,
        printer_app_code: defaultPrinter.printer_app_code,
      });
      notificationService.showSuccess('Print job queued successfully');
    } catch (e) {
      loggingService.error('Error printing label', e);
      notificationService.showError('Error printing label');
    } finally {
      loadingService.stopLoading('main-loader:print-label');
    }
  };

  const onClickPay = async () => {
    if (!createdShipment.value || !calculatedCharge.value) {
      return;
    }
    paymentStatus.value = 'pending';
    generatedLabel.value = null;
    loadingService.startLoading('main-loader:company');
    await $store.dispatch('companyStore/syncCompanyData');
    /** payment will be from balance if it is manual shipment or shopify order shipment **/
    // if ($store.state.paymentSettingsStore.payment_method == "BALANCE" || createdShipment.value.order_code!=null) {
    if (
      ($store.state.companyStore.balance || 0) < calculatedCharge.value.totals.after_vat.amount &&
      !$store.state.companyStore.credit_enabled
    ) {
      notificationService.showError('Not enough balance');
      isAddFundModalOpen.value = true;
      return;
    }
    // }

    try {
      loadingService.startLoading('main-loader:pay-shipment');
      const amount = PriceService.shippingPriceFormat(
        calculatedCharge.value.totals.after_vat.amount,
        'en',
      );
      const toPay = Number(amount.replace(/,/g, ''));

      const makePaymentResponse = await shipmentService.makePayment(
        createdShipment.value.shipment_code,
        toPay,
      );

      let successMessage = '';
      if (makePaymentResponse.is_already_paid == 1) {
        successMessage = 'Your payment is already deducted for this shipment';
      } else {
        successMessage = `Shipment booked successfully for ${PriceService.formatPrice(
          calculatedCharge.value.totals.after_vat.amount,
        )}`;
      }
      /** now all shipments (manual or shopify order) shipments will be done with balance that's why below lines are commentd**/
      //  if ($store.state.paymentSettingsStore.payment_method == "BALANCE") {
      const response = await shipmentService.generateLabel(createdShipment.value.shipment_code);
      paymentStatus.value = 'success';
      onLabelGenerated(response, successMessage);
      /* }
       else {
         const timeInterval = 5;
         const timeOut = 65;
         let currentTime = 0;
         let isApiCallInProgress = false;
        // loadingService.startLoading("main-loader:get-label", "Label is being generated, please wait.");
         loadingService.startLoading("main-loader:get-label");
         getLabelInterval.value = setInterval(async () => {
           if (createdShipment.value) {
             if (!isApiCallInProgress) {
               try {
                 isApiCallInProgress = true;
                 const response = await shipmentService.getLabel(createdShipment.value.shipment_code);
                 isApiCallInProgress = false;
                 if (response.p_status == "success" || response.p_status == "error" || currentTime > timeOut) {
                   clearGetLabelInterval(getLabelInterval.value);
                   paymentStatus.value = response.p_status;

                   if (response.p_status == "success" && response.data) {
                     onLabelGenerated(response.data, successMessage);
                   } else {
                     notificationService.showError(response.message ?? "Failed to generate label");
                   }
                   console.log("timeout or payment done");
                   loadingService.stopLoading("main-loader:get-label", true);
                 }
                 currentTime = currentTime + timeInterval;
                 console.log("Current Time ", currentTime);
               } catch (error: any) {
                 console.log("Error ", error);
                 if (error?.response?.data?.message) {
                   notificationService.showError(error?.response?.data?.message);
                 } else {
                   notificationService.showError("Failed to generate label");
                 }
                 loadingService.stopLoading("main-loader:get-label", true);
               }
             }
           } else {
             clearGetLabelInterval(getLabelInterval.value);
           }
         }, timeInterval * 1000);
       }*/
      console.log('Reached at end');
    } catch (e: any) {
      console.log('Error##### ', e);
      const errorMessage = e.response.data.error;
      if (errorMessage != null) {
        const errorMessageLowerCase = errorMessage.toLowerCase();
        if (
          errorMessageLowerCase.includes('timeout') ||
          errorMessageLowerCase.includes('timedout') ||
          errorMessageLowerCase.includes('timed out') ||
          errorMessageLowerCase.includes('timed')
        ) {
          notificationService.showError('Timeout creating shipping label. Please try again.');
        } else {
          notificationService.showError(errorMessage);
        }
      } else if (e.response.data.message) {
        notificationService.showError(e.response.data.message);
      } else {
        notificationService.showError('Error creating shipping label. Please try again');
      }
      if (e.response?.data?.invalid_shipping_template) {
        step.value = 2;
      }
      /*notificationService.showError(
        e.response.data.error,
      );*/ /* notificationService.showError(
          'Creating shipment failed. Please ensure addresses are correct both for the sender and the receiver. If the problem persists, please contact support.',
        );*/
      loggingService.error('Error paying shipment', e);
    } finally {
      loadingService.stopLoading('main-loader:pay-shipment');
    }
  };
  const onLabelGenerated = (shipment: IShipment, message: string) => {
    bookedShipment.value = shipment;
    $store.dispatch('companyStore/syncCompanyData');
    $store.dispatch('shipmentStore/refreshDrafts');
    $store.dispatch('shipmentStore/refreshBookings');
    notificationService.showSuccess(message);
    generatedLabel.value = shipment.label;
    onClickNext();
  };
  const clearGetLabelInterval = (intervalId: ReturnType<typeof setInterval> | null) => {
    if (intervalId) {
      clearInterval(intervalId);
    }
  };
  const downloadLabel = async () => {
    if (!bookedShipment.value || !bookedShipment.value.label || !windowRef) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:download-pdf');

      const blob: Blob = await HelperService.toBlob(
        `data:application/pdf;base64,${encodeURI(bookedShipment.value.label)}`,
      );

      windowRef.location = URL.createObjectURL(blob);
    } catch (error) {
      windowRef.close();
      loggingService.error('Error while downloading PDF', error);
      notificationService.showError('Error while downloading PDF');
    } finally {
      loadingService.stopLoading('main-loader:download-pdf');
    }
  };

  const onCloseCreateShipment = () => {
    $store.dispatch('shipmentStore/closeCreateShipmentModal');
  };
  const onClickPrevious = () => {
    step.value -= 1;
  };
  const onClickNext = async () => {
    if (step.value === 1) {
      await $v.value.one.$validate();
    }

    if (step.value === 2) {
      await $v.value.two.$validate();
    }

    if (step.value === 3) {
      await $v.value.three.$validate();
    }

    if (step.value === 4) {
      await $v.value.four.$validate();
    }
    console.log('Step1 Error ', $v.value.one.$error);
    console.log('Step2 Error ', $v.value.two.$error);
    console.log('Step3 Error ', $v.value.three.$error);
    console.log('Step4 Error ', $v.value.four.$error);

    if (step.value === 1 && $v.value.one.$error) {
      console.log('Errors ', $v.value.one.$error);
      /** this is to make sure both tabs validations view **/
      if ($v.value.one.sender.$error && stepOneTabValue.value == 'recipient') {
        stepOneTabValue.value = 'sender';
      } else {
        stepOneTabValue.value = 'recipient';
      }
      return;
    }

    if (step.value == 2 && $v.value.one.$error) {
      step.value = 1;
      if ($v.value.one.sender.$error) {
        stepOneTabValue.value = 'sender';
      } else {
        stepOneTabValue.value = 'recipient';
      }
      return;
    }
    if (step.value === 2 && $v.value.two.$error) {
      console.log('Carrier: ', steps.value.two.carrier);
      console.log('Carrier Code: ', carrierCode.value);
      if ($v.value.two.carrier.$error) {
        notificationService.showError('No carrier has been selected.');
      } else if ($v.value.two.shippingProduct.$error) {
        notificationService.showError('No shipping product has been selected.');
      } else if ($v.value.two.weightClass.$error) {
        notificationService.showError('No weight class has been selected.');
      } else if (
        $v.value.two.servicePoint.$error &&
        steps.value.two.shippingProduct?.hasMultiParcelShop
      ) {
        notificationService.showError('Nearest service point not found.');
      }
      return;
    }
    if (step.value === 3 && $v.value.three.$error) {
      return;
    }

    /** means carrier is pre-selected and need to fetch its service points**/
    console.log('L2083');
    console.log('step.value', step.value);
    console.log('steps.value.two.carrier ', steps.value.two.carrier);
    console.log('steps.value.two.servicePoint ', steps.value.two.servicePoint);
    if (step.value === 1 && steps.value.two.carrier) {
      console.log('L2085 fetching service points');
      getNearestServicePoints(
        steps.value.two.carrier.carrier_id as any,
        steps.value.one.recipient.receiverCountry!.iso,
        steps.value.one.recipient.address1,
        steps.value.one.recipient.zipCode,
        steps.value.one.recipient.city,
      );
    }
    if (step.value === 2) {
      /** commented now **/
      //await validateParcelShopsAndGoToNextStep('sender');
      //await getNearestServicePoints(steps.value.one.carrier!.carrier_id as any, steps.value.one.receiverCountry!.iso as any, steps.value.three.address1 as any, steps.value.three.zipCode as any, steps.value.three.city as any);
      if (!createdShipment.value) {
        onCreateShipment();
        return;
      } else {
        onUpdateShipment();
        return;
      }
    }
    if (step.value == 3) {
      if (createdShipment.value) {
        const isError = await checkEmailTrackingMessage();
        if (!isError) {
          await onUpdateShipment();
          return;
        }
        return;
      }
    }

    /** commented now **/
    /*if (step.value === 3) {
      await validateParcelShopsAndGoToNextStep('receiver');
      return;
    }*/

    if (step.value === 4 && $v.value.four.$error) {
      return;
    }
    stepper.value?.next();
  };
  const checkEmailTrackingMessage = async () => {
    if (!createdShipment.value) {
      return false;
    }
    let isError = false;
    try {
      loadingService.startLoading('main-loader:check-tracking-email');
      await shipmentService.checkTrackingEmail(createdShipment.value?.shipment_code, {
        additional_services: Object.entries(steps.value.three.additionalServices)
          .filter(([, value]) => value)
          .map(([key]) => Number(key)),
      });
    } catch (error: any) {
      if (error.response.data?.error) {
        notificationService.showError(error.response.data?.error);
      }
      isError = true;
    } finally {
      loadingService.stopLoading('main-loader:check-tracking-email');
    }
    return isError;
  };
  const onChangeZipCode = async (source: 'sender' | 'recipient', zip: string | number | null) => {
    if (
      (source !== 'sender' && source !== 'recipient') ||
      !steps.value.one.sender.senderCountry ||
      !steps.value.one.recipient.receiverCountry ||
      !zip ||
      typeof zip !== 'string'
    ) {
      return;
    }

    let iso: ISOCountries | null = null;

    if (source === 'sender') {
      iso = steps.value.one.sender.senderCountry.iso;
    }

    if (source === 'recipient') {
      iso = steps.value.one.recipient.receiverCountry.iso;
    }

    if (!iso) {
      return;
    }

    const re = new RegExp(postCodes[iso].regex);

    if (!re.test(zip)) {
      return;
    }

    let countryIso: string | null = null;
    let zipCode: string | null = null;

    if (source === 'sender') {
      countryIso = steps.value.one.sender.senderCountry.iso;
      zipCode = steps.value.one.sender.zipCode;
    }

    if (source === 'recipient') {
      countryIso = steps.value.one.recipient.receiverCountry.iso;
      zipCode = steps.value.one.recipient.zipCode;
    }

    if (!countryIso || !zipCode) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:get-city-from-zip-code');
      const geo = await mapService.getMapInfo(countryIso, zipCode);
      /** commented now **/
      // await getNearestServicePoints(steps.value.two.carrier!.carrier_id as any, steps.value.one.recipient!.receiverCountry.iso as any, steps.value.one.recipient.address1 as any, steps.value.one.recipient.zipCode as any, steps.value.one.recipient.city as any)
      if (_.size(geo.results) > 0) {
        const results = _.head(Object.values(geo.results));
        const result = _.head(results);

        if (source === 'sender') {
          steps.value.one.sender.city = result?.city || '';
        }

        if (source === 'recipient') {
          steps.value.one.recipient.city = result?.city || '';
        }
      }
    } catch (e) {
      notificationService.showError('Error getting city from zip code');
      loggingService.error('Error getting city from zip code', e);
    } finally {
      loadingService.stopLoading('main-loader:get-city-from-zip-code');
      loadingService.stopLoading('main-loader:service-points');
    }
  };

  const onCloseAddFunds = () => {
    isAddFundModalOpen.value = false;
  };
  const hydrateDefaultFields = () => {
    if (denmark) {
      steps.value.one.sender.senderCountry = denmark;
    }

    steps.value.one.sender.name = $store.state.companyStore.company_name;
    steps.value.one.sender.address1 = $store.state.companyStore.address;
    steps.value.one.sender.email = $store.state.userStore.email;
    steps.value.one.sender.phone = $store.state.companyStore.phone_no;
    steps.value.one.sender.city = $store.state.companyStore.city;
    steps.value.one.sender.zipCode = $store.state.companyStore.zipcode;
  };
  const getAddressBookRecords = async () => {
    try {
      loadingService.startLoading('main-loader:address-book');
      const response = await addressBookService.getAddressBookRecords(1, 1000);
      addressBookRecords.value = response.entities;
    } catch (e) {
      notificationService.showError('Error getting address book records');
      loggingService.error('Error getting address book records', e);
    } finally {
      loadingService.stopLoading('main-loader:address-book');
    }
  };
  const isRequiredReceiverPhone = () => {
    const adService = additionalServiceCharges.value.find(
      (item: IAdditionalServiceCharge) => item.additional_service_code == carrier_sms_as_code,
    );
    const montyProduct = steps.value.two?.shippingProduct?.product_id;
    let required;
    let isRequiredForMontyProduct = false;
    let isRequiredForAS = false;
    if (montyProduct) {
      isRequiredForMontyProduct = phoneRequiredForMontyProducts[montyProduct ?? ''] ?? false;
    }
    if (adService) {
      isRequiredForAS = steps.value.three.additionalServices[adService.as_charge_code] ?? false;
    }
    required = isRequiredForMontyProduct || isRequiredForAS;
    console.log('isRequiredForMontyProduct ', isRequiredForMontyProduct);
    console.log('isRequiredForAS ', isRequiredForAS);
    console.log('montyProduct ', montyProduct);
    return required;
  };

  const getCarriers = async (senderCountry: number, receiverCountry: number) => {
    shippingProducts.value = [];
    weightClasses.value = [];
    if (loadingService.loadings.has('main-loader:carriers')) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:carriers');
      const response = await carrierService.getShippingProducts(senderCountry, receiverCountry);
      carriers.value = _.get(response, 'carriers', []) as ICarrier[];
      productWithClientIds.value = _.get(response, 'productWithClientIds', []) as any;
    } catch (e) {
      notificationService.showError('Error getting carriers');
      loggingService.error('Error getting carriers', e);
    } finally {
      loadingService.stopLoading('main-loader:carriers');
    }
  };
  const onChangeCountryOnStep1 = async () => {
    if (!steps.value.one.sender.senderCountry || !steps.value.one.recipient.receiverCountry) {
      console.log('change country null');
      return;
    }

    carrierCode.value = null;
    shippingProductCode.value = null;
    weightClassCode.value = null;
    steps.value.two.carrier = null;
    steps.value.two.shippingProduct = null;
    steps.value.two.weightClass = null;
    // steps.value.one.parcels = [];
    await getCarriers(
      steps.value.one.sender.senderCountry.country_code,
      steps.value.one.recipient.receiverCountry.country_code,
    );
  };
  const getShipment = async () => {
    try {
      loadingService.startLoading('main-loader:get-shipment');
      await shipmentService
        .getShipment($store.state.shipmentStore.shipmentCode)
        .then((response) => {
          createdShipment.value = response;
        });
    } finally {
      loadingService.stopLoading('main-loader:get-shipment');
    }
  };
  const hydrateFromPredefinedValues = async () => {
    if (!$store.state.shipmentStore.createShipmentPredefinedValues) {
      return;
    }
    steps.value.one.sender.senderCountry =
      $store.state.shipmentStore.createShipmentPredefinedValues.one.sender.senderCountry;
    steps.value.one.recipient.receiverCountry =
      $store.state.shipmentStore.createShipmentPredefinedValues.one.recipient.receiverCountry;

    await onChangeCountryOnStep1();

    steps.value.one = {
      ...steps.value.one,
      ...$store.state.shipmentStore.createShipmentPredefinedValues.one,
    };
    steps.value.two = {
      ...steps.value.two,
      ...$store.state.shipmentStore.createShipmentPredefinedValues.two,
    };
    console.log('Step 2 ', steps.value.two);

    /*/!** when come from continue from draft or create manual shipment from orders shipping products list was not showing **!/
    const carrier = $store.state.shipmentStore.createShipmentPredefinedValues.two?.carrier;
    if (carrier) {
      shippingProducts.value = carriers.value.find((item) => item.carrier_code == carrier.carrier_code)?.products as IShippingProduct[];
    }*/

    carrierCode.value = steps.value.two.carrier?.carrier_code ?? null;
    if (steps.value.two.carrier) {
      shippingProducts.value = carriers.value.find(
        (item) => item.carrier_code == steps.value.two.carrier?.carrier_code,
      )?.products as IShippingProduct[];
    }
    shippingProductCode.value = steps.value.two.shippingProduct?.carrier_product_code ?? null;
    if (shippingProductCode.value) {
      await onChangeShippingProduct(shippingProductCode.value);
    }
    const stepTwoPredefinedValues = $store.state.shipmentStore.createShipmentPredefinedValues.two;
    if (stepTwoPredefinedValues) {
      steps.value.two.weightClass = stepTwoPredefinedValues.weightClass;
      weightClassCode.value = stepTwoPredefinedValues.weightClass?.weight_class_code ?? null;

      if (steps.value.one.recipient) {
        servicePointSearch.value = {
          address: steps.value.one.recipient.address1 || null,
          city: steps.value.one.recipient.city || null,
          zipCode: steps.value.one.recipient.zipCode || null,
        };
      }
    }

    /** end here **/

    steps.value.three = {
      ...steps.value.three,
      ...$store.state.shipmentStore.createShipmentPredefinedValues.three,
    };
    hydrateAdditionalServices();
    steps.value.four = {
      ...steps.value.four,
      ...$store.state.shipmentStore.createShipmentPredefinedValues.four,
    };

    /*const parcelsFilled: boolean = steps.value.one.parcels.every(
      (parcel: IFormParcel) => parcel.quantity && parcel.weightClass,
    )
    if (parcelsFilled) {
      addParcels();
    }*/
  };
  onBeforeMount(async () => {
    /*loadingService.startLoading("main-loader:testing", "testing...");
    setTimeout(() => {
      loadingService.stopLoading("main-loader:testing",true);
    }, 3000);*/
    hydrateDefaultFields();
    if ($store.state.shipmentStore.createShipmentPredefinedValues) {
      if ($store.state.shipmentStore.shipmentCode) {
        await getShipment();
      }
      /** commented now **/
      await hydrateFromPredefinedValues();
    } else {
      /** it is old commented **/
      // getDefaultTemplate();
    }

    await getAddressBookRecords();
    window.addEventListener('beforeunload', handleBeforeUnload);
  });
  onBeforeUnmount(() => {
    clearGetLabelInterval(getLabelInterval.value);
    $store.dispatch('shipmentStore/setCreateShipmentData', null);
    window.removeEventListener('beforeunload', handleBeforeUnload);
  });
</script>

<style lang="scss">
  .create-shipment-stepper-v2 {
    &.q-stepper--horizontal .q-stepper__step-inner {
      min-height: 400px !important;
      padding: 0 20px !important;
    }

    &-navigation {
      display: flex;
      gap: 15px;
      justify-content: end;
      width: 100%;
      padding: 20px !important;

      &-next {
        min-width: 100px;
      }

      &-previous {
        min-width: 100px;
        height: 38px;
      }
    }

    &-shipments-header {
      display: flex;
    }

    &-shipments-details {
      display: flex;
      flex-direction: row;
      max-height: 330px;
      overflow-y: auto;
      @media (max-width: 700px) {
        flex-direction: column;
      }

      &-radio-card {
        margin: 12px 0;
      }
    }

    &-shipment {
      width: 100%;
      height: 286px;
      border: 1px solid rgba(125, 131, 152, 0.2);
      border-radius: 4px;

      &-separator {
        margin-top: 10px !important;
      }

      &-carrier {
        padding: 12px 16px;
        background: #f7f7f7;
      }
    }
  }
</style>
