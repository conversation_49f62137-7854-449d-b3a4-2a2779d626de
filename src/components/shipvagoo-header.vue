<template>
  <header>
    <div style="display: flex;align-items: center;">
      <router-link to="/" class="logo">
        <img style="width: 140px" :src="Logo" alt="Logo" decoding="async"/>
      </router-link>
      <div class="desktop-menu">
        <template v-for="(list,index) in menu">
          <button
            @mouseover="onMouseOver(list,index)"
            @mouseleave="onMouseLeave(list,index)"
            v-if="!list.child"
            :key="`${list.href}_${list.title}`"
            class="desktop-menu--item"
            @click="$router.push(list.href)">

            <embed
              v-if="list.icon"
              :style="`width: ${list.icon.attributes.size}`"
              :src="list.icon.attributes.src" style="margin-right:7px;"/>
            <!--<q-icon
              :name="`img:${list.icon.attributes.src}`"
              style="font-size: 12px; margin-right: 5px;"/>-->
            {{ $t(list.title) }}
          </button>
          <div
            @mouseover="onMouseOver(list,index)"
            @mouseleave="onMouseLeave(list,index)"
            v-if="list.child"
            :key="`${list.href}_${list.title}`"
            class="desktop-menu--container"
            style="position: relative"
          >
            <div class="desktop-menu--item">
              <embed
                v-if="list.icon"
                :style="`width: ${list.icon.attributes.size}`"
                :src="list.icon.attributes.src" style="margin-right:7px;"/>
              {{ $t(list.title) }}
              <!--<q-icon name="ion-ios-arrow-down" style="margin-left: 4px"/>-->
            </div>
            <div class="desktop-menu--menu">
              <template v-for="child in list.child">
                <button
                  v-if="!child.child"
                  :key="`${child.href}_${child.title}_${$route.path === child.href}`"
                  v-ripple
                  class="desktop-menu--menu-item"
                  :class="{ 'desktop-menu--menu-item--active': $route.path === child.href }"
                  @click="child.onClick?.() || $router.push(child.href)"
                >
                  <div>{{ $t(child.title) }}</div>
                  <div v-if="child.href === '/orders'" class="desktop-menu--menu-item--count">
                    {{ $store.state.orderStore.count || 0 }}
                  </div>
                </button>

                <div
                  v-if="child.child"
                  :key="`${child.href}_${child.title}`"
                  style="position: relative"
                >
                  <div :key="`${list.href}_${child.title}`" class="desktop-menu--menu-item">
                    <span>{{ $t(child.title) }}</span>
                    <q-icon v-if="child.child" name="ion-ios-arrow-down rotate-270"/>
                  </div>
                  <div v-if="child.child" class="desktop-menu--sub-menu--container">
                    <div style="width: 4px"/>
                    <div class="desktop-menu--sub-menu">
                      <template v-for="child2 in child.child">
                        <button
                          v-if="!child2.child"
                          :key="`${child2.href}_${child2.title}_${$route.path === child2.href}`"
                          v-ripple
                          class="desktop-menu--menu-item"
                          :class="{ 'desktop-menu--menu-item--active': $route.path === child2.href }"
                          @click="$router.push(child2.href)"
                        >
                          {{ $t(child2.title) }}
                        </button>
                        <div
                          v-if="child2.child"
                          :key="`${child2.href}_${child2.title}`"
                          class="desktop-menu--menu-item"
                        >
                          <span>{{ $t(child2.title) }}</span>
                          <q-icon v-if="child2.child" name="ion-ios-arrow-down rotate-270"/>
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </template>
      </div>
    </div>

    <div style="display: flex;flex-direction: row;">
      <div style="display: flex;flex-direction: row;"
           @mouseenter="hideCompanyDropdown">
        <template v-for="list in menu1">
          <button
            v-if="!list.child"
            :key="`${list.href}_${list.title}`"
            class="desktop-menu--item-icons"
            @click="onClickMenu1Icon(list)">
            <q-icon
              v-if="list.icon"
              :name="`img:${list.icon.attributes.src}`"
              style="font-size: 20px; "/>
          </button>
          <div
            v-if="list.child"
            :key="`${list.href}_${list.title}`"
            class="desktop-menu--container"
            style="position: relative"
          >
            <div class="desktop-menu--item-icons">
              <q-icon
                v-if="list.icon"
                :name="`img:${list.icon.attributes.src}`"
                style="font-size: 20px;"/>
              <!--<q-icon name="ion-ios-arrow-down" style="margin-left: 4px"/>-->
            </div>
            <div class="desktop-menu--menu">
              <template v-for="child in list.child">
                <button
                  v-if="!child.child"
                  :key="`${child.href}_${child.title}_${$route.path === child.href}`"
                  v-ripple
                  class="desktop-menu--menu-item"
                  :class="{ 'desktop-menu--menu-item--active': $route.path === child.href }"
                  @click="child.onClick?.() || $router.push(child.href)"
                >
                  <div>{{ $t(child.title) }}</div>
                </button>

                <div
                  v-if="child.child"
                  :key="`${child.href}_${child.title}`"
                  style="position: relative"
                >
                  <div :key="`${list.href}_${child.title}`" class="desktop-menu--menu-item">
                    <span>{{ $t(child.title) }}</span>
                    <q-icon v-if="child.child" name="ion-ios-arrow-down rotate-270"/>
                  </div>
                  <div v-if="child.child" class="desktop-menu--sub-menu--container">
                    <div style="width: 4px"/>
                    <div class="desktop-menu--sub-menu">
                      <template v-for="child2 in child.child">
                        <button
                          v-if="!child2.child"
                          :key="`${child2.href}_${child2.title}_${$route.path === child2.href}`"
                          v-ripple
                          class="desktop-menu--menu-item"
                          :class="{ 'desktop-menu--menu-item--active': $route.path === child2.href }"
                          @click="child2.onClick?.() || $router.push(child2.href)"
                        >
                          {{ $t(child2.title) }}
                        </button>
                        <div
                          v-if="child2.child"
                          :key="`${child2.href}_${child2.title}`"
                          class="desktop-menu--menu-item"
                        >
                          <span>{{ $t(child2.title) }}</span>
                          <q-icon v-if="child2.child" name="ion-ios-arrow-down rotate-270"/>
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </template>
      </div>
      <div class="actions">
        <shipvagoo-button
          class="profile-info-button"
          style="cursor: default"
          padding="0 4px"
          color="transparent"
          height="100%"
          unelevated
        >
          <div class="auth-logo">
            <div class="profile-info">
              <p>{{ companyName }}</p>
              <span>{{ $store.getters['companyStore/formattedBalance'] }}</span>
            </div>
            <div class="icon">
              <img src="/assets/img/user.svg" alt="User Photo" decoding="async"/>
            </div>
          </div>
          <!-- <shipvagoo-menu
             ref="company_menu_dropdown"
             border-radius="0 0 15px 15px"
             style="transform: translateY(1px)">
             <div class="profile-menu">
               <div class="user-info">
                 <div class="profile_name-and-status">
                   <div class="name">
                     <p>
                       <strong>{{ $store.state.companyStore.company_name }}</strong>
                     </p>
                   </div>
                   &lt;!&ndash; <div class="status">
                      <div class="box-size"></div>
                    </div>&ndash;&gt;
                 </div>
                 <div class="balance">
                   <span>{{ $store.getters['companyStore/formattedBalance'] }}</span>
                 </div>
               </div>
 
               <shipvagoo-menu-item clickable @click="$router.push('/company')">
                 {{ $t('common.company') }}
               </shipvagoo-menu-item>
               <shipvagoo-menu-item clickable @click="onClickAddFunds">
                 {{ $t('common.add_funds') }}
               </shipvagoo-menu-item>
               &lt;!&ndash; <shipvagoo-menu-item has-caret clickable>
                 Print
   
                 <shipvagoo-menu
                   anchor="top left"
                   self="top right"
                   style="transform: translateX(-4px)"
                 >
                   <shipvagoo-menu-item clickable>PDF Settings</shipvagoo-menu-item>
                   <shipvagoo-menu-item clickable>Printers</shipvagoo-menu-item>
                 </shipvagoo-menu>
               </shipvagoo-menu-item>
                &ndash;&gt;
               <shipvagoo-menu-item
 
                 has-caret clickable>
                 {{ $t('common.language') }}
 
                 <shipvagoo-menu
                   anchor="top left"
                   self="top right"
                   style="transform: translateX(-4px)"
                 >
                   <shipvagoo-menu-item clickable @click="setLanguage('en')">
                     <img src="/assets/img/uk.svg" alt="Uk" decoding="async"/>&nbsp;&nbsp;
                     <span>{{ $t('common.english') }}</span>
                   </shipvagoo-menu-item>
                   <shipvagoo-menu-item clickable @click="setLanguage('dk')">
                     <img src="/assets/img/denmark.svg" alt="Uk" decoding="async"/>&nbsp;&nbsp;
                     <span>{{ $t('common.danish') }}</span>
                   </shipvagoo-menu-item>
                 </shipvagoo-menu>
               </shipvagoo-menu-item>
               <shipvagoo-separator/>
               <shipvagoo-menu-item clickable @click="logout">
                 {{ $t('common.sign_out') }}
               </shipvagoo-menu-item>
             </div>
           </shipvagoo-menu>-->
        </shipvagoo-button>
        <div class="menu-icon" @click="toggleSidebar">
          <img
            class="menu-icon--img"
            src="/assets/img/hamburger.svg"
            alt="Hamburger Logo"
            decoding="async"
          />
        </div>
      </div>
    </div>
  </header>

  <shipvagoo-sidebar-menu v-click-outside="onClickOutside" :status="isSidebarCollapsed"/>

  <q-dialog persistent no-backdrop-dismiss no-esc-dismiss v-model="isAddFundsModalOpen">
    <add-balance @close="onCloseAddFunds"/>
  </q-dialog>
  <q-dialog v-model="logoutConfirm">
    <confirm-logout @confirm="logout"></confirm-logout>
  </q-dialog>
</template>

<script setup lang="ts">
  import {Ref, ref, watch, computed} from 'vue';
  import {Router, useRouter} from 'vue-router';
  import {useQuasar} from 'quasar';
  import {useStore} from 'vuex';
  //import {useI18n} from 'vue-i18n';
  import ShipvagooSidebarMenu from './shipvagoo-sidebar-menu.vue';
  import {_my_menu, _my_menu1} from '../menu/menu';
  import {authService, loadingService, loggingService} from '../services/_singletons';
  import {IRootState} from '../model/store.model';
  import Logo from '../assets/img/shipvagoo_dark.png';
  import useWidth from '../hooks/useWidth';
  import vClickOutside from '../directives/v-click-outside.directive';
  //import LanguageService from '../services/language.service';

  import shipvagooMenu from '../components/ui-kit/shipvagoo-menu.vue';

  const isSidebarCollapsed: Ref<boolean> = ref(true);
  const $router: Router = useRouter();
  const $route = computed(() => $router.currentRoute.value);
  const $store = useStore<IRootState>();
  const $q = useQuasar();
  //const {locale} = useI18n();
  const menu = ref(_my_menu.map((item) => ({...item, isVisible: false})));
  const menu1 = ref(_my_menu1.map((item) => ({...item, isVisible: false})));

  const isAddFundsModalOpen = ref<boolean>(false);
  const logoutConfirm = ref<boolean>(false);
  const company_menu_dropdown = ref<typeof shipvagooMenu | null>(null);
  defineExpose({
    $q,
  });
  const width = useWidth();

  const companyName = computed(() => {
    let name = $store.state.companyStore.company_name;
    if (name.length > 12) {
      name = name.substring(0, 11) + '..'
    }
    return name;
  })
  watch(
    () => width.value,
    (val) => {
      if (val <= 1024) {
        return;
      }

      if (!isSidebarCollapsed.value) {
        isSidebarCollapsed.value = true;
      }
    },
  );
  /*const onMouseEnterCompanyDropdown = () => {
    console.log('mouse enter')
    company_menu_dropdown.value?.show()
  }*/
  const hideCompanyDropdown = () => {
    console.log('mouse leave')
    if (company_menu_dropdown.value) {
      company_menu_dropdown.value.hide()
    }
  }
  const onMouseOver = (list: any, index: number) => {
    if (menu.value[index].icon != undefined) {
      menu.value[index].icon!.attributes.src = list.icon.attributes.hover_src
    }
  }
  const onMouseLeave = (list: any, index: number) => {
    if (menu.value[index].icon != undefined) {
      menu.value[index].icon!.attributes.src = list.icon.attributes.original
    }
  }
  const toggleSidebar = () => {
    isSidebarCollapsed.value = !isSidebarCollapsed.value;
  };

  const onClickMenu1Icon = (list: any) => {
    //list.href!=''? $router.push(list.href):''
    if (list.href != null && list.href != '') {
      $router.push(list.href);
    } else {
      if (list.type == 'dialog') {
        if (list.key == 'add_funds') {
          isAddFundsModalOpen.value = true;
        } else if (list.key === 'logout') {
          logoutConfirm.value = true;
        }
      }
    }
  }
  const logout = async () => {
    try {
      loadingService.startLoading('main-loader:logout');
      $router.push('/login');
      $store.dispatch('userStore/resetUser');
      await authService.logout();
    } catch (error) {
      loggingService.error('Error while logout', error);
    } finally {
      loadingService.stopLoading('main-loader:logout');
    }
  };

  const onClickOutside = (event: MouseEvent | Event) => {
    if (
      event.target instanceof HTMLElement &&
      (event.target.classList.contains('menu-icon') ||
        event.target.classList.contains('menu-icon--img'))
    ) {
      return;
    }

    if (!isSidebarCollapsed.value) {
      isSidebarCollapsed.value = true;
    }
  };

  const onCloseAddFunds = () => {
    isAddFundsModalOpen.value = false;
  };

  /*const setLanguage = (language: 'en' | 'dk') => {
    locale.value = language;
    LanguageService.setLanguage(language);
  };*/

  $router.beforeEach((to, from, next) => {
    $store.dispatch('companyStore/syncCompanyData');
    menu.value.forEach((item, index) => {
      onMouseLeave(item, index)
    })
    next();
  })
</script>

<style scoped lang="scss">
  p {
    margin: 0;
  }

  li {
    &:hover {
      cursor: pointer;
    }
  }

  .icon {
    &:hover {
      cursor: pointer;
    }

    @media (min-width: 1024px) {
      display: none;
    }
  }

  header {
    position: relative;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 59px;
    padding-right: 36px;
    padding-left: 40px;
    background-color: #FFFFFF;

    -moz-box-shadow: 0 2px 16px rgba(0, 0, 0, 0.15);
    -webkit-box-shadow: 0 2px 16px rgba(0, 0, 0, 0.15);
    box-shadow: 0 2px 16px rgba(0, 0, 0, 0.15);

    @media (max-width: 1024px) {
      padding-right: 10px;
      padding-left: 10px;
    }

    .actions {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      width: 115px;
      max-width: 115px;
    }

    .auth-logo {
      box-sizing: border-box;
      line-height: 0;
    }

    .menu-icon {
      box-sizing: border-box;
      display: none;
      line-height: 0;

      @media (max-width: 1024px) {
        display: block;
      }
    }

    .logo {
      box-sizing: border-box;
      line-height: 0;
    }

    .profile-info {
      display: none;
      color: #1f2124;
    }

    @media screen and (min-width: 1025px) {
      border-color: transparent;

      .contain {
        height: 39px;
        padding: 0 40px;
      }

      .auth-logo {
        display: flex;
        margin-right: 0;
      }

      .profile-info {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-right: 13px;

        p {
          margin-top: 2px;
          font-size: 14px;
        }

        span {
          display: block;
          margin-top: 18px;
          font-size: 13px;
          color: $third-three;
        }
      }

      .balance {
        display: none;
      }
    }
  }

  .desktop-menu--item {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 59px;
    padding: 0 17px;
    font-size: 15px;
    font-weight: 500;
    line-height: 17px;
    color: #313131;
    cursor: default;
    background-color: transparent;
    border: 0;
    transition: background-color 0.3s cubic-bezier(0.25, 0.8, 0.5, 1),
    opacity 0.4s cubic-bezier(0.25, 0.8, 0.5, 1);

    & + .desktop-menu--menu {
      position: absolute;
      display: none;

      &:hover {
        display: flex;
      }
    }

    &:hover {
      color: white;
      background-color: #243E90;
      //background-color: #FFFFFF;
    }

    &:hover + .desktop-menu--menu {
      display: flex;
      margin-left: 10px;
    }
  }

  .desktop-menu--item-icons {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 59px;
    padding: 0 13px;
    font-size: 15px;
    font-weight: 500;
    line-height: 17px;
    color: #313131;
    cursor: default;
    background-color: transparent;
    border: 0;
    transition: background-color 0.3s cubic-bezier(0.25, 0.8, 0.5, 1),
    opacity 0.4s cubic-bezier(0.25, 0.8, 0.5, 1);

    & + .desktop-menu--menu {
      position: absolute;
      display: none;

      &:hover {
        display: flex;
        margin-left: 10px;
      }
    }

    &:hover {
      background-color: #e0e0e0;
    }

    &:hover + .desktop-menu--menu {
      display: flex;
      margin-left: 10px;
    }
  }

  .desktop-menu--menu {
    position: relative;
    top: 58px;
    left: -10px;
    z-index: 99;
    display: flex;
    flex-direction: column;
    min-width: 190px;
    padding-top: 12px;
    padding-bottom: 15px;
    color: #313131;
    background-color: #ffff;
    // border-top: 1px solid #131393;
    border-bottom-right-radius: 15px;
    border-bottom-left-radius: 15px;
    box-shadow: 0 6px 25px rgba(125, 124, 135, 0.62);

    &:hover {
      display: flex;
      margin-left: 10px;
    }

    .desktop-menu--sub-menu:hover {
      display: flex;
    }
  }

  .desktop-menu--sub-menu--container {
    position: absolute;
    top: 0;
    right: 100%;
    //  left: 100%;
    z-index: 99;
    display: none;
    flex-direction: row;
    // border: 1px solid #e0e0e0;
    border-radius: 7px;
    box-shadow: 0 6px 6px rgba(161, 159, 187, 0.62);

    &:hover {
      display: flex;
    }
  }

  .desktop-menu--sub-menu {
    display: flex;
    flex-direction: column;
    min-width: 185px;
    padding-top: 12px;
    padding-bottom: 15px;
    color: #313131;
    background-color: #ffffff;
    //background-color: #243E90;
    border-radius: 5px;
  }

  button.desktop-menu--item,
  button.desktop-menu--menu-item {
    cursor: pointer;
  }

  .desktop-menu--menu-item {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 13px 25px;
    font-size: 15px;
    line-height: 17px;
    color: #313131;
    text-align: left;
    cursor: default;
    background-color: transparent;
    border: 0;

    &:hover {
      color: white;
      background-color: #243E90;
    }

    &:hover + .desktop-menu--sub-menu--container {
      display: flex;
    }

    &--active {
      color: white;
      background-color: #243E90;
    }
  }

  .desktop-menu--container {
    &:hover .desktop-menu--item {
      color: #FFFFFF;
      background-color: #243E90;
    }
  }

  .desktop-menu {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 37px;
    padding-left: 24px;
    margin-left: 60px;
    //background-color: #1467CC;
    // background-color: #1f2124;
    transition: height 150ms ease-in-out, opacity 150ms ease-in-out;
    @media (max-width: 1024px) {
      display: none;
      max-width: 100%;
      height: 0;
      overflow: hidden;
      pointer-events: none;
      opacity: 0;
    }
  }
</style>

<style lang="scss">
  .profile-menu {
    z-index: 30;
    width: 213px;
    padding-top: 25px;
    overflow: hidden;
    //background-color: $menu-color;
    background-color: #FFFFFF;
    border-bottom-right-radius: 15px;
    border-bottom-left-radius: 15px;

    .user-info {
      margin-right: 20px;
      margin-bottom: 15px;
      margin-left: 25px;

      .profile_name-and-status {
        display: flex;
        align-items: center;
      }

      .name {
        flex: 1;

        p {
          font-size: 16px;
          color: #1f2124;
        }
      }

      .status {
        .box-size {
          width: 8px;
          height: 8px;
          overflow: hidden;
          background: $primary-one;
          border-radius: 50%;
        }
      }

      .balance {
        span {
          font-size: 12px;
          color: $third-three;
        }
      }
    }

    .menu {
      ul {
        padding: 0;
        margin: 0;

        li {
          position: relative;
          padding: 13px 0 13px 25px;
          list-style: none;
          transition: background-color 0.3s cubic-bezier(0.25, 0.8, 0.5, 1),
          opacity 0.4s cubic-bezier(0.25, 0.8, 0.5, 1);

          &:hover {
            background: $primary-three;

            .sub-menu {
              z-index: 30;
              display: block;
            }
          }

          a {
            font-size: 15px;
            color: #fff;
          }

          .sub-menu {
            position: absolute;
            top: 0;
            left: -135px;
            z-index: 30;
            display: none;
            width: 135px;

            ul {
              margin-right: 5px;
              overflow: hidden;
              background-color: $menu-color;
              border-radius: 10px;

              li {
                display: flex;
                align-items: center;
                transition: background-color 0.3s cubic-bezier(0.25, 0.8, 0.5, 1),
                opacity 0.4s cubic-bezier(0.25, 0.8, 0.5, 1);

                a {
                  display: inline-block;
                }

                img {
                  margin-right: 5px;
                }
              }
            }
          }

          .cat {
            position: relative;
            display: flex;
            padding-right: 20px;

            a {
              flex: 1;
            }

            img {
              margin-top: -3px;
              transform: rotate(270deg);
            }
          }
        }
      }
    }

    .sign-out {
      position: relative;
      border-top: 1px solid $secondary-one;
      transition: background-color 0.3s cubic-bezier(0.25, 0.8, 0.5, 1),
      opacity 0.4s cubic-bezier(0.25, 0.8, 0.5, 1);

      p {
        padding: 13px 0 13px 25px;
      }

      &:hover {
        cursor: pointer;
        background: $primary-three;
      }
    }
  }

  .profile-info-button {
    margin-right: -4px;

    @media (max-width: 1024px) {
      align-items: center;
      justify-content: center;
      margin-right: 21px;
    }
  }

  a {
    text-decoration: none;
  }

  .desktop-menu--menu-item--count {
    display: flex;
    align-items: center;
    height: 18px;
    padding: 5px;
    font-size: 12px;
    font-variation-settings: 'wght' 700;
    color: #389b99;
    background-color: #ffffff;
    border-radius: 9px;
  }

  svg:hover {
    fill: red;
  }
</style>
