<template>
  <div>
    <shipvagoo-label :label="$t('common.parcels')"/>
    <table class="parcel_table">
      <thead>
      <tr>
        <th class="delete-button">{{$t('common.delete')}}</th>
        <th class="quantity">{{$t('common.quantity')}}</th>
        <th class="weight_class">{{$t('common.weight_class')}}</th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="parcel in parcels" :key="'parcel-' + parcel.id">
        <td class="delete">
          <shipvagoo-button
            class="delete_button"
            flat
            icon="delete"
            padding="0"
            @click="onClickDeleteParcel(parcel.id)"
          />
        </td>
        <td class="quantity">
          <shipvagoo-input
            v-model="parcel.quantity"
            :outlined="false"
            borderless
            input-style="text-align: center"
            type="tel"
            readonly
          />
        </td>
        <td class="weight_class">
          <shipvagoo-select
            v-model="parcel.weightClass"
            :outlined="false"
            borderless
            :options="weightClasses"
          >
            <template #selected-item="scope">
                <span v-if="scope.opt">
                  {{ parseFloat(scope.opt.min_weight) }}-{{ parseFloat(scope.opt.max_weight) }}
                  {{ scope.opt.unit.toLowerCase() }}
                </span>
            </template>
            <template #option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section>
                  <q-item-label>
                    {{ parseFloat(scope.opt.min_weight) }}-{{ parseFloat(scope.opt.max_weight) }}
                    {{ scope.opt.unit.toLowerCase() }}
                  </q-item-label>
                </q-item-section>
              </q-item>
            </template>
          </shipvagoo-select>
        </td>
      </tr>
      <tr v-if="parcels.length === 0">
        <td colspan="3" style="padding: 10px">No parcel added</td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
  import {IWeightClass} from '../model/carrier.model';

  interface IParcel {
    /**
     * UUID
     */
    id: string;
    quantity: number | null;
    weightClass: IWeightClass | null;
  }

  interface Props {
    parcels: IParcel[];
    weightClasses: IWeightClass[];
  }

  defineProps<Props>();

  const emit = defineEmits(['deleteParcel']);

  const onClickDeleteParcel = (id: string) => {
    emit('deleteParcel', id);
  };
</script>

<style scoped lang="scss">
  .parcel_table {
    width: 100%;
    overflow: hidden;
    border-spacing: 0;
    border: 1px solid $primary-three;
    border-radius: 5px;

    th {
      padding: 10px;
      font-size: 15px;
      font-weight: 500;
      color: #ffffff;
      text-align: center;
      vertical-align: middle;
      background-color: #243E90;

      &.delete-button {
        text-align: left;
      }

      &.quantity {
        width: 115px;
        text-align: center;
      }

      &.weight_class {
        padding-left: 25px;
        text-align: left;
      }

      &.shop-delivery {
        text-align: left;
      }
    }

    tr {
      &:not(:first-child) td {
        border-top: 1px solid $primary-three;
      }
    }

    td {
      font-size: 15px;
      text-align: center;

      &.delete > * {
        width: 100%;
      }

      &.weight_class {
        width: 360px;
        padding-left: 46.5px !important;
      }

      &:not(:first-child) {
        border-left: 1px solid $primary-three;
      }
    }

    &.parcels_table {
      margin-top: 40px !important;

      @media (max-width: $tablet) {
        display: none;
      }
    }
  }
</style>
