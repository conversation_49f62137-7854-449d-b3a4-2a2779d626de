<template>
  <div class="shipvagoo_info" :style="{ 'border-color': $props.color }">
    <div class="shipvagoo_info-head" :style="{ 'background-color': $props.color }">
      <img v-if="$props.iconType === 'src'" src="../assets/img/info.svg" decoding="async" />
      <q-icon v-if="$props.iconType === 'q-icon'" :size="$props.size" :name="$props.icon" />
    </div>
    <div class="shipvagoo_info-content" :class="{ 'shipvagoo_info-content-dense': $props.dense }">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
  interface Props {
    icon?: string;
    size?: string;
    color?: string;
    dense?: boolean;
    iconType?: 'src' | 'q-icon';
  }

  withDefaults(defineProps<Props>(), {
    color: '#1f2124',
    dense: false,
    iconType: 'src',
    icon: 'info',
    size: '20px',
  });
</script>

<style lang="scss" scoped>
  .shipvagoo_info {
    display: flex;
    overflow: hidden;
    border: 1px solid;
    border-radius: 5px;

    &-head {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 40px;
      color: #fff;
      border-top-right-radius: 5px;
      border-bottom-right-radius: 5px;

      img {
        width: 18.59px;
        height: 18.59px;
      }
    }

    &-content {
      padding: 20px;
      font-size: 12px;

      :deep(p) {
        margin-bottom: 10px;
      }

      :deep(p:last-child) {
        margin-bottom: 0;
      }

      &-dense {
        padding: 10px;
        color: #1f2124;
      }
    }
  }
</style>
