<template>
  <div
    v-if="trackingMessage"
    :style="{ backgroundColor: '#fff'  }"
    class="full-width integration-box flex column justify-between">
    <div class="q-mb-sm">
      <div class="row dark-text text-weight-7 text-thirteen">
        <div class="col-6">
          Type
        </div>
        <div class="col-6">
          Trigger
        </div>
      </div>
      <div class="row dark-text text-thirteen q-mt-sm">
        <div class="col-6">
          {{ capitalize(trackingMessage.type) }}
        </div>
        <div class="col-6">
          {{ trackingMessage.message_hook?.content }}
        </div>
      </div>
    </div>
    <div>
      <div class="row dark-text text-weight-7 text-thirteen">
        <div class="col-6">
          Country
        </div>
        <div class="col-6">
          Order integration
        </div>
      </div>
      <div class="row dark-text text-thirteen q-mt-sm">
        <div class="col-6">
          <div v-if="trackingMessage.receiver_country" class="flex items-center no-wrap" style="gap: 7px;">
            <img
              decoding="async"
              :src="`/assets/img/${trackingMessage.receiver_country?.iso?.toLowerCase?.() ?? ''}.svg`"
              style="width: 20px; height: 20px; border-radius: 50%; object-fit: cover"
            />
            <div class="dark-text text-thirteen full-height">
              {{ trackingMessage.receiver_country?.default_name }}
            </div>
          </div>
        </div>
        <div class="col-6">
          {{ trackingMessage.integration?.name ?? "" }}
        </div>
      </div>
    </div>
    <div class="flex row justify-between gap-10">
      <shipvagoo-button
        v-for="action in actions"
        :key="action.label"
        class="full-width"
        height="32px"
        style="flex:1;min-height: 32px !important"
        :label="action.label"
        @click="action.handler"
        outline
      />
    </div>

  </div>

</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits } from "vue";
import { IPersonalizedMessage } from "../model/personalized-message.model";
import { capitalize } from "lodash-es";

interface IProps {
  trackingMessage: IPersonalizedMessage;
}

const props = defineProps<IProps>();

const emit = defineEmits(["edit", "delete"]);


const actions = computed(() => [
  {
    label: "Edit",
    handler: () => emit("edit", props.trackingMessage)
  },
  {
    label: "Delete",
    handler: () => emit("delete")
  }
]);
</script>

<style lang="scss" scoped>
.integration-box {
  padding: 18px;
  border: 1px solid rgba(125, 131, 152, 0.20);
  border-radius: 8px;
}


</style>
