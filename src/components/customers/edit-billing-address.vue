<template>
  <shipvagoo-modal separator-class="q-mx-lg" title="Edit customer" size="md">
    <shipvagoo-card-section>
      <div class="two-columns-responsive gap-20">
        <shipvagoo-input-v1
          v-model="form.name"
          class="flex-1"
          label="Name"
          placeholder="Enter name"
          :error="$v.name.$error"
          :error-message="$v.name.$errors?.[0]?.$message.toString()"
        />
        <shipvagoo-input-v1
          v-model="form.email"
          class="flex-1"
          label="Email"
          placeholder="Enter email"
          :error="$v.email.$error"
          :error-message="$v.email.$errors?.[0]?.$message.toString()"
        />
      </div>
      <div class="two-columns-responsive gap-20 q-mt-lg">
        <shipvagoo-input-v1
          v-model="form.phone"
          class="flex-1"
          :label="`Mobile ${getDialCode(form.country?.iso)}`"
          placeholder="Enter phone/mobile"
          :error="$v.phone.$error"
          :error-message="$v.phone.$errors?.[0]?.$message.toString()"
        />
        <shipvagoo-input-v1
          v-model="form.address"
          class="flex-1"
          label="Address"
          placeholder="Enter address"
          :error="$v.address.$error"
          :error-message="$v.address.$errors?.[0]?.$message.toString()"
        />
      </div>
      <div class="two-columns-responsive gap-20 q-mt-lg">
        <shipvagoo-input-v1
          v-model="form.address2"
          class="flex-1"
          label="Address 2"
          placeholder="Enter address 2"
          :error="$v.address2.$error"
          :error-message="$v.address2.$errors?.[0]?.$message.toString()"
        />
        <shipvagoo-input-v1
          v-model="form.zipcode"
          class="flex-1"
          label="Postal code"
          placeholder="Enter postal code"
          :error="$v.zipcode.$error"
          :error-message="$v.zipcode.$errors?.[0]?.$message.toString()"
          @update:model-value="onChangeZipCode($event)"
        />
      </div>
      <div class="two-columns-responsive gap-20 q-mt-lg">
        <shipvagoo-input-v1
          v-model="form.city"
          class="flex-1"
          label="City"
          placeholder="Enter city"
          :error="$v.city.$error"
          :error-message="$v.city.$errors?.[0]?.$message.toString()"
        />
        <shipvagoo-select-v1
          v-model="form.country"
          class="flex-1"
          label="Country"
          placeholder="Select country"
          option-value="country_code"
          option-label="default_name"
          :error="$v.country.$error"
          :error-message="$v.country.$errors[0]?.$message.toString()"
          :options="$store.state.countryStore.countries"
        >
          <template #prepend>
            <shipvagoo-country-icon v-if="form.country" :country="form.country" />
          </template>
          <template #option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section side>
                <shipvagoo-country-icon v-if="scope.opt" :country="scope.opt" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ scope.opt.default_name }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </shipvagoo-select-v1>
      </div>
    </shipvagoo-card-section>
    <shipvagoo-card-actions>
      <shipvagoo-button color="brand" outline min-width="80px" v-close-popup>
        Cancel
      </shipvagoo-button>
      <shipvagoo-button
        color="brand"
        label="Save"
        min-width="80px"
        @click="onClickUpdateBillingAddress"
      />
    </shipvagoo-card-actions>
  </shipvagoo-modal>
</template>

<script setup lang="ts">
  import useVuelidate from '@vuelidate/core';
  import { helpers, required, numeric, email, maxLength } from '@vuelidate/validators';
  import { onBeforeMount, ref } from 'vue';
  import * as _ from 'lodash-es';
  import { ICountry } from '../../model/country.model';
  import { IOrderAddress } from '../../model/order.model';
  import HelperService from '../../services/helper.service';
  import { useHelper } from '../../composeable/Helper';
  import {
    loadingService,
    loggingService,
    mapService,
    notificationService,
    orderService,
  } from '../../services/_singletons';
  import { useStore } from '../../store';
  import postCodes from '../../assets/json/postcodes.json';

  const { replaceDialCode, getDialCode } = useHelper();
  const $store = useStore();
  const emit = defineEmits(['close', 'update']);
  const props = defineProps<{
    billingAddress: IOrderAddress;
    orderCode: number;
  }>();

  interface IOrderAddressForm {
    name: string;
    email: string | null;
    phone: string | null;
    address: string;
    address2: string | null;
    zipcode: string;
    city: string;
    country: ICountry | null;
    province_code: string | null;
    province: string | null;
    latitude: string | null;
    longitude: string | null;
  }

  const form = ref<IOrderAddressForm>({
    name: '',
    email: '',
    phone: '',
    address: '',
    address2: '',
    zipcode: '',
    city: '',
    country: null,
    province_code: '',
    province: '',
    latitude: '',
    longitude: '',
  });

  const $v = useVuelidate(
    {
      name: {
        required: helpers.withMessage('This field is required', required),
      },
      email: {
        required: helpers.withMessage('This field is required', required),
        email: helpers.withMessage('Enter a valid email address.', email),
      },
      phone: {
        required: helpers.withMessage('This field is required', required),
      },
      address: {
        required: helpers.withMessage('This field is required', required),
        maxLength: maxLength(60),
      },
      address2: {
        maxLength: maxLength(60),
      },
      zipcode: {
        required: helpers.withMessage('This field is required', required),
        numeric: helpers.withMessage('Invalid zipcode', numeric),
      },
      city: {
        required: helpers.withMessage('This field is required', required),
      },
      country: {
        required: helpers.withMessage('This field is required', required),
      },
    },
    form,
  );

  const onClickUpdateBillingAddress = async () => {
    await $v.value.$validate();

    if (
      $v.value.$error ||
      !form.value.phone ||
      !form.value.country ||
      !form.value.city ||
      !form.value.zipcode ||
      !form.value.address
    ) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:update-billingAddress');

      const name = HelperService.parseFullName(form.value.name);

      await orderService.updateBilling(props.orderCode, {
        name: form.value.name,
        first_name: `${name.first} ${name.middle}`.trim() ?? null,
        last_name: name.last ?? null,
        email: form.value.email,
        phone: form.value.phone,
        mobile: form.value.phone,
        address1: form.value.address,
        address2: form.value.address2 ?? '',
        city: form.value.city,
        zip: form.value.zipcode,
        country: String(form.value.country.default_name),
        country_code: String(form.value.country.iso),
        dialCode: getDialCode(form.value.country.iso, false),
        latitude: form.value.latitude,
        longitude: form.value.longitude,
        province: form.value.province,
        province_code: form.value.province_code,
      });
      emit('update');
      emit('close');
    } catch (error) {
      loggingService.error('Error updating billingAddress', error);
      notificationService.showError('Error updating billingAddress');
    } finally {
      loadingService.stopLoading('main-loader:update-billingAddress');
    }
  };

  const onChangeZipCode = async (zip: string | number | null) => {
    if (!form.value.country || !zip || typeof zip !== 'string') {
      return;
    }

    const re = new RegExp(postCodes[form.value.country.iso].regex);

    if (!re.test(zip)) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:get-city-from-zip-code');
      const geo = await mapService.getMapInfo(form.value.country.iso, zip);

      if (_.size(geo.results) > 0) {
        const results = _.head(Object.values(geo.results));
        const result = _.head(results);

        form.value.city = result?.city || '';

        form.value.province = result?.province || form.value.province;
        form.value.province_code = result?.province_code || form.value.province_code;
        form.value.latitude = result?.latitude || form.value.latitude;
        form.value.longitude = result?.longitude || form.value.longitude;
      }
    } catch (e) {
      notificationService.showError('Error getting city from zip code');
      loggingService.error('Error getting city from zip code', e);
    } finally {
      loadingService.stopLoading('main-loader:get-city-from-zip-code');
    }
  };

  const hydratePredefinedValues = () => {
    form.value.name = `${props.billingAddress.name}`;
    form.value.email = props.billingAddress.email;
    form.value.phone = replaceDialCode(
      props.billingAddress.phone,
      props.billingAddress.dialCode ?? '',
    );
    form.value.address = props.billingAddress.address1;
    form.value.address2 = props.billingAddress.address2;
    form.value.zipcode = props.billingAddress.zip;
    form.value.city = props.billingAddress.city;
    form.value.country =
      $store.state.countryStore.countriesIndexedByIso[props.billingAddress.country_code] || null;

    form.value.province = form.value.province;
    form.value.province_code = form.value.province_code;
    form.value.latitude = form.value.latitude;
    form.value.longitude = form.value.longitude;
  };
  onBeforeMount(() => {
    hydratePredefinedValues();
  });
</script>
