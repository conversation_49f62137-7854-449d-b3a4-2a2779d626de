<template>
  <div class="delete-popup">
    <shipvagoo-card-section
      class="ui_kit-shipvagoo-modal-header row items-center"
      style="padding: 10px 20px 0 20px"
    >
      <q-space />
      <slot name="icon-right" />
      <q-btn v-close-popup icon="close" color="text-dark" flat round dense @click="onClickClose" />
    </shipvagoo-card-section>
    <div class="text-center text-dark text-sixteen text-weight-7" style="margin-top: -10px">
      {{ title ?? 'Are you sure?' }}
    </div>
    <div
      style="font-size: 13px; color: #7d8398; text-align: center"
      class="q-ma-md dark-text text-fourteen"
    >
      {{ confirmationMessage ?? 'Are you sure you want to delete this return portal?' }}
    </div>
    <div class="q-ma-md flex justify-between gap-10 q-mt-md">
      <div class="flex-1">
        <shipvagoo-button
          height="36px"
          style="min-height: 36px"
          no-caps
          @click="onClickClose"
          v-close-popup
          class="full-width"
          outline
          :label="cancelLabel ?? 'Cancel'"
        />
      </div>
      <div class="flex-1">
        <shipvagoo-button
          height="36px"
          style="min-height: 36px"
          no-caps
          @click="onClickConfirm"
          v-close-popup
          class="full-width"
          :label="confirmLabel ?? 'Confirm'"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  const emit = defineEmits(['close', 'confirm']);

  interface IProps {
    confirmationMessage: string | null;
    title?: string | null;
    confirmLabel?: string | null;
    cancelLabel?: string | null;
  }

  defineProps<IProps>();
  const onClickClose = () => {
    emit('close');
  };
  const onClickConfirm = () => {
    emit('confirm');
  };
</script>

<style scoped>
  .delete-popup {
    width: 450px;
    max-width: 450px;
    background-color: white;
  }
</style>
