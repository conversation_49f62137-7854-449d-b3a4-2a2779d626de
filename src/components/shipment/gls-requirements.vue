<template>
  <shipvagoo-modal size="md" title="GLS Requirements">
    <shipvagoo-card-section>
      <p>
        There are a number of size and packaging requirements you need to take into consideration,
        when using GLS services.
      </p>
      <h5>Shipping products</h5>
      <p>
        There are different types of GLS services (shipping products) that you can purchase and book
        through Shipvagoo.
      </p>
      <h5>Shop Delivery</h5>
      <p>
        GLS Shop Delivery is an affordable and efficient product that you can use if you wish to
        send packages to private recipients by using GLS' network of Parcel Shops. If you're running
        a webshop, use Shipvagoo's shipping modules for free and give your customers the chance to
        choose their desired pick-up point themselves.
      </p>
      <h5>Home Delivery</h5>
      <p>
        GLS Home Delivery is a product that you can use to have parcels delivered to your customers'
        home address. This delivery method is especially convenient if your shipments weigh a bit
        more, because your customers won't have to think about using any extra resources in order to
        transport their parcel home from e.g. a Parcel Shop.
      </p>
      <h5>Business Parcel</h5>
      <p>
        GLS Business Parcel can be used to deliver parcels to those customers who wish to have their
        purchase delivered to their place of work. Use GLS Business Parcel and cater to the needs of
        both your business and private customers.
      </p>
      <h5>Expected delivery time</h5>
      <p>Expected delivery time within Denmark is 1-2 working days.</p>
      <h5>Weight and dimensions</h5>
      <p>It's important that your shipment should comply with the maximum dimensions and weight.</p>
      <table class="gls-requirements-table">
        <thead>
          <tr>
            <td>Length + circ.</td>
            <td>Max. length</td>
            <td>Max. weight</td>
          </tr>
        </thead>
        <tbody  >
          <tr>
            <td>300 cm</td>
            <td>200 cm</td>
            <td>20 kg</td>
          </tr>
        </tbody>
      </table>
    </shipvagoo-card-section>
  </shipvagoo-modal>
</template>

<style scoped lang="scss">
  h5 {
    margin: 10px 0;
  }

  .gls-requirements-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #ddd;

    td {
      padding: 10px;
      border: 1px solid #ddd;
    }
  }
</style>
