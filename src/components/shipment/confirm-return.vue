<template>
  <shipvagoo-modal title="Confirm Return" size="sm" @close="onClose">
    <shipvagoo-card-section>
      Are you sure you want to create return for this shipment for {{ formattedPrice }}?
    </shipvagoo-card-section>
    <shipvagoo-card-actions>
      <shipvagoo-button style="flex: 1" outline @click="onClose">Cancel</shipvagoo-button>
      <shipvagoo-button style="flex: 1" @click="onConfirm">Confirm</shipvagoo-button>
    </shipvagoo-card-actions>
  </shipvagoo-modal>
</template>

<script setup lang="ts">
  import { computed, onBeforeMount, ref } from 'vue';
  import { IShipment } from '../../model/shipment.model';
  import {
    loadingService,
    loggingService,
    notificationService,
    shipmentService,
  } from '../../services/_singletons';

  interface Props {
    shipment: IShipment;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{
    (event: 'close'): void;
    (event: 'confirm', shipment: IShipment): void;
  }>();

  const price = ref<number>(0);

  const formattedPrice = computed(() => {
    return price.value.toLocaleString('da-DK', {
      style: 'currency',
      currency: 'DKK',
    });
  });

  const calculateCharge = async () => {
    try {
      loadingService.startLoading('main-loader:calculate-charge');
      const response = await shipmentService.calculateShipmentCharge(props.shipment.shipment_code);
      price.value = response.totals.after_vat.amount;
    } catch (e) {
      notificationService.showError('Failed to calculate charge');
      loggingService.error('Failed to calculate charge', e);
    } finally {
      loadingService.stopLoading('main-loader:calculate-charge');
    }
  };

  const onConfirm = () => {
    emit('confirm', props.shipment);
    emit('close');
  };

  const onClose = () => {
    emit('close');
  };

  onBeforeMount(() => {
    calculateCharge();
  });
</script>
