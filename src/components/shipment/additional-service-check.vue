<template>
  <div class="additional-service-check">
    <img :src="AdditionalServiceCheck" />
    <span>{{ label }}</span>
  </div>
</template>

<script setup lang="ts">
  import AdditionalServiceCheck from '../../assets/img/additional-service-check.svg';

  interface Props {
    label: string;
  }

  defineProps<Props>();
</script>

<style scoped lang="scss">
  .additional-service-check {
    display: flex;
    gap: 10px;
    align-items: center;
  }
</style>
