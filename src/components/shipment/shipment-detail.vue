<template>
  <shipvagoo-modal size="s-lg" :header-separator="false" title="Shipment Details">
    <div class="shipment-basic-detail-bg flex dark-text">
      <div class="shipment-id flex items-center">
        <div class="q-pl-lg">
          <div class="text-thirteen">Shipment ID:</div>
          <div class="text-fourteen text-weight-7 basic-text-margin">
            {{ shipment.shipment_code }}
          </div>
        </div>
      </div>
      <div class="created-at flex items-center">
        <div class="q-pl-lg">
          <div class="text-thirteen">Created Date:</div>
          <div class="text-fourteen text-weight-7 basic-text-margin">
            {{ dayjs(String(shipment.created_at)).format('DD/MM/YYYY - HH:mm') }}
          </div>
        </div>
      </div>
      <div class="carrier flex items-center">
        <div class="q-pl-lg">
          <div class="text-thirteen">Carrier</div>
          <div
            class="text-fourteen flex items-center text-weight-7 basic-text-margin"
            v-if="carrier"
          >
            <img
              title="Carrier"
              decoding="async"
              style="
                width: 16px;
                height: 16px;
                margin-right: 5px;
                border-radius: 50%;
                object-fit: cover;
              "
              :src="carrier?.icon"
            />
            <div>{{ carrier.name }}</div>
          </div>
        </div>
      </div>
    </div>
    <shipvagoo-card-section style="padding-bottom: 7px !important">
      <div class="booked_detail-modal_content-address">
        <div class="booked_detail-modal_content-address-title dark-text text-weight-7">
          <p>{{ $t('common.sender') }}:</p>
        </div>
        <div class="booked_detail-modal_content-address-title dark-text text-weight-7">
          <p>Recipient</p>
        </div>
      </div>
      <shipvagoo-separator color="rgba(125, 131, 152, 0.20)" />
      <div class="booked_detail-modal_content-address q-mt-sm dark-text">
        <div>
          <div class="flex justify-between">
            <div class="text-weight-7">
              <div>Name</div>
            </div>
            <div class="q-mr-md text-weight-4 dark-text text-thirteen">
              <div>
                {{ `${shipment.sender.firstName} ${shipment.sender.lastName}` }}
              </div>
            </div>
          </div>
          <div class="flex justify-between q-mt-sm">
            <div class="text-weight-7">
              <div>Address</div>
            </div>
            <div class="q-mr-md text-weight-4 dark-text text-thirteen">
              <div class="text-right">
                {{ shipment.sender.addressLine1 }}<br />
                {{ shipment.sender.country }}-{{ shipment.sender.postCode }}
                {{ shipment.sender.city }}
              </div>
            </div>
          </div>
          <div class="flex justify-between q-mt-sm">
            <div class="text-weight-7">
              <div>Phone</div>
            </div>
            <div class="q-mr-md text-weight-4 dark-text text-thirteen">
              <div class="text-right">
                {{ shipment.sender.contactInfo.dialCode }}
                {{ shipment.sender.contactInfo.telephone }}
              </div>
            </div>
          </div>
          <div class="flex justify-between q-mt-sm">
            <div class="text-weight-7">
              <div>Email</div>
            </div>
            <div class="q-mr-md text-weight-4 dark-text text-thirteen">
              <div class="text-right">
                {{ shipment.sender.contactInfo.email }}
              </div>
            </div>
          </div>
        </div>
        <div>
          <div>
            <div class="flex justify-between">
              <div class="text-weight-7">
                <div>Name</div>
              </div>
              <div class="text-weight-4 dark-text text-thirteen">
                <div>
                  <span v-if="shipment.type?.toLowerCase() == 'return'"
                    >{{ shipment.recipient?.company }} /</span
                  >
                  {{ `${shipment.recipient.firstName} ${shipment.recipient.lastName}` }}
                </div>
              </div>
            </div>
            <div class="flex justify-between q-mt-sm">
              <div class="text-weight-7">
                <div>Address</div>
              </div>
              <div class="text-weight-4 dark-text text-thirteen">
                <div class="text-right">
                  {{ shipment.recipient.addressLine1 }}<br />
                  {{ shipment.recipient.country }}-{{ shipment.recipient.postCode }}
                  {{ shipment.recipient.city }}
                </div>
              </div>
            </div>
            <div class="flex justify-between q-mt-sm">
              <div class="text-weight-7">
                <div>Phone</div>
              </div>
              <div class="text-weight-4 dark-text text-thirteen">
                <div class="text-right" v-if="shipment.recipient.contactInfo.telephone">
                  {{ shipment.recipient.contactInfo.dialCode }}
                  {{ shipment.recipient.contactInfo.telephone }}
                </div>
              </div>
            </div>
            <div class="flex justify-between q-mt-sm">
              <div class="text-weight-7">
                <div>Email</div>
              </div>
              <div class="text-weight-4 dark-text text-thirteen">
                <div class="text-right">
                  {{ shipment.recipient.contactInfo.email }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--      <template v-if="true">
              <div class="booked_detail-modal_content-section">
                {{ $t('common.resolution_method') }}
              </div>
              <div style="padding-top: 20px">
                {{ shipment.return_order?.resolution?.name }}
              </div>
            </template>

<template v-if="shipment.source_type !== 'MANUAL' && shipment.source_id">
              <div class="booked_detail-modal_content-section">
                {{ $t('common.reference') }}
              </div>
              <div v-if="shipment.source_type === 'FULFILLMENT'" style="padding-top: 20px">
                {{ shipment.source_id }}
              </div>
              <div v-else style="padding-top: 20px">
                {{ HelperService.formatId(shipment.source_id) }}
              </div>
            </template>

<template v-if="shipment.returning_items">
              <div class="booked_detail-modal_content-section">
                {{ $t('common.returning_items') }}
              </div>
              <div style="margin-top: 20px; border: 1px solid #2929c8; border-radius: 5px">
                <table style="width: 100%">
                  <thead>
                  <tr>
                    <th style="padding: 10px" align="left">{{ $t('common.image') }}</th>
                    <th style="padding: 10px" align="left">{{ $t('common.name') }}</th>
                    <th style="padding: 10px" align="left">{{ $t('common.quantity') }}</th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr v-for="item of shipment.returning_items" :key="`${item.name}-${item.quantity}`">
                    <td style="padding: 5px 10px">
                      <img
                        v-if="item.image"
                        :src="item.image"
                        decoding="async"
                        style="width: 50px; border-radius: 5px; object-fit: cover"
                      />
                      <span v-else>No Image</span>
                    </td>
                    <td style="padding: 5px 10px">{{ item.name }}</td>
                    <td style="padding: 5px 10px">{{ item.quantity }}</td>
                  </tr>
                  </tbody>
                </table>
              </div>
            </template>

<div class="booked_detail-modal_content-section">{{ $t('common.additional_services') }}</div>


<div style="display: flex; padding-top: 20px">
  <div style="flex: 1">
    <additional-service-check v-for="additionalServiceName in additionalServices" :key="additionalServiceName"
      :label="additionalServiceName" />
  </div>

</div>

<div v-if="!isDraft && shipment.carrier_reference" class="booked_detail-modal_content-section">
  {{ $t('shipment_detail.registered_shipment_information') }}
</div>

<div v-if="!isDraft" class="shipment_detail-modal-parcel_informations">
  <info-bar v-if="carrier && lastUpdatedEventDate !== ''" class="shipment_detail-modal-carrier_reference__updated_at"
    dense>
    {{ $t('common.last_updated') }}: {{ lastUpdatedEventDate }}
  </info-bar>
  <div v-for="parcel in shipment.parcels" :key="parcel.carrierReference"
    class="shipment_detail-modal-carrier_reference">
    <div v-if="carrier && parcel.carrierReference" class="shipment_detail-modal-carrier_reference__text">
      {{ $t('common.tracking_no') }}:
      <shipvagoo-hyperlink type="external-link" :to="carrier.tracking_url + parcel.carrierReference"
        :label="parcel.carrierReference" blank noopener noreferer />
    </div>
    <div v-if="carrier && parcel.carrierIntegrationReference" class="shipment_detail-modal-carrier_reference__text">
      {{ $t('common.tracking_code') }}: {{ parcel.carrierIntegrationReference }}
    </div>
  </div>
</div>
-->
    </shipvagoo-card-section>
    <shipvagoo-card-section
      v-if="shipment.return_order?.resolution || receiverParcelShop"
      style="padding-top: 0 !important"
      class="booked_detail-modal_content-address"
    >
      <div>
        <shipvagoo-separator class="q-my-sm q-mr-md" color="rgba(125, 131, 152, 0.20)" />
        <div v-if="shipment.return_order?.resolution">
          <div class="text-weight-7">Refund Preference:</div>
          <shipvagoo-separator class="q-my-sm q-mr-md" color="rgba(125, 131, 152, 0.20)" />
          <div>
            {{ shipment.return_order?.resolution?.name }}
          </div>
        </div>
      </div>
      <div>
        <shipvagoo-separator class="q-my-sm" color="rgba(125, 131, 152, 0.20)" />
        <div v-if="receiverParcelShop">
          <div class="text-weight-7">Parcel Shop:</div>
          <shipvagoo-separator class="q-my-sm" color="rgba(125, 131, 152, 0.20)" />
          <div class="flex justify-end text-right">
            <div
              style="padding-top: 10px; line-height: 16px"
              class="text-weight-4 dark-text text-thirteen"
            >
              <p>{{ receiverParcelShop.name }}</p>
              <p>{{ receiverParcelShop.addressLine1 }}</p>
              <p v-if="receiverParcelShop.addressLine2">{{ receiverParcelShop.addressLine2 }}</p>
              <p>Pakkeshop: {{ shipment.recipient.parcelShopId }}</p>
              <p>
                {{ receiverParcelShop.country }}-{{ receiverParcelShop.postCode }}
                {{ receiverParcelShop.city }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </shipvagoo-card-section>
    <shipvagoo-card-section v-if="shipment.returning_items">
      <template v-if="shipment.returning_items">
        <div class="booked_detail-modal_content-section dark-text text-weight-7">
          <shipvagoo-separator class="q-my-sm" color="rgba(125, 131, 152, 0.20)" />
          <div>Returning Items:</div>
        </div>
        <div>
          <table style="width: 100%" class="returning-items-table">
            <thead>
              <tr>
                <th style="width: 40% !important; padding: 5px" align="left"></th>
                <th style="width: 30% !important; padding: 5px" align="left">
                  {{ $t('common.name') }}
                </th>
                <th style="width: 30% !important; padding: 5px" align="right">
                  {{ $t('common.quantity') }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item of shipment.returning_items" :key="`${item.name}-${item.quantity}`">
                <td style="width: 40% !important" class="q-pt-md">
                  <img
                    v-if="item.image"
                    :src="item.image"
                    decoding="async"
                    style="width: 56px; border-radius: 5px; object-fit: cover"
                  />
                  <span v-else>No Image</span>
                </td>
                <td style="width: 30% !important" align="left" class="q-pt-md">
                  <div>
                    {{ item.name }}
                    <div class="text-twelve" v-if="item.description">
                      Variant: {{ item.description }}
                    </div>
                  </div>
                </td>
                <td style="width: 40% !important" align="right">{{ item.quantity }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </template>
    </shipvagoo-card-section>
    <shipvagoo-card-section>
      <div class="flex">
        <div style="flex: 1" class="flex items-end">
          <div class="flex full-width q-pr-md justify-center items-center">
            <shipvagoo-button-dropdown-v1
              color="#243E90"
              bg-color="#FFFFFF"
              class="q-mr-sm"
              outline
              no-caps
              label="Print at"
              dropdown-icon="fas fa-chevron-right"
              style="flex: 1; height: 40px !important"
            >
              <shipvagoo-list>
                <shipvagoo-menu-item v-if="$store.state.printerStore.printers.length <= 0">
                  {{ $t('common.no_printer_is_set') }}
                </shipvagoo-menu-item>
                <shipvagoo-menu-item
                  v-close-popup
                  clickable
                  v-for="printer in $store.state.printerStore.printers"
                  :key="`printer-${printer.printer_code}`"
                  @click="onClickPrintLabel(printer)"
                >
                  {{ printer.name }}
                </shipvagoo-menu-item>
              </shipvagoo-list>
            </shipvagoo-button-dropdown-v1>
            <shipvagoo-button-dropdown-v1
              no-wrap
              outline
              no-caps
              label="Download"
              dropdown-icon="fas fa-chevron-right"
              style="flex: 1; height: 42px !important"
            >
              <shipvagoo-list>
                <shipvagoo-menu-item
                  v-if="!isDraft && !noInvoice && showDownload"
                  @click="onClickDownloadInvoice(shipment.shipment_code)"
                  v-close-popup
                  clickable
                >
                  Invoice
                </shipvagoo-menu-item>
                <shipvagoo-menu-item @click="onClickDownloadLabel" v-close-popup clickable>
                  Shipping label
                </shipvagoo-menu-item>
              </shipvagoo-list>
            </shipvagoo-button-dropdown-v1>
          </div>
        </div>
        <div style="flex: 1">
          <div v-if="!isDraft">
            <mini-shipment-charge-table :shipment="shipment" />
          </div>
        </div>
      </div>
    </shipvagoo-card-section>
    <!--    <shipvagoo-card-actions align="left" v-if="!isDraft && !noInvoice && showDownload">
          <shipvagoo-button @click="onClickDownloadInvoice(shipment.shipment_code)">
            <span>{{ $t('shipment_detail.download_invoice') }}</span>
            <q-icon
              size="sm"
              class="q-ml-sm"
              name="download"
            />
          </shipvagoo-button>
        </shipvagoo-card-actions>-->
  </shipvagoo-modal>
</template>
<script setup lang="ts">
  import dayjs from 'dayjs';
  import { computed, onBeforeMount, ref } from 'vue';
  import * as _ from 'lodash-es';
  //import {useI18n} from 'vue-i18n';
  import { IAdditionalServiceCharge, IParcelShop, IShipment } from '../../model/shipment.model';
  import CarrierService from '../../services/carrier.service';
  import HelperService from '../../services/helper.service';
  import WindowService from '../../services/window.service';
  import {
    invoiceService,
    loadingService,
    loggingService,
    notificationService,
    shipmentService,
  } from '../../services/_singletons';
  import { useStore } from '../../store';
  import { IPrinter } from '~/model/printer.model';

  const $store = useStore();

  //const {t} = useI18n();

  interface Props {
    shipment: IShipment;
    isDraft?: boolean;
    noInvoice?: boolean;
  }

  let windowRef: Window | null;

  const props = withDefaults(defineProps<Props>(), {
    isDraft: false,
    noInvoice: false,
  });
  const emits = defineEmits(['downloadLabel', 'printLabel']);
  const carrier = computed(() =>
    CarrierService.getCarrierFromShippingProductCode(
      $store.state.courierStore.carriers,
      $store.state.courierStore.carrierProducts,
      props.shipment.carrier_product_code,
    ),
  );
  const showDownload = ref<boolean>(false);
  /*const shipmentEvents = computed(() => props.shipment.shipment_events || []);
const lastUpdatedEventDate = computed(() => {
  const event = _.maxBy(shipmentEvents.value, (e: IShipmentEvent) => e.event_created_at);

  return event
    ? dayjs(event.event_created_at).format('DD/MM/YYYY HH:mm:ss')
    : dayjs(props.shipment.updated_at).format('DD/MM/YYYY HH:mm:ss');
});*/
  const additionalServiceCharges = ref<IAdditionalServiceCharge[]>([]);
  /*const additionalServices = computed((): string[] => {
  if (!props.shipment.additional_services) {
    return [];
  }

  return props.shipment.additional_services
    .map((asCode) => {
      const additionalServiceCharge = additionalServiceCharges.value.find(
        (asc) => asc.as_charge_code === asCode,
      );

      if (!additionalServiceCharge) {
        return '';
      }

      return additionalServiceCharge.additional_service.name;
    })
    .filter((v) => v !== '');
});*/
  const receiverParcelShop = ref<IParcelShop | null>(null);
  /*
const modalTitle = computed(() => {
  const createdAt: string = dayjs(props.shipment.created_at).format('DD MMM YYYY');

  if (props.isDraft) {
    return `${t('common.created_at')} ${createdAt}`;
  }

  const id: string = HelperService.formatId(props.shipment.shipment_code);

  return `Shipment ID: ${id} - ${t('common.created_at')} ${createdAt}`;
});*/

  const downloadInvoice = async (shipmentCode: number) => {
    if (!windowRef) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:download-pdf');

      const invoice = await shipmentService.getInvoiceFromShipment(shipmentCode);
      const response = await invoiceService.getInvoicePdf(invoice.invoice_code);
      const blob: Blob = await HelperService.toBlob(
        `data:application/pdf;base64,${encodeURI(response.file)}`,
      );

      windowRef.location = URL.createObjectURL(blob);
    } catch (error) {
      windowRef.close();
      loggingService.error('Error while downloading PDF', error);
      notificationService.showError('Error while downloading PDF');
    } finally {
      loadingService.stopLoading('main-loader:download-pdf');
    }
  };

  const getAdditionalServices = async () => {
    try {
      loadingService.startLoading('main-loader:get-additional-services');
      additionalServiceCharges.value = await shipmentService.getAdditionalServiceCharges(
        props.shipment.sender_country_code,
        props.shipment.receiver_country_code,
        props.shipment.carrier_product_code,
      );
    } catch (e) {
      loggingService.error('Error getting additional services', e);
      notificationService.showError('Error getting additional services');
    } finally {
      loadingService.stopLoading('main-loader:get-additional-services');
    }
  };

  const onClickDownloadLabel = () => {
    emits('downloadLabel', props.shipment);
  };
  const onClickDownloadInvoice = async (invoiceCode: number) => {
    windowRef = WindowService.openWindow('Shipment Invoice Preview');

    downloadInvoice(invoiceCode);
  };
  const onClickPrintLabel = async (printer: IPrinter) => {
    emits('printLabel', { printer: printer, shipment: props.shipment });
  };

  const getParcelShop = async () => {
    if (!props.shipment.recipient.parcelShopId || !carrier.value) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:get-parcel-shop');

      const parcelShop: IParcelShop = await shipmentService.getParcelShop(
        props.shipment.recipient.parcelShopId,
        carrier.value.carrier_id,
      );
      receiverParcelShop.value = parcelShop;
    } catch (e) {
      loggingService.error('Error getting parcel shop', e);
      notificationService.showError('Error getting parcel shop');
    } finally {
      loadingService.stopLoading('main-loader:get-parcel-shop');
    }
  };

  onBeforeMount(() => {
    getAdditionalServices();
    if (dayjs().month() > dayjs(props.shipment.created_at).month()) {
      showDownload.value = true;
    }
    if (props.shipment.recipient.parcelShopId) {
      getParcelShop();
    }
  });
</script>

<style lang="scss" scoped>
  .booked_detail {
    &-modal_content {
      padding: 20px;

      &-address {
        display: flex;

        p {
          margin: 0;
        }

        & > * {
          flex: 1;
        }

        &-title {
          margin-bottom: 10px;
        }
      }

      &-section {
        padding-top: 24px;
        padding-bottom: 8px;
        border-bottom: 1px solid rgba(125, 131, 152, 0.2);
      }
    }
  }

  .shipment_detail-modal-carrier_reference + .shipment_detail-modal-carrier_reference {
    padding-top: 20px;
  }

  .shipment_detail-modal-carrier_reference__updated_at {
    margin-top: 14px;
    margin-bottom: 14px;
  }

  .shipment_detail-modal-carrier_reference__text {
    margin: 0;
    font-size: 15px;
    line-height: 20px;
    color: #1f2124;
  }

  .shipment-basic-detail-bg {
    flex-wrap: wrap;
    width: 100%;
    height: 60px;
    font-family: Helvetica, sans-serif;
    color: #212121;
    background: #f7f7f7;

    .shipment-id {
      flex: 1;
      border: 1px solid rgba(125, 131, 152, 0.2);
    }

    .created-at {
      flex: 1;
      border: 1px solid rgba(125, 131, 152, 0.2);
      border-left: none;
    }

    .carrier {
      flex: 1;
      border: 1px solid rgba(125, 131, 152, 0.2);
      border-left: none;
    }

    .basic-text-margin {
      margin-top: 3px;
    }
  }

  .returning-items-table {
    width: 100%;
    border-collapse: collapse;

    tbody tr:not(:last-child) {
      border-bottom: 1px solid rgba(125, 131, 152, 0.2);
    }
  }
</style>
