<template>
  <div class="mini-shipment-charge-table--container dark-text">
    <div class="shipment-header">
      <div class="full-width overview-header dark-text text-thirteen text-weight-7 flex items-center">
        Overview:
      </div>
      <shipvagoo-separator color="rgba(125, 131, 152, 0.20)" style="margin-right: 13px;margin-left: 13px;"/>
      <div class="shipment-body dark-text text-thirteen"
           :style="{minHeight:height}"
      >
        <table v-if="calculatedCharge" class="mini-shipment-charge-table">
          <tbody>
          <tr>
            <td class="text-weight-7 dark-text">Service</td>
            <td class=" dark-text" align="right">{{ calculatedCharge['display-name'] }}</td>
            <th/>
          </tr>
          <tr v-for="parcel in calculatedCharge.parcels" :key="parcel.description + uuid()">
            <td class="text-weight-7 dark-text">{{ parcel.count }} x {{ parcel.weight }}</td>
            <td class=" dark-text" align="right">
              {{ PriceService.formatPrice(parcel.item_total) }}
            </td>
          </tr>
          <tr
            v-for="additionalService in calculatedCharge.additional_services"
            :key="`${additionalService.name} ${additionalService.price}`"
          >
            <td class="text-weight-7 dark-text">
              {{ additionalService.name }}
            </td>
            <td class=" dark-text" align="right">
              {{ PriceService.formatPrice(parseFloat(additionalService.price)) }}
            </td>
          </tr>
          <tr>
            <td class="text-weight-7 dark-text">VAT</td>
            <td class=" dark-text" align="right">
              {{ PriceService.formatPrice(calculatedCharge.totals.vat.amount) }}
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="shipment-footer dark-text text-thirteen flex justify-center">
      <table v-if="calculatedCharge" class="mini-shipment-charge-table">
        <tbody>
        <tr>
          <td class="text-weight-7 dark-text">
            Total
          </td>
          <td class="text-sixteen blue-text" align="right">
            <strong>
              {{ PriceService.formatPrice(Number((calculatedCharge.totals.after_vat.amount).toFixed(3))) }}
            </strong>
          </td>
        </tr>
        </tbody>
      </table>
    </div>
    <div class="flex justify-center items-center">
      <q-inner-loading :showing="loadingService.loadings.has('mini-shipment-charge-table:loading')"
                       class="flex justify-center items-center">
        <q-spinner size="50px" color="primary"/>
      </q-inner-loading>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onBeforeMount, ref} from 'vue';
import {v4 as uuid} from 'uuid';
import {ICalculateChargeResponse, IShipment} from '../../model/shipment.model';
import {loadingService, loggingService, notificationService, shipmentService,} from '../../services/_singletons';
import PriceService from '../../services/price.service';

interface Props {
  shipment: IShipment;
  charges?: ICalculateChargeResponse | undefined | null;
  height?: string
}

const props = withDefaults(defineProps<Props>(), {
  charges: null,
  height: '150px'
});

const calculatedCharge = ref<ICalculateChargeResponse | null>(props.charges);

const calculateCharge = async () => {
  try {
    loadingService.startLoading('mini-shipment-charge-table:loading');
    calculatedCharge.value = props.shipment.source_type === 'RETURN' ? await shipmentService.calculateShipmentCharge(props.shipment.shipment_code) : await shipmentService.calculateShipmentFinalCharge(props.shipment.shipment_code);
  } catch (e) {
    notificationService.showError('Error while calculating charge');
    loggingService.error('Error while calculating charge', e);
  } finally {
    loadingService.stopLoading('mini-shipment-charge-table:loading');
  }
};

onBeforeMount(() => {
  if (!props.charges) {
    calculateCharge();
  }
});
</script>

<style scoped lang="scss">
.mini-shipment-charge-table--container {
  width: 100%;
  min-height: 150px;
}

.mini-shipment-charge-table {
  width: 100%;
  border-collapse: collapse;

  td {
    padding: 8px 13px;
    border: none;
  }
}


.overview-header {
  height: 37px;
  padding-left: 12px;
}


/*.ticketRip {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.circleLeft {
  width: 13px;
  height: 22px;
  border-radius: 0 12px 12px 0;
  border: 1px dashed rgba(125, 131, 152, 0.20);
  border-left: none;
  background: #fff
}

.ripLine {
  width: 100%;
  border-top: 1px solid rgba(125, 131, 152, 0.20);
  border-top-style: dashed;
}

.circleRight {
  width: 13px;
  height: 22px;
  border-radius: 12px 0 0 12px;
  border: 1px dashed rgba(125, 131, 152, 0.20);
  border-right: none;
  background: #fff;
}*/

.shipment-header {
  border: 1px solid rgba(125, 131, 152, 0.20);
  border-bottom: none;
  border-radius: 4px 4px 0 0;
}

.shipment-body {
  border-bottom: none;
}

.shipment-footer {
  height: 40px;
  background: #F7F7F7;
  border-radius: 0 0 4px 4px;
}
</style>
