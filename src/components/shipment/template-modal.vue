<template>
  <shipvagoo-modal :header-separator="false" size="x-md" style="margin-top: 20px;" :title="modalTitle">
    <shipvagoo-separator style="margin:0 20px"></shipvagoo-separator>

    <shipvagoo-stepper ref="stepper" class="create-template-modal-stepper-v2" v-model="step">
      <q-step :name="1" title="Recipient" :done="step > 1">
        <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 15px;padding:0 8px !important;">
          <div class="dark-text text-thirteen text-weight-7">
            Shipment details:
          </div>
          <shipvagoo-separator />
          <shipvagoo-input-v1 v-model="steps.one.templateName" placeholder="Enter shipping rule name"
            :error-message="$v.one.templateName.$errors?.[0]?.$message" :error="$v.one.templateName.$error"
            label="Shipping rule name" />
          <div class="input-double input-double-responsive">
            <shipvagoo-select-v1 v-model="steps.one.senderCountry" label="Origin country" :options="countries" disable
              option-label="default_name" option-value="country_code"
              :error-message="$v.one.senderCountry.$errors?.[0]?.$message" :error="$v.one.senderCountry.$error">
              <template #prepend>
                <shipvagoo-country-icon v-if="steps.one.senderCountry" :country="steps.one.senderCountry" />
              </template>
              <template #option="scope">
                <q-item v-bind="scope.itemProps">
                  <q-item-section side>
                    <shipvagoo-country-icon v-if="scope.opt" :country="scope.opt" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ scope.opt.default_name }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
            </shipvagoo-select-v1>

            <shipvagoo-select-v1 v-model="steps.one.receiverCountry" label="Destination country" :options="countries"
              :error-message="$v.one.receiverCountry.$errors?.[0]?.$message" :error="$v.one.receiverCountry.$error"
              option-label="default_name" option-value="country_code" placeholder="Select"
              @update:model-value="onChangeReceiverCountry" use-input input-debounce="0"
              @filter="onFilterReceiverCountries" fill-input hide-selected>
              <template #prepend>
                <shipvagoo-country-icon v-if="steps.one.receiverCountry" :country="steps.one.receiverCountry" />
              </template>
              <template #option="scope">
                <q-item v-bind="scope.itemProps">
                  <q-item-section side>
                    <shipvagoo-country-icon v-if="scope.opt" :country="scope.opt" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ scope.opt.default_name }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
            </shipvagoo-select-v1>

          </div>
          <shipvagoo-separator class="q-my-md" />
          <div class="gap-10 create-shipment-stepper-v2-shipments-details dark-text text-thirteen text-weight-7">
            <div class="flex-1">
              <div>Carrier</div>
              <shipvagoo-radio-card class="create-shipment-stepper-v2-shipments-details-radio-card"
                v-for="(carrier, index) in carriers" :key="`shipment_create_carrier${index}`" v-model="carrierCode"
                :radio-value="carrier.carrier_code" :img-src="carrier.icon" :label="carrier.name"
                @update:model-value="onChangeCarrier">
              </shipvagoo-radio-card>
            </div>
            <div class="flex-1">
              <div>Service</div>
              <shipvagoo-radio-card class="create-shipment-stepper-v2-shipments-details-radio-card"
                v-for="(shippingProduct, index) in shippingProducts" :key="`shipment_create_shipping_product${index}`"
                v-model="shippingProductCode" :radio-value="shippingProduct.carrier_product_code"
                :label="shippingProduct.name" @update:model-value="onChangeShippingProduct">
              </shipvagoo-radio-card>
              <!--            <div v-if="!shippingProducts.length" class="dark-subtitle-text q-mt-md text-weight-4">Services not found</div>-->
            </div>
            <div class="flex-1">
              <div>Parcel weight</div>
              <shipvagoo-radio-card class="create-shipment-stepper-v2-shipments-details-radio-card"
                v-for="(weightClass, index) in weightClasses" :key="`shipment_create_weight_class${index}`"
                v-model="weightClassCode" :radio-value="weightClass.weight_class_code" :label="`${parseFloat(weightClass.min_weight.toString())}-${parseFloat(weightClass.max_weight.toString())}
                          ${weightClass.unit.toLowerCase()}`">
              </shipvagoo-radio-card>
            </div>
          </div>
          <div v-if="!carriers.length" class="dark-subtitle-text text-center q-mt-md">
            No carriers found
          </div>
          <div class="q-mt-xs" v-if="steps.one.carrier">
            Read more about {{ steps.one.carrier?.name }} general information, surcharges and more <strong>
              <a :href="carrierInfoLink(steps.one.carrier?.carrier_id)" class="blue-text" @click.stop
                target="_blank">click
                here.</a>
            </strong>
          </div>
        </shipvagoo-card-section>
      </q-step>
      <q-step :name="2" title="Tracking" :done="step > 2">
        <div>
          <div class="dark-text text-thirteen text-weight-7">Tracking:</div>
          <shipvagoo-separator class="q-my-md" />
          <div class="two-columns-responsive gap-10">
            <template v-for="(additionalServiceCharge, index) in additionalServiceCharges">
              <shipvagoo-checkbox-card :key="`as_charges${additionalServiceCharge.additional_service_code}_${index}`"
                v-model="steps.two.additionalServices[additionalServiceCharge.as_charge_code]" class="flex-1"
                v-if="additionalServiceCharge.additional_service_code == tracking_email_as_code"
                :title="`Tracking email ${ShipmentService.formatAdditionalServicePrice(parseFloat(additionalServiceCharge.price))}`"
                subtitle="Send customized, automated and activity based tracking emails to your customers">
              </shipvagoo-checkbox-card>
              <shipvagoo-checkbox-card :key="`as_charges${additionalServiceCharge.additional_service_code}_${index}`"
                v-model="steps.two.additionalServices[additionalServiceCharge.as_charge_code]" class="flex-1"
                v-if="additionalServiceCharge.additional_service_code == tracking_sms_as_code" disable
                :title="`Tracking SMS (0,00 DKK)`"
                subtitle="Send customized, automated and activty based tracking SMS to your customers">
              </shipvagoo-checkbox-card>
            </template>
          </div>
          <div class="q-mt-md q-px-md">
            <div class="dark-text text-weight-7 text-fourteen">What are tracking messages?</div>
            <div class="dark-text text-thirteen q-mt-xs">Tracking messages are automated tracking emails sent tor your
              clients to keep them informed about the delivery status of their parcels. You can set up tracking messages
              under Settings.
            </div>
          </div>
        </div>
      </q-step>
      <template #navigation>
        <q-stepper-navigation class="create-shipment-stepper-v2-navigation" style="margin-top: -30px;">
          <shipvagoo-button color="brand" class="create-shipment-stepper-v2-navigation-previous" v-if="step > 1"
            label="Back" outline no-caps @click="onClickPrevious" />
          <shipvagoo-button color="brand" v-if="step == 1" class="create-shipment-stepper-v2-navigation-next"
            label="Next" no-caps @click="onClickNext" />
          <shipvagoo-button v-if="step === 2 && isCreate" class="create-shipment-stepper-v2-navigation-next"
            label="Save" @click="onCreateTemplateSubmit" no-caps />
          <shipvagoo-button v-if="step === 2 && isEdit" class="create-shipment-stepper-v2-navigation-next" label="Save"
            @click="onEditTemplateSubmit" no-caps />
        </q-stepper-navigation>
      </template>
    </shipvagoo-stepper>
  </shipvagoo-modal>

  <q-dialog v-model="isGlsRequirementsOpen">
    <gls-requirements />
  </q-dialog>

  <q-dialog v-model="isDaoRequirementsOpen">
    <dao-requirements />
  </q-dialog>
  <q-dialog v-model="showAdditionalServicesDialog">
    <additional-services />
  </q-dialog>
</template>

<script setup lang="ts">
import useVuelidate, { ValidationArgs } from "@vuelidate/core";
import { helpers, required } from "@vuelidate/validators";
import * as _ from "lodash-es";
import { ComponentPublicInstance, computed, onBeforeMount, ref, watch } from "vue";
import { ICarrier, IShippingProduct, IWeightClass } from "../../model/carrier.model";
import { ICountry } from "../../model/country.model";
import {
  IAdditionalServiceCharge
} from "../../model/shipment.model";
import {
  carrierService,
  loadingService,
  loggingService,
  notificationService,
  shipmentService,
  templateService
} from "../../services/_singletons";
import { useStore } from "../../store";
import { ITemplate } from "../../model/template.model";
import CarrierService from "../../services/carrier.service";
import ShipmentService from "../../services/shipment.service";
import { useHelper } from "../../composeable/Helper";


const { denmark, carrierInfoLink, extractMinMaxWeight } = useHelper();
const $store = useStore();
const tracking_email_as_code = 3730866037;
const tracking_sms_as_code = 3333128889;
//const carrier_email_as_code = 1097535283;
//const carrier_sms_as_code = 270943235;
interface Props {
  template?: ITemplate | null;
}

const step = ref<number>(1);
const props = defineProps<Props>();
const isEdit = computed(() => !!props.template);
const isCreate = computed(() => !props.template);
const modalTitle = computed(() => {
  return isEdit.value
    ? "Edit shipping rule"
    : "Create shipping rule";
});

interface ITemplateForm {
  one: {
    templateName: string;
    senderCountry: ICountry;
    receiverCountry: ICountry | null;
    carrier: ICarrier | null;
    shippingProduct: IShippingProduct | null;
    weightClass: IWeightClass | null;
    shippingAgreement: string
  },
  two: {
    additionalServices: { [key: number]: boolean };
  }
}

const emit = defineEmits(["close"]);
const shippingProducts = ref<IShippingProduct[]>([]);
//const shipmentAggrements = ref<string[]>(['Shipvagoo']);
const carriers = ref<ICarrier[]>([]);
const productWithClientIds = ref<string>("{}");
const countries = ref<ICountry[]>([]);
const weightClasses = ref<IWeightClass[]>([]);
const additionalServiceCharges = ref<IAdditionalServiceCharge[]>([]);
const isGlsRequirementsOpen = ref<boolean>(false);
const isDaoRequirementsOpen = ref<boolean>(false);
const showAdditionalServicesDialog = ref<boolean>(false);

const receiverCountries = computed(() => {
  return $store.state.countryStore.countries;
});

const steps = ref<ITemplateForm>({
  one: {
    templateName: "",
    senderCountry: denmark,
    receiverCountry: null,
    carrier: null,
    shippingProduct: null,
    shippingAgreement: "Shipvagoo",
    weightClass: null
  },
  two: {
    additionalServices: {}
  }
});
const stepper = ref<ComponentPublicInstance<{ next: () => void; previous: () => void }> | null>(
  null
);
const carrierCode = ref<number | null>();
const shippingProductCode = ref<number | null>();
const weightClassCode = ref<number | null>();

const $v = useVuelidate<ITemplateForm, ValidationArgs<ITemplateForm>>(
  {
    one: {
      templateName: {
        required: helpers.withMessage("This field is required.", required)
      },
      senderCountry: {
        required: helpers.withMessage("This field is required.", required)
      },
      receiverCountry: {
        required: helpers.withMessage("This field is required.", required)
      },
      carrier: {
        required: helpers.withMessage("This field is required.", required)
      },
      shippingProduct: {
        required: helpers.withMessage("This field is required.", required)
      },
      shippingAgreement: {
        required: helpers.withMessage("This field is required.", required)
      },
      weightClass: {
        required: helpers.withMessage("This field is required.", required)
      }
    },
    two: {
      additionalServices: {}
    }
  },
  steps
);



const onFilterReceiverCountries = (val: string, update: CallableFunction) => {
  if (val === "") {
    update(() => {
      countries.value = receiverCountries.value;
    });
    return;
  }
  update(() => {
    const needle = val.toLowerCase();
    countries.value = receiverCountries.value.filter((country: ICountry) => {
      return country.name.toLowerCase().indexOf(needle) > -1;
    });
  });
};
const onClickPrevious = () => {
  step.value -= 1;
};
const onClickNext = async () => {
  if (step.value === 1) {
    await $v.value.one.$validate();
  }
  if (step.value === 1 && $v.value.one.$error) {
    if ($v.value.one.carrier.$error) {
      notificationService.showError("No carrier has been selected.");
    } else if ($v.value.one.shippingProduct.$error) {
      notificationService.showError("No shipping product has been selected.");
    } else if ($v.value.one.weightClass.$error) {
      notificationService.showError("No weight class has been selected.");
    }
    return;
  }
  stepper.value?.next();
};

/** these are self defined due to limit types of radios card **/
watch(() => carrierCode.value, currentVal => {
  if (currentVal) {
    steps.value.one.carrier = getCarrier(currentVal);
  } else {
    steps.value.one.carrier = null;
  }
});
watch(() => shippingProductCode.value, currentVal => {
  if (currentVal) {
    steps.value.one.shippingProduct = getShippingProduct(currentVal);
  } else {
    steps.value.one.shippingProduct = null;
  }
});
watch(() => weightClassCode.value, currentVal => {
  if (currentVal) {
    steps.value.one.weightClass = getWeightClass(currentVal);
  } else {
    steps.value.one.weightClass = null;
  }
});

const onCreateTemplateSubmit = async () => {
  await $v.value.$validate();
  if (
    $v.value.$error ||
    !steps.value.one.shippingProduct ||
    !steps.value.one.senderCountry ||
    !steps.value.one.receiverCountry
  ) {
    notificationService.showError("Seems any field is not filled.");
    return;
  }
  try {
    loadingService.startLoading("main-loader:create-template");
    const product_with_client_ids = JSON.parse(JSON.stringify(productWithClientIds.value));
    const ubsend_client_id = product_with_client_ids[steps.value.one.shippingProduct.product_id] !== undefined ? product_with_client_ids[steps.value.one.shippingProduct.product_id] : "";
    await templateService.createTemplate({
      ubsend_client_id,
      name: steps.value.one.templateName,
      is_default: 0,
      sender_country_code: steps.value.one.senderCountry.country_code,
      receiver_country_code: steps.value.one.receiverCountry.country_code,
      carrier_product_code: steps.value.one.shippingProduct.carrier_product_code,
      parcels: [
        {
          count: 1,
          weight: parseFloat((steps.value.one.weightClass?.max_weight?.toString()) || "0"),
          height: 1,
          width: 1,
          length: 1,
          min_weight: parseFloat((steps.value.one.weightClass?.min_weight?.toString()) || "0"),
          max_weight: parseFloat((steps.value.one.weightClass?.max_weight?.toString()) || "0"),
          unit: steps.value.one.weightClass?.unit.toString() ?? "",
          description: `Parcel ${steps.value.one.weightClass?.min_weight} - ${steps.value.one.weightClass?.max_weight} ${steps.value.one.weightClass?.unit}`
        }
      ],
      additional_services: Object.entries(steps.value.two.additionalServices)
        .filter(([, value]) => value)
        .map(([key]) => Number(key))
    });
    notificationService.showSuccess(`Template ${steps.value.one.templateName} created successfully`);
    emit("close", "create");
  } catch (e) {
    loggingService.error("Error creating template", e);
    notificationService.showError("Error creating template");
  } finally {
    loadingService.stopLoading("main-loader:create-template");
  }
};

const onEditTemplateSubmit = async () => {
  await $v.value.$validate();

  if (
    $v.value.$error ||
    !steps.value.one.shippingProduct ||
    !steps.value.one.senderCountry ||
    !steps.value.one.receiverCountry ||
    !props.template?.template_code
  ) {
    return;
  }

  try {
    loadingService.startLoading("main-loader:update-template");
    const product_with_client_ids = JSON.parse(JSON.stringify(productWithClientIds.value));
    const ubsend_client_id = product_with_client_ids[steps.value.one.shippingProduct.product_id] !== undefined ? product_with_client_ids[steps.value.one.shippingProduct.product_id] : "";

    await templateService.updateTemplate(props.template.template_code, {
      ubsend_client_id,
      name: steps.value.one.templateName,
      is_default: 0,
      sender_country_code: steps.value.one.senderCountry.country_code,
      receiver_country_code: steps.value.one.receiverCountry.country_code,
      carrier_product_code: steps.value.one.shippingProduct.carrier_product_code,
      parcels: [
        {
          count: 1,
          weight: parseFloat((steps.value.one.weightClass?.max_weight?.toString()) || "0"),
          height: 1,
          width: 1,
          length: 1,
          unit: steps.value.one.weightClass?.unit.toString() ?? "",
          min_weight: parseFloat((steps.value.one.weightClass?.min_weight?.toString()) || "0"),
          max_weight: parseFloat((steps.value.one.weightClass?.max_weight?.toString()) || "0"),
          description: `Parcel ${steps.value.one.weightClass?.min_weight} - ${steps.value.one.weightClass?.max_weight} ${steps.value.one.weightClass?.unit}`
        }
      ],
      additional_services: Object.entries(steps.value.two.additionalServices)
        .filter(([, value]) => value)
        .map(([key]) => Number(key))
    });
    notificationService.showSuccess(`Template ${steps.value.one.templateName} updated successfully`);
    emit("close", "edit");
  } catch (e) {
    loggingService.error("Error updating template", e);
    notificationService.showError("Error updating template");
  } finally {
    loadingService.stopLoading("main-loader:update-template");
  }
};

const getCarriers = async (senderCountry: number, receiverCountry: number) => {
  try {
    shippingProducts.value = [];
    weightClasses.value = [];
    loadingService.startLoading("main-loader:carriers");
    const response = await carrierService.getShippingProducts(senderCountry, receiverCountry);
    carriers.value = _.get(response, "carriers", []);
    productWithClientIds.value = _.get(response, "productWithClientIds", []) as any;
  } catch (e) {
    console.log("Error ", e);
    notificationService.showError("Error getting carriers");
    loggingService.error("Error getting carriers", e);
  } finally {
    loadingService.stopLoading("main-loader:carriers");
  }
};

const getWeightClasses = async (shippingProduct: IShippingProduct | null) => {
  if (!shippingProduct || !steps.value.one.senderCountry || !steps.value.one.receiverCountry) {
    return;
  }

  try {
    const product_with_client_ids = JSON.parse(JSON.stringify(productWithClientIds.value));
    const ubsend_client_id = product_with_client_ids[shippingProduct.product_id] !== undefined ? product_with_client_ids[shippingProduct.product_id] : "";
    loadingService.startLoading("main-loader:get-weight-classes");
    const response = await shipmentService.getWeightClasses(
      shippingProduct.carrier_product_code,
      steps.value.one.senderCountry.country_code,
      steps.value.one.receiverCountry.country_code,
      ubsend_client_id
    );
    weightClasses.value = response;
    if (props.template) {
      hydrateWeightClass();
    }
  } catch (e) {
    notificationService.showError("Error getting weight classes");
    loggingService.error("Error getting weight classes", e);
  } finally {
    loadingService.stopLoading("main-loader:get-weight-classes");
  }
};

// const onClickAddParcel = () => {
//   form.value.parcels.push({
//     id: uuid(),
//     quantity: null,
//     weightClass: null,
//   });
// };

const getAdditionalServices = async (shippingProduct: IShippingProduct | null) => {
  if (!shippingProduct || !steps.value.one.senderCountry || !steps.value.one.receiverCountry) {
    return;
  }

  try {
    loadingService.startLoading("main-loader:get-additional-services");
    additionalServiceCharges.value = await shipmentService.getAdditionalServiceCharges(
      steps.value.one.senderCountry.country_code,
      steps.value.one.receiverCountry.country_code,
      shippingProduct.carrier_product_code
    );
    steps.value.two.additionalServices = {};

    const [firstAdditionalService] = additionalServiceCharges.value || [];
    steps.value.two.additionalServices[firstAdditionalService.as_charge_code] = true;
  } catch (e) {
    loggingService.error("Error getting additional services", e);
    notificationService.showError("Error getting additional services");
  } finally {
    loadingService.stopLoading("main-loader:get-additional-services");
  }
};

const onChangeShippingProduct = async (
  shipping_product_code: number | null
) => {
  if (!shipping_product_code) {
    return;
  }

  const shippingProduct = await getShippingProduct(shipping_product_code);
  steps.value.one.weightClass = null;
  weightClassCode.value = null;
  await Promise.all([getAdditionalServices(shippingProduct), getWeightClasses(shippingProduct)]);
};

const onChangeReceiverCountry = async () => {
  console.log("country changed");
  if (!steps.value.one.senderCountry || !steps.value.one.receiverCountry) {
    console.log("change country null");
    return;
  }
  steps.value.one.carrier = null;
  steps.value.one.shippingProduct = null;
  steps.value.one.weightClass = null;
  try {
    loadingService.startLoading("main-loader:carriers");
    await getCarriers(
      steps.value.one.senderCountry.country_code,
      steps.value.one.receiverCountry.country_code
    );
  } catch (e) {
    console.log("Error ", e);
    notificationService.showError("Error getting carriers");
    loggingService.error("Error getting carriers", e);
  } finally {
    loadingService.stopLoading("main-loader:carriers");
  }
};

const getCountries = async () => {
  countries.value = $store.state.countryStore.countries;
  // countries.value = $store.state.countryStore.countries.filter(
  //   (c) => c.iso === 'DK' || c.iso === 'SE' || c.iso === 'DE',
  // );
};

const getCarrier = (carrier_code: number): ICarrier | null => {
  return carriers.value.find((carrier: ICarrier) => carrier.carrier_code == carrier_code) ?? null;
};
const getShippingProduct = (shipping_product_code: number): IShippingProduct | null => {
  return shippingProducts.value.find((shipping_product: IShippingProduct) => shipping_product.carrier_product_code == shipping_product_code) ?? null;
};
const getWeightClass = (weight_class_code: number): IWeightClass | null => {
  return weightClasses.value.find((weightClass: IWeightClass) => weightClass.weight_class_code == weight_class_code) ?? null;
};
const onChangeCarrier = (carrier_code: number) => {
  const carrier = getCarrier(carrier_code);
  steps.value.one.shippingProduct = null;
  steps.value.one.weightClass = null;
  weightClasses.value = [];
  if (carrier == null) {
    return;
  }
  if (carrier && carrier.products) {
    shippingProducts.value = carrier.products;
  } else {
    shippingProducts.value = $store.state.courierStore.carrierProducts.filter(
      (p) => p.carrier_code === steps.value.one.carrier?.carrier_code
    );
  }
};

const hydrateWeightClass = async () => {
  if (props.template && weightClasses.value.length) {
    const parcel = props.template.parcels[0];
    let weights = extractMinMaxWeight(parcel?.description);
    const weightClass = weightClasses.value.find((weightClass: IWeightClass) => {
      return (weightClass.min_weight == weights[0]) && (weightClass.max_weight == weights[1]);
    });
    steps.value.one.weightClass = weightClass ?? null;
    weightClassCode.value = weightClass?.weight_class_code ?? null;
  }
};
const hydrateFormFromTemplate = async () => {
  if (!props.template) {
    return;
  }

  steps.value.one.templateName = props.template.name;
  steps.value.one.senderCountry = denmark;
  steps.value.one.receiverCountry = countries.value.find(
    (c) => c.country_code === props.template?.receiver_country_code
  ) as ICountry;
  await onChangeReceiverCountry();

  steps.value.one.carrier = CarrierService.getCarrierFromShippingProductCode(
    $store.state.courierStore.carriers,
    $store.state.courierStore.carrierProducts,
    props.template.carrier_product_code
  ) as ICarrier;

  if (steps.value.one.carrier) {
    steps.value.one.carrier.products = carriers.value.find((c: ICarrier) => {
      return c.carrier_code === steps.value.one.carrier?.carrier_code;
    })!.products;
    await onChangeCarrier(steps.value.one.carrier.carrier_code);
    carrierCode.value = steps.value.one.carrier.carrier_code;
  }
  steps.value.one.shippingProduct = shippingProducts.value.find(
    (p) => p.carrier_product_code === props.template?.carrier_product_code
  ) as IShippingProduct;
  if (steps.value.one.shippingProduct) {
    await onChangeShippingProduct(steps.value.one.shippingProduct.carrier_product_code);
    shippingProductCode.value = steps.value.one.shippingProduct.carrier_product_code;


  }
  /* form.value.parcels = ShipmentService.parcelToFormParcel(
     props.template.parcels,
     weightClasses.value,
   );*/

  steps.value.two.additionalServices = {};
  if (props.template.additional_services) {
    props.template.additional_services.forEach((asChargeCode) => {
      const additionalService = additionalServiceCharges.value.find(
        (as) => as.as_charge_code === asChargeCode
      );
      if (!additionalService) return;
      steps.value.two.additionalServices[additionalService.as_charge_code] = true;
    });
  }
};

onBeforeMount(async () => {
  await getCountries();

  if (props.template) {
    await hydrateFormFromTemplate();
  }
});
</script>