<template>
  <shipvagoo-modal :title="`Refund shipment ID: ${shipment.shipment_code}`">
    <shipvagoo-card-section>
      <shipvagoo-textarea v-model="form.cancelReason" placeholder="Cancel reason..." />
    </shipvagoo-card-section>
    <shipvagoo-separator />
    <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 10px">
      <shipvagoo-checkbox
        v-model="form.acceptPenalty"
        label="I understand that the shipping label must not be used. I accept a fee of 200,00 DKK plus shipping costs in case of violation of this."
      />
      <shipvagoo-checkbox
        v-model="form.acceptTerms"
        label="I have read and agree to the Terms and Conditions"
      />
    </shipvagoo-card-section>
    <shipvagoo-separator />
    <shipvagoo-card-actions style="display: flex; justify-content: space-between">
      <shipvagoo-button min-width="120px" outline>Cancel</shipvagoo-button>
      <shipvagoo-button
        min-width="120px"
        padding="0 20px"
        disable
        @click="onClickCancelShipment"
      >
<!--        :disable="!form.acceptPenalty || !form.acceptTerms"-->

        Cancel shipment
      </shipvagoo-button>
    </shipvagoo-card-actions>
  </shipvagoo-modal>
</template>

<script setup lang="ts">
  import useVuelidate, { ValidationArgs } from '@vuelidate/core';
  import { helpers, required } from '@vuelidate/validators';
  import { ref } from 'vue';
  import { IShipment } from '../../model/shipment.model';
  import PriceService from '../../services/price.service';
  import {
    loadingService,
    loggingService,
    notificationService,
    shipmentService,
  } from '../../services/_singletons';
  import { useStore } from '../../store';

  const $store = useStore();

  interface Props {
    shipment: IShipment;
  }

  interface IForm {
    cancelReason: string | null;
    acceptPenalty: boolean;
    acceptTerms: boolean;
  }

  const form = ref<IForm>({
    cancelReason: null,
    acceptPenalty: false,
    acceptTerms: false,
  });

  const $v = useVuelidate<IForm, ValidationArgs<IForm>>(
    {
      cancelReason: {
        required: helpers.withMessage('This field is required.', required),
      },
      acceptPenalty: {},
      acceptTerms: {},
    },
    form,
  );

  const props = defineProps<Props>();
  const emit = defineEmits(['close', 'refresh']);

  const onClickCancelShipment = async () => {
    await $v.value.$validate();

    if (
      $v.value.$error ||
      !form.value.cancelReason ||
      !form.value.acceptPenalty ||
      !form.value.acceptTerms
    ) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:cancel-shipment');
      const shipment = await shipmentService.cancelShipment(
        props.shipment.shipment_code,
        form.value.cancelReason,
      );
      const calculatedCharge = await shipmentService.calculateShipmentCharge(
        shipment.shipment_code,
      );
      notificationService.showSuccess(
        `Shipment successfully canceled, ${PriceService.formatPrice(
          calculatedCharge.totals.after_vat.amount,
        )} DKK has been refunded to your account.`,
      );
      $store.dispatch('companyStore/syncCompanyData');
      emit('refresh');
      emit('close');
    } catch (e) {
      loggingService.error('Error while cancelling shipment', e);
      notificationService.showError('Error while cancelling shipment');
    } finally {
      loadingService.stopLoading('main-loader:cancel-shipment');
    }
  };
</script>
