<template>
  <div class="shipment_card">
    <div class="shipment_card-info">
      <div class="shipment_card-info-name">
        {{ shipment.recipient.firstName }} {{ shipment.recipient.lastName }}
      </div>
      <div class="shipment_card-info-created">Created {{ dayjs(shipment.created_at).format('DD MMM YYYY') }}</div>
    </div>
    <div class="shipment_card-status_color" />
    <shipvagoo-button
      class="shipment_card-close"
      icon="close"
      size="10px"
      padding="4px"
      height="4px"
      flat
    />
  </div>
</template>

<script setup lang="ts">
  import dayjs from 'dayjs';
  import { IShipment } from '../../model/shipment.model';

  interface Props {
    shipment: IShipment;
  }

  defineProps<Props>();
</script>

<style scoped lang="scss">
  .shipment_card {
    position: relative;
    overflow: hidden;
    background: #070726;
    border: 1px solid #2929c8;
    border-radius: 8px;

    &-close {
      position: absolute;
      top: 0;
      right: 0;
    }

    &-info {
      padding: 40px 25px;
      &-name {
        font-size: 15px;
        font-weight: 700;
        line-height: 18px;
      }

      &-created {
        margin-top: 13px;
        font-size: 12px;
        line-height: 14px;
      }
    }

    &-status_color {
      position: absolute;
      right: -1px;
      bottom: -1px;
      left: -1px;
      height: 8px;
      background: #7bc9ee;
      border-radius: 0 0 8px 8px;
    }
  }
</style>