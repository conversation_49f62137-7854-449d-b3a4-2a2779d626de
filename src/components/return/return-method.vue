<template>
  <div
    class="bg-white return-portal-modal">
    <div class="row items-center q-px-md q-py-sm header-bg">
      <div class="text-white" style="font-size:16px;font-weight: 500;">{{title}}</div>
      <q-space/>
      <slot name="icon-right"/>
      <q-btn v-close-popup icon="close"
             color="white" flat round dense @click="onClickClose"/>
    </div>
    <div class="slot-container">
      <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
        <shipvagoo-input v-model="form.name" :label="$t('common.name')"/>
        <!--<shipvagoo-input v-model="form.description" :label="$t('common.description')"/>-->
        <div style="display: flex;flex-direction: row;align-items: end; justify-content: space-between">
          <shipvagoo-select
            style="width: 100%;"
            v-model="form.template"
            :label="$t('common.shipment_template')"
            :options="templates"
            option-value="template_code"
            option-label="name"
            @update:model-value="onChangeTemplate"
          >
            <template v-slot:prepend>
              <div style="left: 111px" class="icon-wrapper">
                <q-icon
                  class="q-ml-xs"
                  style="font-size: 12px; color:gray !important"
                  name="fas fa-exclamation-circle"
                >
                  <q-tooltip anchor="top middle" self="bottom middle" :offset="[10,10]">
                    Applies return parcel transfer weight
                  </q-tooltip>
                </q-icon>
              </div>
            </template>
          </shipvagoo-select>
          <!-- <q-btn
             :disable="!form.template"
             style="width: 33%; font-size: 14px; height: 41px;"
             no-caps
             dense
             @click="onClickEditTemplate(form.template)"
             outline color="brand" size="xs" label="Edit Template"/>-->
        </div>
        <shipvagoo-select
          v-if="servicePoints.length > 0"
          v-model="form.service_point"
          :options="servicePoints"
          :label="$t('common.service_point')"
          option-label="label"
          option-value="id"
        >
          <template v-slot:prepend>
            <div style="left: 77px" class="icon-wrapper">
              <q-icon
                class="q-ml-xs"
                style="font-size: 12px; color:gray !important"
                name="fas fa-exclamation-circle"
              >
                <q-tooltip anchor="top middle" self="bottom middle" :offset="[10,10]">
                  The return parcel will be delivered to choosen service point
                </q-tooltip>
              </q-icon>
            </div>
          </template>
        </shipvagoo-select>
        <shipvagoo-input-group>
          <shipvagoo-select
            v-model="form.price_type"
            :label="$t('common.price_type')"
            :options="priceTypes"
          />
          <shipvagoo-select v-model="form.currency" :label="$t('common.currency')" disable/>
        </shipvagoo-input-group>

        <shipvagoo-input
          @update:modelValue="onAddPrice"
          hide-bottom-space
          borderless
          v-if="form.price_type.value == 'fixed'" v-model="form.price"
          :error="$v.price.$error"
          :error-message="String($v.price.$errors?.[0]?.$message ?? null)"
          label="Price ex. VAT">
        </shipvagoo-input>

      </shipvagoo-card-section>
      <shipvagoo-card-actions>
        <shipvagoo-button v-if="!isEdit" min-width="120px" @click="onClickCreateReturnMethod">
          {{ $t('common.create') }}
        </shipvagoo-button>
        <shipvagoo-button v-if="isEdit" min-width="120px" @click="onClickUpdateReturnMethod">
          {{ $t('common.update') }}
        </shipvagoo-button>
      </shipvagoo-card-actions>
    </div>
  </div>
  <!--  <q-dialog v-model="isTemplateDetailOpen" @before-hide="onCloseTemplateDetail()">
      <template-modal :template="selectedTemplate" @close="onCloseTemplateDetail"/>
    </q-dialog>-->
  <!--<shipvagoo-modal :title="title">
    <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
      <shipvagoo-input v-model="form.name" :label="$t('common.name')" />
      <shipvagoo-input v-model="form.description" :label="$t('common.description')" />
      <shipvagoo-select
        v-model="form.template"
        :label="$t('common.shipment_template')"
        :options="templates"
        option-value="template_code"
        option-label="name"
        @update:model-value="onChangeTemplate"
      />
      <shipvagoo-select
        v-if="servicePoints.length > 0"
        v-model="form.service_point"
        :options="servicePoints"
        :label="$t('common.service_point')"
        option-label="label"
        option-value="id"
      />
      <shipvagoo-input-group>
        <shipvagoo-select
          v-model="form.price_type"
          :label="$t('common.price_type')"
          :options="priceTypes"
        />
        <shipvagoo-select v-model="form.currency" :label="$t('common.currency')" disable />
      </shipvagoo-input-group>

      <shipvagoo-input v-if="form.price_type.value == 'fixed'" v-model="form.price" :label="$t('common.price_excl_vat')" />
    </shipvagoo-card-section>
    <shipvagoo-separator />
    <shipvagoo-card-actions>
      <shipvagoo-button v-if="!isEdit" min-width="120px" @click="onClickCreateReturnMethod">
        {{ $t('common.create') }}
      </shipvagoo-button>
      <shipvagoo-button v-if="isEdit" min-width="120px" @click="onClickUpdateReturnMethod">
        {{ $t('common.update') }}
      </shipvagoo-button>
    </shipvagoo-card-actions>
  </shipvagoo-modal>-->
</template>

<script setup lang="ts">
  import {computed, onBeforeMount, ref} from 'vue';
  import {useI18n} from 'vue-i18n';
  import {IOption} from '../../model/common.model';
  import {IRequestParam} from '../../model/http.model';
  import {IReturnPortal, IReturnPortalMethod} from '../../model/return-portal.model';
  import {IParcelShop, IParcelShopSingleResponse} from '../../model/shipment.model';
  import {ITemplate} from '../../model/template.model';
  import {
    loadingService,
    loggingService,
    notificationService,
    returnPortalService, shipmentService,
    templateService,
  } from '../../services/_singletons';
  import {helpers, requiredIf} from "@vuelidate/validators";
  import useVuelidate, {ValidationArgs} from "@vuelidate/core";
  import PriceService from "../../services/price.service";
  import {danishNumberFormat} from "../../helpers";

  interface Props {
    returnPortal: IReturnPortal;
    returnMethod: IReturnPortalMethod | null;
  }

  const mustBeValidInput = helpers.withParams(
    {type: 'validDecimal'},
    (value: any) => {
      const regex = /^(\d{1,3}(\.\d{3})*|\d+)(,\d{2})?$/;
      if (!regex.test(value as string)) {
        return false;
      }
      return true;
    }
  )

  interface IFormPrice {
    [key: string]: any
  }


  const {t} = useI18n();
  const servicePoints = ref<(IParcelShop & { label: string })[]>([]);
  const props = defineProps<Props>();
  const emit = defineEmits<{
    (event: 'close'): void;
    (event: 'on-create'): void;
    (event: 'on-update'): void;
  }>();

  const priceTypes = [{value: 'fixed', label: 'Fixed'}, {value: 'automatic', label: 'Automatic'}];

  const templates = ref<ITemplate[]>([]);

  const isEdit = computed<boolean>(() => {
    return !!props.returnMethod;
  });

  const title = computed<string>(() => {
    if (isEdit.value) {
      return t('return_methods.edit_return_method');
    }

    return t('return_methods.add_return_method');
  });

  interface IForm {
    name: string | null;
    description: string | null;
    template: ITemplate | null;
    service_point: IParcelShop | null;
    price_type: IOption<string, string>;
    currency: 'DKK';
    price: string | null;
  }

  const form = ref<IForm>({
    name: null,
    description: null,
    template: null,
    service_point: null,
    price_type: priceTypes[0],
    currency: 'DKK',
    price: null,
  });
  const $v = useVuelidate<IFormPrice, ValidationArgs<IFormPrice>>(
    {
      price: {
        requiredIf: helpers.withMessage(
          'This field is required',
          requiredIf(() => form.value.price_type.value == 'fixed'),
        ),
        mustBeValidInput: helpers.withMessage('Only valid input is required.', mustBeValidInput)
      }
    },
    form,
  );
  /*  const isTemplateDetailOpen = ref<boolean>(false);
    const selectedTemplate = ref<ITemplate | null>(null);*/
  const onAddPrice = async () => {
    await $v.value.$validate();
    if ($v.value.$error || !form.value.price) {
      return;
    }
  }
  const getTemplates = async () => {
    try {
      loadingService.startLoading('main-loader:get-templates');
      const params: IRequestParam[] = [{key: 'sorting[created_at]', value: 'desc'}];
      const response = await templateService.getTemplates(1, 1000, params);
      templates.value = response.entities;
    } catch (error) {
      loggingService.error('Error loading templates', error);
      notificationService.showError('Error loading templates');
    } finally {
      loadingService.stopLoading('main-loader:get-templates');
    }
  };

  const formattedPrice = () => {
    const amount = form.value.price as string;
    const price = PriceService.shippingPriceFormat(Number(amount.replace(/[,.]/g, (match) => {
      return match === ',' ? '.' : '';
    })), 'en').replace(/,/g, '');
    return Number(price)
  }
  const onClickCreateReturnMethod = async () => {
    if (!form.value.name || !form.value.template) {
      return;
    }
    await $v.value.$validate();
    if (form.value.price_type.value == 'fixed' && $v.value.$error) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:create-return-method');

      await returnPortalService.createReturnPortalMethod(props.returnPortal.return_portal_code, {
        name: form.value.name,
        description: form.value.description || '',
        template_code: form.value.template.template_code,
        price_type: form.value.price_type.value,
        price: form.value.price_type.value == 'fixed' ? formattedPrice() : 0,
        currency_code: 3744202329,
        parcelshop_code: form.value.service_point?.id
      });

      emit('close');
      emit('on-create');
    } catch (e) {
      console.log(e)
      loggingService.error('Error creating return method', e);
      notificationService.showError('Error creating return method');
    } finally {
      loadingService.stopLoading('main-loader:create-return-method');
    }
  };

  const onClickUpdateReturnMethod = async () => {
    if (!props.returnMethod || !form.value.name || !form.value.template) {
      return;
    }
    await $v.value.$validate();
    if (form.value.price_type.value == 'fixed' && $v.value.$error) {
      return;
    }
    try {
      loadingService.startLoading('main-loader:update-return-method');

      await returnPortalService.updateReturnPortalMethod(
        props.returnPortal.return_portal_code,
        props.returnMethod.method_code,
        {
          name: form.value.name,
          description: form.value.description || '',
          template_code: form.value.template.template_code,
          price_type: form.value.price_type.value,
          price: form.value.price_type.value == 'fixed' ? formattedPrice() : 0,
          currency_code: 3744202329,
          parcelshop_code: form.value.service_point?.id
        },
      );

      emit('close');
      emit('on-update');
    } catch (e) {
      loggingService.error('Error updating return method', e);
      notificationService.showError('Error updating return method');
    } finally {
      loadingService.stopLoading('main-loader:update-return-method');
    }
  };

  const onChangeTemplate = async (template: ITemplate) => {
    try {
      loadingService.startLoading('main-loader:service-points');
      const response = await shipmentService.getParcelShopsByReturnPortalCode(
        props.returnPortal.return_portal_code,
        template.template_code
      );
      servicePoints.value = response.map((parcelShop: IParcelShopSingleResponse) => ({
        ...parcelShop.parcelShop,
        label: `${parcelShop.parcelShop.name} - ${parcelShop.parcelShop.addressLine1}, Pakkeshop:
        ${parcelShop.parcelShop.id}, ${parcelShop.parcelShop.country}-${parcelShop.parcelShop.postCode},
        ${parcelShop.parcelShop.city}`,
      }));
      if (props.returnMethod && props.returnMethod.parcelshop_code) {
        form.value.service_point = servicePoints.value.find((item) => item.id == props.returnMethod?.parcelshop_code) as IParcelShop;
      }
    } catch (e) {
      servicePoints.value = [];
      notificationService.showError('No service points found');
    } finally {
      loadingService.stopLoading('main-loader:service-points');
    }
  }

  const hydrate = async () => {
    await getTemplates();

    if (props.returnMethod) {
      form.value.name = props.returnMethod.name;
      form.value.description = props.returnMethod.description;


      const price_type = priceTypes.find(priceType => priceType.value.toUpperCase() == props.returnMethod?.price_type);
      if (price_type !== undefined) form.value.price_type = price_type;

      const template = templates.value.find(
        (_template: ITemplate) => _template.template_code === props.returnMethod?.template_code,
      );

      if (template) {
        form.value.template = template;
        await onChangeTemplate(template)
      }
      form.value.price = danishNumberFormat(props.returnMethod.price).toString();
    }
  };
  /*const onClickEditTemplate = async (row: ITemplate | null) => {
    selectedTemplate.value = row;
    isTemplateDetailOpen.value = true;
    await getTemplates();
  };
  const onCloseTemplateDetail = () => {
    isTemplateDetailOpen.value = false;
    selectedTemplate.value = null;
  };*/
  const onClickClose = () => {
    emit('close');
  };
  onBeforeMount(() => {
    hydrate();
  });
</script>
<style scoped>
  .return-portal-modal {
    width: 650px;
    max-width: 650px !important;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 15px;
  }

  .header-bg {
    background-color: #243E90;
  }

  .slot-container {
    max-height: calc(100vh - 48px - 67px);
    overflow: auto;
  }

  .text-brand {
    color: #243E90 !important;
  }

  .bg-brand {
    background: #243E90 !important;
  }

  .icon-wrapper {
    position: absolute;
    top: -36px;
  }
</style>
