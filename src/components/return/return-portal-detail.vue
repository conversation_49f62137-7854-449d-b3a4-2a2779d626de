<template>
  <shipvagoo-modal size="md" :header-separator="false" title="Return portal">
    <shipvagoo-separator style="margin: 0 20px"></shipvagoo-separator>
    <shipvagoo-stepper ref="stepper" class="create-template-modal-stepper-v2" v-model="step">
      <q-step :name="1" title="General" :done="step > 1">
        <div>
          <div class="two-columns-responsive gap-10">
            <shipvagoo-select-v1
              disable
              placeholder="Select"
              class="flex-1"
              v-model="steps.one.return_country"
              label="Return country"
              :options="countries"
              option-label="default_name"
              option-value="country_code"
              hide-bottom-space
              :error="$v.one.return_country.$error"
              :error-message="$v.one.return_country.$errors?.[0]?.$message"
            >
              <template #prepend>
                <shipvagoo-country-icon
                  v-if="steps.one.return_country"
                  :country="steps.one.return_country"
                />
              </template>
              <template #option="scope">
                <q-item v-bind="scope.itemProps">
                  <q-item-section side>
                    <shipvagoo-country-icon v-if="scope.opt" :country="scope.opt" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ scope.opt.default_name }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
            </shipvagoo-select-v1>
            <shipvagoo-select-v1
              class="flex-1"
              v-model="steps.one.integration"
              label="Integration"
              :options="integrations"
              option-label="name"
              option-value="integration_code"
              :error="$v.one.integration.$error"
              :error-message="$v.one.integration.$errors?.[0]?.$message"
            >
            </shipvagoo-select-v1>
          </div>
          <div class="two-columns-responsive gap-10 q-mt-md">
            <shipvagoo-input-v1
              class="flex-1"
              v-model="steps.one.portal_name"
              placeholder="Enter return portal name"
              label="Return portal name"
              :error="$v.one.portal_name.$error"
              :error-message="$v.one.portal_name.$errors?.[0]?.$message"
            />
            <div class="flex-1"></div>
          </div>
        </div>
      </q-step>
      <q-step :name="2" title="Return setup" :done="step > 2">
        <div class="rounded-borders-box">
          <div class="dark-text text-thirteen text-weight-7">Return address:</div>
          <shipvagoo-separator class="q-my-sm" />
          <div class="two-columns-responsive gap-10 q-mt-sm">
            <shipvagoo-input-v1
              class="flex-1"
              v-model="steps.two.company_name"
              placeholder="Enter company name"
              label="Company name"
              :error="$v.two.company_name.$error"
              :error-message="$v.two.company_name.$errors?.[0]?.$message"
            />
            <shipvagoo-input-v1
              class="flex-1"
              v-model="steps.two.attn"
              placeholder="Enter att."
              label="Att."
              :error="$v.two.attn.$error"
              :error-message="$v.two.attn.$errors?.[0]?.$message"
            />
          </div>

          <div class="two-columns-responsive gap-10 q-mt-md">
            <shipvagoo-input-v1
              class="flex-1"
              v-model="steps.two.address"
              placeholder="Enter return address"
              label="Address"
              :error="$v.two.address.$error"
              :error-message="$v.two.address.$errors?.[0]?.$message"
            />
            <shipvagoo-input-v1
              class="flex-1"
              v-model="steps.two.zip_code"
              placeholder="Enter postal code"
              @update:model-value="onChangeZipCode($event)"
              label="Postal code"
              :error="$v.two.zip_code.$error"
              :error-message="$v.two.zip_code.$errors?.[0]?.$message"
            />
          </div>
          <div class="two-columns-responsive gap-10 q-mt-md">
            <shipvagoo-input-v1
              class="flex-1"
              v-model="steps.two.city"
              placeholder="Enter city"
              label="City"
              :error="$v.two.city.$error"
              :error-message="$v.two.city.$errors?.[0]?.$message"
            />
            <shipvagoo-select-v1
              placeholder="Select"
              class="flex-1"
              disable
              v-model="steps.two.country"
              label="Country"
              :options="countries"
              option-label="default_name"
              option-value="country_code"
              :error="$v.two.country.$error"
              :error-message="$v.two.country.$errors?.[0]?.$message"
            >
              <template #prepend>
                <shipvagoo-country-icon v-if="steps.two.country" :country="steps.two.country" />
              </template>
              <template #option="scope">
                <q-item v-bind="scope.itemProps">
                  <q-item-section side>
                    <shipvagoo-country-icon v-if="scope.opt" :country="scope.opt" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ scope.opt.default_name }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
            </shipvagoo-select-v1>
          </div>
          <div class="two-columns-responsive gap-10 q-mt-md">
            <shipvagoo-input-v1
              class="flex-1"
              v-model="steps.two.phone"
              placeholder="Enter phone"
              label="Phone"
              :error="$v.two.phone.$error"
              :error-message="$v.two.phone.$errors?.[0]?.$message"
            />
            <shipvagoo-input-v1
              class="flex-1"
              v-model="steps.two.email"
              placeholder="Enter email"
              label="Email"
              :error="$v.two.email.$error"
              :error-message="$v.two.email.$errors?.[0]?.$message"
            />
          </div>
        </div>
        <div class="rounded-borders-box q-mt-md">
          <div class="dark-text text-thirteen text-weight-7">Return landing page:</div>
          <shipvagoo-separator class="q-my-sm" />
          <shipvagoo-input-v1
            class="flex-1"
            v-model="steps.two.headline"
            placeholder="Lets find your order"
            label="Headline"
            :error="$v.two.headline.$error"
            :error-message="$v.two.headline.$errors?.[0]?.$message"
          />
          <div class="q-mt-md">
            <shipvagoo-wysiwyg-v1
              label="Return guidelines"
              placeholder="To find your order, please provide details about your purchase"
              v-model="steps.two.body_text"
            />
          </div>
          <div class="q-mt-md">
            <shipvagoo-input-v1
              class="shipvagoo-company-logo-url"
              label-slot
              v-model="steps.two.company_logo_url"
              :error="$v.two.company_logo_url.$error"
              :error-message="$v.two.company_logo_url.$errors?.[0]?.$message"
            >
              <template v-slot:append>
                <div>
                  <q-icon
                    class="q-ml-xs"
                    style="font-size: 12px; color: gray !important"
                    name="fas fa-exclamation-circle"
                  >
                    <q-tooltip>
                      Redirect your customer to this ulr by clicking on the company logo
                    </q-tooltip>
                  </q-icon>
                </div>
              </template>
              <template v-slot:label>
                <label style="margin-top: 10px !important">Company logo URL </label>
              </template>
            </shipvagoo-input-v1>
          </div>
          <div class="q-mt-md">
            <shipvagoo-filepicker
              ref="refCompanyLogo"
              placeholder="Choose file maximum size of 512 x 512 px (Only PNG format)"
              accept="image/png"
              max-file-size="1024000"
              label="Company logo (optional)"
              clearable
              v-model="logoFile"
              @update:modelValue="onChangeLogo"
              :filter="checkFileType"
              @rejected="onRejected"
            ></shipvagoo-filepicker>
          </div>
          <div class="flex q-mt-xs" v-if="steps.two.logo_name">
            <div class="dark-text text-twelve">{{ steps.two.logo_name }}</div>
            <q-icon
              @click="onClickDeleteLogo"
              class="q-ml-sm cursor-pointer"
              name="fas fa-trash-alt"
              color="red"
              style="margin-top: 1px; font-size: 13px"
            ></q-icon>
          </div>
        </div>
        <div class="rounded-borders-box q-mt-md">
          <div class="dark-text text-thirteen text-weight-7">Return reasons:</div>
          <shipvagoo-separator class="q-my-sm" />
          <shipvagoo-table-v1
            style="height: 290px"
            :loading="reasonsLoading"
            hide-bottom
            :columns="reasonColumns"
            :rows="reasons"
            :rows-per-page-options="[0]"
            hide-pagination
          >
            <template #body-cell-name="props">
              <q-td :props="props">
                <shipvagoo-checkbox-v1
                  @update:modelValue="
                    (value) => onCheckedReturnReason(value, props.row.return_reason_code)
                  "
                  :disable="props.row.enable_comment == 1"
                  v-model="selectedReasons[props.row.return_reason_code]"
                >
                  <div style="margin-top: -2px">{{ props.row.description }}</div>
                </shipvagoo-checkbox-v1>
              </q-td>
            </template>
            <template #body-cell-more="props">
              <q-td :props="props" class="more">
                <q-btn
                  v-if="props.row.enable_comment == 0"
                  dense
                  flat
                  round
                  style="color: #1f2124"
                  class="more-action"
                  field="edit"
                  icon="more_horiz"
                >
                  <shipvagoo-menu>
                    <shipvagoo-menu-item clickable @click="onClickDeleteReturnReason(props.row)">
                      {{ $t('common.delete') }}
                    </shipvagoo-menu-item>
                  </shipvagoo-menu>
                </q-btn>
              </q-td>
            </template>
          </shipvagoo-table-v1>
          <div class="q-mt-md flex justify-end">
            <shipvagoo-button
              style="min-width: 80px"
              label="Add"
              no-caps
              @click="onClickAddReturnReason"
            />
          </div>
          <Transition>
            <div v-if="isClickedOnAddReason" class="q-mt-md">
              <shipvagoo-separator />
              <shipvagoo-input-v1
                class="flex-1 q-mt-md"
                v-model="returnReasonDesc"
                placeholder="Enter reason"
                label="Add reason"
              />
              <div class="flex justify-end q-mt-md">
                <shipvagoo-button
                  style="min-width: 90px"
                  label="Create"
                  no-caps
                  @click="onClickCreateReturnReason"
                />
              </div>
            </div>
          </Transition>
        </div>
        <div class="rounded-borders-box q-mt-md">
          <div class="dark-text text-thirteen text-weight-7">Refund preference:</div>
          <shipvagoo-separator class="q-my-sm" />
          <div v-for="resolution in resolutions" class="q-my-sm">
            <shipvagoo-checkbox-v1
              @update:modelValue="
                (value) => onCheckedResolutions(value, resolution.resolution_code)
              "
              v-model="selectedResolutions[resolution.resolution_code]"
            >
              <strong style="margin-top: -2px">{{ resolution.name }}</strong>
            </shipvagoo-checkbox-v1>
          </div>
        </div>
      </q-step>
      <q-step :name="3" title="Carrier" :done="step > 3">
        <div class="rounded-borders-box">
          <div class="dark-text text-thirteen text-weight-7">Return shipment:</div>
          <shipvagoo-separator class="q-my-sm" />
          <div class="q-mt-md flex gap-10">
            <div>
              <shipvagoo-checkbox-v1
                @click="() => (parcelShopToParcelShop = !parcelShopToParcelShop)"
                v-model="parcelShopToParcelShop"
              ></shipvagoo-checkbox-v1>
            </div>
            <div class="dark-text" style="margin-top: 2px; line-height: 18px">
              <div>Parcel shop to parcel shop</div>
              <div>Collect your returns at the selected parcel shop</div>
            </div>
          </div>
          <div class="q-mt-md flex gap-10">
            <div>
              <shipvagoo-checkbox-v1 disable v-model="parcelShopToAddress"></shipvagoo-checkbox-v1>
            </div>
            <div class="text-grey-7" style="margin-top: 2px; line-height: 18px">
              <div>Parcel shop to return address</div>
              <div>Your returns are delivered to your return address</div>
            </div>
          </div>
        </div>
        <div class="q-mt-md">
          <shipvagoo-table-v1
            v-model:pagination="methodPagination"
            :columns="methodColumns"
            :rows="methods"
            :total="methods.length"
            @row-click="onClickReturnMethodRow"
          >
            <template #body-cell-via="props">
              <q-td :props="props">
                <div
                  v-if="
                    CarrierService.getCarrierFromShippingProductCode(
                      carriers,
                      $store.state.courierStore.carrierProducts,
                      props.row.template.carrier_product_code,
                    )?.carrier_id?.toLowerCase()
                  "
                  class="flex items-center no-wrap"
                >
                  <img
                    class="q-mr-sm"
                    decoding="async"
                    style="width: 20px; height: 20px; border-radius: 50%; object-fit: cover"
                    :src="`${
                      CarrierService.getCarrierFromShippingProductCode(
                        carriers,
                        $store.state.courierStore.carrierProducts,
                        props.row.template.carrier_product_code,
                      )?.icon
                    }`"
                  />
                  <div>
                    {{
                      CarrierService.getCarrierFromShippingProductCode(
                        carriers,
                        $store.state.courierStore.carrierProducts,
                        props.row.template.carrier_product_code,
                      )?.name
                    }}
                  </div>
                </div>
              </q-td>
            </template>
            <template #body-cell-return_country="props">
              <q-td key="destination_country" :props="props">
                <div
                  class="flex items-center no-wrap"
                  v-if="props.row && getCountry(props.row.template.receiver_country_code)"
                >
                  <img
                    class="orders-country_flag q-mr-sm"
                    :src="
                      '/assets/img/' +
                      getCountry(props.row.template.receiver_country_code)?.iso.toLowerCase() +
                      '.svg'
                    "
                    decoding="async"
                  />
                  <div>
                    {{ getCountry(props.row.template.receiver_country_code).default_name }}
                  </div>
                </div>
              </q-td>
            </template>
            <template #body-cell-more="props">
              <q-td :props="props" class="more">
                <q-btn
                  dense
                  flat
                  round
                  style="color: #1f2124"
                  class="more-action"
                  field="edit"
                  icon="more_horiz"
                >
                  <shipvagoo-menu>
                    <shipvagoo-menu-item clickable @click="onClickDeleteReturnMethod(props.row)">
                      {{ $t('common.delete') }}
                    </shipvagoo-menu-item>
                  </shipvagoo-menu>
                </q-btn>
              </q-td>
            </template>
          </shipvagoo-table-v1>
          <div class="q-mt-md flex justify-end">
            <shipvagoo-button
              style="min-width: 80px"
              label="Add"
              no-caps
              @click="onClickAddReturnMethod"
            />
          </div>
          <shipvagoo-separator class="q-my-md" />
          <div class="q-mt-md" v-if="isClickedReturnMethod" id="return-method-form">
            <div class="two-columns-responsive gap-10">
              <shipvagoo-input-v1
                v-model="returnMethod.name"
                class="flex-1"
                placeholder="Enter return method"
                label="Return method"
                :error="$v1.name.$error"
                :error-message="String($v1.name.$errors?.[0]?.$message ?? null)"
              />
              <shipvagoo-select-v1
                placeholder="Select"
                class="flex-1"
                v-model="returnMethod.carrier"
                :label="$t('common.carrier')"
                :options="carriers"
                option-label="name"
                @update:model-value="onChangeCarrier"
                option-value="carrier_code"
                :error="$v1.carrier.$error"
                :error-message="String($v1.carrier.$errors?.[0]?.$message ?? null)"
              >
                <template #prepend>
                  <img
                    v-if="returnMethod.carrier"
                    width="17"
                    height="17"
                    :src="returnMethod.carrier?.icon"
                    alt=""
                    decoding="async"
                    style="border-radius: 50%"
                  />
                </template>
                <template #option="scope">
                  <q-item v-bind="scope.itemProps">
                    <q-item-section side>
                      <img width="20" height="20" :src="scope.opt.icon" alt="" decoding="async" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ scope.opt.name }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
              </shipvagoo-select-v1>
            </div>
            <shipvagoo-select-v1
              placeholder="Select"
              class="q-mt-md"
              v-model="returnMethod.template"
              label="Shipping rule"
              :options="templatesRefs"
              option-value="template_code"
              option-label="name"
              @update:model-value="onChangeTemplate"
              :error="$v1.template.$error"
              :error-message="String($v1.template.$errors?.[0]?.$message ?? null)"
              @filter="onFilterTemplates"
              use-input
              input-debounce="300"
              fill-input
              hide-selected
            >
              <template #prepend>
                <q-icon name="search" size="sm"></q-icon>
              </template>
            </shipvagoo-select-v1>
            <shipvagoo-select-v1
              placeholder="Select"
              class="q-mt-md"
              v-if="servicePoints.length > 0"
              v-model="returnMethod.service_point"
              :options="servicePoints"
              label="Service point"
              option-label="label"
              option-value="id"
              :error="$v1.service_point.$error"
              :error-message="String($v1.service_point.$errors?.[0]?.$message ?? null)"
            />
            <div class="two-columns-responsive gap-10 q-mt-md">
              <shipvagoo-input-v1
                placeholder="Enter return cost"
                class="flex-1"
                v-model="returnMethod.price"
                label="Return cost"
                :error="$v1.price.$error"
                :error-message="String($v1.price.$errors?.[0]?.$message ?? null)"
              >
              </shipvagoo-input-v1>
              <shipvagoo-select-v1
                class="flex-1"
                v-model="returnMethod.currency"
                :label="$t('common.currency')"
                disable
              >
                <template #prepend>
                  <shipvagoo-country-icon :country="denmark" />
                </template>
              </shipvagoo-select-v1>
            </div>
            <div class="flex justify-end q-mt-md">
              <shipvagoo-button
                style="min-width: 90px"
                :label="returnMethod.method_code ? 'Update' : 'Create'"
                no-caps
                @click="
                  returnMethod.method_code
                    ? onClickUpdateReturnMethod()
                    : onClickCreateReturnMethod()
                "
              />
            </div>
            <shipvagoo-separator class="q-my-md" />
          </div>
        </div>
      </q-step>
      <template #navigation>
        <q-stepper-navigation
          class="create-shipment-stepper-v2-navigation q-px-lg"
          style="margin-top: -30px"
        >
          <shipvagoo-button
            color="brand"
            class="create-shipment-stepper-v2-navigation-previous"
            v-if="step > 1"
            label="Back"
            outline
            no-caps
            @click="onClickPrevious"
          />
          <shipvagoo-button
            color="brand"
            v-if="step == 1 || step == 2"
            class="create-shipment-stepper-v2-navigation-next"
            label="Next"
            no-caps
            @click="onClickNext"
          />
          <shipvagoo-button
            v-if="step === 3"
            class="create-shipment-stepper-v2-navigation-next"
            label="Save"
            @click="onClickSave"
            no-caps
          />
        </q-stepper-navigation>
      </template>
    </shipvagoo-stepper>
  </shipvagoo-modal>
</template>

<script setup lang="ts">
  import {
    IReturnPortal,
    IReturnPortalMethod,
    IReturnPortalReason,
    IReturnPortalResolution,
  } from '../../model/return-portal.model';
  import { ComponentPublicInstance, computed, onBeforeMount, onMounted, ref } from 'vue';
  import { ICountry } from '../../model/country.model';
  import { IOrderIntegration } from '../../model/order-integration.model';
  import { useHelper } from '../../composeable/Helper';
  import { useStore } from '../../store';
  import {
    carrierService,
    loadingService,
    loggingService,
    mapService,
    notificationService,
    orderIntegrationService,
    returnPortalService,
    shipmentService,
    templateService,
  } from '../../services/_singletons';
  import { ILanguage } from '../../model/language.model';
  import postCodes from '../../assets/json/postcodes.json';
  import * as _ from 'lodash-es';
  import { QTableProps } from 'quasar';
  import CarrierService from '../../services/carrier.service';
  import { Pagination } from '../../model/common.model';
  import { ICarrier, IShippingProduct } from '../../model/carrier.model';
  import { ITemplate } from '../../model/template.model';
  import { IParcelShop, IParcelShopSingleResponse } from '../../model/shipment.model';
  import { IRequestParam } from '../../model/http.model';
  import PriceService from '../../services/price.service';
  import useVuelidate, { ValidationArgs } from '@vuelidate/core';
  import { helpers, maxLength, required, requiredIf } from '@vuelidate/validators';

  const {
    denmark,
    mustBeValidInput,
    mustBeValidLength,
    mustBeValidMinValue,
    mustBeValidURL,
    mustBeValidName,
    mustBeValidUrlName,
    mustBeValidEmail,
  } = useHelper();
  const $store = useStore();

  interface Props {
    returnPortal: IReturnPortal | null;
  }

  const _props = defineProps<Props>();
  const emit = defineEmits(['close']);

  interface IReturnMethod {
    method_code: string | number | null;
    name: string | null;
    carrier: ICarrier | null;
    template: ITemplate | null;
    service_point: IParcelShop | null;
    currency: string | null;
    price: string | null;
  }

  interface IReturnPortalSteps {
    one: {
      return_country: ICountry;
      integration: IOrderIntegration | null;
      portal_name: string | null;
      language: ILanguage;
    };
    two: {
      company_name: string | null;
      attn: string | null;
      address: string | null;
      city: string | null;
      zip_code: string | null;
      country: ICountry | null;
      phone: string | null;
      email: string | null;
      company_logo_url?: string | null;
      logo: any;
      logo_name?: string | null;
      headline: string | null;
      body_text: string;
    };
    three: {};
  }

  const companyStore = $store.state.companyStore;
  const reasonsLoading = ref<boolean>(false);
  const integrations = ref<IOrderIntegration[]>([]);
  const step = ref<number>(1);
  const stepper = ref<ComponentPublicInstance<{ next: () => void; previous: () => void }> | null>(
    null,
  );
  const steps = ref<IReturnPortalSteps>({
    one: {
      return_country: denmark,
      integration: null,
      portal_name: null,
      language: $store.state.languageStore.languages[0],
    },
    two: {
      company_name: null,
      attn: null,
      address: null,
      city: null,
      zip_code: null,
      country: denmark,
      phone: null,
      email: null,
      logo: null,
      logo_name: null,
      headline: null,
      body_text: '',
    },
    three: {},
  });
  const logoFile = ref<any>();
  const refCompanyLogo = ref<any>();
  const reasons = ref<IReturnPortalReason[]>([]);
  const selectedReasons = ref<any>({});
  const selectedResolutions = ref<any>({});
  const resolutions = ref<IReturnPortalResolution[]>([]);
  const methods = ref<IReturnPortalMethod[]>([]);
  const createdReturnPortal = ref<IReturnPortal | null>(null);
  const isClickedOnAddReason = ref<boolean>(false);
  const returnReasonDesc = ref<string | null>(null);
  const parcelShopToParcelShop = ref<boolean>(true);
  const parcelShopToAddress = ref<boolean>(false);
  const methodPagination = ref<Pagination>({
    page: 1,
    rowsPerPage: 5,
  });
  const servicePoints = ref<(IParcelShop & { label: string })[]>([]);
  const templates = ref<ITemplate[]>([]);
  const templatesRefs = ref<ITemplate[]>([]);
  const isClickedReturnMethod = ref<boolean>(false);
  const returnMethod = ref<IReturnMethod>({
    method_code: null,
    carrier: null,
    name: null,
    template: null,
    service_point: null,
    price: null,
    currency: 'DKK',
  });

  const onChangeCarrier = (carrier: ICarrier) => {
    returnMethod.value.template = null;
    const products = $store.state.courierStore.carrierProducts.filter(
      (product: IShippingProduct) => product.carrier_code == carrier.carrier_code,
    );
    const shippingProductCodes =
      products?.map((product: IShippingProduct) => product.carrier_product_code) ?? [];
    templatesRefs.value = templates.value.filter((template: ITemplate) => {
      return shippingProductCodes.includes(template.carrier_product_code);
    });
  };

  const onFilterTemplates = (val: string, update: CallableFunction) => {
    if (val === '') {
      update(() => {
        if (returnMethod.value.carrier) {
          onChangeCarrier(returnMethod.value.carrier);
        } else {
          templatesRefs.value = templates.value;
        }
      });
      return;
    }
    update(() => {
      const needle = val.toLowerCase();
      templatesRefs.value = templates.value.filter((template: ITemplate) => {
        return template.name.toLowerCase().indexOf(needle) > -1;
      });
    });
  };

  const $v1 = useVuelidate<IReturnMethod, ValidationArgs<IReturnMethod>>(
    {
      method_code: {},
      carrier: {
        required: helpers.withMessage('This field is required.', required),
      },
      name: {
        required: helpers.withMessage('This field is required.', required),
      },
      template: { required: helpers.withMessage('This field is required.', required) },
      price: {
        required: helpers.withMessage('This field is required.', required),
        mustBeValidInput: helpers.withMessage('Only valid input is required.', mustBeValidInput),
        mustBeValidLength: helpers.withMessage(
          'Amount value can not be greater than 6 digits.',
          mustBeValidLength,
        ),
        minValue: helpers.withMessage(
          'Amount value should not be a negative value.',
          mustBeValidMinValue(-1),
        ),
      },
      service_point: {
        requiredIf: helpers.withMessage(
          'This field is required',
          requiredIf(() => servicePoints.value.length > 0),
        ),
      },
      currency: {},
    },
    returnMethod,
  );

  const $v = useVuelidate<IReturnPortalSteps, ValidationArgs<IReturnPortalSteps>>(
    {
      one: {
        return_country: {
          required: helpers.withMessage('This field is required.', required),
        },
        integration: {
          required: helpers.withMessage('This field is required.', required),
        },
        portal_name: {
          required: helpers.withMessage('This field is required.', required),
          maxLength: helpers.withMessage(
            'Portal name length should not be exceeded more than 50 characters.',
            maxLength(50),
          ),
          mustBeValidName: helpers.withMessage(
            'Portal name can not contain these special characters: #, ?, / ',
            mustBeValidUrlName,
          ),
        },
        language: {},
      },
      two: {
        company_name: {
          required: helpers.withMessage('This field is required.', required),
          mustBeValidName: helpers.withMessage('Company name is not valid.', mustBeValidName),
        },
        attn: {
          required: helpers.withMessage('This field is required.', required),
          mustBeValidName: helpers.withMessage('Att name is not valid.', mustBeValidName),
        },
        address: {
          required: helpers.withMessage('This field is required.', required),
        },
        city: {
          required: helpers.withMessage('This field is required.', required),
        },
        zip_code: {
          required: helpers.withMessage('This field is required.', required),
        },
        country: {
          required: helpers.withMessage('This field is required.', required),
        },
        phone: {
          required: helpers.withMessage('This field is required.', required),
        },
        email: {
          required: helpers.withMessage('This field is required.', required),
          email: helpers.withMessage('This field must be a valid email.', (value: any) =>
            mustBeValidEmail(value, null),
          ),
        },
        logo: {},
        company_logo_url: {
          mustBeValidURL: helpers.withMessage('This field must be a valid URL', mustBeValidURL),
        },
        headline: {
          required: helpers.withMessage('This field is required.', required),
        },
        body_text: {},
      },
      three: {},
    },
    steps,
  );

  /** computed properties **/
  const methodColumns = computed((): QTableProps['columns'] => [
    {
      label: 'Return method',
      name: 'name',
      field: (row) => row.name,
      align: 'left',
    },
    {
      label: 'Carrier',
      name: 'via',
      field: () => '',
      align: 'left',
    },
    {
      label: 'Return country',
      name: 'return_country',
      field: () => '',
      align: 'left',
    },
    {
      label: 'Return cost',
      name: 'return_cost',
      field: (row) => PriceService.formatPrice(Number(row.price), 'de-DE'),
      align: 'left',
    },
    {
      label: 'Action',
      name: 'more',
      field: 'more',
    },
  ]);
  const carriers = ref<ICarrier[]>([]);
  /** computed properties **/

  const countries = computed(() => {
    return $store.state.countryStore.countries;
  });
  const reasonColumns = computed((): QTableProps['columns'] => [
    {
      label: 'Reason',
      name: 'name',
      field: '',
      align: 'left',
    },
    { label: '', name: 'more', field: 'more' },
  ]);

  /** functions starts here **/
  const onClickReturnMethodRow = async (event: Event, row: IReturnPortalMethod) => {
    resetReturnMethod();
    const product = $store.state.courierStore.carrierProducts.find(
      (product: IShippingProduct) =>
        row.template?.carrier_product_code == product.carrier_product_code,
    );
    const carrier =
      carriers.value.find((carrier) => carrier.carrier_code == product?.carrier_code) ?? null;
    if (row.template) {
      await onChangeTemplate(row.template);
    }
    returnMethod.value = {
      name: row.name,
      carrier: carrier,
      template: row.template,
      price: PriceService.shippingPriceFormat(Number(row.price), 'de') ?? 0,
      method_code: row.method_code,
      currency: 'DKK',
      service_point:
        servicePoints.value.find(
          (servicePoint: IParcelShop) => servicePoint.id == row.parcelshop_code,
        ) ?? null,
    };
    isClickedReturnMethod.value = true;
  };

  const getCarriers = async (senderCountry: number, receiverCountry: number) => {
    try {
      loadingService.startLoading('main-loader:carriers');
      const response = await carrierService.getShippingProducts(senderCountry, receiverCountry);
      carriers.value = _.get(response, 'carriers', []) as ICarrier[];
    } catch (e) {
      notificationService.showError('Error getting carriers');
      loggingService.error('Error getting carriers', e);
    } finally {
      loadingService.stopLoading('main-loader:carriers');
    }
  };
  const getCountry = (country_code: number): ICountry => {
    return $store.state.countryStore.countriesIndexedByCountryCode[country_code];
  };
  const onChangeTemplate = async (template: ITemplate) => {
    try {
      if (!createdReturnPortal.value) {
        return;
      }
      loadingService.startLoading('main-loader:service-points');
      const response = await shipmentService.getParcelShopsByReturnPortalCode(
        createdReturnPortal.value.return_portal_code,
        template.template_code,
      );
      servicePoints.value = response.map((parcelShop: IParcelShopSingleResponse) => ({
        ...parcelShop.parcelShop,
        label: `${parcelShop.parcelShop.name} - ${parcelShop.parcelShop.addressLine1}, Pakkeshop:
        ${parcelShop.parcelShop.id}, ${parcelShop.parcelShop.country}-${parcelShop.parcelShop.postCode},
        ${parcelShop.parcelShop.city}`,
      }));
      /* if (returnMethod && returnMethod.parcelshop_code) {
       form.value.service_point = servicePoints.value.find((item) => item.id == props.returnMethod?.parcelshop_code) as IParcelShop;
     }*/
    } catch (e) {
      servicePoints.value = [];
      notificationService.showError('No service points found');
    } finally {
      loadingService.stopLoading('main-loader:service-points');
    }
  };
  const formattedPrice = () => {
    const amount = returnMethod.value.price as string;
    const price = PriceService.shippingPriceFormat(
      Number(
        amount.replace(/[,.]/g, (match) => {
          return match === ',' ? '.' : '';
        }),
      ),
      'en',
    ).replace(/,/g, '');
    return Number(price);
  };
  const resetReturnMethod = () => {
    returnMethod.value = {
      method_code: null,
      carrier: null,
      name: null,
      template: null,
      service_point: null,
      price: null,
      currency: 'DKK',
    };
  };
  const onClickCreateReturnMethod = async () => {
    $v1.value.$validate();
    if ($v1.value.$error) {
      return;
    }
    if (!createdReturnPortal.value || !returnMethod.value.template) {
      return;
    }
    try {
      loadingService.startLoading('main-loader:create-return-method');
      await returnPortalService.createReturnPortalMethod(
        createdReturnPortal.value.return_portal_code,
        {
          name: returnMethod.value.name ?? '',
          description: '',
          template_code: returnMethod.value.template?.template_code,
          price_type: 'fixed',
          price: formattedPrice(),
          currency_code: 3744202329,
          parcelshop_code: returnMethod.value.service_point?.id,
        },
      );
      notificationService.showSuccess('Return method created successfully.');
      getReturnMethods();
      resetReturnMethod();
      isClickedReturnMethod.value = false;
    } catch (e) {
      console.log(e);
      loggingService.error('Error creating return method', e);
      notificationService.showError('Error creating return method');
    } finally {
      loadingService.stopLoading('main-loader:create-return-method');
    }
  };

  const onClickUpdateReturnMethod = async () => {
    $v1.value.$validate();
    if ($v1.value.$error) {
      return;
    }
    if (!createdReturnPortal.value || !returnMethod.value) {
      return;
    }
    try {
      loadingService.startLoading('main-loader:update-return-method');
      await returnPortalService.updateReturnPortalMethod(
        createdReturnPortal.value.return_portal_code,
        Number(returnMethod.value.method_code),
        {
          name: returnMethod.value?.name ?? '',
          description: '',
          template_code: Number(returnMethod.value.template?.template_code),
          price_type: 'fixed',
          price: formattedPrice(),
          currency_code: 3744202329,
          parcelshop_code: returnMethod.value.service_point?.id,
        },
      );
      notificationService.showSuccess('Return method created successfully.');
      getReturnMethods();
      resetReturnMethod();
      isClickedReturnMethod.value = false;
    } catch (e) {
      loggingService.error('Error updating return method', e);
      notificationService.showError('Error updating return method');
    } finally {
      loadingService.stopLoading('main-loader:update-return-method');
    }
  };

  const getTemplates = async () => {
    try {
      loadingService.startLoading('main-loader:get-templates');
      const params: IRequestParam[] = [{ key: 'sorting[created_at]', value: 'desc' }];
      const response = await templateService.getTemplates(1, 10000, params);
      templates.value = response.entities;
      templatesRefs.value = response.entities;
    } catch (error) {
      loggingService.error('Error loading templates', error);
      notificationService.showError('Error loading templates');
    } finally {
      loadingService.stopLoading('main-loader:get-templates');
    }
  };
  const onClickAddReturnMethod = () => {
    $v1.value.$reset();
    resetReturnMethod();
    isClickedReturnMethod.value = true;
  };
  const onClickDeleteReturnMethod = async (row: IReturnPortalMethod) => {
    if (!createdReturnPortal.value) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:delete-return-method');
      await returnPortalService.deleteReturnPortalMethod(
        createdReturnPortal.value.return_portal_code,
        row.method_code,
      );
      getReturnMethods();
    } catch (error) {
      loggingService.error('Error deleting return method', error);
      notificationService.showError('Error deleting return method');
    } finally {
      loadingService.stopLoading('main-loader:delete-return-method');
    }
  };
  const onClickCreateReturnReason = async () => {
    if (!returnReasonDesc.value || !createdReturnPortal.value) {
      notificationService.showError('Please enter return reason.');
      return;
    }
    try {
      loadingService.startLoading('main-loader:create-return-reason');
      const commentRequired = returnReasonDesc.value.toLocaleLowerCase().includes('other');
      await returnPortalService.createReturnPortalReason(
        createdReturnPortal.value.return_portal_code,
        {
          description: returnReasonDesc.value,
          enable_comment: commentRequired,
          comment_required: commentRequired,
          available_days: 0,
        },
      );
      isClickedOnAddReason.value = false;
      returnReasonDesc.value = null;
      getReturnReasons();
    } catch (e) {
      loggingService.error('Error creating return reason', e);
      notificationService.showError('Error creating return reason');
    } finally {
      loadingService.stopLoading('main-loader:create-return-reason');
    }
  };
  const onCheckedResolutions = async (value: boolean, resolution_code: number) => {
    reasonsLoading.value = true;
    try {
      if (!createdReturnPortal.value) {
        return;
      }
      await returnPortalService.updateReturnPortalResolution(
        createdReturnPortal.value.return_portal_code,
        resolution_code,
        {
          status: value,
        },
      );
    } finally {
      reasonsLoading.value = false;
    }
  };

  const onCheckedReturnReason = async (value: boolean, return_reason_code: number) => {
    reasonsLoading.value = true;
    try {
      if (!createdReturnPortal.value) {
        return;
      }
      await returnPortalService.updateReturnPortalReason(
        createdReturnPortal.value.return_portal_code,
        return_reason_code,
        {
          status: value,
        },
      );
    } finally {
      reasonsLoading.value = false;
    }
  };
  const onClickAddReturnReason = () => {
    isClickedOnAddReason.value = true;
  };
  const onClickDeleteReturnReason = async (row: IReturnPortalReason) => {
    console.log('Delete ');
    if (!createdReturnPortal.value) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:delete-return-reason');
      await returnPortalService.deleteReturnPortalReason(
        createdReturnPortal.value.return_portal_code,
        row.return_reason_code,
      );
      getReturnReasons();
    } catch (error) {
      loggingService.error('Error deleting return reason', error);
      notificationService.showError('Error deleting return reason');
    } finally {
      loadingService.stopLoading('main-loader:delete-return-reason');
    }
  };
  const getReturnReasons = async () => {
    if (!createdReturnPortal.value) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:get-return-reasons');
      reasons.value = await returnPortalService.getReturnPortalReasons(
        createdReturnPortal.value.return_portal_code,
      );
      reasons.value = reasons.value.sort(
        (a: IReturnPortalReason, b: IReturnPortalReason) => a.enable_comment - b.enable_comment,
      );
      reasons.value.forEach((item: IReturnPortalReason) => {
        selectedReasons.value[item.return_reason_code] = !!item.status;
      });
    } catch (error) {
      loggingService.error('Error getting return reasons', error);
      notificationService.showError('Error getting return reasons');
    } finally {
      loadingService.stopLoading('main-loader:get-return-reasons');
    }
  };
  const onClickDeleteLogo = () => {
    steps.value.two.logo_name = null;
    steps.value.two.logo = null;
    refCompanyLogo.value?.removeFileAtIndex(0);
    //logoFile.value = null;
  };

  function checkFileType(files: FileList | readonly any[]) {
    const fileArray = Array.from(files);
    return fileArray.filter((file: File) => file.type === 'image/png');
  }

  function onRejected() {
    notificationService.showError('Please select an png format image.');
    onClickDeleteLogo();
  }

  const onChangeLogo = (file: File) => {
    if (file != null) {
      steps.value.two.logo_name = file.name ?? null;
      createBase64Image(file);
    } else {
      steps.value.two.logo = null;
    }
  };
  const createBase64Image = (fileObject: any) => {
    const reader = new FileReader();
    reader.onloadend = () => {
      steps.value.two.logo = reader?.result?.toString();
    };
    reader.readAsDataURL(fileObject);
  };
  const onChangeZipCode = async (zip: string | number | null) => {
    if (!steps.value.two.country || !zip || typeof zip !== 'string') {
      return;
    }
    const re = new RegExp(postCodes[steps.value.two.country.iso].regex);
    if (!re.test(zip)) {
      return;
    }
    try {
      loadingService.startLoading('main-loader:get-city-from-zip-code');
      const geo = await mapService.getMapInfo(steps.value.two.country.iso, zip);
      if (_.size(geo.results) > 0) {
        const results = _.head(Object.values(geo.results));
        const result = _.head(results);

        steps.value.two.city = result?.city || '';
      }
    } catch (e) {
      notificationService.showError('Error getting city from zip code');
      loggingService.error('Error getting city from zip code', e);
    } finally {
      loadingService.stopLoading('main-loader:get-city-from-zip-code');
    }
  };
  const onClickNext = async () => {
    if (step.value == 1) {
      await $v.value.one.$validate();
      if ($v.value.one.$error) {
        return;
      }
      if (!createdReturnPortal.value) {
        createReturnPortal();
        return;
      }
    }
    if (step.value == 2) {
      await $v.value.two.$validate();
      if ($v.value.two.$error) {
        return;
      }
    }
    stepper.value?.next();
  };
  const onClickPrevious = () => {
    stepper.value?.previous();
  };
  const onClickSave = async () => {
    await updateReturnPortal();
    await updateReturnPortalText();
    await updateReturnPortalReceiver();
    emit('close');
  };
  const updateReturnPortal = async () => {
    await $v.value.one.$validate();
    if (
      $v.value.one.$error ||
      !steps.value.one.portal_name ||
      !steps.value.one.integration?.integration_code ||
      !createdReturnPortal.value
    ) {
      return;
    }
    try {
      loadingService.startLoading('main-loader:update-return-portal');
      await returnPortalService.updateReturnPortal(createdReturnPortal.value.return_portal_code, {
        portal_name: steps.value.one.portal_name,
        sender_country_code: steps.value.one.return_country.country_code,
        integration_code: steps.value.one.integration?.integration_code,
        language_code: steps.value.one.language?.language_code,
        confirmation_note: '',
        is_active: true,
      });
      notificationService.showSuccess('Return portal updated successfully.');
    } catch (error) {
      loggingService.error('Error updating return portal', error);
      notificationService.showError('Error updating return portal');
    } finally {
      loadingService.stopLoading('main-loader:update-return-portal');
    }
  };
  const updateReturnPortalText = async () => {
    if (!createdReturnPortal.value || !steps.value.two.headline || !steps.value.two.body_text) {
      return;
    }
    try {
      loadingService.startLoading('main-loader:update-receiver');
      await returnPortalService.updateReturnPortalText(
        createdReturnPortal.value.return_portal_code,
        {
          page_title: steps.value.two.headline,
          content: steps.value.two.body_text,
        },
      );
    } catch (error) {
      loggingService.error('Error updating receiver', error);
      notificationService.showError('Error updating return portal');
    } finally {
      loadingService.stopLoading('main-loader:update-receiver');
    }
  };
  const updateReturnPortalReceiver = async () => {
    $v.value.two.$validate;
    if ($v.value.two.$error) {
      step.value = 2;
      return;
    }
    if (
      !createdReturnPortal.value ||
      !steps.value.two.country ||
      !steps.value.two.city ||
      !steps.value.two.email ||
      !steps.value.two.phone ||
      !steps.value.two.zip_code ||
      !steps.value.two.attn ||
      !steps.value.two.address
    ) {
      notificationService.showError('Fill all required receiver fields.');
      return;
    }
    try {
      loadingService.startLoading('main-loader:update-receiver');
      let payload: any = {
        company_name: steps.value.two.company_name,
        attn: steps.value.two.attn,
        city: steps.value.two.city,
        country_code: steps.value.two.country.country_code,
        email: steps.value.two.email,
        phone: steps.value.two.phone,
        zipcode: steps.value.two.zip_code,
        address: steps.value.two.address,
        company_logo_url: steps.value.two.company_logo_url,
        logo_name: steps.value.two.logo_name ?? null,
      };
      if (steps.value.two.logo) {
        payload['logo'] = steps.value.two.logo ?? null;
      }

      await returnPortalService.updateReturnPortalReceiver(
        createdReturnPortal.value.return_portal_code,
        payload,
      );
      notificationService.showSuccess('Return portal updated successfully.');
    } catch (error) {
      loggingService.error('Error updating receiver', error);
      notificationService.showError('Error saving return portal');
    } finally {
      loadingService.stopLoading('main-loader:update-receiver');
    }
  };
  const createReturnPortal = async () => {
    try {
      if (!steps.value.one.portal_name || !steps.value.one.integration?.integration_code) {
        return;
      }
      loadingService.startLoading('main-loader:add-return-portal');
      const response = await returnPortalService.createReturnPortal({
        portal_name: steps.value.one.portal_name,
        sender_country_code: steps.value.one.return_country.country_code,
        integration_code: steps.value.one.integration?.integration_code,
        language_code: steps.value.one.language?.language_code,
        confirmation_note: '',
        is_active: true,
      });
      createdReturnPortal.value = response;
      setCompanyDetails(createdReturnPortal.value);
      stepper.value?.next();
      notificationService.showSuccess('Return Portal Created');
      getReturnReasons();
      getReturnResolutions();
      getReturnMethods();
    } catch (error: any) {
      if (error.response.status == 422) {
        if (error.response.data.error.portal_name) {
          notificationService.showError(error.response.data.error.portal_name[0]);
        } else {
          notificationService.showError('Error adding return portal');
        }
      } else {
        notificationService.showError('Error adding return portal');
      }
    } finally {
      loadingService.stopLoading('main-loader:add-return-portal');
    }
  };
  const getReturnResolutions = async () => {
    if (!createdReturnPortal.value) {
      return;
    }
    try {
      loadingService.startLoading('main-loader:get-return-resolutions');
      resolutions.value = await returnPortalService.getReturnPortalResolutions(
        createdReturnPortal.value.return_portal_code,
      );
      resolutions.value.forEach((item: IReturnPortalResolution) => {
        selectedResolutions.value[item.resolution_code] = !!item.status;
      });
    } catch (error) {
      loggingService.error('Error getting return resolutions', error);
      notificationService.showError('Error getting return resolutions');
    } finally {
      loadingService.stopLoading('main-loader:get-return-resolutions');
    }
  };

  const getReturnMethods = async () => {
    if (!createdReturnPortal.value) {
      return;
    }
    try {
      loadingService.startLoading('main-loader:get-return-methods');
      methods.value = await returnPortalService.getReturnPortalMethods(
        createdReturnPortal.value.return_portal_code,
      );
      getTemplates();
    } catch (error) {
      loggingService.error('Error getting return methods', error);
      notificationService.showError('Error getting return methods');
    } finally {
      loadingService.stopLoading('main-loader:get-return-methods');
    }
  };

  const getOrderIntegrations = async () => {
    try {
      loadingService.startLoading('main-loader:get-order-integrations');
      const response = await orderIntegrationService.getOrderIntegrations(1, 1000);
      integrations.value = response.entities;
    } catch (error) {
      loggingService.error('Error getting order integrations', error);
      notificationService.showError('Error getting order integrations');
    } finally {
      loadingService.stopLoading('main-loader:get-order-integrations');
    }
  };
  const hydratePredefinedValues = () => {
    if (_props.returnPortal) {
      createdReturnPortal.value = _props.returnPortal;
      steps.value.one.portal_name = _props.returnPortal.portal_name;
      steps.value.one.integration =
        integrations.value.find(
          (i) => i.integration_code === _props.returnPortal?.integration_code,
        ) || null;
      setCompanyDetails(_props.returnPortal);
      // steps.value.two.logo = _props.returnPortal.logo ?? null;
    }
  };
  const setCompanyDetails = (returnPortal: IReturnPortal) => {
    if (returnPortal.country_code) {
      steps.value.two.country =
        $store.state.countryStore.countriesIndexedByCountryCode[returnPortal.country_code] ??
        denmark;
    }
    steps.value.two.company_name = returnPortal.company_name;
    steps.value.two.address = returnPortal.address;
    steps.value.two.phone = returnPortal.phone;
    steps.value.two.attn = returnPortal.attn;
    steps.value.two.city = returnPortal.city;
    steps.value.two.email = returnPortal.email;
    steps.value.two.zip_code = returnPortal.zipcode;
    steps.value.two.headline = returnPortal.page_title;
    steps.value.two.body_text = returnPortal.content ?? '';
    steps.value.two.company_logo_url = returnPortal.meta
      ? returnPortal.meta.company_logo_url ?? companyStore.website
      : companyStore.website;
    steps.value.two.logo_name = returnPortal.meta ? returnPortal.meta.logo_name : null;
  };
  onBeforeMount(async () => {
    await getOrderIntegrations();
    await getCarriers(denmark.country_code, denmark.country_code);
    if (_props.returnPortal) {
      createdReturnPortal.value = _props.returnPortal;
      hydratePredefinedValues();
      getReturnReasons();
      getReturnResolutions();
      getReturnMethods();
    }
  });
  onMounted(() => {
    if ($store.state.languageStore.languages)
      steps.value.one.language = $store.state.languageStore.languages[0];
  });
</script>

<style scoped lang="scss">
  .rounded-borders-box {
    width: 100%;
    padding: 18px;
    border: 1px solid #7d839833;
    border-radius: 8px;
  }
</style>
<style lang="scss">
  .shipvagoo-company-logo-url {
    .q-field__append {
      position: absolute;
      top: -14px;
      left: 110px;
    }
  }
</style>
