<template>
  <div
    class="bg-white return-portal-modal">
    <div class="row items-center q-px-md q-py-sm header-bg">
      <div class="text-white" style="font-size:16px;font-weight: 500;">{{title}}</div>
      <q-space/>
      <slot name="icon-right"/>
      <q-btn v-close-popup icon="close"
             color="white" flat round dense @click="onClickClose"/>
    </div>

    <div class="slot-container">
      <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
        <shipvagoo-input v-model="form.name" :label="$t('common.name')"/>
      </shipvagoo-card-section>
      <shipvagoo-separator/>
      <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 16px">
        <shipvagoo-checkbox
          v-model="form.enable_comment"
          :label="$t('return_reasons.enable_comment')"
        />
        <shipvagoo-checkbox
          v-if="form.enable_comment"
          v-model="form.comment_required"
          :label="$t('return_reasons.comment_required')"
        />
      </shipvagoo-card-section>
      <shipvagoo-separator/>
      <shipvagoo-card-actions>
        <shipvagoo-button v-if="!isEdit" min-width="120px" @click="onClickCreateReturnReason">
          {{ $t('common.create') }}
        </shipvagoo-button>
        <shipvagoo-button v-if="isEdit" min-width="120px" @click="onClickUpdateReturnReason">
          {{ $t('common.update') }}
        </shipvagoo-button>
      </shipvagoo-card-actions>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {computed, onBeforeMount, ref} from 'vue';
  import {useI18n} from 'vue-i18n';
  import {IReturnPortal, IReturnPortalReason} from '../../model/return-portal.model';
  import {
    loadingService,
    loggingService,
    notificationService,
    returnPortalService,
  } from '../../services/_singletons';

  interface Props {
    returnPortal: IReturnPortal;
    returnReason: IReturnPortalReason | null;
  }

  const {t} = useI18n();
  const props = defineProps<Props>();
  const emit = defineEmits<{
    (event: 'close'): void;
    (event: 'on-create'): void;
    (event: 'on-update'): void;
  }>();

  const isEdit = computed(() => {
    return !!props.returnReason;
  });

  const title = computed(() => {
    if (isEdit.value) {
      return t('return_reasons.edit_return_reason');
    }

    return t('return_reasons.add_return_reason');
  });

  interface IForm {
    name: string | null;
    only_available_for_days: string | null;
    enable_comment: boolean;
    comment_required: boolean;
  }

  const form = ref<IForm>({
    name: null,
    only_available_for_days: null,
    enable_comment: false,
    comment_required: false,
  });
  const onClickClose = () => {
    emit('close');
  };
  const onClickCreateReturnReason = async () => {
    if (!form.value.name) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:create-return-reason');

      await returnPortalService.createReturnPortalReason(props.returnPortal.return_portal_code, {
        description: form.value.name,
        enable_comment: form.value.enable_comment,
        comment_required: form.value.comment_required,
        available_days:
          form.value.only_available_for_days && form.value.only_available_for_days !== ''
            ? parseInt(form.value.only_available_for_days, 10)
            : 0,
      });

      emit('close');
      emit('on-create');
    } catch (e) {
      loggingService.error('Error creating return reason', e);
      notificationService.showError('Error creating return reason');
    } finally {
      loadingService.stopLoading('main-loader:create-return-reason');
    }
  };

  const onClickUpdateReturnReason = async () => {
    if (!props.returnReason || !form.value.name) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:update-return-reason');

      await returnPortalService.updateReturnPortalReason(
        props.returnPortal.return_portal_code,
        props.returnReason.return_reason_code,
        {
          description: form.value.name,
          enable_comment: form.value.enable_comment,
          comment_required: form.value.comment_required,
          available_days:
            form.value.only_available_for_days && form.value.only_available_for_days !== ''
              ? parseInt(form.value.only_available_for_days, 10)
              : 0,
        },
      );

      emit('close');
      emit('on-update');
    } catch (e) {
      loggingService.error('Error updating return reason', e);
      notificationService.showError('Error updating return reason');
    } finally {
      loadingService.stopLoading('main-loader:update-return-reason');
    }
  };

  const hydrateForm = () => {
    if (!props.returnReason) {
      return;
    }

    form.value.name = props.returnReason.description;
    form.value.only_available_for_days =
      props.returnReason.available_days > 0 ? String(props.returnReason.available_days) : null;
    form.value.enable_comment = Boolean(props.returnReason.enable_comment);
    form.value.comment_required = Boolean(props.returnReason.comment_required);
  };

  onBeforeMount(() => {
    if (isEdit.value) {
      hydrateForm();
    }
  });
</script>
<style scoped lang="css">
  .return-portal-modal {
    width: 650px;
    max-width: 650px !important;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 15px;
  }

  .header-bg {
    background-color: #243E90;
  }
</style>