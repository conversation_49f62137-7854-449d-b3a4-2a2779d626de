<template>
  <div
    class="bg-white return-portal-modal">
    <div class="row items-center q-px-md q-py-sm header-bg">
      <div class="text-white" style="font-size:16px;font-weight: 500;">{{title}}</div>
      <q-space/>
      <slot name="icon-right"/>
      <q-btn v-close-popup icon="close"
             color="white" flat round dense @click="onClickClose"/>
    </div>
    <div class="slot-container">
      <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
        <shipvagoo-input v-model="form.name" :label="$t('common.name')"/>
        <shipvagoo-input v-model="form.description" :label="$t('common.description')"/>
        <!--      <shipvagoo-input-->
        <!--        v-model="form.only_available_for_days"-->
        <!--        :label="$t('common.only_available_for_days')"-->
        <!--      />-->
      </shipvagoo-card-section>
      <shipvagoo-separator/>
      <shipvagoo-card-actions>
        <shipvagoo-button v-if="!isEdit" min-width="120px" @click="onClickCreateReturnResolution">
          {{ $t('common.create') }}
        </shipvagoo-button>
        <shipvagoo-button v-if="isEdit" min-width="120px" @click="onClickUpdateReturnResolution">
          {{ $t('common.update') }}
        </shipvagoo-button>
      </shipvagoo-card-actions>
    </div>
  </div>
  <!--  <shipvagoo-modal :title="title" size="sm">
    <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
      <shipvagoo-input v-model="form.name" :label="$t('common.name')" />
      <shipvagoo-input v-model="form.description" :label="$t('common.description')" />
&lt;!&ndash;      <shipvagoo-input&ndash;&gt;
&lt;!&ndash;        v-model="form.only_available_for_days"&ndash;&gt;
&lt;!&ndash;        :label="$t('common.only_available_for_days')"&ndash;&gt;
&lt;!&ndash;      />&ndash;&gt;
    </shipvagoo-card-section>
    <shipvagoo-separator />
    <shipvagoo-card-actions>
      <shipvagoo-button v-if="!isEdit" min-width="120px" @click="onClickCreateReturnResolution">
        {{ $t('common.create') }}
      </shipvagoo-button>
      <shipvagoo-button v-if="isEdit" min-width="120px" @click="onClickUpdateReturnResolution">
        {{ $t('common.update') }}
      </shipvagoo-button>
    </shipvagoo-card-actions>
  </shipvagoo-modal>-->
</template>

<script setup lang="ts">
  import {computed, onBeforeMount, ref} from 'vue';
  import {IReturnPortal, IReturnPortalResolution} from '../../model/return-portal.model';
  import {
    loadingService,
    loggingService,
    notificationService,
    returnPortalService,
  } from '../../services/_singletons';

  interface Props {
    returnPortal: IReturnPortal;
    returnResolution: IReturnPortalResolution | null;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{
    (event: 'close'): void;
    (event: 'on-create'): void;
    (event: 'on-update'): void;
  }>();

  const isEdit = computed(() => {
    return !!props.returnResolution;
  });

  const title = computed(() => {
    if (isEdit.value) {
      return 'Edit Return Resolution';
    }

    return 'Add Return Resolution';
  });

  interface IForm {
    name: string | null;
    description: string | null;
    only_available_for_days: string | null;
  }

  const form = ref<IForm>({
    name: null,
    description: null,
    only_available_for_days: null,
  });

  const onClickCreateReturnResolution = async () => {
    if (!form.value.name) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:create-return-resolution');

      await returnPortalService.createReturnPortalResolution(
        props.returnPortal.return_portal_code,
        {
          name: form.value.name,
          description: form.value.description || '',
          available_days:
            form.value.only_available_for_days && form.value.only_available_for_days !== ''
              ? parseInt(form.value.only_available_for_days, 10)
              : 0,
        },
      );

      emit('close');
      emit('on-create');
    } catch (e) {
      loggingService.error('Error creating return resolution', e);
      notificationService.showError('Error creating return resolution');
    } finally {
      loadingService.stopLoading('main-loader:create-return-resolution');
    }
  };

  const onClickUpdateReturnResolution = async () => {
    if (!props.returnResolution || !form.value.name) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:update-return-resolution');

      await returnPortalService.updateReturnPortalResolution(
        props.returnPortal.return_portal_code,
        props.returnResolution.resolution_code,
        {
          name: form.value.name,
          description: form.value.description || '',
          available_days:
            form.value.only_available_for_days && form.value.only_available_for_days !== ''
              ? parseInt(form.value.only_available_for_days, 10)
              : 0,
        },
      );

      emit('close');
      emit('on-update');
    } catch (e) {
      loggingService.error('Error updating return resolution', e);
      notificationService.showError('Error updating return resolution');
    } finally {
      loadingService.stopLoading('main-loader:update-return-resolution');
    }
  };
  const onClickClose = () => {
    emit('close');
  };
  const hydrateForm = () => {
    if (!props.returnResolution) {
      return;
    }

    form.value.name = props.returnResolution.name;
    form.value.description = props.returnResolution.description;
    form.value.only_available_for_days =
      props.returnResolution.available_days > 0
        ? String(props.returnResolution.available_days)
        : null;
  };

  onBeforeMount(() => {
    if (isEdit.value) {
      hydrateForm();
    }
  });
</script>
<style scoped lang="css">
  .return-portal-modal {
    width: 650px;
    max-width: 650px !important;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 15px;
  }

  .header-bg {
    background-color: #243E90;
  }

  .slot-container {
    max-height: calc(100vh - 48px - 67px);
    overflow: auto;
  }

  .text-brand {
    color: #243E90 !important;
  }

  .bg-brand {
    background: #243E90 !important;
  }
</style>
