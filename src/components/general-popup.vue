<template>
  <div class="general-popup">
    <shipvagoo-card-section class="ui_kit-shipvagoo-modal-header row items-center" style="padding: 10px 20px 0 20px">
      <q-space />
      <slot name="icon-right" />
      <q-btn v-close-popup icon="close" color="grey" flat round dense @click="onClickClose" />
    </shipvagoo-card-section>
    <div class="text-center text-dark text-sixteen text-weight-7" style="margin-top: -10px">
      <slot name="heading"/>
    </div>
    <div style=" font-size: 13px;color: #7D8398;text-align: center; " class="q-ma-md">
      <slot name="body" />
    </div>
    <div class="q-ma-md full-width q-mt-md">
      <slot name="actions"/>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(['close']);

const onClickClose = () => {
  emit('close');
};
</script>

<style scoped>
.general-popup {
  width: 450px;
  max-width: 450px;
  background-color: white;
}


</style>