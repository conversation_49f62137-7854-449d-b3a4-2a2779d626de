<template>
  <div class="add-new-card-box" :style="{height:height}">
    <div class="row cursor-pointer">
      <div class="col-md-12 flex justify-center">
        <q-icon name="ion-add-circle-outline"
                size="lg" style="color: #7D8398" />
      </div>
      <div class="col-md-12 flex justify-center q-mt-sm">
        <div class="text-fourteen dark-subtitle-text text-grey-7 text-bold">{{ label }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  label: {
    type: String,
    required: false,
    default: "Add New"
  },
  height: {
    type: String,
    required: false,
    default: "230px"
  }
});
</script>

<style scoped lang="scss">
.add-new-card-box {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  border: 1px solid rgba(125, 131, 152, 0.20);
  //  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

</style>