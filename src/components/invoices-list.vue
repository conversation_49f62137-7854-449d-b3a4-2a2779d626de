<template>
  <div class="invoices_page">
    <shipvagoo-placeholder v-if="!rows.length" text="No invoices are available">
    </shipvagoo-placeholder>
    <div>
      <shipvagoo-table-v1
        v-if="rows.length"
        v-model:pagination="pagination"
        :rows="rows"
        :columns="columns"
        :total="pagination.rowsNumber"
        :loading="loadingService.loadings.has('main-loader:loading-invoices')"
        @request="getInvoices($event.pagination)"
        @row-click="onClickDownloadInvoice"
        class="invoices-page-table"
      >
        <template #body-cell-created_at="props">
          <q-td :props="props">
            <span class="text-no-wrap">{{
              dayjs(String(props.row.created_at)).format('DD/MM/YYYY')
            }}</span
            ><br />
            <span class="text-no-wrap">{{
              dayjs(String(props.row.created_at)).format('HH:mm')
            }}</span>
          </q-td>
        </template>
        <!--        <template #body-cell-country="props">
                  <q-td :props="props">
                    <div v-if="props" style="display: flex; gap: 10px; justify-content: flex-start">
                      <img
                        decoding="async"
                        :src="`/assets/img/${props.row.recipient?.country?.toLowerCase?.() ?? ''}.svg`"
                        style="width: 20px; height: 20px; border-radius: 50%; object-fit: cover"
                      />
                      {{ props.row.recipient.city }}
                    </div>
                  </q-td>
                </template>
                
                <template #body-cell-month="props">
                  <q-td :props="props">
                    {{ dayjs(props.row.created_at).format('MMMM') }}
                  </q-td>
                </template>
                <template #body-cell-year="props">
                  <q-td :props="props">
                    {{ dayjs(props.row.created_at).format('YYYY') }}
                  </q-td>
                </template>
                -->
        <template #body-cell-carrier="props">
          <q-td :props="props">
            <div
              class="flex items-center no-wrap"
              v-if="
                CarrierService.getCarrierFromShippingProductCode(
                  $store.state.courierStore.carriers,
                  $store.state.courierStore.carrierProducts,
                  asInvoice(props.row).shipment?.carrier_product_code,
                )?.carrier_id?.toLowerCase()
              "
            >
              <div
                class="shipvagoo-table-carrier-logo"
                :style="{
                  backgroundImage:
                    'url(' +
                    CarrierService.getCarrierFromShippingProductCode(
                      $store.state.courierStore.carriers,
                      $store.state.courierStore.carrierProducts,
                      asInvoice(props.row).shipment?.carrier_product_code,
                    )?.icon +
                    ')',
                }"
              />
              <div class="text-no-wrap q-ml-sm shipped-carrier">
                {{
                  CarrierService.getCarrierFromShippingProductCode(
                    $store.state.courierStore.carriers,
                    $store.state.courierStore.carrierProducts,
                    asInvoice(props.row).shipment?.carrier_product_code,
                  )?.name
                }}
              </div>
            </div>
          </q-td>
        </template>
        <template #body-cell-service="props">
          <q-td :props="props">
            <div
              class="flex items-center no-wrap"
              v-if="
                CarrierService.getCarrierFromShippingProductCode(
                  $store.state.courierStore.carriers,
                  $store.state.courierStore.carrierProducts,
                  asInvoice(props.row).shipment?.carrier_product_code,
                )?.carrier_id?.toLowerCase()
              "
            >
              <div class="text-no-wrap q-ml-sm shipped-carrier">
                {{
                  $store.state.courierStore.carrierProducts.find(
                    (item: IShippingProduct) =>
                      item.carrier_product_code ==
                      asInvoice(props.row).shipment?.carrier_product_code,
                  )?.name
                }}
              </div>
            </div>
          </q-td>
        </template>
        <template #body-cell-more="props">
          <q-td :props="props" class="more">
            <q-btn
              dense
              flat
              round
              style="color: #1f2124"
              class="more-action"
              field="edit"
              icon="more_horiz"
            >
              <shipvagoo-menu>
                <shipvagoo-menu-item clickable @click="onClickDownloadInvoice(null, props.row)">
                  {{ $t('common.download') }}
                </shipvagoo-menu-item>
              </shipvagoo-menu>
            </q-btn>
          </q-td>
        </template>
      </shipvagoo-table-v1>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, onBeforeMount, Ref, ref, watch } from 'vue';
  import { QTableProps } from 'quasar';
  import dayjs from 'dayjs';
  import * as _ from 'lodash-es';
  //import {useI18n} from 'vue-i18n';
  import {
    invoiceService,
    loadingService,
    loggingService,
    notificationService,
  } from '../services/_singletons';
  import { Pagination } from '../model/common.model';
  import HelperService from '../services/helper.service';
  import { IInvoice } from '../model/invoice.model';
  import { IRequestParam } from '../model/http.model';
  //  import {useHelper} from "../composeable/Helper";
  import { PDFDocument } from 'pdf-lib';
  import WindowService from '../services/window.service';
  import PriceService from '../services/price.service';
  import { ITransaction } from '../model/transactions.model';
  import CarrierService from '../services/carrier.service';
  import { useStore } from '../store';
  import { IShippingProduct } from '../model/carrier.model';

  //const {t} = useI18n();
  const $store = useStore();
  const props = defineProps({
    searchParam: {
      type: String,
      default: '',
    },
  });
  const search: Ref<string> = ref('');
  watch(
    () => props.searchParam,
    (currentVal) => {
      search.value = currentVal;
      getInvoices(null);
    },
  );
  const columns = computed<QTableProps['columns']>(() => [
    {
      name: 'created_at',
      align: 'left',
      label: 'Transaction date',
      field: (row: IInvoice) => dayjs(String(row.created_at)).format('DD/MM/YYYY HH:mm:ss'),
      sortable: false,
    },
    {
      name: 'transaction_type',
      align: 'left',
      label: 'Transaction type',
      field: (row: ITransaction) => `Shipment ID: ${row.shipment_code}`,
      sortable: false,
    },
    {
      name: 'id',
      align: 'left',
      label: 'Invoice number',
      field: (row: IInvoice) => HelperService.formatId(row.id),
      sortable: false,
    },
    {
      name: 'carrier',
      field: '',
      required: true,
      label: 'Carrier',
      align: 'left',
      sortable: false,
    },
    {
      name: 'service',
      field: '',
      required: true,
      label: 'Service',
      align: 'left',
      sortable: false,
    },
    {
      name: 'payment_method',
      field: (row: IInvoice) => {
        const payment_method = row.transaction?.payment_method;
        if (payment_method) {
          const type = payment_method.type;
          const details = payment_method.details;
          if (type == 'card') {
            if (details) {
              return `${
                details.brand.charAt(0).toUpperCase() + details.brand.slice(1)
              } (${details.bin.substring(0, 4)} **** **** ${details.last4})`;
            } else {
              return type.charAt(0).toUpperCase() + type.slice(1);
            }
          } else {
            return type.charAt(0).toUpperCase() + type.slice(1);
          }
        } else {
          return 'Balance';
        }
      },
      required: true,
      label: 'Payment method',
      align: 'left',
      sortable: false,
    },
    {
      name: 'amount',
      align: 'right',
      label: 'Total amount',
      field: (row: ITransaction) => {
        //const shipment_items = row.invoice_items.find((item: any) => item.type == 'Shipments');
        const invoiceItems = row.invoice_items;
        let totalPrice: any = 0;
        let total: any = 0;
        invoiceItems.forEach((item: any) => {
          let totalUnitPrice = 0;
          const taxPercentage = parseFloat(row.tax_percent) / 100;
          if (item) {
            totalUnitPrice = item.items.reduce(
              (total: number, item: any) => total + item.unit_price * item.count,
              0,
            );
          }
          totalPrice = totalPrice + Number(totalUnitPrice + totalUnitPrice * taxPercentage);
          total = totalPrice.toFixed(3);
        });
        return PriceService.formatPrice(Number(total), 'de-DE');
      },
      sortable: false,
    },
    /*  {
      name: 'payment_method',
      align: 'left',
      label: 'Payment Method',
      field: 'payment_method',
      sortable: false,
    },*/

    { name: 'more', label: 'Action', field: 'more', sortable: false },
  ]);

  const rows = ref<IInvoice[]>([]);
  const pagination: Ref<Pagination> = ref({
    page: 1,
    rowsPerPage: 10,
    rowsNumber: 0,
    descending: true,
  });
  const asInvoice = (row: IInvoice): IInvoice => row;
  const getInvoices = async (tablePagination: Pagination | null) => {
    try {
      loadingService.startLoading('main-loader:loading-invoices');
      const page = tablePagination?.page ?? 1;
      const limit = tablePagination?.rowsPerPage ?? 10;

      const params: IRequestParam[] = [];

      if (search.value && search.value !== '') {
        params.push({ key: 'conditions[invoice_code:like]', value: search.value });
      }
      params.push({ key: 'with', value: 'shipment,transaction' });
      if (tablePagination?.sortBy) {
        const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
        params.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
      } else {
        params.push({ key: 'sorting[created_at]', value: 'desc' });
      }

      const invoices = await invoiceService.getInvoices(page, limit, params);

      rows.value = invoices.entities;
      pagination.value.rowsNumber = invoices.total;
      pagination.value.page = page;
      pagination.value.rowsPerPage = limit;
      pagination.value.descending = tablePagination?.descending;
      pagination.value.sortBy = tablePagination?.sortBy;
    } catch (e) {
      loggingService.error('Error loading invoices', e);
      notificationService.showError('Error loading invoices');
    } finally {
      loadingService.stopLoading('main-loader:loading-invoices');
    }
  };

  const onClickDownloadInvoice = async (event: Event | null, row: IInvoice) => {
    if (event && HelperService.containsIgnoredRowElements(event)) {
      return;
    }
    try {
      loadingService.startLoading('main-loader:print-sales-invoice-receipt');
      const response = await invoiceService.getInvoicePdf(row.invoice_code);
      const filePath = response.file;

      await (async () => {
        const mergedPdf = await PDFDocument.create();
        if (filePath != undefined) {
          const pdfDoc = await PDFDocument.load(filePath as string);
          const pages = await mergedPdf.copyPages(pdfDoc, pdfDoc.getPageIndices());
          pages.forEach((page) => mergedPdf.addPage(page));
        }
        const url = URL.createObjectURL(
          await HelperService.toBlob(await mergedPdf.saveAsBase64({ dataUri: true })),
        );
        const windowRef = WindowService.openWindow('Invoice Preview');
        windowRef.location = url;
      })();
    } catch (e) {
      loggingService.error('Error while printing receipt', e);
      notificationService.showError('Error while printing receipt');
    } finally {
      loadingService.stopLoading('main-loader:print-sales-invoice-receipt');
    }
  };

  onBeforeMount(() => {
    getInvoices(null);
  });
</script>

<style>
  .invoices-page-table th:nth-child(-n + 6),
  .invoices-page-table td:nth-child(-n + 6) {
    width: 14%;
  }

  .invoices-page-table th:nth-child(7),
  .invoices-page-table td:nth-child(7) {
    width: 6%;
  }

  .invoices-page-table th:nth-child(8),
  .invoices-page-table td:nth-child(8) {
    width: 10%;
  }
</style>
