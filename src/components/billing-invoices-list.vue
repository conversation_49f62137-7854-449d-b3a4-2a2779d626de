<template>
  <div>
    <shipvagoo-placeholder v-if="!rows.length" text="No billing invoices are available">
    </shipvagoo-placeholder>
    <div v-if="rows.length">
      <shipvagoo-table-v1
        v-model:pagination="pagination"
        :rows="rows"
        :columns="columns"
        :total="pagination.rowsNumber"
        :loading="loadingService.loadings.has('main-loader:loading-invoices')"
        @request="getBillingInvoices($event.pagination)"
      >
        <template #body-cell-created_at="props">
          <q-td :props="props">
            <span class="text-no-wrap">{{
              dayjs(String(props.row.created_at)).format('DD/MM/YYYY')
            }}</span
            ><br />
            <span class="text-no-wrap">{{
              dayjs(String(props.row.created_at)).format('HH:mm')
            }}</span>
          </q-td>
        </template>
        <template #body-cell-month="props">
          <q-td :props="props">
            {{ props.row.month }}
          </q-td>
        </template>
        <template #body-cell-year="props">
          <q-td :props="props">
            {{ props.row.year }}
          </q-td>
        </template>

        <template #body-cell-more="props">
          <q-td :props="props" class="more">
            <q-btn
              dense
              flat
              round
              style="color: #1f2124"
              class="more-action"
              field="edit"
              icon="more_horiz"
            >
              <shipvagoo-menu>
                <shipvagoo-menu-item clickable @click="onClickViewInvoice(props.row)">
                  View invoice
                </shipvagoo-menu-item>
              </shipvagoo-menu>
            </q-btn>
          </q-td>
        </template>
      </shipvagoo-table-v1>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, onBeforeMount, Ref, ref, watch } from 'vue';
  import { QTableProps } from 'quasar';
  import dayjs from 'dayjs';
  import * as _ from 'lodash-es';
  import { useI18n } from 'vue-i18n';
  import {
    invoiceService,
    loadingService,
    loggingService,
    notificationService,
  } from '../services/_singletons';
  import { Pagination } from '../model/common.model';
  import { IBillingInvoice, IInvoice } from '../model/invoice.model';
  import { IRequestParam } from '../model/http.model';
  import PriceService from '../services/price.service';
  import { useHelper } from '../composeable/Helper';
  import HelperService from '../services/helper.service';
  const { showPdf } = useHelper();

  const props = defineProps({
    searchParam: {
      type: String,
      default: '',
    },
  });
  watch(
    () => props.searchParam,
    (currentVal) => {
      search.value = currentVal;
      getBillingInvoices(null);
    },
  );
  const { t } = useI18n();

  const search: Ref<string> = ref(props.searchParam);
  const columns = computed<QTableProps['columns']>(() => [
    {
      name: 'created_at',
      align: 'left',
      label: 'Date',
      field: (row: IBillingInvoice) => dayjs(String(row.created_at)).format('DD/MM/YYYY HH:mm:ss'),
      sortable: false,
    },
    {
      name: 'id',
      align: 'left',
      label: 'Invoice number',
      field: (row: IBillingInvoice) => HelperService.formatId(row.id),
      sortable: false,
    },
    {
      name: 'id',
      align: 'left',
      label: 'Economic invoice ID',
      field: (row: IBillingInvoice) => row.economic_invoice_id,
      sortable: false,
    },
    {
      name: 'start_date',
      align: 'left',
      label: 'Start date',
      field: (row: IBillingInvoice) => dayjs(String(row.start_date)).format('DD/MM/YYYY'),
      sortable: false,
    },
    {
      name: 'end_date',
      align: 'left',
      label: 'End date',
      field: (row: IBillingInvoice) => dayjs(String(row.end_date)).format('DD/MM/YYYY'),
      sortable: false,
    },
    {
      name: 'due_date',
      align: 'left',
      label: 'Due date',
      field: (row: IBillingInvoice) =>
        row.due_date ? dayjs(String(row.due_date)).format('DD/MM/YYYY') : '-',
      sortable: false,
    },
    {
      name: 'month',
      align: 'left',
      label: t('common.month'),
      field: 'month',
      sortable: false,
    },
    {
      name: 'year',
      align: 'left',
      label: t('common.year'),
      field: 'year',
      sortable: false,
    },
    {
      name: 'payment_status',
      align: 'left',
      label: 'Payment status',
      field: 'payment_status',
      sortable: false,
    },
    {
      name: 'amount',
      align: 'right',
      label: 'Total amount',
      field: (row: IBillingInvoice) => PriceService.formatPrice(Number(row.total_amount), 'de-DE'),
      sortable: false,
    },
    { name: 'more', label: 'Action', field: 'more', sortable: false },
  ]);

  const rows = ref<IInvoice[]>([]);
  const pagination: Ref<Pagination> = ref({
    page: 1,
    rowsPerPage: 10,
    rowsNumber: 0,
    descending: true,
  });

  const getBillingInvoices = async (tablePagination: Pagination | null) => {
    try {
      loadingService.startLoading('main-loader:loading-invoices');
      const page = tablePagination?.page ?? 1;
      const limit = tablePagination?.rowsPerPage ?? 10;

      const params: IRequestParam[] = [];

      if (search.value && search.value !== '') {
        params.push({ key: 'conditions[id:like]', value: search.value });
      }

      if (tablePagination?.sortBy) {
        const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
        params.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
      } else {
        params.push({ key: 'sorting[created_at]', value: 'desc' });
      }

      const invoices = await invoiceService.getBillingInvoices(page, limit, params);

      rows.value = invoices.entities;
      pagination.value.rowsNumber = invoices.total;
      pagination.value.page = page;
      pagination.value.rowsPerPage = limit;
      pagination.value.descending = tablePagination?.descending;
      pagination.value.sortBy = tablePagination?.sortBy;
    } catch (e) {
      loggingService.error('Error loading invoices', e);
      notificationService.showError('Error loading invoices');
    } finally {
      loadingService.stopLoading('main-loader:loading-invoices');
    }
  };

  const onClickViewInvoice = async (row: IBillingInvoice) => {
    try {
      loadingService.startLoading('main-loader:print-transaction-receipt');
      const response = await invoiceService.downloadBillingInvoicePdf(row.economic_invoice_code);
      await showPdf(response.file);
    } catch (e) {
      loggingService.error('Error while printing receipt', e);
      notificationService.showError('Error while showing invoice');
    } finally {
      loadingService.stopLoading('main-loader:print-transaction-receipt');
    }
  };

  onBeforeMount(() => {
    getBillingInvoices(null);
  });
</script>
