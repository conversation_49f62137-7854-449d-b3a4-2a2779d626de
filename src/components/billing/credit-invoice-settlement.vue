<template>
  <shipvagoo-modal :header-separator="false" title="Credit invoice settlement" size="xs">
    <shipvagoo-separator style="margin: 0 20px"></shipvagoo-separator>

    <!-- Invoice Details Section -->
    <shipvagoo-card-section class="text-center justify-center items-center q-my-md">
      <strong class="text-twenty-four blue-text">
        {{ formatAmount(creditInvoice.total_amount) }} DKK
      </strong>
      <div class="dark-text text-twelve q-mt-sm">Total invoice amount</div>
      <div class="dark-text text-ten q-mt-xs">
        Due date: {{ formatDate(creditInvoice.due_date) }}
      </div>
    </shipvagoo-card-section>

    <shipvagoo-card-section>
      <shipvagoo-input-v1
        v-model="form.invoiceNo"
        label="Invoice number"
        placeholder="Enter invoice number"
        :disable="true"
        :error="$v.invoiceNo.$error"
        :error-message="String($v.invoiceNo.$errors?.[0]?.$message ?? null)"
      />

      <shipvagoo-input-v1
        class="q-mt-md"
        @update:modelValue="onUpdateAmount"
        v-model="form.amountPayable"
        label="Amount payable (DKK)"
        placeholder="Enter amount payable"
        :error="$v.amountPayable.$error"
        :error-message="String($v.amountPayable.$errors?.[0]?.$message ?? null)"
      />
    </shipvagoo-card-section>
    <!-- Action Buttons -->
    <shipvagoo-card-actions style="display: flex; justify-content: flex-end !important">
      <div class="flex" style="gap: 10px">
        <shipvagoo-button outline no-caps @click="onCancel" label="Cancel" width="70px" dense />
        <shipvagoo-button
          :disable="!isFormValid"
          no-caps
          @click="onSave"
          label="Save"
          width="70px"
          dense
        />
      </div>
    </shipvagoo-card-actions>
  </shipvagoo-modal>
</template>

<script setup lang="ts">
  import useVuelidate, { ValidationArgs } from '@vuelidate/core';
  import { helpers, required } from '@vuelidate/validators';
  import { ref, computed, onBeforeUnmount } from 'vue';
  import { useHelper } from '../../composeable/Helper';
  import { notificationService } from '../../services/_singletons';
  import PriceService from '../../services/price.service';
  import HelperService from '../../services/helper.service';

  // Props interface
  export interface IBillingInvoice {
    id: number;
    start_date: string;
    created_at: string;
    end_date: string;
    due_date: string;
    month: string;
    year: string;
    payment_status: string;
    total_amount: number;
  }

  interface ICreditSettlementForm {
    invoiceNo: string;
    amountPayable: string | number | null;
  }

  // Props
  const props = defineProps<{
    creditInvoice: IBillingInvoice;
  }>();

  // Composables and services
  const { mustBeValidInput, mustBeValidLength, mustBeValidMinValue, mustBeValidMaxValue } =
    useHelper();

  // Emits
  const emit = defineEmits(['close', 'refresh', 'save']);

  // Reactive data
  const settlementInterval = ref<ReturnType<typeof setInterval> | null>(null);

  const form = ref<ICreditSettlementForm>({
    invoiceNo: HelperService.formatId(props.creditInvoice.id),
    amountPayable: props.creditInvoice.total_amount,
  });

  // Computed properties
  const isFormValid = computed(() => {
    return !$v.value.$error && form.value.invoiceNo && form.value.amountPayable;
  });

  // Validation rules
  const $v = useVuelidate<ICreditSettlementForm, ValidationArgs<ICreditSettlementForm>>(
    {
      invoiceNo: {
        required: helpers.withMessage('Invoice number is required.', required),
      },
      amountPayable: {
        required: helpers.withMessage('Amount payable is required.', required),
        mustBeValidInput: helpers.withMessage('Only valid input is required.', mustBeValidInput),
        mustBeValidLength: helpers.withMessage(
          'Amount value cannot be greater than 6 digits.',
          mustBeValidLength,
        ),
        minValue: helpers.withMessage(
          'Amount value should be greater than 0.',
          mustBeValidMinValue(0.01),
        ),
        maxValue: helpers.withMessage(
          'Amount value cannot exceed the total invoice amount.',
          mustBeValidMaxValue(props.creditInvoice.total_amount),
        ),
      },
    },
    form,
  );

  // Methods
  const formatAmount = (amount: number): string => {
    return PriceService.shippingPriceFormat(amount, 'de-DE');
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString();
  };

  const onUpdateAmount = async () => {
    await $v.value.$validate();
  };

  const onCancel = () => {
    emit('close');
  };

  const onSave = async () => {
    await $v.value.$validate();

    if (!isFormValid.value) {
      return;
    }

    const regex = /^(\d{1,3}(\.\d{3})*|\d+)(,\d{2})?$/;
    if (!regex.test(form.value.amountPayable!.toString())) {
      notificationService.showError('Amount is not in correct format');
      return;
    }

    const amount = form.value.amountPayable!.toString();
    const price = PriceService.shippingPriceFormat(
      Number(
        amount.replace(/[,.]/g, (match) => {
          return match === ',' ? '.' : '';
        }),
      ),
      'en',
    ).replace(/,/g, '');

    // Emit save event with form data
    emit('save', {
      invoiceNo: form.value.invoiceNo,
      amountPayable: price,
      invoiceId: props.creditInvoice.id,
    });
  };

  function clearSettlementInterval(value: ReturnType<typeof setInterval> | null) {
    if (value) {
      clearInterval(value);
    }
  }

  onBeforeUnmount(() => {
    clearSettlementInterval(settlementInterval.value);
  });
</script>

<style scoped lang="scss">
  a {
    color: $primary-one;
    text-decoration: underline;
  }
</style>
