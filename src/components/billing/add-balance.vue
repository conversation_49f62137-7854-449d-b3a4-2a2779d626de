<template>
  <shipvagoo-modal
    :header-separator="false"
    title="Add balance"
    size="xs">
    <shipvagoo-separator style="margin:0 20px"></shipvagoo-separator>
    <shipvagoo-card-section class=" text-center justify-center items-center q-my-md">
      <strong class="text-twenty-four blue-text">
        {{ $store.getters["companyStore/formattedBalance"] }}
      </strong>
      <div class="dark-text text-twelve q-mt-sm">Balance available</div>
    </shipvagoo-card-section>
    <shipvagoo-card-section style="padding-top: 0; padding-bottom: 0">
      <shipvagoo-input-v1
        @update:modelValue="onAddAmount"
        v-model="form.amount"
        :label="$t('add_funds.amount_to_add')"
        :placeholder="$t('add_funds.enter_amount')"
        :error="$v.amount.$error"
        :error-message="String($v.amount.$errors?.[0]?.$message ?? null)"
      />
    </shipvagoo-card-section>
    <shipvagoo-card-section class="flex" style="gap:10px">
      <shipvagoo-button :outline="form.amount!=500"
                        class="flex-1 text-no-wrap" @click="onClickQuickAmount(500)">500 DKK
      </shipvagoo-button>
      <shipvagoo-button :outline="form.amount!=1000"
                        @click="onClickQuickAmount(1000)"
                        class="flex-1 text-no-wrap">1000 DKK
      </shipvagoo-button>
      <shipvagoo-button
        :outline="form.amount!=1500"
        @click="onClickQuickAmount(1500)"
        class="flex-1 text-no-wrap">1500 DKK
      </shipvagoo-button>
      <shipvagoo-button
        :outline="form.amount!=2000"
        @click="onClickQuickAmount(2000)"
        class="flex-1 text-no-wrap">2000 DKK
      </shipvagoo-button>
    </shipvagoo-card-section>
    <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 10px">
      <div style="display: flex;gap: 8px">
        <div>
          <shipvagoo-checkbox-v1
            v-model="form.isReadTermsAndConditions" size="md">
          </shipvagoo-checkbox-v1>
        </div>
        <div
          @click="()=>form.isReadTermsAndConditions=!form.isReadTermsAndConditions"
          class="dark-text text-thirteen cursor-pointer" style="margin-top: 2px;line-height: 18px;">
          By proceeding, you accecpt that you have read and
          understood our <strong>
          <a href="https://shipvagoo.com/virksomhed/vilkaar-og-betingelser"
             class="blue-text"
             @click.stop
             target="_blank">terms of use and conditions</a>
        </strong>
        </div>
      </div>
    </shipvagoo-card-section>
    <shipvagoo-card-actions style="display: flex; justify-content: stretch !important">
      <div class="flex full-width" style="gap:10px">
        <shipvagoo-button
          height="42px"
          v-if="savedDefaultCard"
          :disable="!form.isReadTermsAndConditions"
          style="flex: 1;"
          no-caps
          @click="onClickGoToPaymentsWithSavedCard">
          <div style="line-height: 15px">
            <div class="text-thirteen">Pay with saved card</div>
            <div class="text-ten" v-if="savedDefaultCard">({{
                savedDefaultCard.bin.substring(0, 4) + " xxxx xxxx " + savedDefaultCard.last4
              }})
            </div>
          </div>
        </shipvagoo-button>
        <shipvagoo-button
          height="42px"
          outline
          :disable="!form.isReadTermsAndConditions"
          style="flex: 1;"
          no-caps
          @click="onClickGoToPayments"
          label="Go to payment" />
      </div>
      <!--      <div class="q-mt-sm" v-if="savedDefaultCard">
              <div style="color: #1f2124;font-size: 16px;">
                {{ savedDefaultCard.bin.substring(0, 4) + ' xxxx xxxx ' + savedDefaultCard.last4 }}
              </div>
              <div style="color: #1f2124;font-size: 16px;font-weight: 500">
                Expiry: {{ `${savedDefaultCard.exp_month}/${savedDefaultCard.exp_year.substring(2, 4)}` }}
              </div>
            </div>-->
    </shipvagoo-card-actions>
  </shipvagoo-modal>

  <q-dialog
    :square="false"
    maximized
    @hide="onHideMakePaymentDialog"
    v-model="openMakePaymentModal"
    persistent
    no-backdrop-dismiss
  >
    <make-payment :link="link" title="Add balance"></make-payment>
  </q-dialog>

</template>

<script setup lang="ts">
import useVuelidate, { ValidationArgs } from "@vuelidate/core";
import { helpers, required } from "@vuelidate/validators";
import { ref, computed, onBeforeUnmount } from "vue";
import { useHelper } from "../../composeable/Helper";
import {
  IAddFundResponse,
  IAddFundWithSavedCardResponse,
  IVerifyPaymentResponse
} from "../../model/transactions.model";
import {
  loadingService,
  transactionService,
  notificationService,
  loggingService
} from "../../services/_singletons";
import { useStore } from "../../store";
import PriceService from "../../services/price.service";

const { mustBeValidInput, mustBeValidLength, mustBeValidMinValue } = useHelper();
const $store = useStore();
const addFundsInterval = ref<ReturnType<typeof setInterval> | null>(null);
// const {t} = useI18n();

const emit = defineEmits(["close", "refresh"]);

interface IAddFundsForm {
  amount: string | number | null;
  isReadTermsAndConditions: boolean;
}

const link = ref<string>("");
const payment_id = ref<number | null>(null);
const transaction_code = ref<number | null>(null);
const openMakePaymentModal = ref<boolean>(false);
const form = ref<IAddFundsForm>({
  amount: null,
  isReadTermsAndConditions: false
});
const savedDefaultCard = computed(() => {
  return $store.state.paymentSettingsStore.cards ? $store.state.paymentSettingsStore.cards.find(e => e.is_default) : null;
});
const onClickQuickAmount = (amount: number) => {
  form.value.amount = amount;
};
const $v = useVuelidate<IAddFundsForm, ValidationArgs<IAddFundsForm>>(
  {
    amount: {
      required: helpers.withMessage("This field is required.", required),
      mustBeValidInput: helpers.withMessage("Only valid input is required.", mustBeValidInput),
      mustBeValidLength: helpers.withMessage("Amount value can not be greater than 6 digits.", mustBeValidLength),
      minValue: helpers.withMessage("Amount value should be greater than 0.", mustBeValidMinValue(0.000001))
    },
    isReadTermsAndConditions: {}
  },
  form
);

const onCloseAddFunds = () => {
  emit("close");
};
const onAddAmount = async () => {
  await $v.value.$validate();
  if ($v.value.$error || !form.value.amount || !form.value.isReadTermsAndConditions) {
    return;
  }
};

const onHideMakePaymentDialog = () => {
  openMakePaymentModal.value = false;
  verifyAddedFunds(payment_id.value, "directCard");
};
const verifyAddedFunds = async (_payment_id: number | string | null, paymentWith: string) => {
  try {
    loadingService.startLoading("main-loader:get-transactions-add-funds");
    const timeInterval = 5;
    const timeOut = 60;
    let currentTime = 0;
    let isApiCallInProgress = false;
    if (paymentWith == "directCard") {
      notificationService.showError("Loading your page. Please hold on for a moment while we verify if something went wrong.");
    }
    addFundsInterval.value = setInterval(async () => {
      if (!isApiCallInProgress && _payment_id) {
        console.log("Current Time: ", currentTime);
        if (currentTime % 10 === 0 && currentTime < timeOut && paymentWith == "directCard") {
          notificationService.showError("Loading your page. Please hold on for a moment while we verify if something went wrong.");
        }
        try {
          isApiCallInProgress = true;
          const verifyAddFund: IVerifyPaymentResponse = await transactionService.verifyPayment({
            payment_id: _payment_id
          });
          isApiCallInProgress = false;
          if (verifyAddFund.p_status == "success" || verifyAddFund.p_status == "error" || currentTime >= timeOut) {
            clearAddFundInterval(addFundsInterval.value);
            if (verifyAddFund.p_status == "success") {
              notificationService.showSuccess("Balance added successfully.");
              onPaymentEnd();
            } else if (verifyAddFund.p_status == "error") {
              notificationService.showError(verifyAddFund.message ?? "Error while adding funds");
            }
            console.log("timeout or payment done");
            loadingService.stopLoading("main-loader:get-transactions-add-funds");
          }
          currentTime = currentTime + timeInterval;
        } catch (error: any) {
          loadingService.stopLoading("main-loader:get-transactions-add-funds");
          onPaymentEnd();
          if (error.response.data.error?.amount) {
            throw new Error(error.response.data.error?.amount[0]);
          } else {
            throw new Error(error.response.data.error);
          }
        }

      }
    }, timeInterval * 1000);

  } catch (error: any) {
    console.log("Error ", error);
    loadingService.stopLoading("main-loader:get-transactions-add-funds");
    onPaymentEnd();
    if (error.response.data.error?.amount) {
      throw new Error(error.response.data.error?.amount[0]);
    } else {
      throw new Error(error.response.data.error);
    }
  }
};
const onPaymentEnd = () => {
  onCloseAddFunds();
  emit("refresh");
  setTimeout(() => {
    $store.dispatch("companyStore/syncCompanyData");
  }, 1000);
};
const onClickGoToPayments = async () => {
  await $v.value.$validate();
  const regex = /^(\d{1,3}(\.\d{3})*|\d+)(,\d{2})?$/;
  if ($v.value.$error || !form.value.amount || !form.value.isReadTermsAndConditions) {
    return;
  }
  if (!regex.test(form.value.amount.toString())) {
    notificationService.showError("Amount is not in correct format");
    return;
  }
  const amount = form.value.amount.toString();
  const price = PriceService.shippingPriceFormat(Number(amount.replace(/[,.]/g, (match) => {
    return match === "," ? "." : "";
  })), "en").replace(/,/g, "");

  try {
    loadingService.startLoading("main-loader:get-transactions-add-funds");
    const addFund: IAddFundResponse = await transactionService.addFund({
      amount: price
    }).catch(error => {
      if (error.response?.data?.error?.amount) {
        throw new Error(error.response.data.error?.amount[0]);
      } else {
        throw new Error(error.response?.data?.error ?? "Internet connectivity issue.");
      }
    });
    link.value = addFund.link;
    payment_id.value = addFund.payment_id;
    transaction_code.value = addFund.transaction_code;
    openMakePaymentModal.value = true;
    loadingService.stopLoading("main-loader:get-transactions-add-funds");
  } catch (e) {
    if (typeof e === "string") {
      notificationService.showError(e);
    } else if (e instanceof Error) {
      notificationService.showError(e.message);
    } else {
      notificationService.showError("Error while adding funds");
    }
    loggingService.error("Error while adding funds", e);
    loadingService.stopLoading("main-loader:get-transactions-add-funds");
  }

};
const onClickGoToPaymentsWithSavedCard = async () => {
  await $v.value.$validate();
  const regex = /^(\d{1,3}(\.\d{3})*|\d+)(,\d{2})?$/;


  if ($v.value.$error || !form.value.amount || !form.value.isReadTermsAndConditions) {
    return;
  }
  if (!regex.test(form.value.amount.toString())) {
    notificationService.showError("Amount is not in correct format");
    return;
  }
  const amount = form.value.amount.toString();
  const price = PriceService.shippingPriceFormat(Number(amount.replace(/[,.]/g, (match) => {
    return match === "," ? "." : "";
  })), "en").replace(/,/g, "");
  try {
    loadingService.startLoading("main-loader:get-transactions-add-funds");
    const addFund: IAddFundWithSavedCardResponse = await transactionService.addFundWithSavedCard({
      amount: price
    }).catch(error => {
      if (error.response?.data?.error?.amount) {
        throw new Error(error.response.data.error?.amount[0]);
      } else {
        throw new Error(error.response?.data?.error ?? "Internet connectivity issue.");
      }
    });
    await verifyAddedFunds(addFund.data.payment_id, "savedCard");
  } catch (e) {
    console.log("Error## ", e);
    if (typeof e === "string") {
      notificationService.showError(e);
    } else if (e instanceof Error) {
      notificationService.showError(e.message);
    } else {
      notificationService.showError("Error while adding funds");
    }
    loggingService.error("Error while adding funds", e);
    loadingService.stopLoading("main-loader:get-transactions-add-funds");
  }
};

function clearAddFundInterval(value: ReturnType<typeof setInterval> | null) {
  if (value) {
    clearInterval(value);
  }
}

onBeforeUnmount(() => {
  clearAddFundInterval(addFundsInterval.value);
});
</script>
<style scoped lang="scss">
a {
  color: $primary-one;
  text-decoration: underline;
}
</style>
