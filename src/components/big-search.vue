<template>
  <div class="big_search-container">
    <form style="flex: 1" @submit="(e) => e.preventDefault()">
      <shipvagoo-input
        v-model="currentSearch"
        :placeholder="placeholder"
        :debounce="1000"
        autocomplete="off"
        :loading="loading"
      >
        <template #prepend>
          <q-icon :name="`img:${SearchIcon}`" size="16px" />
        </template>
      </shipvagoo-input>
    </form>

    <q-btn
      v-if="isFilterAvailable"
      round
      color="transparent"
      size="xs"
      :class="{ 'is-open': isFilterContextOpen }"
    >
      <img src="../assets/img/configuration.svg" decoding="async" />

      <q-menu v-model="isFilterContextOpen" class="big_search-configuration_menu">
        <q-list>
          <div class="menu-label">View</div>
          <q-item v-close-popup clickable>
            <q-item-section>Extra Columns</q-item-section>
          </q-item>
          <q-item v-close-popup clickable>
            <q-item-section>Rows per page</q-item-section>
          </q-item>
          <shipvagoo-separator />
          <div class="menu-label">Filter</div>
          <q-item v-close-popup clickable>
            <q-item-section>Bin</q-item-section>
          </q-item>
          <q-item v-close-popup clickable>
            <q-item-section>Fullfillment</q-item-section>
          </q-item>
          <q-item v-close-popup clickable>
            <q-item-section>Invoice</q-item-section>
          </q-item>
          <q-item v-close-popup clickable>
            <q-item-section>Item Quality</q-item-section>
          </q-item>
          <q-item v-close-popup clickable>
            <q-item-section>Note</q-item-section>
          </q-item>
        </q-list>
      </q-menu>
    </q-btn>
  </div>
</template>

<script setup lang="ts">
  import { computed, Ref, ref, WritableComputedRef } from 'vue';
  import SearchIcon from '../assets/img/search-white.svg';
  import { useI18n } from 'vue-i18n';

  interface Props {
    modelValue: string;
    isFilterAvailable?: boolean;
    placeholder?: string;
    loading?: boolean;
  }

  const props: Readonly<Props> = withDefaults(defineProps<Props>(), {
    isFilterAvailable: false,
    placeholder: () => {
      const { t } = useI18n();
      return t('common.search');
    },
    loading: false,
  });
  const isFilterContextOpen: Ref<boolean> = ref<boolean>(false);
  const emit = defineEmits(['update:modelValue']);
  //const interval = ref<any>(null);
  const currentSearch: WritableComputedRef<string> = computed({
    get: () => props.modelValue,
    set: (value: string) => {
      /* interval.value = setTimeout(() => {
        emit('update:modelValue', value);
      }, 1000);
      clearInterval(interval); */
      emit('update:modelValue', value);
    },
  });
</script>

<style lang="scss" scoped>
  .big_search {
    &-head {
      display: flex;
      align-items: center;
      margin: 15px;
    }

    &-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 40px;

      input {
        display: flex;
        flex: 1;
        width: 0;
        min-width: 0 !important;
        height: 40px;
        font-size: 12px;
        color: #ffffff;
        background-color: transparent;
        border: none;

        &:focus {
          outline: none;
        }
      }

      button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        padding: 0;
        margin-right: 13px;
        background-color: transparent;
        border: none;
        border-radius: 14px;

        /*&:hover,
        &.is-open {
          background-color: #2929c8 !important;
        }*/
      }
    }

    &-configuration_menu {
      background-color: #ffffff !important;
    }
  }
</style>

<style lang="scss">
  .big_search-configuration_menu {
    background: #151526 !important;
    border: 0;

    .q-item {
      padding: 9px 20px;
      color: #ffffff !important;

      /*&:hover {
        background-color: #2929c8 !important;
      }*/
    }

    .menu-label {
      padding: 9px 20px;
      font-size: 12px;
    }
  }
</style>
