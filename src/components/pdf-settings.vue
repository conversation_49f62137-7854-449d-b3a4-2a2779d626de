<template>
  <div
    class="bg-white return-portal-modal">
    <div class="row items-center q-px-md q-py-sm header-bg">
      <div class="text-white" style="font-size:16px;font-weight: 500;">PDF Setting</div>
      <q-space/>
      <slot name="icon-right"/>
      <q-btn v-close-popup icon="close"
             color="white" flat round dense @click="onClickClose"/>
    </div>
    <div class="slot-container">
      <div style="margin: 30px;">
        <shipvagoo-select
          v-model="shippingLabelFormat"
          label="Shipping Label Format"
          :options="formats"
        />
        <shipvagoo-select
          class="q-mt-lg"
          v-model="pickListFormat" label="Picking List Format" :options="formats"/>
      </div>
      <shipvagoo-separator color="#DDDDDD">
      </shipvagoo-separator>
      <div class="flex justify-end q-pt-md q-pr-lg q-pb-md">
        <shipvagoo-button
          class="save_button"
          label="Save"
          min-width="120px"
          @click="updatePdfSettings"
        />
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
  import {onBeforeMount} from 'vue';
  import {
    loadingService,
    loggingService,
    notificationService,
    pdfSettingsService
  } from '../services/_singletons';

  const emit = defineEmits<{
    (event: 'close'): void;
  }>();


  let shippingLabelFormat = $ref<'A4' | '10x19'>('A4');
  let pickListFormat = $ref<'A4' | '10x19'>('A4');
  const formats = ['A4', '10x19'];

  const getPdfSettings = async () => {
    try {
      loadingService.startLoading('main-loader:pdf-settings');
      const response = await pdfSettingsService.getPdfSettings();
      shippingLabelFormat = response.shipping_label_format || 'A4';
      pickListFormat = response.pick_list_format || 'A4';
    } catch (e) {
      notificationService.showError('Error while loading pdf settings');
      loggingService.error('Error while loading pdf settings', e);
    } finally {
      loadingService.stopLoading('main-loader:pdf-settings');
    }
  };

  const updatePdfSettings = async () => {
    try {
      loadingService.startLoading('main-loader:update-pdf-settings');
      await pdfSettingsService.updatePdfSettings({
        shipping_label_format: shippingLabelFormat,
        pick_list_format: pickListFormat,
      });
      notificationService.showSuccess('PDF setting saved successfully.');
      onClickClose();
    } catch (e) {
      notificationService.showError('Error while updating pdf settings');
      loggingService.error('Error while updating pdf settings', e);
    } finally {
      loadingService.stopLoading('main-loader:update-pdf-settings');
    }
  };
  const onClickClose = () => {
    emit('close');
  };
  onBeforeMount(() => {
    getPdfSettings();
  });
</script>
<style scoped lang="css">
  .return-portal-modal {
    width: 450px;
    max-width: 450px !important;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 15px;
  }

  .header-bg {
    background-color: #243E90;
  }

  .slot-container {
    max-height: calc(100vh - 48px - 67px);
    overflow: auto;
  }

  .text-brand {
    color: #243E90 !important;
  }

  .bg-brand {
    background: #243E90 !important;
  }
</style>
