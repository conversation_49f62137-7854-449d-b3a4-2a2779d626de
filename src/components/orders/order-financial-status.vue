<template>
  <shipvagoo-status-tag
    :color="statusConfig.color"
    :bg-color="statusConfig.bgColor"
    :text="statusConfig.text">
  </shipvagoo-status-tag>
</template>

<script setup lang="ts">
import { computed } from "vue";

interface StatusConfig {
  color: string;
  bgColor: string;
  text: string;
}

interface IProps {
  financialStatus: string;
}

const props = defineProps<IProps>();
const statusConfig = computed<StatusConfig>(() => {
  const status = props.financialStatus.toLowerCase();
  switch (status) {
    case "pending":
      return { color: "#7D8398", bgColor: "#E5E5E5", text: "Pending" };
    case "refunded":
      return { color: "#7D8398", bgColor: "#E5E5E5", text: "Refunded" };
    case "partially_refunded":
      return { color: "#7D8398", bgColor: "#E5E5E5", text: "Partially refunded" };
    case "paid":
      return { color: "#0C9247", bgColor: "#BEF3D7", text: "Paid" };
    case "marked_as_paid":
      return { color: "#7D8398", bgColor: "#E5E5E5", text: "Mark as paid" };
    case "unpaid":
    case "un_paid":
      return { color: "#7D8398", bgColor: "#E5E5E5", text: "Unpaid" };
    case "partially_paid":
      return { color: "#7D8398", bgColor: "#E5E5E5", text: "Partially paid" };
    case "authorized":
      return { color: "#8E7200", bgColor: "#FEECA1", text: "Authorized" };
    case "expiring":
      return { color: "#FF5F00", bgColor: "#FFD4BB", text: "Expiring" };
    case "overdue":
      return { color: "#FF5F00", bgColor: "#FFD4BB", text: "Overdue" };
    case "expired":
      return { color: "#BE4748", bgColor: "#FFD4E0", text: "Expired" };
    default:
      return { color: "#7D8398", bgColor: "#E5E5E5", text: "Unknown" };
  }
});
</script>

