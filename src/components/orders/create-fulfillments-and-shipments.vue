<template>
  <shipvagoo-modal-v1
    v-if="isModalReadyToShow"
    title="Process completed"
    size="sm"
    @close="onClose"
    class="dark-text"
  >
    <div>
      <div class="icon-box">
        <div style="width: auto">
          <div class="full-width flex justify-center">
            <img :src="heading.icon" style="width: 60px; height: 60px" />
          </div>
          <div class="text-center q-mt-md text-weight-7 text-fourteen">
            {{ heading.message }}
          </div>
        </div>
      </div>
      <shipvagoo-separator color="#DDDDDD" />
      <order-action-result-item
        style="margin-top: 10px"
        label="Fulfillments"
        :result-type="fulfillmentResultType"
        :results="fulfillments_results"
      ></order-action-result-item>
      <order-action-result-item
        style="margin-bottom: 10px"
        label="Shipments"
        :result-type="shipmentResultType"
        :results="shipments_results"
      ></order-action-result-item>
    </div>
    <shipvagoo-separator color="#DDDDDD" v-if="successCount > 0" />
    <shipvagoo-card-actions v-if="successCount > 0">
      <shipvagoo-button outline min-width="100px" @click="onClickDownload">
        Download
      </shipvagoo-button>
      <shipvagoo-button-dropdown-v1
        color="#243E90"
        bg-color="#FFFFFF"
        split
        min-width="140px"
        dropdown-icon="fas fa-caret-down"
        outline
        no-caps
        label="Print and close"
        @click="onClickPrintAndClose"
      >
        <shipvagoo-list>
          <shipvagoo-menu-item has-caret clickable>
            Print

            <shipvagoo-menu anchor="top right">
              <shipvagoo-menu-item
                v-for="printer in $store.state.printerStore.printers"
                :key="`printer-${printer.printer_code}`"
                clickable
                @click="onClickPrintLabelsAt(printer)"
              >
                {{ printer.name }}
              </shipvagoo-menu-item>
              <shipvagoo-menu-item v-if="!$store.state.printerStore.printers.length">
                {{ $t('common.no_printer_is_set') }}
              </shipvagoo-menu-item>
            </shipvagoo-menu>
          </shipvagoo-menu-item>
        </shipvagoo-list>
      </shipvagoo-button-dropdown-v1>
    </shipvagoo-card-actions>
  </shipvagoo-modal-v1>
  <advance-dialog-loader v-else :progress="progress">
    <template #label> {{ orders.length }} fulfillments and shipments are being created </template>
  </advance-dialog-loader>
</template>

<script setup lang="ts">
  // This component should be used with v-if directive
  import { computed, onBeforeMount, ref } from 'vue';
  import _ from 'lodash-es';
  import { PDFDocument } from 'pdf-lib';
  import { IOrder, IOrderMultiActionResult } from '../../model/order.model';
  import { IPrinter } from '../../model/printer.model';
  import {
    loadingService,
    loggingService,
    notificationService,
    orderService,
    printJobService,
    shipmentService,
    carrierService,
  } from '../../services/_singletons';
  import { useStore } from '../../store';
  import WindowService from '../../services/window.service';
  import HelperService from '../../services/helper.service';
  import OrderService from '../../services/order.service';
  import { IFulfillment, IFulfillmentRequestSingle } from '../../model/fulfillment.model';
  import { IShipment } from '../../model/shipment.model';
  import { useHelper } from '../../composeable/Helper';
  import SuccessIcon from '../../assets/img/success_rounded.svg';
  import DangerIcon from '../../assets/img/danger.svg';
  import WarningIcon from '../../assets/img/warning.svg';

  const { handleBeforeUnload, validateReceiverMobile } = useHelper();
  const $store = useStore();

  interface Props {
    orders: IOrder[];
  }

  const props = defineProps<Props>();

  const fulfillments_results = ref<IOrderMultiActionResult[]>([]);
  const shipments_results = ref<IOrderMultiActionResult[]>([]);
  const progress = ref<number>(0);
  const apiCallsCount = ref<number>(0);
  const isModalReadyToShow = ref<boolean>(false);
  const initialOrderCount = ref<number>(0);
  const successOrders = ref<IOrder[]>([]);
  const successCount = computed(() => successOrders.value.length);
  const emit = defineEmits(['close']);
  //const totalShipmentCost = ref<number>(0);
  const createdShipmentCodes = ref(new Set<number>());
  let windowRef: Window | null = null;

  const resultType = (results: IOrderMultiActionResult[]) => {
    const resultsLength = results.length;
    if (results.filter((item) => !item.success).length == resultsLength) {
      return 'danger';
    } else if (results.filter((item) => item.success).length == resultsLength) {
      return 'success';
    } else {
      return 'warning';
    }
  };
  const fulfillmentResultType = computed(() => {
    return resultType(fulfillments_results.value);
  });
  const shipmentResultType = computed(() => {
    return resultType(shipments_results.value);
  });

  const heading = computed(() => {
    if (fulfillmentResultType.value == 'success' && shipmentResultType.value == 'success') {
      return {
        icon: SuccessIcon,
        message: 'Actions have been completed',
      };
    } else if (fulfillmentResultType.value == 'danger' && shipmentResultType.value == 'danger') {
      return {
        icon: DangerIcon,
        message: 'Actions have not been completed',
      };
    } else {
      return {
        icon: WarningIcon,
        message: 'Actions are incomplete',
      };
    }
  });
  const setFulfillmentResult = (
    success: boolean,
    order_number: number | string,
    already: boolean = false,
  ) => {
    fulfillments_results.value.push({
      success: success,
      already: already,
      description: `Order ${order_number}`,
    });
  };
  const setShipmentResult = (
    success: boolean,
    order_number: number | string,
    shipment_id: number | null = null,
    already: boolean = false,
  ) => {
    const index = shipments_results.value.findIndex((item) => item.order_number == order_number);
    const result = {
      success,
      description: `Order ${order_number} ${shipment_id ? '- Shipment ID ' + shipment_id : ''}`,
      order_number,
      already,
    };
    if (index > -1) {
      shipments_results.value[index] = result;
    } else {
      shipments_results.value.push(result);
    }
  };

  const setSliderProgress = () => {
    apiCallsCount.value = apiCallsCount.value + 1;
    progress.value = Math.floor((apiCallsCount.value / (5 * props.orders.length)) * 100);
  };

  const createFulfillmentsAndShipments = async (order: IOrder) => {
    try {
      /* if (!order.delivery_template_code) {
       notificationService.showError(`Delivery method is not set for order ${order.order_number}`);
       return;
     }*/

      const fulfilledItems = OrderService.getFulfilledItems(order.fulfillments, order.line_items);
      const unfulfilledItems = OrderService.getUnfulfilledItems(
        order.fulfillments,
        order.line_items,
      );
      /*  if (fulfilledItems.length > 0 && unFulfilledItems.length === 0) {
        notificationService.showError(`Order ${order.order_number} has already been fulfilled.`);
        setFulfillmentResult(false, order.order_number);
        setShipmentResult(false, order.order_number);
        return;
      } */

      //loadingService.startLoading('main-loader:create-fulfillments-and-shipments');
      const initialFulfillments = order.fulfillments || [];

      let newFulfillments: IFulfillment[] = [];
      let isFulfillmentCreated = true;
      // Response is order object
      if (fulfilledItems.length == 0 || unfulfilledItems.length > 0) {
        /** means not fulfilled yet **/

        const response = await orderService
          .createFulfillment(
            order.order_code,
            null,
            unfulfilledItems.map(
              (item): IFulfillmentRequestSingle => ({
                line_item_code: item.line_item_code,
                quantity: item.quantity,
              }),
            ),
          )
          .then((res) => {
            setFulfillmentResult(true, order.name ?? order.order_number);
            return res;
          })
          .catch((error) => {
            setFulfillmentResult(false, order.name ?? order.order_number);
            const errorMessage = error.response?.data?.error;
            notificationService.showError(errorMessage);
            isFulfillmentCreated = false;
            // throw new Error(error.response.data?.error);
          })
          .finally(() => {
            setSliderProgress();
          });
        console.log('called');
        if (isFulfillmentCreated) {
          newFulfillments = (response?.fulfillments || []).filter(
            (fulfillment) =>
              !initialFulfillments.find((f) => f.fulfillment_code === fulfillment.fulfillment_code),
          );
        }
      } else {
        setFulfillmentResult(true, order.name ?? order.order_number, true);
        setSliderProgress();
        newFulfillments = initialFulfillments;
      }

      if (isFulfillmentCreated) {
        const newFulfillmentsWithoutShipmentCode =
          newFulfillments.filter((fulfillment) => !fulfillment.shipment_code) || [];

        const newFulfillmentCodes = newFulfillmentsWithoutShipmentCode.map(
          (fulfillment) => fulfillment.fulfillment_code,
        );

        let uniqueFulfillmentCodes = _.uniq(newFulfillmentCodes);
        //const createShipmentPromises: Promise<IOrder>[] = [];
        const phoneValidationFailedFulfillment: number[] = [];
        let isShipmentCreatedSuccessfully = false;
        let labelAlreadyGenerated = false;

        /** IF: shipment is not created yet **/
        if (newFulfillmentsWithoutShipmentCode.length > 0) {
          const createShipmentTasks: (() => Promise<IOrder>)[] = [];

          uniqueFulfillmentCodes.forEach((fulfillmentCode) => {
            /** this for phone validation matrix **/
            const shippingProduct = carrierService.getShippingProductFromOrder(
              order,
              $store.state.courierStore.carrierProducts,
            );
            const responseMessage = validateReceiverMobile(
              shippingProduct?.product_id,
              order.shipping_address.phone,
              order.order_number,
            );
            if (responseMessage) {
              notificationService.showError(responseMessage);
              setShipmentResult(false, order.name ?? order.order_number);
              phoneValidationFailedFulfillment.push(fulfillmentCode);
            } else {
              // Store function instead of executing promise immediately
              createShipmentTasks.push(() =>
                orderService
                  .createShipmentFromFulfillment(order.order_code, fulfillmentCode)
                  .then((result) => {
                    setShipmentResult(true, order.name ?? order.order_number);
                    isShipmentCreatedSuccessfully = true;
                    return result;
                  })
                  .catch((error) => {
                    if (error.response.data?.error) {
                      notificationService.showError(error.response.data?.error);
                    }
                    setShipmentResult(false, order.name ?? order.order_number);
                    throw new Error(error.response.data?.error);
                  }),
              );
            }
          });

          // Process shipments sequentially
          for (const createShipment of createShipmentTasks) {
            try {
              await createShipment();
            } catch (error) {
              // Error already handled in the catch block above
              console.error('Shipment creation failed:', error);
            }
          }

          setSliderProgress();
        } else {
          setShipmentResult(true, order.name ?? order.order_number);
          isShipmentCreatedSuccessfully = true;
          setSliderProgress();
          const shippedFulfillments =
            initialFulfillments.filter((fulfillment) => fulfillment.shipment) || [];
          const newFulfillmentCodes = shippedFulfillments.map(
            (fulfillment) => fulfillment.fulfillment_code,
          );
          const labeledShipments =
            shippedFulfillments.filter(
              (fulfillment) => fulfillment.shipment && fulfillment.shipment.label,
            ) || [];

          uniqueFulfillmentCodes = _.uniq(newFulfillmentCodes);
          if (labeledShipments.length > 0) {
            //means label is generated already
            setShipmentResult(
              true,
              order.name ?? order.order_number,
              labeledShipments[0].shipment_code,
              true,
            );
            labelAlreadyGenerated = true;
            setSliderProgress();
          }
        }

        const createLabelTasks: (() => Promise<unknown>)[] = [];

        if (isShipmentCreatedSuccessfully && !labelAlreadyGenerated) {
          //means: new shipment created, not already completed
          uniqueFulfillmentCodes.forEach((fulfillmentCode) => {
            if (!phoneValidationFailedFulfillment.includes(fulfillmentCode)) {
              // Store function instead of executing promise immediately
              createLabelTasks.push(() =>
                generateLabelForFulfillmentsShipment(fulfillmentCode, order.order_number)
                  .then((response: any) => {
                    const shipmentCode = response.fulfillments[0].shipment.shipment_code;
                    setShipmentResult(true, order.name ?? order.order_number, shipmentCode);
                  })
                  .catch((error) => {
                    setShipmentResult(false, order.name ?? order.order_number);
                    if (error.message) {
                      throw new Error(error.message);
                    } else {
                      throw new Error(`Failed to generate label for order ${order.order_number}`);
                    }
                  }),
              );
            }
          });

          // Process labels sequentially
          for (const createLabel of createLabelTasks) {
            try {
              await createLabel();
            } catch (error) {
              // Error already handled in the catch block above
              console.error('Label generation failed:', error);
            }
          }

          setSliderProgress();
        }

        const updatedOrder = await orderService.getOrder(order.order_code).finally(() => {
          setSliderProgress();
        });

        const newFulfillmentsWithShipmentCode = _.uniqBy(
          updatedOrder.fulfillments?.filter(
            (fulfillment) =>
              uniqueFulfillmentCodes.includes(fulfillment.fulfillment_code) &&
              fulfillment.shipment_code,
          ) || [],
          'fulfillment_code',
        );

        newFulfillmentsWithShipmentCode.forEach((fulfillment) => {
          createdShipmentCodes.value.add(fulfillment.shipment_code as number);
        });

        /*const calculateShipmentPricesPromises: Promise<ICalculateChargeResponse>[] = [];

       const shipmentCharges = await Promise.all(calculateShipmentPricesPromises);

      totalShipmentCost.value += shipmentCharges.reduce(
        (acc: number, charge) => acc + charge.totals.after_vat.amount,
        0,
      ); */

        successOrders.value.push(updatedOrder);
      } else {
        setShipmentResult(false, order.name ?? order.order_number);
        setSliderProgress();
      }
    } catch (e) {
      if (typeof e === 'string') {
        notificationService.showError(e);
      } else if (e instanceof Error) {
        notificationService.showError(e.message);
      } else {
        notificationService.showError('Error while creating fulfillments and shipments');
      }
      loggingService.error('Error while creating fulfillments and shipments', e);
      //notificationService.showError('Error while creating fulfillments and shipments');
    } finally {
      loadingService.stopLoading('main-loader:create-fulfillments-and-shipments');
    }
  };

  const generateLabelForFulfillmentsShipment = async (
    fulfillmentCode: number,
    order_number: number,
  ): Promise<any> => {
    try {
      return await orderService.generateLabelForFulfillmentsShipment(fulfillmentCode);
    } catch (e: any) {
      if (e.response.data?.error) {
        if (e.response.data?.error != 'partial_shipping_rule_validation_error') {
          throw new Error(e.response.data?.error);
        } else {
          throw new Error(`Error while generating label for order number ${order_number}`);
        }
      } else {
        throw new Error(`Error while generating label for order number ${order_number}`);
      }
    }
  };
  const attemptToCreateFulfillmentsAndShipments = async () => {
    try {
      window.addEventListener('beforeunload', handleBeforeUnload);
      //loadingService.startLoading('main-loader:create-fulfillment-shipment');
      const createFulfillmentsAndShipmentsToBeQueued: Promise<unknown>[] = [];
      for (const order of props.orders) {
        createFulfillmentsAndShipmentsToBeQueued.push(
          (await createFulfillmentsAndShipments(order)) as any,
        );
      }
      await Promise.allSettled(createFulfillmentsAndShipmentsToBeQueued);

      initialOrderCount.value = props.orders.length;
      isModalReadyToShow.value = true;
      // await $store.dispatch('companyStore/syncCompanyData');
      // await $store.dispatch('orderStore/refreshOrders');
    } catch (error) {
      loggingService.error('Error while creating picking lists', error);
      notificationService.showError('Error while creating picking lists');
    } finally {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      loadingService.stopLoading('main-loader:create-fulfillment-shipment');
    }
  };

  const onClickPrintLabelsAt = async (printer: IPrinter) => {
    try {
      loadingService.startLoading('main-loader:print-label-list');
      const printJobsToBeQueued: Promise<unknown>[] = [];
      const skippedShipments: number[] = [];

      const getShipmentsPromises: Promise<IShipment>[] = [];

      createdShipmentCodes.value.forEach((shipmentCode) => {
        getShipmentsPromises.push(shipmentService.getShipment(shipmentCode));
      });

      const shipments = await Promise.all(getShipmentsPromises);

      shipments.forEach((shipment) => {
        let label: string | null = null;

        if (printer.format === 'A4') {
          label = shipment.label;
        }

        if (printer.format === '10x19') {
          label = shipment.label_a6;
        }

        if (printer.format === 'ZPL') {
          label = shipment.label_zpl;
        }

        if (!label) {
          skippedShipments.push(shipment.shipment_code);
          console.warn(
            `No label found for shipment #${shipment.shipment_code} with format ${printer.format}`,
          );
          return;
        }

        printJobsToBeQueued.push(
          printJobService.queuePrintJob({
            company_code: $store.state.companyStore.company_code,
            printer_app_code: printer.printer_app_code,
            printer: printer.linked_printer,
            format: printer.format,
            document_base64: label,
            reference: `Shipment #${shipment.shipment_code}`,
          }),
        );
      });

      await Promise.all(printJobsToBeQueued);

      if (printJobsToBeQueued.length > 0) {
        if (skippedShipments.length > 0) {
          notificationService.showSuccess(
            `${printJobsToBeQueued.length} shipping labels queued for printing. `,
          );
        } else {
          notificationService.showSuccess('Shipping labels are queued for printing successfully');
        }
      } else {
        notificationService.showError('No labels available for printing');
      }
    } catch (error) {
      console.log('ERROR: ', error);
      loggingService.error('Error while printing shipping labels', error);
      notificationService.showError('Error while printing shipping labels');
    } finally {
      loadingService.stopLoading('main-loader:print-label-list');
    }
  };

  const onClose = () => {
    isModalReadyToShow.value = false;
    initialOrderCount.value = 0;
    successOrders.value = [];
    emit('close');
  };

  const addPage = async (doc: PDFDocument, label: string) => {
    const pdf = await PDFDocument.load(label);
    const pages = await doc.copyPages(pdf, pdf.getPageIndices());
    pages.forEach((page) => {
      doc.addPage(page);
    });
  };

  const fetchLabelsFromShipments = async (): Promise<string[]> => {
    const shipmentCodes: string[] = Array.from(createdShipmentCodes.value).map((code) =>
      code.toString(),
    );
    const getShipmentsPromises = shipmentCodes.map((shipmentCode) =>
      shipmentService.getShipment(shipmentCode),
    );

    const shipments = await Promise.all(getShipmentsPromises);

    return shipments
      .filter((shipment) => shipment.label != null)
      .map((shipment) => shipment.label as string);
  };

  const onDownload = async () => {
    const doc = await PDFDocument.create();
    const labels = await fetchLabelsFromShipments();
    /* const getShipmentsPromises: Promise<IShipment>[] = [];

    createdShipmentCodes.value.forEach((shipmentCode) => {
      getShipmentsPromises.push(shipmentService.getShipment(shipmentCode));
    });

    const shipments = await Promise.all(getShipmentsPromises);

    const labels = shipments
      .filter((shipment) => shipment.label != null)
      .map((shipment) => shipment.label as string); */
    if (!labels.length) {
      notificationService.showError('No label is found for downloading.');
      return;
    }
    const addPagesPromises: Promise<void>[] = [];

    labels.forEach((label) => {
      addPagesPromises.push(addPage(doc, label));
    });

    await Promise.all(addPagesPromises);

    const base64 = await doc.saveAsBase64({ dataUri: true });
    windowRef = WindowService.openWindow('Shipping Label');
    if (!windowRef) {
      throw new Error('Window reference is not available');
    }
    windowRef.location = URL.createObjectURL(await HelperService.toBlob(base64));
  };

  const onClickDownload = () => {
    onDownload();
  };

  const onClickPrintAndClose = async () => {
    if (!$store.state.printerStore.defaultPrinterForLabel) {
      notificationService.showError('Default printer for labels is not set');
      return;
    }
    const labels = await fetchLabelsFromShipments();
    if (!labels.length) {
      notificationService.showError('No label is found for printing.');
      return;
    }
    onClickPrintLabelsAt($store.state.printerStore.defaultPrinterForLabel);
    onClose();
  };

  onBeforeMount(() => {
    attemptToCreateFulfillmentsAndShipments();
  });
</script>

<style scoped lang="scss">
  .icon-box {
    width: 100%;
    padding: 25px 0 25px 0;
  }
</style>
