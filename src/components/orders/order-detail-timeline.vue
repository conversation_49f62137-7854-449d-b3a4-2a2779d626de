<template>
  <div class="order-detail-box-card">
    <div class="full-width q-py-sm q-px-md flex justify-between">
      <div class="text-thirteen text-weight-7">Timeline</div>
    </div>
    <shipvagoo-separator />
    <div class="q-ma-md">
      <shipvagoo-input-v1
        v-model="eventComment"
        @keyup.enter="onAddComment"
        label="Comment"
        placeholder="Write your comment here"
      >
        <template #append>
          <q-icon
            @click="onAddComment"
            name="turn_right"
            class="q-mt-lg"
            right
            style=" color: #7d8398 !important;transform: rotate(-180deg)"
          />
        </template>
      </shipvagoo-input-v1>
      <q-timeline class="shipvagoo-ui-timeline" layout="comfortable" color="brand">
        <q-timeline-entry
          v-for="event in events"
          :key="`${event.event_code}-${event.created_at}`"
          side="left"
        >
          <template #title>
            <div class="dark-text text-twelve" style="margin-left: -4px !important">
              <div v-if="typeof event === 'object'" v-html="event.message" />
              <div v-else-if="typeof event == 'string'" style="font-size: 12px">{{ event }}</div>
            </div>
          </template>
          <template #subtitle>
            <div
              class="dark-text text-twelve text-weight-regular q-mr-sm"
              style="letter-spacing: 0"
            >
              <div>
                {{ dayjs(event.created_at).format('DD/MM/YYYY') }}
              </div>
              <div>
                {{ dayjs(event.created_at).format('HH:mm:ss') }}
              </div>
            </div>
          </template>
        </q-timeline-entry>
      </q-timeline>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import dayjs from 'dayjs';
  import { IOrder } from '../../model/order.model';
  import {
    loadingService,
    loggingService,
    notificationService,
    orderService,
  } from '../../services/_singletons';
  import * as _ from 'lodash-es';

  interface IProps {
    order: IOrder;
  }

  const props = defineProps<IProps>();
  const emit = defineEmits(['refresh-order']);
  const eventComment = ref('');

  const events = computed(() => {
    if (!props.order.events) {
      return [];
    }

    // eslint-disable-next-line @typescript-eslint/naming-convention, no-underscore-dangle
    let _events = props.order.events.map((event) => {
      return {
        ...event,
        date: dayjs(event.created_at).format('DD/MM/YYYY HH:mm:ss'),
      };
    });

    _events = _.orderBy(_events, ['created_at', 'id'], ['desc', 'desc']);

    _events = _events.map((event) => {
      const el = document.createElement('div');
      el.innerHTML = event.message;

      const links = el.querySelectorAll('a');

      links.forEach((link) => {
        link.setAttribute('target', '_blank');
      });

      return { ...event, message: el.innerHTML };
    });

    return _events;
  });
  const onAddComment = async () => {
    if (!eventComment.value) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:add-comment');
      await orderService.addComment(props.order.order_code, eventComment.value);
      eventComment.value = '';
      emit('refresh-order');
    } catch (e) {
      notificationService.showError('Error while adding comment');
      loggingService.error('Error while adding comment', e);
    } finally {
      loadingService.stopLoading('main-loader:add-comment');
    }
  };
</script>

<style lang="scss">
  .shipvagoo-ui-timeline {
    padding: 0 15px;

    .q-timeline__subtitle {
      opacity: 1 !important;
    }

    .q-timeline__dot::before {
      width: 10px !important;
      height: 10px !important;
      background: transparent !important;
      border: 1px solid !important;
    }

    .q-timeline__dot::after {
      top: 14px !important;
      bottom: -4px !important;
      left: 4.3px !important;
      background: #243e90 !important;
      opacity: 1 !important;
    }

    .q-timeline__dot:after {
      width: 1px !important;
    }

    .q-timeline__entry {
      display: block !important;
    }
  }
</style>
