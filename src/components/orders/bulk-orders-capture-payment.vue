<template>
  <shipvagoo-modal-v1
    v-if="isModalReadyToShow"
    title="Process completed"
    size="sm"
    @close="onClose"
    class="dark-text"
  >
    <div>
      <div class="icon-box">
        <div style="width: auto">
          <div class="full-width flex justify-center">
            <img :src="heading.icon" style="width: 60px; height: 60px" />
          </div>
          <div class="text-center q-mt-md text-weight-7 text-fourteen">
            {{ heading.message }}
          </div>
        </div>
      </div>
      <shipvagoo-separator color="#DDDDDD" />
      <order-action-result-item
        style="margin-top: 10px"
        class="q-mb-md"
        label="Captured payments"
        :result-type="capturedResultType"
        :results="captured_results"
      ></order-action-result-item>
    </div>
    <shipvagoo-separator color="#DDDDDD" v-if="successCount > 0" />
    <shipvagoo-card-actions v-if="successCount > 0">
      <shipvagoo-button outline min-width="100px" @click="onClose"> Close </shipvagoo-button>
    </shipvagoo-card-actions>
  </shipvagoo-modal-v1>
  <advance-dialog-loader v-else :progress="progress">
    <template #label> {{ orders.length }} payments are being captured </template>
  </advance-dialog-loader>
</template>

<script setup lang="ts">
  import { computed, onBeforeMount, ref } from 'vue';
  import _ from 'lodash-es';
  import { IOrder, IOrderMultiActionResult, IOrderTransaction } from '../../model/order.model';
  import {
    loadingService,
    loggingService,
    notificationService,
    orderService,
  } from '../../services/_singletons';

  import OrderService from '../../services/order.service';

  import { useHelper } from '../../composeable/Helper';
  import SuccessIcon from '../../assets/img/success_rounded.svg';
  import DangerIcon from '../../assets/img/danger.svg';
  import WarningIcon from '../../assets/img/warning.svg';

  const { handleBeforeUnload } = useHelper();

  interface Props {
    orders: IOrder[];
  }

  const props = defineProps<Props>();

  const captured_results = ref<IOrderMultiActionResult[]>([]);

  const progress = ref<number>(0);
  const apiCallsCount = ref<number>(0);
  const isModalReadyToShow = ref<boolean>(false);
  const initialOrderCount = ref<number>(0);
  const successOrders = ref<IOrder[]>([]);
  const successCount = computed(() => successOrders.value.length);
  const emit = defineEmits(['close']);

  const resultType = (results: IOrderMultiActionResult[]) => {
    const resultsLength = results.length;
    if (results.filter((item) => !item.success).length == resultsLength) {
      return 'danger';
    } else if (results.filter((item) => item.success).length == resultsLength) {
      return 'success';
    } else {
      return 'warning';
    }
  };
  const capturedResultType = computed(() => {
    return resultType(captured_results.value);
  });

  const heading = computed(() => {
    if (capturedResultType.value == 'success') {
      return {
        icon: SuccessIcon,
        message: 'Actions have been completed',
      };
    } else if (capturedResultType.value == 'danger') {
      return {
        icon: DangerIcon,
        message: 'Actions have not been completed',
      };
    } else {
      return {
        icon: WarningIcon,
        message: 'Actions are incomplete',
      };
    }
  });
  const setPaymentCapturedResults = (
    success: boolean,
    order_number: number | string,
    already: boolean = false,
  ) => {
    captured_results.value.push({
      success: success,
      already: already,
      description: `Order ${order_number}`,
    });
  };

  const setSliderProgress = () => {
    apiCallsCount.value = apiCallsCount.value + 1;
    progress.value = Math.floor((apiCallsCount.value / props.orders.length) * 100);
  };

  const authorizedAmount = (order: IOrder) => {
    const amount =
      order.transactions?.reduce((acc: number, transaction: IOrderTransaction) => {
        if (
          transaction.kind.toLowerCase() === 'authorization' &&
          transaction.status.toLowerCase() == 'success'
        ) {
          return acc + Number(transaction.amount);
        }
        return acc;
      }, 0) ?? 0;
    return amount;
  };

  const captureAmount = (order: IOrder) => {
    const amount =
      order.transactions?.reduce((acc: number, transaction: IOrderTransaction) => {
        if (
          transaction.kind.toLowerCase() === 'capture' &&
          transaction.status.toLowerCase() == 'success'
        ) {
          return acc + Number(transaction.amount);
        }
        return acc;
      }, 0) ?? 0;
    return amount;
  };
  const orderRemainingBalance = (order: IOrder) => {
    const price = OrderService.getPrice(order);
    const currency =
      order.transactions?.find(
        (transaction) =>
          transaction.kind.toLowerCase() == 'capture' ||
          transaction.kind.toLowerCase() == 'authorization',
      )?.currency ?? price.currency;
    return {
      amount: authorizedAmount(order) - captureAmount(order),
      currency: currency,
    };
  };


  const capturePayment = async (order: IOrder) => {
    try {
      const balance = orderRemainingBalance(order);
      const response = await orderService.capturePayment(order.order_code, {
        amount: balance.amount,
        currency_code: balance.currency,
      });
      if (response.data == 'SUCCESS') {
        setPaymentCapturedResults(true, order.name ?? order.order_number);
      } else {
        setPaymentCapturedResults(false, order.name ?? order.order_number);
      }
    } catch (e: any) {
      setPaymentCapturedResults(false, order.name ?? order.order_number);
      if (typeof e === 'string') {
        notificationService.showError(e);
      } else if (e.response?.data?.error) {
        notificationService.showError(
          `Order ${order.name}: ${e?.response?.data?.error ?? 'Error while capturing payments'}`,
        );
      } else if (e instanceof Error) {
        notificationService.showError(e.message);
      }
      loggingService.error('Error while capturing payments', e);
    } finally {
      loadingService.stopLoading('main-loader:create-fulfillments-and-shipments');
      setSliderProgress();
    }
  };

  const attemptToCapturePayment = async () => {
    try {
      window.addEventListener('beforeunload', handleBeforeUnload);
      const capturePaymentsToBeQueued: Promise<unknown>[] = [];
      for (const order of props.orders) {
        capturePaymentsToBeQueued.push((await capturePayment(order)) as any);
      }
      await Promise.allSettled(capturePaymentsToBeQueued);
      initialOrderCount.value = props.orders.length;
      isModalReadyToShow.value = true;
    } catch (error) {
      loggingService.error('Error while creating picking lists', error);
      notificationService.showError('Error while creating picking lists');
    } finally {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      loadingService.stopLoading('main-loader:create-fulfillment-shipment');
    }
  };

  const onClose = () => {
    isModalReadyToShow.value = false;
    initialOrderCount.value = 0;
    successOrders.value = [];
    emit('close');
  };

  onBeforeMount(() => {
    attemptToCapturePayment();
  });
</script>

<style scoped lang="scss">
  .icon-box {
    width: 100%;
    padding: 25px 0 25px 0;
  }
</style>
