<template>
  <shipvagoo-modal-v1
    v-if="isModalReadyToShow"
    title="Process completed"
    size="sm"
    class="dark-text"
    @close="onClose"
  >
    <div>
      <div class="icon-box">
        <div style="width: auto">
          <div class="full-width flex justify-center">
            <img :src="headingIcon" style="width: 60px; height: 60px" />
          </div>
          <div class="text-center q-mt-md text-weight-7 text-fourteen">
            {{ message }}
          </div>
        </div>
      </div>
      <shipvagoo-separator color="#DDDDDD" />
      <order-action-result-item
        class="q-my-sm"
        label="Picking Lists"
        :result-type="resultType"
        :results="pickListOrderResults"
      ></order-action-result-item>
      <shipvagoo-separator color="#DDDDDD" v-if="successCount > 0" />
      <shipvagoo-card-actions v-if="successCount > 0">
        <shipvagoo-button outline min-width="100px" @click="onClickDownload"
          >Download
        </shipvagoo-button>
        <shipvagoo-button-dropdown-v1
          color="#243E90"
          bg-color="#FFFFFF"
          min-width="140px"
          dropdown-icon="fas fa-caret-down"
          outline
          no-caps
          split
          label="Print and close"
          @click="onClickPrintAndClose"
        >
          <shipvagoo-list>
            <shipvagoo-menu-item has-caret clickable>
              Print

              <shipvagoo-menu anchor="top right">
                <shipvagoo-menu-item
                  v-for="printer in $store.state.printerStore.printers"
                  :key="`printer-${printer.printer_code}`"
                  clickable
                  @click="onClickPrintPickListAt(printer)"
                >
                  {{ printer.name }}
                </shipvagoo-menu-item>
                <shipvagoo-menu-item v-if="!$store.state.printerStore.printers.length">
                  {{ $t('common.no_printer_is_set') }}
                </shipvagoo-menu-item>
              </shipvagoo-menu>
            </shipvagoo-menu-item>
          </shipvagoo-list>
        </shipvagoo-button-dropdown-v1>
      </shipvagoo-card-actions>
    </div>
  </shipvagoo-modal-v1>
  <advance-dialog-loader v-else :progress="progress">
    <template #label> {{ orders.length }} pick lists are being created </template>
  </advance-dialog-loader>
</template>

<script setup lang="ts">
  // This component should be used with v-if directive
  import { onBeforeMount, ref, computed } from 'vue';
  import { PDFDocument } from 'pdf-lib';
  import { IOrder, IOrderMultiActionResult } from '../../model/order.model';
  import { IPrinter } from '../../model/printer.model';
  import {
    loadingService,
    loggingService,
    notificationService,
    orderService,
    printJobService,
  } from '../../services/_singletons';
  import { useStore } from '../../store';
  import WindowService from '../../services/window.service';
  import HelperService from '../../services/helper.service';
  import { useHelper } from '../../composeable/Helper';
  import AlertIcon from '../../assets/img/triangle_red_alert.svg';
  import SuccessIcon from '../../assets/img/success_rounded.svg';
  import WarningIcon from '../../assets/img/warning.svg';

  const { handleBeforeUnload } = useHelper();

  const $store = useStore();

  interface Props {
    orders: IOrder[];
  }

  const props = defineProps<Props>();
  const progress = ref<number>(0);
  const isModalReadyToShow = ref<boolean>(false);
  const initialOrderCount = ref<number>(0);
  const successCount = ref<number>(0);
  const successIndexes = ref<number[]>([]);
  const successOrderCodes = ref<number[]>([]);
  const successOrders = ref<IOrder[]>([]);

  const emit = defineEmits(['close']);
  let windowRef: Window | null = null;

  const pickListOrderResults = ref<IOrderMultiActionResult[]>([]);

  const message = computed(() => {
    return resultType.value === 'danger'
      ? 'Actions have not been completed'
      : resultType.value === 'success'
      ? 'Actions have been completed'
      : 'Actions are incomplete';
  });

  const resultType = computed(() => {
    const resultsLength = pickListOrderResults.value.length;
    if (pickListOrderResults.value.filter((item) => !item.success).length == resultsLength) {
      return 'danger';
    } else if (pickListOrderResults.value.filter((item) => item.success).length == resultsLength) {
      return 'success';
    } else {
      return 'warning';
    }
  });

  const headingIcon = computed(() => {
    return resultType.value === 'danger'
      ? AlertIcon
      : resultType.value === 'success'
      ? SuccessIcon
      : WarningIcon;
  });
  const attemptToCreatePickLists = async () => {
    try {
      window.addEventListener('beforeunload', handleBeforeUnload);
      // loadingService.startLoading('main-loader:create-pick-lists');
      const promises: Promise<unknown>[] = [];

      if (props.orders.length < 1) {
        throw new Error('No orders to create picking lists');
      }

      initialOrderCount.value = props.orders.length;

      props.orders.forEach((order) => {
        promises.push(orderService.createPickList(order.order_code));
      });
      let currentPromise = 0;
      const result = await Promise.allSettled(
        promises.map((promise) =>
          promise.finally(() => {
            currentPromise = currentPromise + 1;
            progress.value = Math.floor(
              (currentPromise / (props.orders.length + promises.length)) * 100,
            );
          }),
        ),
      );
      successCount.value = result.filter((item) => item.status === 'fulfilled').length;
      successIndexes.value = result
        .map((item, index) => (item.status === 'fulfilled' ? index : null))
        .filter((item) => item !== null) as number[];
      successOrderCodes.value = successIndexes.value.map((index) => props.orders[index].order_code);
      const successOrdersPromises: Promise<IOrder>[] = successOrderCodes.value.map((orderCode) => {
        return orderService.getOrder(orderCode).catch(() => {
          const order = props.orders.find((item: IOrder) => item.order_code == orderCode);
          const order_number = order?.name ?? order?.order_number;
          const error = new Error('Custom Error') as any;
          error.order_number = order_number;
          throw error;
        });
      });
      const response = await Promise.all(
        successOrdersPromises.map(async (promise) => {
          return promise
            .then((res) => {
              pushResults(true, res.name ?? res.order_number);
              return promise;
            })
            .catch((error) => {
              pushResults(false, error.order_number);
              return promise;
            })
            .finally(() => {
              currentPromise = currentPromise + 1;
              progress.value = Math.floor(
                (currentPromise / (props.orders.length + promises.length)) * 100,
              );
            });
        }),
      );
      successOrders.value = response;
    } catch (error) {
      loggingService.error('Error while creating picking lists', error);
      notificationService.showError('Error while creating picking lists');
    } finally {
      isModalReadyToShow.value = true;
      window.removeEventListener('beforeunload', handleBeforeUnload);
      loadingService.stopLoading('main-loader:create-pick-lists');
    }
  };
  const pushResults = (success: boolean, order_number: number | string) => {
    pickListOrderResults.value.push({
      success: success,
      description: `Order ${order_number}`,
    });
  };
  const onClickPrintPickListAt = async (printer: IPrinter) => {
    try {
      loadingService.startLoading('main-loader:print-pick-list');
      const printJobsToBeQueued: Promise<unknown>[] = [];

      successOrders.value.forEach((order) => {
        if (!order.pick_list_base64) {
          throw new Error('Picking list is not available');
        }

        printJobsToBeQueued.push(
          printJobService.queuePrintJob({
            company_code: $store.state.companyStore.company_code,
            printer_app_code: printer.printer_app_code,
            printer: printer.linked_printer,
            format: 'A4',
            document_base64: order.pick_list_base64,
            reference: `Picking list #${order.name ?? order.order_number}`,
          }),
        );
      });

      await Promise.all(printJobsToBeQueued);
      notificationService.showSuccess('Picking lists are queued for printing successfully');
    } catch (error) {
      loggingService.error('Error while printing picking lists', error);
      notificationService.showError('Error while printing picking lists');
    } finally {
      loadingService.stopLoading('main-loader:print-pick-list');
    }
  };

  const onClose = () => {
    isModalReadyToShow.value = false;
    initialOrderCount.value = 0;
    successCount.value = 0;
    emit('close');
  };

  const onDownload = async () => {
    if (!windowRef) {
      throw new Error('Window reference is not available');
    }

    const doc = await PDFDocument.create();

    const addPages = successOrders.value.map(async (order) => {
      if (!order.pick_list_base64) {
        throw new Error('Picking list is not available');
      }

      const pdf = await PDFDocument.load(order.pick_list_base64);
      const pages = await doc.copyPages(pdf, pdf.getPageIndices());
      pages.forEach((page) => {
        // doc.addPage(page).drawText('Page 0');
        doc.addPage(page);
      });
      /*const pageLabels = doc.context.obj({
      Nums: [
        0, { S: 'r' },
        4, { S: 'D' },
        7, { S: 'D', P: PDFHexString.fromText('A-'), St: 8 },
      ]
    });

    doc.catalog.set(PDFName.of('PageLabels'), pageLabels);*/
    });

    await Promise.all(addPages);

    const url = URL.createObjectURL(
      await HelperService.toBlob(await doc.saveAsBase64({ dataUri: true })),
    );

    windowRef.location = url;
  };

  const onClickDownload = () => {
    windowRef = WindowService.openWindow('Picking Lists');

    onDownload();
  };

  const onClickPrintAndClose = () => {
    if (!$store.state.printerStore.defaultPrinterForPickDocuments) {
      notificationService.showError('Default printer for picking list is not set');
      return;
    }

    onClickPrintPickListAt($store.state.printerStore.defaultPrinterForPickDocuments);
    onClose();
  };

  onBeforeMount(() => {
    attemptToCreatePickLists();
  });
</script>

<style scoped lang="scss">
  .icon-box {
    width: 100%;
    padding: 25px 0 25px 0;
  }

  .label-heading {
    font-size: 14px;
    font-weight: 500;
    color: #313131;
  }

  .slide-fade-enter-active {
    transition: all 0.3s ease-out;
  }

  .slide-fade-enter-from,
  .slide-fade-leave-to {
    opacity: 0;
    transform: translateX(20px);
  }
</style>
