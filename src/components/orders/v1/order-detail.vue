<template>
  <shipvagoo-modal
    :header-separator="false"
    title=""
    class="bg-modal shipvagoo-order-detail"
    size="x-lg"
  >
    <template v-slot:under-header>
      <div>
        <div class="shipvagoo-order-detail-header justify-center q-pa-md q-mx-md dark-text">
          <div class="flex items-center">
            <div class="text-weight-7 text-twenty">{{ row.name ?? row.order_number }}</div>
            <div>
              <shipvagoo-status-tag
                class="q-ml-sm"
                :color="OrderService.getFulfillmentStatus(row).color"
                :bg-color="OrderService.getFulfillmentStatus(row).bgColor"
                :text="OrderService.getFulfillmentStatus(row).text"
              >
              </shipvagoo-status-tag>
              <shipvagoo-status-tag
                v-if="checkRefunded"
                class="q-ml-sm"
                text="Return in progress"
                bg-color="#D5EBFF"
                color="#0094D5"
              />

              <order-financial-status
                v-if="row.authorization_status !== 'expired'"
                class="q-ml-sm"
                :financial-status="row.financial_status.toLowerCase()"
              />
              <shipvagoo-status-tag
                v-if="isPaymentDue"
                class="q-ml-sm"
                text="Due"
                bg-color="#FFD4BB"
                color="#FF5F00"
              />
              <shipvagoo-status-tag
                v-if="row.authorization_status === 'expiring'"
                class="q-ml-sm"
                text="Expiring"
                bg-color="#FFD4BB"
                color="#FF5F00"
              />
              <shipvagoo-status-tag
                v-else-if="row.authorization_status === 'expired'"
                class="q-ml-sm"
                text="Expired"
                bg-color="#FFD4E0"
                color="#BE4748"
              />
            </div>
          </div>
          <div class="text-thirteen q-mt-sm">
            {{ dayjs(row.created_at).format('DD/MM/YYYY') }} at
            {{ dayjs(row.created_at).format('HH:mm:ss') }} from Shopify
          </div>
        </div>
      </div>
    </template>
    <div class="full-width dark-text" style="padding-bottom: 80px">
      <div class="q-pa-md shipvagoo-order-detail-body">
        <div class="shipvagoo-order-detail-basic">
          <div class="row equal-height-row">
            <div class="col-md-4 col-sm-6 col-12 q-pr-md">
              <div class="order-detail-box-card">
                <div class="full-width q-py-sm q-px-md flex justify-between">
                  <div class="text-thirteen text-weight-7">Customer</div>
                  <q-btn
                    @click="showEditCustomerPopup = true"
                    dense
                    flat
                    size="xs"
                    icon="far fa-edit"
                    :disabled="
                      OrderService.getFulfillmentStatus(row).status == 'fulfilled' ||
                      OrderService.getFulfillmentStatus(row).status == 'partially_fulfilled'
                    "
                  />
                </div>
                <shipvagoo-separator />
                <div
                  v-if="row.billing_address"
                  class="q-py-sm q-px-md text-thirteen"
                  style="line-height: 19px"
                >
                  <div>
                    <q-icon name="fa-light fa-user" class="margin-top-minus-two"></q-icon>
                    <span class="q-ml-sm"> {{ row.billing_address.name }} </span>
                  </div>
                  <div>
                    <q-icon name="fa-light fa-location-dot"></q-icon>
                    <span class="q-ml-sm">
                      {{ `${row.billing_address.address1}` }}
                      <span v-if="row.billing_address.address2">{{
                        row.billing_address.address2
                      }}</span>
                    </span>
                  </div>
                  <div class="margin-left-20px">
                    {{ row.billing_address?.country_code }}-{{ row.billing_address.zip }}
                    {{ row.billing_address.city }}
                  </div>
                  <div>
                    <q-icon name="fa-light fa-envelope"></q-icon>
                    <span class="q-ml-sm">{{ row.billing_address.email }}</span>
                  </div>
                  <div v-if="customerPhone">
                    <q-icon name="fa-light fa-mobile"></q-icon>
                    <span class="q-ml-sm">
                      {{ customerPhone }}
                    </span>
                  </div>
                </div>
              </div>
              <div class="order-detail-box-card q-mt-md">
                <div class="full-width q-py-sm q-px-md flex justify-between">
                  <div class="text-thirteen text-weight-7">Recipient</div>
                  <q-icon
                    @click="onClickEditReceiver"
                    name="far fa-edit"
                    color="dark-gray"
                    class="cursor-pointer"
                  />
                </div>
                <shipvagoo-separator />
                <div
                  class="q-py-sm q-px-md text-thirteen"
                  style="line-height: 19px"
                  v-if="row.shipping_address"
                >
                  <div>
                    <q-icon name="fa-light fa-user"></q-icon>
                    <span class="q-ml-sm">{{ row.shipping_address?.name }}</span>
                  </div>
                  <div>
                    <q-icon name="fa-light fa-location-dot"></q-icon>
                    <span class="q-ml-sm">{{ fullAddress }}</span>
                  </div>
                  <div class="margin-left-20px">
                    {{ _.get(row, 'shipping_address.country_code', '') }}-{{
                      _.get(row, 'shipping_address.zip', '')
                    }}
                    {{ _.get(row, 'shipping_address.city', '') }}
                  </div>
                  <div>
                    <q-icon name="fa-light fa-envelope"></q-icon>
                    <span class="q-ml-sm">{{ _.get(row, 'shipping_address.email', '') }} </span>
                  </div>
                  <div v-if="receiverPhone">
                    <q-icon name="fa-light fa-mobile"></q-icon>
                    <span class="q-ml-sm">{{ receiverPhone }} </span>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-4 col-sm-6 col-12 q-pr-md">
              <div class="order-detail-box-card full-height flex justify-between column">
                <div>
                  <div class="full-width q-py-xs q-px-md flex justify-between items-center">
                    <div class="text-thirteen text-weight-7">Payment</div>
                    <order-financial-status
                      v-if="row.authorization_status !== 'expired'"
                      :financial-status="row.financial_status.toLowerCase()"
                    />
                    <shipvagoo-status-tag
                      v-else-if="row.authorization_status?.toLowerCase() === 'expiring'"
                      text="Expiring"
                      bg-color="#FFD4BB"
                      color="#FF5F00"
                    />
                    <shipvagoo-status-tag
                      v-else-if="row.authorization_status === 'expired'"
                      text="Expired"
                      bg-color="#FFD4E0"
                      color="#BE4748"
                    />
                  </div>
                  <shipvagoo-separator />
                  <!-- <div class="q-py-sm q-px-md  text-thirteen" v-if="row.subtotal_price_set.presentment_money &&
                    row.subtotal_price_set.presentment_money.currency_code != 'DKK'
                  ">
                    Price is converted based on Shopify exchange rate.
                  </div> -->
                  <div class="flex justify-between q-px-md q-mt-sm">
                    <div>
                      <span class="text-thirteen text-weight-7">Subtotal</span>
                      <span class="q-ml-sm text-eleven">({{ getItemsQuantity }} items)</span>
                    </div>
                    <div class="text-fourteen">
                      {{ PriceService.formatInternationalPrice(subTotal) }}
                    </div>
                  </div>
                  <template v-if="discountData.length">
                    <div
                      class="flex justify-between q-px-md q-mt-sm"
                      v-for="(disc, index) in discountData"
                    >
                      <div>
                        <span class="text-thirteen text-weight-7" v-if="index == 0">Discount</span>
                        <span v-else style="padding-left: 56px"></span>
                        <span class="q-ml-sm text-eleven">{{ disc.title }}</span>
                      </div>
                      <div class="text-fourteen">
                        -{{
                          PriceService.formatInternationalPrice({
                            amount: disc.amount,
                            currency: disc.currency,
                          })
                        }}
                      </div>
                    </div>
                  </template>
                  <div class="flex justify-between q-px-md q-mt-sm">
                    <div class="flex items-center">
                      <span class="text-weight-7 text-thirteen">Shipping</span>
                      <span class="q-ml-sm text-eleven"
                        ><!--({{ row.shipping_lines?.[0]?.title?.length > 30 ?
                        `${row.shipping_lines?.[0]?.title?.substring(0, 27)}...` : row.shipping_lines?.[0]?.title
                        }})
                        <q-tooltip>{{ row.shipping_lines?.[0]?.title }}</q-tooltip>-->
                        <shipvagoo-tooltip
                          :tooltip-text="row.shipping_lines?.[0]?.title"
                          font-size="11px"
                        >
                          ({{
                            row.shipping_lines?.[0]?.title?.length > 30
                              ? `${row.shipping_lines?.[0]?.title?.substring(0, 27)}...`
                              : row.shipping_lines?.[0]?.title
                          }})
                        </shipvagoo-tooltip>
                      </span>
                    </div>
                    <div class="text-fourteen">
                      {{ PriceService.formatInternationalPrice(shippingPrice) }}
                    </div>
                  </div>
                  <div class="flex justify-between q-px-md q-mt-sm">
                    <div class="text-thirteen">
                      <span class="text-weight-7">Tax </span>
                      <small class="q-ml-sm"
                        ><span v-if="row.tax_lines?.length">({{ row.tax_lines?.[0]?.title }})</span>
                        {{ row.taxes_included ? '(included)' : '(excluded)' }}</small
                      >
                    </div>
                    <div class="text-fourteen">
                      {{ PriceService.formatInternationalPrice(vatAmount) }}
                    </div>
                  </div>

                  <div class="flex justify-between q-px-md q-mt-sm text-thirteen">
                    <div class="text-weight-7">Total</div>
                    <div class="text-brand text-fourteen">
                      {{ PriceService.formatInternationalPrice(price) }}
                    </div>
                  </div>
                  <div
                    v-if="
                      row.financial_status.toLowerCase() === 'refunded' ||
                      row.financial_status.toLowerCase() === 'partially_refunded'
                    "
                    class="flex justify-between q-px-md q-mt-sm text-thirteen"
                  >
                    <div class="text-weight-7">Refunded</div>
                    <div class="text-brand text-fourteen">
                      {{
                        ' - ' +
                        PriceService.formatInternationalPrice({
                          currency: localCurrency,
                          amount: refundAmount ?? 0.0,
                        })
                      }}
                    </div>
                  </div>
                  <div
                    v-if="
                      row.financial_status.toLowerCase() === 'refunded' ||
                      row.financial_status.toLowerCase() === 'partially_refunded'
                    "
                    class="flex justify-between q-px-md q-mt-sm text-thirteen"
                  >
                    <div class="text-weight-7">Net amount</div>
                    <div class="text-brand text-fourteen">
                      {{
                        PriceService.formatInternationalPrice({
                          currency: localCurrency,
                          amount: price.amount - refundAmount,
                        })
                      }}
                    </div>
                  </div>
                  <!-- <div class="flex justify-between q-px-md q-mt-sm text-thirteen">
                    <div class="text-weight-7">Total (store currency)</div>
                    <div class="text-brand text-fourteen">
                      {{ PriceService.formatInternationalPrice(totalShopMoney) }}
                    </div>
                  </div> -->
                  <div class="q-py-md">
                    <shipvagoo-separator />
                    <div
                      class="flex justify-between q-px-md q-mt-sm"
                      v-if="authorizedAmount.amount > 0"
                    >
                      <div class="flex items-center">
                        <span class="text-weight-7 text-thirteen">Authorized</span>
                      </div>
                      <div class="text-fourteen">
                        {{ PriceService.formatInternationalPrice(authorizedAmount) }}
                      </div>
                    </div>
                    <div class="flex justify-between q-px-md q-mt-sm">
                      <div class="flex items-center">
                        <span class="text-weight-7 text-thirteen">Paid</span>
                      </div>
                      <div class="text-fourteen">
                        {{ PriceService.formatInternationalPrice(paidAmount) }}
                      </div>
                    </div>
                    <shipvagoo-separator class="q-mt-sm" />
                  </div>
                </div>
                <div
                  class="q-pb-md"
                  v-if="
                    row.financial_status.toLowerCase() == 'authorized' ||
                    row.financial_status.toLowerCase() == 'partially_paid' ||
                    row.financial_status.toLowerCase() == 'pending' ||
                    row.authorization_status == 'expired'
                  "
                >
                  <DashboardTaskAlert
                    v-if="row.authorization_status == 'expired'"
                    type="general"
                    :label="`<strong>${PriceService.formatInternationalPrice(
                      authorizedAmount,
                    )}</strong> of the balance is currently unauthorized`"
                    class="q-ma-sm"
                  ></DashboardTaskAlert>
                  <div class="flex justify-end q-px-md q-mt-sm text-thirteen">
                    <shipvagoo-button
                      v-if="showCapturePaymentButton && row.authorization_status != 'expired'"
                      @click="onClickCapturePayment"
                      style="min-width: 80px !important"
                      outline
                      label="Capture payment"
                    >
                    </shipvagoo-button>
                    <shipvagoo-button
                      v-else-if="showMarkAsPaidButton"
                      outline
                      style="min-width: 80px !important"
                      @click="showMarkAsPaid = true"
                      label="Mark as paid"
                    >
                    </shipvagoo-button>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-4 col-sm-6 col-12">
              <div class="order-detail-box-card">
                <div class="full-width q-py-sm q-px-md flex justify-between">
                  <div class="text-thirteen text-weight-7">Notes</div>
                  <q-icon
                    @click="editNoteModal = true"
                    name="far fa-edit"
                    color="dark-gray"
                    class="cursor-pointer"
                  />
                </div>
                <shipvagoo-separator />
                <div class="q-py-sm q-px-md text-thirteen" style="line-height: 19px">
                  <div>{{ note ?? 'No notes from customer' }}</div>
                </div>
              </div>
              <div class="order-detail-box-card q-mt-md">
                <div class="full-width q-py-sm q-px-md flex justify-between">
                  <div class="text-thirteen text-weight-7">Shipping rule</div>
                  <q-btn
                    @click="onClickEditDeliveryMethod"
                    dense
                    flat
                    size="xs"
                    icon="far fa-edit"
                    :disabled="OrderService.isOrderShipmentCreated(row)"
                  />
                </div>
                <shipvagoo-separator />
                <div class="q-py-sm q-px-md text-thirteen">
                  <DashboardTaskAlert
                    v-if="templateStatusOptions"
                    :type="templateStatusOptions.type"
                    :label="templateStatusOptions.description"
                    class="q-my-sm"
                    @resolve-now="isEditDeliveryMethodModalOpen = true"
                    @accept-now="acceptShipmentTemplate"
                    @deny-now="denyShipmentTemplate"
                    @close="onClickCloseTemplateAlert"
                  ></DashboardTaskAlert>

                  <div v-if="row.delivery_template">
                    {{ row.delivery_template?.name }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--      <div class="shipvagoo-order-detail-header flex items-center q-mx-md q-px-md">
              <div class=" text-weight-7 text-sixteen">Order {{ row.order_number }}</div>
            </div>-->
      <div class="q-pl-md">
        <div class="row">
          <div class="col-md-8 col-sm-12 q-pr-md">
            <order-unfulfilled-items
              class="q-mt-sm"
              v-if="unfulfilledItems?.length > 0"
              :unfulfilled-items="unfulfilledItems"
              :order="row"
              @refresh-order="refreshOrder"
            />
            <order-fulfillment-return
              class="q-mt-sm"
              v-for="[, fulfillment_returns] of Object.entries(fulfillmentReturns)"
              :key="`${fulfillment_returns[0].fulfillment_code}-${fulfillment_returns[0].updated_at}`"
              :fulfillment="fulfillment_returns"
              :order="row"
              :fulfillment-items="
                OrderService.getFulfilledItems(fulfillment_returns, row.line_items)
              "
              @refresh-order="refreshOrder"
            />
            <order-fulfillment
              class="q-mt-sm"
              v-if="row.fulfillments && row.fulfillments.length > 0"
              v-for="[, fulfillment] of Object.entries(fulfillments)"
              :key="`${fulfillment[0].fulfillment_code}-${fulfillment[0].updated_at}`"
              :fulfillment="fulfillment"
              :order="row"
              :fulfillment-items="OrderService.getFulfilledItems(fulfillment, row.line_items)"
              @refresh-order="refreshOrder"
            />
            <order-onhold-items
              class="q-mt-sm"
              v-for="onholdItem in onholdItems"
              :onhold-items="onholdItem"
              :order="row"
              @refresh-order="refreshOrder"
            />
            <div class="q-mt-sm">
              <shipvagoo-checkbox
                v-if="row.cancelled_fulfillments?.length"
                v-model="isShowCancelledFulfillments"
                :label="`Show cancelled fulfillments (${
                  Object.entries(cancelledFulfilledItems).length
                })`"
              />
            </div>
            <div v-if="isShowCancelledFulfillments">
              <order-fulfillment
                class="q-mt-sm"
                v-for="[, fulfillment] of Object.entries(cancelledFulfilledItems)"
                :key="`${fulfillment[0].fulfillment_code}-${fulfillment[0].updated_at}`"
                :fulfillment="fulfillment"
                :order="row"
                :fulfillment-items="OrderService.getFulfilledItems(fulfillment, row.line_items)"
                is-cancelled
              />
            </div>
          </div>
          <div class="col-md-4 col-sm-12 q-mt-sm" style="margin-left: -4px">
            <order-detail-timeline :order="row" @refreshOrder="refreshOrder" />
          </div>
        </div>
      </div>
    </div>
  </shipvagoo-modal>
  <q-dialog v-model="editNoteModal">
    <shipvagoo-modal :header-separator="false" title="Add note" size="sm">
      <div class="q-px-lg">
        <shipvagoo-separator />
        <shipvagoo-input class="q-mt-lg" label="Notes" v-model="noteInput" placeholder="Add note" />
      </div>
      <shipvagoo-card-actions>
        <shipvagoo-button min-width="80px" @click="onClickSaveNote"> Save</shipvagoo-button>
      </shipvagoo-card-actions>
    </shipvagoo-modal>
  </q-dialog>
  <q-dialog v-model="editReceiverModal" no-backdrop-dismiss @close="onCloseEditReceiverModal">
    <edit-receiver
      :order="row"
      :shipping-address="row.shipping_address"
      @close="onCloseEditReceiverModal"
      @refresh="refreshOrder"
    />
  </q-dialog>
  <q-dialog v-if="row.customer" v-model="editCustomerModal">
    <edit-billing-address
      :billing-address="row.billing_address"
      :order-code="row.order_code"
      @close="onCloseEditCustomer"
      @update="refreshOrder"
    />
  </q-dialog>
  <q-dialog v-if="row.customer" v-model="showEditCustomerPopup">
    <confirm-popup
      confirmation-message="By changing the customer information it will not reflect on Shopify Admin"
      title="Edit customer"
      confirm-label="Yes"
      cancel-label="No"
      @confirm="editCustomerModal = true"
    />
  </q-dialog>
  <q-dialog v-model="isEditDeliveryMethodModalOpen">
    <change-shipment-template
      :order-code="row.order_code"
      :delivery-template-code="row.delivery_template_code"
      :return-template-code="row.return_template_code"
      :shipping-address="row.shipping_address"
      :shipping-line-code="
        row.shipping_lines.length ? row.shipping_lines[row.shipping_lines.length - 1].code : null
      "
      @close="onCloseEditDeliveryMethodModal"
    />
  </q-dialog>
  <q-dialog v-model="showCapturePayment">
    <capture-payment :order="row" @refresh-order="onRefreshFromCapture"></capture-payment>
  </q-dialog>
  <q-dialog v-model="showMarkAsPaid">
    <confirm-popup
      confirmation-message="If you received payment manually, mark the order as paid. This payment won't be captured shopify and you won't be able to refund it"
      title="Mark as paid"
      confirm-label="Mark as paid"
      cancel-label="Cancel"
      @confirm="onClickMarkAsPaid"
    />
  </q-dialog>
</template>

<script setup lang="ts">
  import { computed, onBeforeMount, ref, Ref, watch } from 'vue';
  import dayjs from 'dayjs';
  import * as _ from 'lodash-es';
  import {
    IOrder,
    IIntCurrency,
    IOrderProduct,
    IOrderAddress,
    IDiscountAllocation,
    IShippingLine,
    IOrderTransaction,
  } from '../../../model/order.model';
  import OrderService from '../../../services/order.service';
  import { useRouter } from 'vue-router';
  import {
    loadingService,
    loggingService,
    notificationService,
    orderService,
  } from '../../../services/_singletons';
  import { useStore } from '../../../store';
  import PriceService from '../../../services/price.service';
  import { IFulfillment } from '../../../model/fulfillment.model';
  import { useHelper } from '../../../composeable/Helper';

  const $store = useStore();
  const $router = useRouter();
  const props = defineProps<{ row: IOrder }>();
  const { replaceDialCode, findLocalMoney } = useHelper();
  // eslint-disable-next-line consistent-return
  /** computed properties **/

  const showMarkAsPaidButton = computed(() => {
    const paymentType =
      props.row.payment_gateway_names?.[0]?.toLowerCase() == 'bogus' ? 'test' : 'live';

    if (paymentType == 'live') {
      return (
        (props.row.financial_status.toLowerCase() == 'pending' ||
          props.row.financial_status.toLowerCase() == 'partially_paid') &&
        (props.row.total_outstanding ?? 0) > 0
      );
    } else {
      return props.row.total_outstanding != null && props.row.total_outstanding > 0;
    }
  });
  const showCapturePaymentButton = computed(() => {
    const paymentType =
      props.row.payment_gateway_names?.[0]?.toLowerCase() == 'bogus' ? 'test' : 'live';

    if (paymentType == 'live') {
      return (
        props.row.financial_status.toLowerCase() == 'authorized' || props.row.total_outstanding == 0
      );
    } else {
      return authorizedAmount.value.amount - paidAmount.value.amount > 0;
    }
  });
  const authorizedAmount = computed(() =>
    getTransactionAmount(['authorization'], props.row.transactions ?? [], price.value.currency),
  );

  const paidAmount = computed(() =>
    getTransactionAmount(['capture', 'sale'], props.row.transactions ?? [], price.value.currency),
  );

  const receiverPhone = computed(() => formatPhone(props.row.shipping_address));
  const customerPhone = computed(() => formatPhone(props.row.billing_address));

  const getItemsQuantity = computed(() => {
    return props.row.line_items.reduce(
      (accumulator: number, orderProduct: IOrderProduct) => accumulator + orderProduct.quantity,
      0,
    );
  });
  const templateStatusOptions = computed(() => {
    if (!showTemplateAlert.value) {
      return null;
    }
    /*  if (props.row.template_status === 'existing' || !props.row.template_status) {
      return null;
    } */
    if (!props.row.delivery_template_code) {
      // const deliveryMethod = props.row.shipping_lines?.[0]?.title;
      return {
        type: 'error',
        description: `Shipping rule is not configured`,
      };
    }

    if (props.row.template_status === 'created') {
      return {
        type: 'warning',
        description: 'New shipping rule created and assigned as per order information',
      };
    }

    const orderWeight = (props.row.total_weight ?? 0) / 1000;
    const templateWeight =
      props.row.delivery_template?.parcels?.reduce((acc: number, item: any) => {
        console.log('item: ', item);
        console.log('max_weight: ', item.max_weight);
        return acc + parseFloat(item.max_weight ?? 0);
      }, 0) ?? 0;
    console.log('Order Weight: ', orderWeight);
    console.log('Template Weight: ', templateWeight);
    if (orderWeight > templateWeight) {
      return {
        type: 'warning',
        description: `Order weight is exceeding carrier limit for this service. Carrier terms and surcharge will apply`,
      };
    }
    return null;
    /* if (props.row.template_status === 'manual') {
      return {
        type: 'warning',
        description: 'shipment template assigned manually for this order.',
      };
    } */
  });

  const checkRefunded = computed(() => {
    const shouldRefund =
      props.row.fulfillment_returns?.some((item: IFulfillment) => item.refunded !== 1) ?? false;
    return shouldRefund && props.row.financial_status == 'paid';
  });

  const showMarkAsPaid = ref<boolean>(false);
  const showCapturePayment = ref<boolean>(false);
  const editNoteModal: Ref<boolean> = ref(false);
  const editReceiverModal: Ref<boolean> = ref(false);
  const editCustomerModal: Ref<boolean> = ref(false);
  const showEditCustomerPopup: Ref<boolean> = ref(false);
  const isEditDeliveryMethodModalOpen: Ref<boolean> = ref(false);
  const emit = defineEmits(['refresh-order', 'refresh-orders']);
  const isShowCancelledFulfillments = ref<boolean>(false);

  const note = ref<string | null>(props.row.notes);
  const noteInput = ref<string | null>(props.row.notes);
  const price = ref<IIntCurrency>(OrderService.getPrice(props.row));
  const shippingPrice = ref<IIntCurrency>(OrderService.getShippingPrice(props.row));
  const refundAmount = ref<number>(OrderService.getRefundAmount(props.row));
  //const totalShopMoney = ref<IIntCurrency>(OrderService.getTotalShopMoney(props.row));
  const vatAmount = ref<IIntCurrency>(OrderService.getVat(props.row));
  const currency = ref<string>(OrderService.getStoreCurrency(props.row));
  const localCurrency = ref<string>(OrderService.getLocalCurrency(props.row));
  const showTemplateAlert = ref<boolean>(true);
  /*const isOrderOpen = computed(() => {
  return props.row.order_status === "OPEN";
});

const isOrderProcessing = computed(() => {
  return (
    props.row.order_status === "PROCESSING" ||
    props.row.order_status === "CAPTURED_PAYMENT" ||
    props.row.order_status === "CREATED_FULFILLMENT" ||
    props.row.order_status === "CREATED_SHIPMENT_LABEL" ||
    props.row.order_status === "PARTIAL_FULFILLMENT"
  );
});*/

  const onClickCapturePayment = () => {
    showCapturePayment.value = true;
    console.log('Capture clicked');
  };
  const onClickEditDeliveryMethod = () => {
    isEditDeliveryMethodModalOpen.value = true;
  };
  const formatPhone = (address: IOrderAddress) => {
    const phone = address.phone;
    const dialCode = address.dialCode ?? '';
    return phone ? dialCode + replaceDialCode(phone, dialCode) : '';
  };

  const acceptShipmentTemplate = async () => {
    try {
      loadingService.startLoading('main-loader:accept-shipment-template');

      await orderService.acceptShipmentTemplate(props.row.order_code, {
        type: 'accept',
      });
      emit('refresh-order', props.row.order_code);
    } catch (e: any) {
      loggingService.error('Error accepting or ', e);
      notificationService.showError(
        e.response.data.error ? e.response.data.error : 'Error accepting templates',
      );
    } finally {
      loadingService.stopLoading('main-loader:accept-shipment-template');
    }
  };

  const onClickCloseTemplateAlert = () => {
    showTemplateAlert.value = false;
  };
  const denyShipmentTemplate = async () => {
    try {
      loadingService.startLoading('main-loader:deny-shipment-template');

      await orderService.denyShipmentTemplate(props.row.order_code, {
        type: 'deny',
      });
      emit('refresh-order', props.row.order_code);
    } catch (e: any) {
      loggingService.error('Error accepting or ', e);
      notificationService.showError(
        e.response.data.error ? e.response.data.error : 'Error denying template',
      );
    } finally {
      loadingService.stopLoading('main-loader:deny-shipment-template');
    }
  };

  const discountData = computed(() => {
    if (!props.row?.discount_applications || !props.row?.line_items) {
      return [];
    }

    const discountSummary = new Map<number, { title: string; amount: number; currency: string }>();

    props.row.discount_applications.forEach((item, index) => {
      discountSummary.set(index, {
        title: item.title ?? item.code ?? '',
        amount: 0,
        currency: 'DKK',
      });
    });

    const processDiscountAllocations = (allocations: IDiscountAllocation[]) => {
      allocations.forEach((discount_allocation) => {
        const discount_application_index = discount_allocation.discount_application_index ?? -1;
        const discountEntry = discountSummary.get(discount_application_index);

        if (discountEntry) {
          const localMoney = findLocalMoney(discount_allocation.amount_set);
          const amount = Number(localMoney?.amount || 0);

          discountEntry.amount += amount;
          discountEntry.currency = localMoney.currency || discountEntry.currency;
        }
      });
    };

    props.row.line_items.forEach((item: IOrderProduct) => {
      if (item.discount_allocations && _.isArray(item.discount_allocations)) {
        processDiscountAllocations(item.discount_allocations);
      }
    });

    props.row.shipping_lines.forEach((item: IShippingLine) => {
      if (item.discount_allocations && _.isArray(item.discount_allocations)) {
        processDiscountAllocations(item.discount_allocations);
      }
    });

    return Array.from(discountSummary.values());
  });

  const isPaymentDue = computed(() => {
    const payment_terms = props.row.payment_terms;
    if (payment_terms) {
      return (
        payment_terms.payment_schedules[0]?.due_at < new Date().toISOString() &&
        props.row.financial_status.toLowerCase() !== 'paid' &&
        props.row.financial_status.toLowerCase() !== 'refunded' &&
        props.row.financial_status.toLowerCase() !== 'partially_refunded' &&
        props.row.authorization_status !== 'expired'
      );
    }
    return false;
  });
  const discount = computed(() => {
    if (discountData.value.length) {
      return OrderService.getDiscount(props.row);
    } else {
      return null;
    }
  });

  const subTotal = computed<IIntCurrency>(() => {
    let subTotal = OrderService.getSubtotal(props.row);
    if (discountData.value.length && discount.value) {
      subTotal.amount = Number(discount.value.amount) + Number(subTotal.amount);
    }
    return subTotal;
  });

  const fullAddress = computed(() => {
    return props.row.shipping_address
      ? `${props.row.shipping_address.address1} ${
          props.row.shipping_address.address2 ? props.row.shipping_address?.address2 : ''
        }`
      : '';
  });

  const fulfillments = computed(() => {
    return OrderService.groupFulfillmentsById(props.row.fulfillments);
  });

  const fulfillmentReturns = computed(() => {
    return OrderService.groupFulfillmentsById(props.row.fulfillment_returns);
  });

  const onholdItems = computed(() => {
    const result = props.row.line_items
      ? OrderService.mapLineItemsByFulfillmentOrder(props.row.line_items)
      : [];
    console.log('Results: ', result);
    return result;
  });
  const unfulfilledItems = computed(() => {
    return props.row.line_items
      ? OrderService.getUnfulfilledItems(props.row.fulfillments, props.row.line_items)
      : [];
  });

  const cancelledFulfilledItems = computed(() => {
    return OrderService.groupFulfillmentsById(props.row.cancelled_fulfillments);
  });

  /*const onChangePrice = (amount: string, maxAmount: number) => {
  const parsedPrice = parseFloat(amount);
  const regex = /^(\d{1,3})(\.\d{3})*(,\d{2})?$/;

  if (!parsedPrice) {
    refundPaymentAmount.value = "0";
  }

  if (parsedPrice < 0) {
    refundPaymentAmount.value = "0";
  }

  if (!regex.test(amount)) {
    notificationService.showError("refund amount is not in correct format");
    refundPaymentAmount.value = amount;
  }

  if (parsedPrice > maxAmount) {
    refundPaymentAmount.value = amount;
  }
};

const getValidPrice = (amount: string, maxAmount: number) => {
  const parsedPrice = parseFloat(amount);
  const regex = /^(\d{1,3})(\.\d{3})*(,\d{2})?$/;

  if (!parsedPrice) {
    return true;
  }

  if (parsedPrice < 0) {
    return true;
  }

  if (!regex.test(amount)) {
    return true;
  }

  if (parsedPrice > maxAmount) {
    return true;
  }
  return false;
};*/
  watch(
    () => props.row,
    (newRow: IOrder) => {
      price.value = OrderService.getPrice(newRow);
      refundAmount.value = OrderService.getRefundAmount(newRow);
      currency.value = OrderService.getStoreCurrency(newRow);
      note.value = newRow.notes;
      noteInput.value = newRow.notes;
    },
    { deep: true },
  );
  /** when we create manual shipment from order-fulfillment-return, refresh the current opened order**/
  watch(
    () => $store.state.shipmentStore.isCreateShipmentModalOpen,
    (changeVal: boolean) => {
      if (!changeVal) {
        refreshOrder();
      }
    },
  );

  function getTransactionAmount(
    kinds: string[],
    transactions: IOrderTransaction[] = [],
    fallbackCurrency: string,
  ) {
    const amount =
      transactions?.reduce((acc, transaction) => {
        if (
          kinds.includes(transaction.kind.toLowerCase()) &&
          transaction.status.toLowerCase() === 'success'
        ) {
          return acc + Number(transaction.amount);
        }
        return acc;
      }, 0) ?? 0;

    const currency =
      transactions.find((transaction) => kinds.includes(transaction.kind.toLowerCase()))
        ?.currency ?? fallbackCurrency;

    return { amount, currency };
  }

  const refreshOrder = () => {
    emit('refresh-order', props.row.order_code);
    $router.replace({ name: 'Orders' });
  };

  /*const refreshOrders = () => {
  emit('refresh-orders');
  $router.replace({name: 'Orders'})
};*/

  const onClickSaveNote = async () => {
    if (noteInput.value === null) {
      noteInput.value = '';
      return;
    }

    try {
      loadingService.startLoading('main-loader:update-note');
      await orderService.updateNote(props.row.order_code, noteInput.value);
      editNoteModal.value = false;
      refreshOrder();
    } catch (e) {
      notificationService.showError(`Error while updating order ${props.row.order_number} note`);
      loggingService.error(`Error while updating order ${props.row.order_number} note`, e);
    } finally {
      loadingService.stopLoading('main-loader:update-note');
    }
  };

  /*
const onClickCapturePayment = async () => {
  try {
    loadingService.startLoading("main-loader:capture-payment");
    await orderService.capturePayment(props.row.order_code);
    refreshOrder();
  } catch (e) {
    loggingService.error("Error while capturing payment", e);
    notificationService.showError("Error while capturing payment");
  } finally {
    loadingService.stopLoading("main-loader:capture-payment");
  }
};


const onClickRefundPayment = async () => {
  try {
    const refundAmount = PriceService.shippingPriceFormat(
      Number(
        refundPaymentAmount.value.replace(/[,.]/g, (match) => {
          return match === "," ? "." : "";
        })
      ),
      "en"
    ).replace(/,/g, "");
    loadingService.startLoading("main-loader:refund-payment");
    await orderService.refundPayment(props.row.order_code, refundAmount);
    refreshOrder();
    refundPaymentAmount.value = "";
    returnPaymentModal.value = false;
  } catch (e) {
    loggingService.error("Error while refund payment", e);
    notificationService.showError("Error while refund payment");
  } finally {
    loadingService.stopLoading("main-loader:refund-payment");
  }
};
*/
  const onClickMarkAsPaid = async () => {
    try {
      loadingService.startLoading('main-loader:mark-as-paid');
      await orderService.markAsPaid(props.row.order_code);
      refreshOrder();
    } catch (e) {
      loggingService.error('Error while marking as paid', e);
      notificationService.showError('Error while marking as paid');
    } finally {
      loadingService.stopLoading('main-loader:mark-as-paid');
    }
  };

  onBeforeMount(() => {
    // console.log(props.row);
  });

  const onCloseEditDeliveryMethodModal = () => {
    isEditDeliveryMethodModalOpen.value = false;
    refreshOrder();
  };

  const onClickEditReceiver = () => {
    editReceiverModal.value = true;
  };

  const onCloseEditReceiverModal = () => {
    editReceiverModal.value = false;
  };

  const onCloseEditCustomer = () => {
    editCustomerModal.value = false;
  };

  const onRefreshFromCapture = () => {
    showCapturePayment.value = false;
    refreshOrder();
  };
</script>

<style scoped lang="scss">
  .shipvagoo-order-detail-body {
    padding: 17px 4px 17px 17px;
  }
  .shipvagoo-order-detail {
    &-header {
      min-height: 60px;
      max-height: 75px;
      background-color: white;
      border: 1px solid rgba(125, 131, 152, 0.2);
      border-radius: 4px;
    }

    .equal-height-row {
      display: flex;
    }

    .equal-height-row [class^='col-'] {
      display: flex;
      flex-direction: column;
    }

    .equal-height-row [class^='col-'] .order-detail-box-card {
      flex-grow: 1;
      min-height: 160px;
    }
  }
</style>
