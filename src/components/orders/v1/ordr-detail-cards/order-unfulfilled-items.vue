<template>
  <div v-bind="$attrs" class="order-detail-box-card dark-text">
    <div class="full-width q-py-sm q-px-md flex justify-between items-center">
      <div class="flex items-center">
        <shipvagoo-status-tag text="Unfulfilled" bg-color="#FEECA1" color="#8E7200" />
        <div class="text-thirteen text-weight-7 q-ml-md">
          {{ order.name ?? order.order_number }}
        </div>
      </div>
      <div class="text-thirteen">
        {{ dayjs(order.created_at).format('DD/MM/YYYY') }} -
        {{ dayjs(order.created_at).format('HH:mm') }}
      </div>
    </div>
    <shipvagoo-separator />
    <div
      v-for="(unfulfilledItem, index) of unfulfilledItems"
      :key="`${unfulfilledItem.line_item_code}-${unfulfilledItem.quantity}`"
    >
      <div class="order_detail-order_item">
        <div
          v-if="unfulfilledItem.variant"
          class="flex items-center q-py-sm"
          style="min-height: 90px"
        >
          <div class="flex items-center">
            <img
              v-if="unfulfilledItem?.variant?.variant_image"
              decoding="async"
              class="order-detail-product-img"
              :src="unfulfilledItem?.variant?.variant_image"
            />
          </div>
          <div class="q-pl-lg flex items-center">
            <div>
              <div class="text-thirteen text-weight-7">
                {{ unfulfilledItem?.title ?? '' }}
              </div>
              <div class="text-eleven">
                <div class="q-mt-xs" v-if="unfulfilledItem.variant?.title != 'Default Title'">
                  <span>Variant: </span>
                  {{ unfulfilledItem.variant?.title ?? '' }}
                </div>
                <div class="q-mt-xs">
                  <span>Weight: </span>
                  {{ unfulfilledItem.variant?.weight + ' ' + unfulfilledItem.variant?.weight_unit }}
                  <!-- {{
                  Number(unfulfilledItem?.grams) > 999
                    ? (Number(unfulfilledItem?.grams) / 1000).toFixed(2) + ' kg'
                    : unfulfilledItem?.grams + ' g'
                }} -->
                </div>
                <div class="q-mt-xs" v-if="unfulfilledItem?.variant?.sku">
                  <span>SKU: </span>{{ unfulfilledItem?.variant?.sku }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="flex items-center q-py-sm" style="min-height: 90px">
          <div class="flex items-center">
            <img
              v-if="unfulfilledItem.product?.image_url"
              decoding="async"
              class="order-detail-product-img"
              :src="unfulfilledItem.product.image_url"
            />
          </div>
          <div class="q-pl-lg flex items-center">
            <div>
              <div class="text-thirteen">
                {{ unfulfilledItem.product?.title }}
                {{ unfulfilledItem.product?.sku && `- ${unfulfilledItem.product?.sku}` }}
              </div>
              <div class="text-eleven q-mt-sm">
                <span class="text-weight-7">Weight: </span>{{ unfulfilledItem.product?.weight }}
                {{ unfulfilledItem.grams ? 'g' : 'kg' }}
              </div>
              <div class="text-eleven q-mt-sm" v-if="unfulfilledItem.product?.sku">
                <span class="text-weight-7">SKU: </span>{{ unfulfilledItem.product?.sku }}
              </div>
            </div>
          </div>
        </div>
        <div class="orders_page-modal-order_modal-fulfillments-header-prices flex items-center">
          <div>
            {{
              PriceService.formatInternationalPrice(findLocalMoney(unfulfilledItem.price_set) || 0)
            }}
            <span class="q-mx-sm">x</span>
            {{ unfulfilledItem.quantity }}
          </div>
          <div class="text-weight-7">
            {{ findTotalLocalMoney(unfulfilledItem) }}
          </div>
        </div>
      </div>
      <div class="q-mx-md" v-if="index < unfulfilledItems.length - 1">
        <shipvagoo-separator />
      </div>
    </div>
    <shipvagoo-separator />
    <shipvagoo-card-actions>
      <shipvagoo-button-dropdown-v1
        color="#243E90"
        bg-color="#FFFFFF"
        dropdown-icon="fas fa-caret-down"
        label="Print picking list"
        v-if="order.pick_list_base64"
        outline
        no-caps
      >
        <shipvagoo-list>
          <shipvagoo-menu-item v-close-popup clickable @click="onClickPreviewPickList">
            Download picking list
          </shipvagoo-menu-item>
          <shipvagoo-menu-item clickable has-caret>
            Print picking list at
            <shipvagoo-menu>
              <shipvagoo-menu-item v-if="$store.state.printerStore.printers.length <= 0">
                No printer is set
              </shipvagoo-menu-item>
              <shipvagoo-menu-item
                v-for="printer in $store.state.printerStore.printers"
                :key="`printer-${printer.printer_code}`"
                clickable
                @click="onClickPrintPickListAt(printer)"
              >
                {{ printer.name }}
              </shipvagoo-menu-item>
            </shipvagoo-menu>
          </shipvagoo-menu-item>
        </shipvagoo-list>
      </shipvagoo-button-dropdown-v1>
      <shipvagoo-button v-if="!order.pick_list_base64" @click="onClickCreatePickList" outline>
        Create picking list
      </shipvagoo-button>
      <shipvagoo-button @click="onClickCreateFulfillment" outline> Fulfill items </shipvagoo-button>
      <shipvagoo-button @click="onClickCreateFulfillmentAndShipment" outline>
        Fulfill items and create shipment
      </shipvagoo-button>
    </shipvagoo-card-actions>
  </div>

  <q-dialog v-model="isFulfillmentModalOpen">
    <fulfillment-modal
      :order="order"
      :unfulfilled-items="unfulfilledItems"
      @close="isFulfillmentModalOpen = false"
      @on-created-fulfillments="emit('refresh-order')"
    />
  </q-dialog>

  <q-dialog v-model="isEditDeliveryMethodModalOpen" persistent>
    <change-shipment-template
      v-if="shipment"
      :shipment-code="shipment.shipment_code"
      :delivery-template-code="shipment.delivery_template_code ?? null"
      :return-template-code="order.return_template_code"
      :shipping-address="order.shipping_address"
      :shipping-line-code="
        order.shipping_lines.length
          ? order.shipping_lines[order.shipping_lines.length - 1].code
          : null
      "
      @close="onCloseDeliveryMethodModal"
    />
  </q-dialog>
</template>

<script setup lang="ts">
  import * as _ from 'lodash-es';
  import { ref, onBeforeMount } from 'vue';
  import dayjs from 'dayjs';
  import { IOrder, IOrderProduct } from '../../../../model/order.model';
  import PriceService from '../../../../services/price.service';
  import WindowService from '../../../../services/window.service';
  import {
    loadingService,
    loggingService,
    notificationService,
    orderService,
    printJobService,
  } from '../../../../services/_singletons';
  import HelperService from '../../../../services/helper.service';
  import OrderService from '../../../../services/order.service';
  import { useStore } from '../../../../store';
  import { IPrinter } from '../../../../model/printer.model';
  import { useHelper } from '../../../../composeable/Helper';
  import { IShippingProduct } from '../../../../model/carrier.model';
  import { IFulfillment } from '../../../../model/fulfillment.model';
  import { AxiosError } from 'axios';
  import { IShipment } from '../../../../model/shipment.model';

  const { handleBeforeUnload, findLocalMoney, validateReceiverMobile } = useHelper();

  const $store = useStore();

  interface Props {
    order: IOrder;
    unfulfilledItems: IOrderProduct[];
  }

  const props = defineProps<Props>();
  const emit = defineEmits(['refresh-order']);

  let windowRef: Window | null;
  const isFulfillmentModalOpen = ref<boolean>(false);
  const shippingProduct = ref<IShippingProduct | null>(null);
  const isEditDeliveryMethodModalOpen = ref<boolean>(false);
  const shipment = ref<IShipment | null>(null);
  const _fulfillmentCode = ref<number | null>(null);

  /*const totalPrice = computed(() => {
    return _.sumBy(props.unfulfilledItems, (item) => {
      return Number(item.product?.selling_price) * Number(item.quantity);
    });
  });*/

  const findTotalLocalMoney = (unfulfilled: any) => {
    let itemPrice = findLocalMoney(unfulfilled.price_set);
    let totalPrice = Number(itemPrice.amount ?? 0) * Number(unfulfilled.quantity);

    let final = PriceService.formatInternationalPrice({
      currency: itemPrice.currency,
      amount: totalPrice,
    });
    return final;
  };

  const onClickCreateFulfillment = async () => {
    isFulfillmentModalOpen.value = true;
  };

  const onClickCreatePickList = async () => {
    try {
      loadingService.startLoading('main-loader:create-pick-list');
      await orderService.createPickList(props.order.order_code);
      emit('refresh-order');
    } catch (error) {
      loggingService.error('Error while previewing PDF', error);
      notificationService.showError('Error while previewing PDF');
    } finally {
      loadingService.stopLoading('main-loader:create-pick-list');
    }
  };

  const previewPickList = async () => {
    if (!windowRef || !props.order.pick_list_base64) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:preview-pdf');

      const blob: Blob = await HelperService.toBlob(
        `data:application/pdf;base64,${encodeURI(props.order.pick_list_base64)}`,
      );

      windowRef.location = URL.createObjectURL(blob);
    } catch (error) {
      windowRef.close();
      windowRef = null;
      loggingService.error('Error while previewing PDF', error);
      notificationService.showError('Error while previewing PDF');
    } finally {
      loadingService.stopLoading('main-loader:preview-pdf');
    }
  };

  const onClickPreviewPickList = () => {
    windowRef = WindowService.openWindow('Picking List Preview');

    previewPickList();
  };

  const setCarrierProduct = (): void => {
    const carrierProductCode = props.order.delivery_template?.carrier_product_code;
    if (carrierProductCode) {
      shippingProduct.value =
        $store.state.courierStore.carrierProducts?.find(
          (p) => p.carrier_product_code == carrierProductCode,
        ) ?? null;
    }
  };

  const onClickCreateShipment = async (orderCode: number, fulfillmentCode: number) => {
    if (!isShippingRuleSet()) return;

    const responseMessage = validateReceiverMobile(
      shippingProduct.value?.product_id,
      props.order.shipping_address.phone,
      props.order.order_number,
    );

    if (handleValidationError(responseMessage)) return;

    try {
      loadingService.startLoading('main-loader:create-shipment-from-fulfillment');

      const shipmentResponse = await createShipment(orderCode, fulfillmentCode);
      if (!shipmentResponse) return;

      await generateShipmentLabel(fulfillmentCode);
    } catch (e) {
      handleShipmentError(e);
    } finally {
      loadingService.stopLoading('main-loader:create-shipment-from-fulfillment');
    }
  };

  // **Helper Methods**
  const isShippingRuleSet = () => {
    if (props.order.delivery_template_code === null) {
      notificationService.showError(
        `Shipping rule is not set for order ${props.order.order_number}. Please set a shipping rule before attempting to create a shipment.`,
      );
      return false;
    }
    return true;
  };

  const handleValidationError = (responseMessage: string | null) => {
    if (responseMessage) {
      notificationService.showError(responseMessage);
      emit('refresh-order');
      return true;
    }
    return false;
  };

  const createShipment = async (orderCode: number, fulfillmentCode: number) => {
    try {
      loadingService.startLoading('main-loader:create-shipment');
      const response = await orderService.createShipmentFromFulfillment(orderCode, fulfillmentCode);
      if (response && response.fulfillments) {
        const fulfillment = selectedFulfillment(response.fulfillments, fulfillmentCode);
        if (fulfillment) {
          shipment.value = fulfillment.shipment;
          _fulfillmentCode.value = fulfillmentCode;
        }
      }
      return response;
    } catch (error) {
      if (error instanceof AxiosError) {
        notificationService.showError(error.response?.data?.error);
      }
      return null;
    } finally {
      loadingService.stopLoading('main-loader:create-shipment');
    }
  };

  const generateShipmentLabel = async (fulfillmentCode: number) => {
    try {
      loadingService.startLoading('main-loader:create-shipment-label');
      const response = await orderService.generateLabelForFulfillmentsShipment(fulfillmentCode);
      if (response && response.fulfillments) {
        $store.dispatch('companyStore/syncCompanyData');

        const fulfillment = selectedFulfillment(response.fulfillments, fulfillmentCode);
        if (fulfillment?.shipment_code) {
          notificationService.showSuccess(
            `Shipment created successfully for fulfillment ${fulfillment.name}`,
          );
        }
      }
      _fulfillmentCode.value = null;
      shipment.value = null;
      emit('refresh-order');
    } catch (error) {
      if (error instanceof AxiosError) {
        if (
          error.response?.data?.error &&
          error.response?.data?.error != 'partial_shipping_rule_validation_error'
        ) {
          notificationService.showError(error.response?.data?.error);
          emit('refresh-order');
        }
        if (error.response?.data?.invalid_shipping_template) {
          isEditDeliveryMethodModalOpen.value = true;
        }
      }
    } finally {
      loadingService.stopLoading('main-loader:create-shipment-label');
    }
  };

  const handleShipmentError = (error: any) => {
    loggingService.error('Error while creating shipment from fulfillment', error);
    notificationService.showError('Error while creating shipment from fulfillment');
  };

  const selectedFulfillment = (fulfillments: IFulfillment[], fulfillmentCode: number) => {
    return fulfillments.find((f) => f.fulfillment_code === fulfillmentCode);
  };

  const onCloseDeliveryMethodModal = async (value: { isSaved: boolean }) => {
    if (value.isSaved && _fulfillmentCode.value) {
      await generateShipmentLabel(_fulfillmentCode.value);
    }
    emit('refresh-order');
    isEditDeliveryMethodModalOpen.value = false;
  };

  const onClickCreateFulfillmentAndShipment = async () => {
    try {
      window.addEventListener('beforeunload', handleBeforeUnload);
      loadingService.startLoading('main-loader:create-fulfillment-and-shipment');

      const unfulfilledItems = OrderService.getUnfulfilledItems(
        props.order.fulfillments,
        props.order.line_items,
      );

      const fulfillmentData = mapUnfulfilledItems(props.unfulfilledItems);

      // Create fulfillment
      const response = await orderService.createFulfillment(
        props.order.order_code,
        null,
        fulfillmentData,
      );

      const createdFulfillments = findCreatedFulfillments(
        response.fulfillments ?? [],
        unfulfilledItems,
      );
      if (!createdFulfillments.length) {
        throw new Error('Could not find created fulfillment');
      }

      const lastFulfillment = createdFulfillments[createdFulfillments.length - 1];
      await onClickCreateShipment(props.order.order_code, lastFulfillment.fulfillment_code);
    } catch (error: unknown) {
      handleShipmentFulfillmentError(error);
    } finally {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      loadingService.stopLoading('main-loader:create-fulfillment-and-shipment');
    }
  };

  /**
   * Extracts unfulfilled items and maps them for fulfillment creation.
   */
  const mapUnfulfilledItems = (items: any[]) => {
    return items.map((item) => ({
      line_item_code: item.line_item_code,
      quantity: item.quantity,
    }));
  };

  /**
   * Finds fulfillments that match unfulfilled items.
   */
  const findCreatedFulfillments = (fulfillments: any[], unfulfilledItems: any[]) => {
    return (
      fulfillments?.filter((fulfillment) =>
        unfulfilledItems.find(
          (item) =>
            fulfillment.line_item_code === item.line_item_code &&
            fulfillment.quantity === item.quantity &&
            fulfillment.shipment_code === null,
        ),
      ) || []
    );
  };

  /**
   * Handles API errors and shows appropriate notifications.
   */
  const handleShipmentFulfillmentError = (error: unknown) => {
    if (error instanceof AxiosError) {
      const errorMessage = error.response?.data?.error || error.response?.data?.message;
      if (errorMessage) {
        const errorMessageLowerCase = errorMessage.toLowerCase();
        if (errorMessageLowerCase.includes('timeout')) {
          notificationService.showError('Timeout creating shipping label. Please try again.');
        } else if (errorMessage.includes('illegal value')) {
          notificationService.showError('Receiver mobile number is not valid.');
        } else if (errorMessage.includes('mandatory value missing. on party_Sms')) {
          notificationService.showError('Receiver mobile number is mandatory.');
        } else if (errorMessageLowerCase.includes('null')) {
          notificationService.showError('Error creating shipping label. Please try again');
        } else {
          notificationService.showError(errorMessage);
        }
      } else {
        notificationService.showError(
          'Error while creating fulfillment and shipment. Please try again',
        );
      }
    } else {
      notificationService.showError('Error while creating fulfillment and shipment');
    }

    loggingService.error('Error while creating fulfillment and shipment', error);
  };

  /*const onClickDefaultPrintPickList = async () => {
    try {
      loadingService.startLoading("main-loader:print-pick-list-with-default-printer");

      if (!props.order.pick_list_base64) {
        throw new Error("Picking list is not generated");
      }

      if (!$store.state.printerStore.defaultPrinterForPickDocuments) {
        notificationService.showError(
          "Default printer is not set. Please set a default printer for picking lists at Settings > Print > Printers."
        );
        throw new Error("No default printer found");
      }loadings

      await printJobService.queuePrintJob({
        company_code: $store.state.companyStore.company_code,
        document_base64: props.order.pick_list_base64,
        format: "A4",
        reference: `Picking list #${props.order.order_number}`,
        printer: $store.state.printerStore.defaultPrinterForPickDocuments.linked_printer,
        printer_app_code: $store.state.printerStore.defaultPrinterForPickDocuments.printer_app_code
      });
      notificationService.showSuccess(
        `Picking list sent to printer ${$store.state.printerStore.defaultPrinterForPickDocuments.name}`
      );
    } catch (e) {
      notificationService.showError("Error while printing picking list");
      loggingService.error("Error while printing picking list", e);
    } finally {
      loadingService.stopLoading("main-loader:print-pick-list-with-default-printer");
    }
  };*/

  const onClickPrintPickListAt = async (printer: IPrinter) => {
    try {
      loadingService.startLoading('main-loader:print-pick-list-at-printer');

      if (!props.order.pick_list_base64) {
        throw new Error('Picking list is not generated');
      }

      await printJobService.queuePrintJob({
        company_code: $store.state.companyStore.company_code,
        document_base64: props.order.pick_list_base64,
        format: 'A4',
        reference: `Picking list #${props.order.name ?? props.order.order_number}`,
        printer: printer.linked_printer,
        printer_app_code: printer.printer_app_code,
      });

      notificationService.showSuccess(`Picking list sent to printer ${printer.name}`);
    } catch (e) {
      notificationService.showError('Error while printing picking list');
      loggingService.error('Error while printing picking list', e);
    } finally {
      loadingService.stopLoading('main-loader:print-pick-list-at-printer');
    }
  };

  onBeforeMount(() => {
    setCarrierProduct();
  });
</script>
