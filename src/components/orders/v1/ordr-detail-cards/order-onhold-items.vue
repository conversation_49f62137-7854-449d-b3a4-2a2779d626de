<template>
  <div v-bind="$attrs" class="order-detail-box-card dark-text">
    <div class="full-width q-py-sm q-px-md flex justify-between items-center">
      <div class="flex items-center">
        <shipvagoo-status-tag text="On hold" bg-color="#FFD6A4" color="#5e4200" />
        <div class="text-thirteen text-weight-7 q-ml-md">
          {{ order.name ?? order.order_number }}
        </div>
      </div>
      <div class="text-thirteen">
        {{ dayjs(order.created_at).format('DD/MM/YYYY') }} -
        {{ dayjs(order.created_at).format('HH:mm') }}
      </div>
    </div>
    <shipvagoo-separator />
    <div
      v-for="(onholdItem, index) of onholdItems"
      :key="`${onholdItem.line_item_code}-${onholdItem.quantity}`"
    >
      <div class="order_detail-order_item">
        <div v-if="onholdItem.variant" class="flex items-center q-py-sm" style="min-height: 90px">
          <div class="flex items-center">
            <img
              v-if="onholdItem?.variant?.variant_image"
              decoding="async"
              class="order-detail-product-img"
              :src="onholdItem?.variant?.variant_image"
            />
          </div>
          <div class="q-pl-lg flex items-center">
            <div>
              <div class="text-thirteen text-weight-7">
                {{ onholdItem?.title ?? '' }}
              </div>
              <div class="text-eleven">
                <div class="q-mt-xs" v-if="onholdItem.variant?.title != 'Default Title'">
                  <span>Variant: </span>
                  {{ onholdItem.variant?.title ?? '' }}
                </div>
                <div class="q-mt-xs">
                  <span>Weight: </span>
                  {{ onholdItem.variant?.weight + ' ' + onholdItem.variant?.weight_unit }}
                </div>
                <div class="q-mt-xs" v-if="onholdItem?.variant?.sku">
                  <span>SKU: </span>{{ onholdItem?.variant?.sku }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="flex items-center q-py-sm" style="min-height: 90px">
          <div class="flex items-center">
            <img
              v-if="onholdItem.product?.image_url"
              decoding="async"
              class="order-detail-product-img"
              :src="onholdItem.product.image_url"
            />
          </div>
          <div class="q-pl-lg flex items-center">
            <div>
              <div class="text-thirteen">
                {{ onholdItem.product?.title }}
                {{ onholdItem.product?.sku && `- ${onholdItem.product?.sku}` }}
              </div>
              <div class="text-eleven q-mt-sm">
                <span class="text-weight-7">Weight: </span>{{ onholdItem.product?.weight }}
                {{ onholdItem.grams ? 'g' : 'kg' }}
              </div>
              <div class="text-eleven q-mt-sm" v-if="onholdItem.product?.sku">
                <span class="text-weight-7">SKU: </span>{{ onholdItem.product?.sku }}
              </div>
            </div>
          </div>
        </div>
        <div class="orders_page-modal-order_modal-fulfillments-header-prices flex items-center">
          <div>
            {{ PriceService.formatInternationalPrice(findLocalMoney(onholdItem.price_set) || 0) }}
            <span class="q-mx-sm">x</span>
            {{ onholdItem.quantity }}
          </div>
          <div class="text-weight-7">
            {{ findTotalLocalMoney(onholdItem) }}
          </div>
        </div>
      </div>
      <div class="q-mx-md" v-if="index < onholdItems.length - 1">
        <shipvagoo-separator />
      </div>
    </div>
    <shipvagoo-separator />
  </div>

  <q-dialog v-model="isFulfillmentModalOpen">
    <fulfillment-modal
      :order="order"
      :unfulfilled-items="onholdItems"
      @close="isFulfillmentModalOpen = false"
      @on-created-fulfillments="emit('refresh-order')"
    />
  </q-dialog>
</template>

<script setup lang="ts">
  import * as _ from 'lodash-es';
  import { ref, onBeforeMount } from 'vue';
  import dayjs from 'dayjs';
  import { IOrder, IOrderProduct } from '../../../../model/order.model';
  import PriceService from '../../../../services/price.service';

  import { useStore } from '../../../../store';
  import { useHelper } from '../../../../composeable/Helper';
  import { IShippingProduct } from '../../../../model/carrier.model';

  const { findLocalMoney } = useHelper();

  const $store = useStore();

  interface Props {
    order: IOrder;
    onholdItems: IOrderProduct[];
  }

  const props = defineProps<Props>();
  const emit = defineEmits(['refresh-order']);

  const isFulfillmentModalOpen = ref<boolean>(false);
  const shippingProduct = ref<IShippingProduct | null>(null);

  const findTotalLocalMoney = (unfulfilled: any) => {
    let itemPrice = findLocalMoney(unfulfilled.price_set);
    let totalPrice = Number(itemPrice.amount ?? 0) * Number(unfulfilled.quantity);

    let final = PriceService.formatInternationalPrice({
      currency: itemPrice.currency,
      amount: totalPrice,
    });
    return final;
  };

  const setCarrierProduct = (): void => {
    const carrierProductCode = props.order.delivery_template?.carrier_product_code;
    if (carrierProductCode) {
      shippingProduct.value =
        $store.state.courierStore.carrierProducts?.find(
          (p) => p.carrier_product_code == carrierProductCode,
        ) ?? null;
    }
  };

  onBeforeMount(() => {
    setCarrierProduct();
  });
</script>
