<template>
  <div
    v-bind="$attrs"
    :key="`${fulfillment[0].fulfillment_code}-${fulfillment[0].updated_at}`"
    class="order-detail-box-card dark-text"
  >
    <div class="full-width q-py-sm q-px-md flex justify-between items-center">
      <div class="flex items-center">
        <shipvagoo-status-tag
          v-if="zeroItemReturned"
          text="Return in progress"
          bg-color="#D5EBFF"
          color="#0094D5"
        />
        <shipvagoo-status-tag
          v-else-if="checkRefunded"
          text="Partially refunded"
          bg-color="#E5E5E5"
          color="#7D8398"
        />
        <shipvagoo-status-tag v-else text="Refunded" bg-color="#E5E5E5" color="#757575" />

        <div class="text-thirteen text-weight-7 q-ml-md">
          {{ fulfillment[0].name }}
          <template v-if="fulfillment[0].packaging">
            - {{ fulfillment[0].packaging.name }}
          </template>
        </div>
        <img
          v-if="carrier"
          :src="carrier?.icon"
          class="q-ml-md"
          height="20"
          width="20"
          decoding="async"
        />
      </div>
      <div class="text-thirteen">
        {{ checkRefunded ? 'Return' : 'Refunded' }}:
        {{ dayjs(fulfillment[0].created_at).format('DD/MM/YYYY') }} -
        {{ dayjs(fulfillment[0].created_at).format('HH:mm') }}
      </div>
    </div>
    <shipvagoo-separator />
    <div
      v-for="(fulfilledItem, index) of fulfillmentItems"
      :key="`${fulfilledItem.line_item_code}-${fulfilledItem.quantity}-${fulfilledItem.updated_at}`"
    >
      <div class="order_detail-order_item">
        <div v-if="fulfilledItem.variant" class="flex q-py-sm">
          <div class="flex items-center">
            <img
              v-if="fulfilledItem.variant?.variant_image"
              decoding="async"
              class="order-detail-product-img"
              :src="fulfilledItem.variant?.variant_image"
            />
            <div class="q-pl-lg flex items-center">
              <div>
                <div class="text-thirteen text-weight-7">
                  {{ fulfilledItem?.title }}
                </div>
                <div class="text-eleven">
                  <div class="q-mt-xs" v-if="fulfilledItem.variant?.title != 'Default Title'">
                    <span>Variant: </span>
                    {{ fulfilledItem.variant?.title ?? '' }}
                  </div>
                  <div class="q-mt-xs" v-if="fulfilledItem.variant?.weight">
                    <span>Weight: </span
                    >{{ `${fulfilledItem.variant?.weight} ${fulfilledItem.variant?.weight_unit}` }}
                  </div>
                  <div class="q-mt-xs">
                    <span>Refunded: </span>
                    {{ fulfillment[index]?.returned_quantity }}/{{ fulfilledItem.quantity }}
                  </div>
                  <div class="q-mt-xs"><span>Reason: </span>{{ fulfillment[index]?.reason }}</div>
                  <div class="q-mt-xs">
                    <span>Comment: </span>
                    {{ fulfillment[index]?.comment }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="flex q-py-sm">
          <div class="flex items-center">
            <img
              v-if="fulfilledItem.product?.image_url"
              decoding="async"
              class="order-detail-product-img"
              :src="fulfilledItem.product.image_url"
            />
            <div class="q-pl-lg flex items-center">
              <div>
                <div class="text-thirteen">
                  {{ fulfilledItem.product?.title }}
                  {{ fulfilledItem.product?.sku && `- ${fulfilledItem.product?.sku}` }}
                </div>
                <div class="text-eleven q-mt-xs">
                  <span class="text-weight-7">Weight: </span>{{ fulfilledItem.product?.weight }}
                  {{ fulfilledItem.grams ? 'g' : 'kg' }}
                </div>
                <div class="text-eleven">
                  <div class="q-mt-xs">
                    <span class="text-weight-7">Refunded: </span>
                    {{ fulfillment[index]?.returned_quantity }}/{{ fulfilledItem.quantity }}
                  </div>
                  <div class="q-mt-xs">
                    <span class="text-weight-7">Reason: </span>{{ fulfillment[index]?.reason }}
                  </div>
                  <div class="q-mt-xs">
                    <span class="text-weight-7">Comment: </span>
                    {{ fulfillment[index]?.comment }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="orders_page-modal-order_modal-fulfillments-header-prices flex items-center">
          <div>
            {{
              PriceService.formatInternationalPrice(findLocalMoney(fulfilledItem.price_set) || 0)
            }}
            <span class="q-mx-sm">x</span>
            {{ fulfilledItem.quantity }}
          </div>
          <div class="text-weight-7">
            {{ findTotalLocalMoney(fulfilledItem) }}
          </div>
        </div>
      </div>
      <div class="q-mx-md" v-if="index < fulfillmentItems.length - 1">
        <shipvagoo-separator />
      </div>
    </div>
    <template v-if="!isCancelled">
      <shipvagoo-separator />
      <shipvagoo-card-actions>
        <shipvagoo-button
          style="height: 40px"
          v-if="!checkRefundStatus()"
          outline
          no-caps
          split
          label="Cancel return"
          @click="onClickCancelFulfillment(fulfillment[0].fulfillment_code)"
        />
        <shipvagoo-button
          v-if="
            (checkRefunded || shippingChargesAvailableToRefund) &&
            (order.financial_status.toLocaleLowerCase() == 'paid' ||
              order.financial_status.toLocaleLowerCase() == 'partially_refunded')
          "
          style="height: 40px"
          outline
          no-caps
          :label="checkRefunded ? 'Create refund' : 'Create shipping refund'"
          @click="onCreateRefund"
        />
        <shipvagoo-button
          style="height: 40px"
          v-if="
            !fulfillment[0].shipment_code && return_shipment_label == null && shipping_label == null
          "
          outline
          no-caps
          label="Create return shipment"
          @click="onClickCreateShipment(order)"
        />

        <shipvagoo-button
          style="height: 40px"
          v-if="fulfillment[0].shipment_code || shipping_label || return_shipment_label"
          outline
          no-caps
          :label="
            fulfillment[0].shipment && shipping_label == null
              ? 'Continue shipment from draft'
              : 'Return shipment details'
          "
          @click="
            fulfillment[0].shipment && shipping_label == null
              ? onClickContinueFromDraft(fulfillment[0].shipment)
              : onClickShipmentDetail(shipment?.shipment_code ?? fulfillment[0].shipment_code)
          "
        />

        <shipvagoo-button-dropdown-v1
          color="#243E90"
          bg-color="#FFFFFF"
          dropdown-icon="fas fa-caret-down"
          v-if="shipping_label || return_shipment_label"
          outline
          no-caps
          label="Return shipping label"
        >
          <shipvagoo-list>
            <shipvagoo-menu-item
              v-close-popup
              clickable
              @click="onClickDownloadLabel(fulfillment[0].fulfillment_code)"
            >
              Download return shipping label
            </shipvagoo-menu-item>
            <shipvagoo-separator />
            <shipvagoo-menu-item clickable has-caret>
              Print return shipment label at
              <shipvagoo-menu anchor="top end" style="width: 160px">
                <shipvagoo-menu-item v-if="$store.state.printerStore.printers.length <= 0">
                  {{ $t('common.no_printer_is_set') }}
                </shipvagoo-menu-item>
                <shipvagoo-menu-item
                  v-for="printer in $store.state.printerStore.printers"
                  :key="`printer-${printer.printer_code}`"
                  clickable
                  @click="onClickPrintLabel(printer, fulfillment[0].fulfillment_code)"
                >
                  {{ printer.name }}
                </shipvagoo-menu-item>
              </shipvagoo-menu>
            </shipvagoo-menu-item>
            <!--            <shipvagoo-menu-item v-if="!checkRefundStatus()" v-close-popup clickable disable @click="
                          onClickCancelFulfillmentAndShipment(
                            fulfillment[0].fulfillment_code,
                            fulfillment[0].shipment_code,
                          )
                          ">
                          {{ $t('order_detail.cancel_return_fulfillment_and_shipment') }}
                        </shipvagoo-menu-item>-->
          </shipvagoo-list>
        </shipvagoo-button-dropdown-v1>
      </shipvagoo-card-actions>
    </template>
  </div>

  <q-dialog v-if="shipment" v-model="isShipmentModelOpen">
    <shipment-detail
      :shipment="shipment"
      @printLabel="(value:any)=>onClickPrintLabel(value.printer,(shipment?.fulfillment_code ?? null))"
      @downloadLabel="onClickDownloadLabel(shipment?.fulfillment_code ?? null)"
    />
  </q-dialog>
  <q-dialog v-model="isOrderFulfillmentRefundModalOpen">
    <order-fulfillment-refund-modal
      :order="order"
      :fulfillment="fulfillment"
      :fulfillment-items="fulfillmentItems"
      @close="isOrderFulfillmentRefundModalOpen = false"
      @on-created-fulfillments="emit('refresh-order')"
    />
  </q-dialog>
</template>

<script setup lang="ts">
  import dayjs from 'dayjs';
  import { computed, Ref, ref } from 'vue';
  import { IFulfillment } from '../../../../model/fulfillment.model';
  import { IOrder, IOrderProduct } from '../../../../model/order.model';
  import PriceService from '../../../../services/price.service';
  import {
    carrierService,
    loadingService,
    loggingService,
    notificationService,
    orderService,
    printJobService,
    shipmentService,
    templateService,
  } from '../../../../services/_singletons';
  import HelperService from '../../../../services/helper.service';
  import WindowService from '../../../../services/window.service';
  import { useStore } from '../../../../store';
  import { useHelper } from '../../../../composeable/Helper';
  import CarrierService from '../../../../services/carrier.service';
  import { ICountry } from '../../../../model/country.model';
  import { ICarrier, IShippingProduct, IWeightClass } from '../../../../model/carrier.model';
  import * as _ from 'lodash-es';
  import { ITemplate } from '../../../../model/template.model';
  import { ICreateNewShipmentForm, IParcelShop, IShipment } from '../../../../model/shipment.model';
  import { inRange } from 'lodash-es';
  import { IPrinter } from '../../../../model/printer.model';
  //import {ICustomer} from "../../model/customer.model";

  const $store = useStore();
  const { findLocalMoney, getShippingCharges, denmark } = useHelper();
  let windowRef: Window | null;

  interface Props {
    fulfillment: IFulfillment[];
    order: IOrder;
    isCancelled?: boolean;
    fulfillmentItems: IOrderProduct[];
  }

  const isOrderFulfillmentRefundModalOpen = ref<boolean>(false);

  const findTotalLocalMoney = (unfulfilled: any) => {
    let itemPrice = findLocalMoney(unfulfilled.price_set);
    let totalPrice = Number(itemPrice.amount ?? 0) * Number(unfulfilled.quantity);

    let final = PriceService.formatInternationalPrice({
      currency: itemPrice.currency,
      amount: totalPrice,
    });
    return final;
  };

  const props = withDefaults(defineProps<Props>(), {
    isCancelled: false,
  });

  /** carriers from manual shipment creation **/
  const carriers = ref<ICarrier[]>([]);
  const productWithClientIds = ref<string>('{}');

  /** this return_shipment_label generated when end user request for return from external return portal **/
  const return_shipment_label = computed<string | null>(() => {
    let shipping_label = null;
    const shipment = shipmentFromReturnOrder.value;
    if (shipment) {
      shipping_label = shipment.label;
    }
    return shipping_label;
  });

  const shipmentFromReturnOrder = computed<IShipment | null>(() => {
    const return_orders = props.order.return_orders;
    let shipment = null;
    if (return_orders && return_orders.length) {
      const fulfillment = props.fulfillment[0];
      if (fulfillment && fulfillment.return_order_code) {
        shipment =
          return_orders.find((item) => item.return_order_code == fulfillment.return_order_code)
            ?.shipment ?? null;
      }
    }
    return shipment;
  });

  const shipping_label = computed(() => {
    /** if shipment is not done from return portal, means manual shipment created **/
    let shipping_label = null;
    const shipment: IShipment | null = props.fulfillment[0]?.shipment;
    if (shipment) {
      shipping_label = shipment.label;
    }
    return shipping_label;
  });
  const onClickDownloadShippingLabel = async () => {
    windowRef = WindowService.openWindow('Shipment Label Preview');
    try {
      if (windowRef && (return_shipment_label.value || shipping_label.value)) {
        const blob: Blob = await HelperService.toBlob(
          `data:application/pdf;base64,${encodeURI(
            return_shipment_label.value ? return_shipment_label.value : shipping_label.value ?? '',
          )}`,
        );
        windowRef.location = URL.createObjectURL(blob);
      }
    } catch (e) {
      notificationService.showError('Failed to download shipping label.');
    }
  };
  const shippingChargesAvailableToRefund = computed(() => {
    return (
      getShippingCharges(props.order).amount.toFixed(2) > 0 &&
      !props.order?.discount_applications?.some((item) => item.target_type === 'shipping_line')
    );
  });

  const checkRefunded = computed<boolean>(() => {
    const hasUnrefundedItems = props.fulfillment.some((item: IFulfillment) => item.refunded !== 1);
    const allReturnedMatchQuantity = props.fulfillment.some(
      (item: IFulfillment) => item.returned_quantity != item.quantity,
    );
    console.log('Some returned ', hasUnrefundedItems);
    console.log('All returned ', allReturnedMatchQuantity);
    console.log('Shipping Charge Amount ', shippingChargesAvailableToRefund.value);
    /** on request of QA and Laravel Dev this line is commented */
    //  return shouldRefund && props.order.financial_status == 'paid';
    return hasUnrefundedItems || allReturnedMatchQuantity;
  });

  const zeroItemReturned = computed(() => {
    return props.fulfillment.every((item: IFulfillment) => item.returned_quantity == 0);
  });

  const checkRefundStatus = () => {
    let flag = false;

    props.fulfillment.forEach((item: IFulfillment) => {
      if (item.refunded === 1) {
        flag = true;
      }
    });

    return flag;
  };

  /*const countReturned = (index: number) => {
    const fulfillment = props.fulfillment[index];
    return fulfillment.refunded;
    /!*let count = 0;
    props.fulfillment.forEach((item: IFulfillment) => {
      if (item.refunded === 1) {
        count += 1;
      }
    });
    return count;*!/
  }*/

  const emit = defineEmits(['refresh-order', 'close']);
  const isShipmentModelOpen: Ref<boolean> = ref(false);

  const shipment = computed<IShipment | null>(() => {
    if (props.fulfillment[0].shipment) {
      return props.fulfillment[0].shipment;
    } else if (shipmentFromReturnOrder.value) {
      return shipmentFromReturnOrder.value;
    }
    return null;
  });

  const carrier = computed(() => {
    if (!shipment.value) {
      return null;
    }

    return CarrierService.getCarrierFromShippingProductCode(
      $store.state.courierStore.carriers,
      $store.state.courierStore.carrierProducts,
      shipment.value.carrier_product_code,
    );
  });

  const onCreateRefund = async () => {
    isOrderFulfillmentRefundModalOpen.value = true;
  };

  const refreshOrder = () => {
    emit('refresh-order');
  };

  const onClickPrintLabel = async (printer: IPrinter, fulfillmentCode: number | null) => {
    try {
      loadingService.startLoading('main-loader:print-label');
      let label: string | null = null;
      if (fulfillmentCode) {
        const response = await orderService.downloadLabelForFulfillmentsShipment(fulfillmentCode);
        label = response.label ?? null;
      } else if (return_shipment_label.value || shipping_label.value) {
        label = return_shipment_label.value || shipping_label.value;
      }
      if (!label) {
        throw new Error('No label found');
      }
      await printJobService.queuePrintJob({
        company_code: $store.state.companyStore.company_code,
        document_base64: label,
        format: printer.format,
        reference: `Shipment #${shipment.value?.shipment_code}`,
        printer: printer.linked_printer,
        printer_app_code: printer.printer_app_code,
      });
      notificationService.showSuccess('Print job queued successfully');
    } catch (e) {
      loggingService.error('Error printing label', e);
      notificationService.showError('Error printing label');
    } finally {
      loadingService.stopLoading('main-loader:print-label');
    }
  };
  const getCarriers = async (senderCountry: number, receiverCountry: number) => {
    try {
      loadingService.startLoading('main-loader:carriers');
      const response = await carrierService.getShippingProducts(senderCountry, receiverCountry);
      carriers.value = _.get(response, 'carriers', []);
      productWithClientIds.value = _.get(response, 'productWithClientIds', []) as any;
    } catch (e) {
      notificationService.showError('Error getting carriers');
      loggingService.error('Error getting carriers', e);
    } finally {
      loadingService.stopLoading('main-loader:carriers');
    }
  };

  const getWeightClasses = async (delivery_template: ITemplate, carrier_product_code: number) => {
    try {
      loadingService.startLoading('main-loader:weight-classes');
      return await shipmentService.getWeightClasses(
        carrier_product_code,
        delivery_template.sender_country_code,
        delivery_template.receiver_country_code,
        delivery_template.ubsend_client_id as number,
      );
    } finally {
      loadingService.stopLoading('main-loader:weight-classes');
    }
  };
  const onClickCreateShipment = async (order: IOrder) => {
    const delivery_template = order.delivery_template as ITemplate;
    if (delivery_template.receiver_country_code != denmark.country_code) {
      notificationService.showError(
        'Creating a shipment between the selected destinations is not possible',
      );
      return;
    }
    if (order.return_template_code === null) {
      notificationService.showError(
        `Return shipping rule is not set for order ${props.order.order_number}. Please set a return shipping rule before attempting to create a shipment.`,
      );
      return;
    }
    try {
      if (delivery_template != null) {
        /** its like continue from draft **/
        await getCarriers(
          delivery_template.sender_country_code,
          delivery_template.receiver_country_code,
        );

        const senderCountry = $store.state.countryStore.countries.find(
          (c: ICountry) => c.country_code === delivery_template.sender_country_code,
        );
        const receiverCountry = $store.state.countryStore.countries.find(
          (c: ICountry) => c.country_code === delivery_template.sender_country_code,
          /** sender and receiver will be same for now **/
        );
        let carrier: ICarrier | null = CarrierService.getCarrierFromShippingProductCode(
          $store.state.courierStore.carriers,
          $store.state.courierStore.carrierProducts,
          delivery_template.carrier_product_code,
        );
        carrier = carriers.value.find(
          (item) => item.carrier_code == carrier?.carrier_code,
        ) as ICarrier;

        /** Show Shop Return Service as selected in products **/
        const shippingProduct: IShippingProduct | undefined = carrier.products.find(
          (product: IShippingProduct) => {
            return product.name.toLowerCase().includes('return');
          },
        );

        /*const shippingProduct: IShippingProduct | undefined =
          $store.state.courierStore.carrierProducts.find(
            (p) => p.carrier_product_code === delivery_template.carrier_product_code,
          );*/

        if (!carrier || !senderCountry || !receiverCountry) {
          notificationService.showError('Carrier not found');
          return;
        }
        if (!shippingProduct) {
          notificationService.showError('Carrier product not found');
          return;
        }
        const weightClasses: IWeightClass[] = await getWeightClasses(
          delivery_template,
          shippingProduct.carrier_product_code,
        );

        /*let servicePoint: IParcelShop | null = null;

        if (row.recipient.parcelShopId) {
          servicePoint = await shipmentService.getParcelShop(
            row.recipient.parcelShopId,
            carrier.carrier_id,
          );
        }*/

        /** this is commenting because only carrier e-email will be selected for additional services **/
        /* const additionalServices = await getAdditionalServices(
           shippingProduct as IShippingProduct,
           senderCountry as ICountry,
           receiverCountry as ICountry,
         );
         console.log("Additional Services1 ", JSON.stringify(additionalServices))
         const additionalServicesForCreateShipmentData: { [key: number]: boolean } = {};
         if (delivery_template.additional_services) {
           delivery_template.additional_services?.forEach((asChargeCode) => {
             const additionalService = additionalServices.find(
               (as) => as.as_charge_code === asChargeCode,
             );
             if (!additionalService) return;
             additionalServicesForCreateShipmentData[additionalService.as_charge_code] = true;
           });
         }*/

        /** get parcels, it is customized **/
        //  await ShipmentService.parcelToFormParcel(row.parcels, weightClasses)
        const fulfillmentItems = props.fulfillmentItems;
        let weight = 0;
        fulfillmentItems.forEach((fulfilledItem) => {
          if (fulfilledItem.product) {
            let weightInGrams = fulfilledItem.grams
              ? fulfilledItem.product?.weight
              : Number(fulfilledItem.product?.weight) * 1000;
            weight += Number(weightInGrams) * fulfilledItem.quantity;
          }
        });
        let parcelWeightClass = weightClasses.find((weightClass) =>
          inRange(
            weight / 1000,
            parseFloat(weightClass.min_weight.toString()),
            parseFloat(weightClass.max_weight.toString()) + 0.1,
          ),
        ) as IWeightClass;
        if (!parcelWeightClass) {
          parcelWeightClass = weightClasses[0];
        }
        //const customer = order.customer as ICustomer;
        const company = $store.state.companyStore;
        const shipping_address = order.shipping_address;
        await $store.dispatch('shipmentStore/setCreateShipmentData', {
          one: {
            sender: {
              senderCountry: senderCountry,
              name: `${shipping_address?.name}`,
              email: shipping_address.email,
              phone: shipping_address.phone,
              zipCode: shipping_address.zip,
              address1: shipping_address.address1,
              address2: shipping_address.address2 || null,
              city: shipping_address.city,
            },
            recipient: {
              receiverCountry: receiverCountry,
              name: company.company_name,
              email: $store.state.userStore.email,
              phone: company.phone_no,
              zipCode: company.zipcode,
              address1: company.address,
              address2: null,
              city: company.city,
            },
          },
          two: {
            carrier: carrier,
            shippingProduct: shippingProduct,
            weightClass: parcelWeightClass ?? null,
            isChooseClosestServicePointChecked: true,
            servicePoint: null,
          },
          three: {
            additionalServices: {},
          },
          four: { isTermsAndConditionsChecked: false, payWith: 0 },
        } as ICreateNewShipmentForm);

        /*  await $store.dispatch('shipmentStore/setCreateShipmentData', {
          one: {
            senderCountry,
            receiverCountry,
            carrier,
            shippingProduct,
            shippingAgreement: 'Shipvagoo',
            //additionalServices: additionalServicesForCreateShipmentData,
            parcels: [{
              id: uuid(),
              quantity: 1,
              weightClass: parcelWeightClass
            }],
          },
          two: {
            name: `${shipping_address?.name}`,
            email: shipping_address.email,
            phone: shipping_address.phone,
            zipCode: shipping_address.zip,
            address1: shipping_address.address1,
            address2: shipping_address.address2 || null,
            city: shipping_address.city,
          },
          three: {
            name: company.company_name,
            email: $store.state.userStore.email,
            phone: company.phone_no,
            zipCode: company.zipcode,
            address1: company.address,
            address2: null,
            city: company.city,
            isChooseClosestServicePointChecked: true,
          },

          four: {isTermsAndConditionsChecked: true, payWith: 0},
        } as ICreateShipmentForm);*/
        await $store.dispatch(
          'shipmentStore/setFulfillmentCode',
          props.fulfillment[0].fulfillment_code,
        );
        await $store.dispatch('shipmentStore/openCreateShipmentModal');
      } else {
        throw new Error('Delivery template does not exist.');
      }
    } catch (e) {
      notificationService.showError('Error while creating shipment.');
    }
  };

  /*const onClickCreateShipment = async (orderCode: number, fulfillmentCode: number) => {
    if (props.order.return_template_code === null) {
      notificationService.showError(
        `Return delivery template is not set for order ${props.order.order_number}. Please set a return delivery template before attempting to create a shipment.`,
      );
      return;
    }

    // if (!props.order.shipping_address.first_name) {
    //   notificationService.showError('Recipient first name is missing');
    // }

    // if (!props.order.shipping_address.last_name) {
    //   notificationService.showError('Recipient last name is missing');
    // }

    // if (!props.order.shipping_address.city) {
    //   notificationService.showError('Recipient city is missing');
    // }

    // if (!props.order.shipping_address.country_code) {
    //   notificationService.showError('Recipient country is missing');
    // }

    // if (!props.order.shipping_address.zip) {
    //   notificationService.showError('Recipient zip code is missing');
    // }

    // if (!props.order.shipping_address.address1) {
    //   notificationService.showError('Recipient address1 is missing');
    // }

    // if (!props.order.shipping_address.phone) {
    //   notificationService.showError('Recipient phone is missing');
    // }

    // if (!props.order.shipping_address.email) {
    //   notificationService.showError('Recipient email is missing');
    // }

    // if (
    //   !props.order.shipping_address.first_name ||
    //   !props.order.shipping_address.last_name ||
    //   !props.order.shipping_address.city ||
    //   !props.order.shipping_address.country_code ||
    //   !props.order.shipping_address.zip ||
    //   !props.order.shipping_address.address1 ||
    //   !props.order.shipping_address.phone ||
    //   !props.order.shipping_address.email
    // ) {
    //   notificationService.showError('There are missing fields in the shipping address. Please fill in all the fields before attempting to create a shipment.');
    //   return;
    // }

    try {
      loadingService.startLoading('main-loader:create-shipment-from-fulfillment');
      await orderService.createShipmentFromFulfillment(orderCode, fulfillmentCode);
      await orderService.generateLabelForFulfillmentsShipment(fulfillmentCode);
      $store.dispatch('companyStore/syncCompanyData');

      if (props.order.fulfillments) {
        const [fulfillment] = props.order.fulfillments.filter(
          (f) => f.fulfillment_code === fulfillmentCode,
        );

        if (fulfillment) {
          notificationService.showSuccess(
            `Shipment created successfully for fulfillment ${fulfillment.name}`,
          );
        }
      }

      refreshOrder();
    } catch (e) {
      loggingService.error('Error while creating shipment from fulfillment', e);
      notificationService.showError('Error while creating shipment from fulfillment');
    } finally {
      loadingService.stopLoading('main-loader:create-shipment-from-fulfillment');
    }
  };*/

  const onClickCancelFulfillment = async (fulfillmentCode: number) => {
    if (!fulfillmentCode) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:cancel-fulfillment');
      await orderService.cancelFullfillment(props.order.order_code, fulfillmentCode);
      notificationService.showSuccess('Fulfillment cancelled successfully');
      refreshOrder();
    } catch (e) {
      loggingService.error('Error while canceling fulfillment', e);
      notificationService.showError('Error while canceling fulfillment');
    } finally {
      loadingService.stopLoading('main-loader:cancel-fulfillment');
    }
  };

  /*const onClickCancelFulfillmentAndShipment = async (
    fulfillmentCode: number,
    shipmentCode: number | null,
  ) => {
    if (!fulfillmentCode || !shipmentCode) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:cancel-fulfillment-and-shipment');
      await orderService.cancelFullfillment(props.order.order_code, fulfillmentCode);
      await shipmentService.cancelShipment(shipmentCode, 'Cancel fulfillment');
      $store.dispatch('companyStore/syncCompanyData');
      notificationService.showSuccess('Fulfillment and shipment cancelled successfully');
      refreshOrder();
    } catch (e) {
      loggingService.error('Error while canceling fulfillment and shipment', e);
      notificationService.showError('Error while canceling fulfillment and shipment');
    } finally {
      loadingService.stopLoading('main-loader:cancel-fulfillment-and-shipment');
    }
  };*/

  const onClickShipmentDetail = async (shipmentCode: number | null) => {
    console.log('ShipmentCode ', shipmentCode);
    if (!shipmentCode) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:shipment-detail');
      isShipmentModelOpen.value = true;
    } catch (e) {
      loggingService.error('Error while getting shipment detail', e);
      notificationService.showError('Error while getting shipment detail');
    } finally {
      loadingService.stopLoading('main-loader:shipment-detail');
    }
  };

  const downloadLabel = async (fulfillmentCode: number) => {
    if (!fulfillmentCode) {
      return false;
    }

    try {
      loadingService.startLoading('main-loader:download-pdf');
      const response = await orderService.downloadLabelForFulfillmentsShipment(fulfillmentCode);

      if (response.label) {
        windowRef = WindowService.openWindow('Shipment Label Preview');
        const blob: Blob = await HelperService.toBlob(
          `data:application/pdf;base64,${encodeURI(response.label)}`,
        );
        windowRef.location = URL.createObjectURL(blob);
        return true;
      } else {
        return false;
      }
    } catch (error) {
      if (windowRef) {
        windowRef.close();
      }
      loggingService.error('Error while downloading PDF', error);
      notificationService.showError('Error while downloading PDF');
    } finally {
      loadingService.stopLoading('main-loader:download-pdf');
      windowRef = null;
    }
  };

  const onClickDownloadLabel = async (fulfillmentCode: number | null) => {
    if (fulfillmentCode) {
      const result = await downloadLabel(fulfillmentCode);
      if (!result) {
        await onClickDownloadShippingLabel();
      }
    } else if (shipment.value?.label) {
      await onClickDownloadShippingLabel();
    }
  };
  const onClickContinueFromDraft = async (row: IShipment | null) => {
    try {
      if (row == null) {
        return new Error('Shipment not found');
      }
      // console.log("Countries ",JSON.stringify($store.state.countryStore.countries))
      loadingService.startLoading('main-loader:continue-from-draft');
      await getCarriers(row.sender_country_code, row.receiver_country_code);
      const senderCountry = $store.state.countryStore.countries.find(
        (c: ICountry) => c.country_code === row.sender_country_code,
      );
      const receiverCountry = $store.state.countryStore.countries.find(
        (c: ICountry) => c.country_code === row.receiver_country_code,
      );
      const carrier: ICarrier | null = CarrierService.getCarrierFromShippingProductCode(
        $store.state.courierStore.carriers,
        $store.state.courierStore.carrierProducts,
        row.carrier_product_code,
      );

      const shippingProduct: IShippingProduct | undefined =
        $store.state.courierStore.carrierProducts.find(
          (p) => p.carrier_product_code === row.carrier_product_code,
        );

      let ubsend_client_id = null;
      if (row.delivery_template_code) {
        const deliverTemplate = await templateService.getTemplate(row.delivery_template_code);
        ubsend_client_id = deliverTemplate.ubsend_client_id;
        console.log('ShippingProduct ', shippingProduct);
        console.log('ubSendClientID: ', ubsend_client_id);
      } else {
        const product_with_client_ids = JSON.parse(JSON.stringify(productWithClientIds.value));
        console.log('Client IDs: ', product_with_client_ids);
        ubsend_client_id =
          product_with_client_ids[shippingProduct!.product_id] !== undefined
            ? product_with_client_ids[shippingProduct!.product_id]
            : '';
        console.log('Client iD: ', ubsend_client_id);
      }
      /*  if (!ubsend_client_id) {
        notificationService.showError('Ubsend client id not found');
        return;
      } */

      let weightClasses: IWeightClass[] = [];
      if (ubsend_client_id) {
        weightClasses = await shipmentService.getWeightClasses(
          row.carrier_product_code,
          row.sender_country_code,
          row.receiver_country_code,
          ubsend_client_id,
        );
      }
      const parcel = row.parcels[0];
      let parcelWeightClass = weightClasses.find((weightClass) =>
        inRange(
          parcel.weight ?? 1,
          parseFloat(weightClass.min_weight.toString()),
          parseFloat(weightClass.max_weight.toString()) + 0.1,
        ),
      ) as IWeightClass;
      if (!parcelWeightClass) {
        parcelWeightClass = weightClasses[0];
      }
      /*** end here ***/
      if (!carrier || !shippingProduct || !senderCountry || !receiverCountry) {
        notificationService.showError('Carrier not found');
        return;
      }

      let servicePoint: IParcelShop | null = null;

      if (row.recipient.parcelShopId) {
        servicePoint = await shipmentService.getParcelShop(
          row.recipient.parcelShopId,
          carrier.carrier_id,
        );
      }

      const additionalServices = await getAdditionalServices(
        shippingProduct,
        senderCountry,
        receiverCountry,
      );

      const additionalServicesForCreateShipmentData: { [key: number]: boolean } = {};

      if (row.additional_services) {
        row.additional_services.forEach((asChargeCode) => {
          const additionalService = additionalServices.find(
            (as) => as.as_charge_code === asChargeCode,
          );

          if (!additionalService) return;

          additionalServicesForCreateShipmentData[additionalService.as_charge_code] = true;
        });
      }
      /*await $store.dispatch('shipmentStore/setCreateShipmentData', {
        one: {
          senderCountry,
          receiverCountry,
          carrier,
          shippingProduct,
          shippingAgreement: 'Shipvagoo',
          additionalServices: additionalServicesForCreateShipmentData,
          parcels: ShipmentService.parcelToFormParcel(row.parcels, weightClasses), // use weight classes variable
        },
        two: {
          name: `${row.sender.firstName} ${row.sender.lastName}`,
          email: row.sender.contactInfo.email,
          phone: row.sender.contactInfo.telephone,
          zipCode: row.sender.postCode,
          address1: row.sender.addressLine1,
          address2: row.sender.addressLine2 || null,
          city: row.sender.city,
        },
        three: {
          name: props.order.shipping_address?.name,//`${row.recipient.firstName} ${row.recipient.lastName}`,
          email: props.order.shipping_address?.email, //row.recipient.contactInfo.email,
          phone: props.order.shipping_address?.phone, //row.recipient.contactInfo.telephone,
          zipCode: props.order.shipping_address?.zip,//row.recipient.postCode,
          address1: props.order.shipping_address?.address1,//row.recipient.addressLine1,
          address2: props.order.shipping_address?.address2, //row.recipient.addressLine2 || null,
          city: props.order.shipping_address?.city,//row.recipient.city,
          isChooseClosestServicePointChecked: !servicePoint,
          servicePoint,
        },
        four: {isTermsAndConditionsChecked: false, payWith: 0},
      } as ICreateShipmentForm);*/
      await $store.dispatch('shipmentStore/setCreateShipmentData', {
        one: {
          sender: {
            senderCountry: senderCountry,
            name: `${row.sender.firstName} ${row.sender.lastName}`,
            email: row.sender.contactInfo.email,
            phone: row.sender.contactInfo.telephone,
            zipCode: row.sender.postCode,
            address1: row.sender.addressLine1,
            address2: row.sender.addressLine2 || null,
            city: row.sender.city,
          },
          recipient: {
            receiverCountry: receiverCountry,
            name: `${row.recipient.firstName} ${row.recipient.lastName}`,
            email: row.recipient.contactInfo.email,
            phone: row.recipient.contactInfo.telephone,
            zipCode: row.recipient.postCode,
            address1: row.recipient.addressLine1,
            address2: row.recipient.addressLine2 || null,
            city: row.recipient.city,
          },
        },
        two: {
          carrier: carrier,
          shippingProduct: shippingProduct,
          weightClass: parcelWeightClass ?? null,
          isChooseClosestServicePointChecked: !servicePoint,
          servicePoint: servicePoint,
        },
        three: {
          additionalServices: additionalServicesForCreateShipmentData,
        },
        four: { isTermsAndConditionsChecked: false, payWith: 0 },
      } as ICreateNewShipmentForm);
      await $store.dispatch('shipmentStore/setShipmentCode', row.shipment_code);
      await $store.dispatch('shipmentStore/openCreateShipmentModal');
    } catch (e) {
      console.log(e);
      notificationService.showError('Error continuing from draft');
      loggingService.error('Error continuing from draft', e);
    } finally {
      loadingService.stopLoading('main-loader:continue-from-draft');
    }
  };

  const getAdditionalServices = async (
    shippingProduct: IShippingProduct,
    senderCountry: ICountry,
    receiverCountry: ICountry,
  ) => {
    return shipmentService.getAdditionalServiceCharges(
      senderCountry.country_code,
      receiverCountry.country_code,
      shippingProduct.carrier_product_code,
    );
  };
</script>

<style>
  .fulfillment_container + .fulfillment_container {
    margin-top: 20px;
  }

  .weight-div {
    margin-top: 10px;
  }

  .order_detail-order_item {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 10px 20px;
  }
</style>
