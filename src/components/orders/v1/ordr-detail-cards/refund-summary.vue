<template>
  <div class="summary-box">
    <p class="text-sixteen text-bold no-padding no-margin">Summary</p>
    <shipvagoo-separator class="q-my-sm"></shipvagoo-separator>
    <div class="text-thirteen summary-item q-mt-sm">
      <span>Item subtotal</span>
      <span>{{ summary.itemSubtotal }} </span>
    </div>
    <div class="text-thirteen summary-item q-mt-sm">
      <span>Shipping</span>
      <span>{{ summary.shippingCost }}</span>
    </div>
    <div class="text-thirteen summary-item q-mt-sm">
      <span>Shipping tax</span>
      <span>{{ summary.tax_amount }}</span>
    </div>
    <div class="summary-refund text-thirteen q-pt-xl q-mb-sm">
      <span><strong>Refund total</strong></span>
      <span
        ><strong>{{ summary.refundTotal }} </strong></span
      >
    </div>

    <shipvagoo-separator class="q-mt-lg"></shipvagoo-separator>

    <shipvagoo-button
      :disabled="summary.disabled"
      class="refund-button q-mt-sm"
      @click="handleRefund"
      >Refund {{ summary.refundTotal }}
    </shipvagoo-button>
    <div class="refund-note">{{ summary.availableForRefund }} available for refund</div>
    <div class="flex text-fourteen text-danger q-mt-sm" v-if="summary.disabled">
      <q-icon color="danger" name="error" style="font-size: 16px"></q-icon>
      <span class="q-ml-xs">Cannot refund more than available</span>
    </div>
  </div>
</template>

<script setup lang="ts">
  interface IProps {
    summary: {
      itemSubtotal: string;
      shippingCost: string;
      refundTotal: string;
      availableForRefund: string;
      disabled: boolean;
      tax_amount: string;
    };
  }

  defineProps<IProps>();
  const emit = defineEmits(['create']);

  const handleRefund = () => {
    emit('create');
  };
</script>

<style scoped>
  .summary-box {
    border: 1px solid #7d839833;
    padding: 16px;
    border-radius: 4px;
    max-width: 400px;
    background-color: #f7f7f7;
    font-family: Arial, sans-serif;
  }

  .summary-item,
  .summary-refund {
    display: flex;
    justify-content: space-between;
  }

  .summary-refund {
    margin-top: 8px;
    font-size: 1.1em;
  }

  .refund-button {
    background-color: #4c1d95;
    color: #fff;
    border: none;
    padding: 10px;
    width: 100%;
    border-radius: 6px;
    font-size: 1em;
    cursor: pointer;
    margin: 12px 0;
  }

  .refund-button:hover {
    background-color: #3b0f70;
  }

  .refund-note {
    font-size: 0.9em;
    color: #212121;
  }
</style>
