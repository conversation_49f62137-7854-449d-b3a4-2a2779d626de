<template>
  <div
    v-bind="$attrs"
    :key="`${fulfillment[0].fulfillment_code}-${fulfillment[0].updated_at}`"
    class="order-detail-box-card dark-text"
  >
    <div class="full-width q-py-sm q-px-md flex justify-between items-center">
      <div class="flex items-center">
        <shipvagoo-status-tag
          v-if="isCancelled"
          text="Cancelled"
          bg-color="#FEECA1"
          color="#8E7200"
        />
        <shipvagoo-status-tag v-else text="Fulfilled" bg-color="#BEF3D7" color="#0C9247" />

        <div class="text-thirteen text-weight-7 q-ml-md">
          {{ fulfillment[0].name }}
          <template v-if="fulfillment[0].packaging">
            - {{ fulfillment[0].packaging.name }}
          </template>
        </div>
        <img
          v-if="carrier"
          :src="carrier?.icon"
          class="q-ml-md"
          height="20"
          width="20"
          decoding="async"
        />
      </div>
      <div class="text-thirteen">
        {{ !isCancelled ? 'Fulfilled:' : '' }}
        {{ dayjs(fulfillment[0].created_at).format('DD/MM/YYYY') }} -
        {{ dayjs(fulfillment[0].created_at).format('HH:mm') }}
      </div>
    </div>
    <shipvagoo-separator />
    <div
      v-for="(fulfilledItem, index) of fulfillmentItems"
      :key="`${fulfilledItem.line_item_code}-${fulfilledItem.quantity}-${fulfilledItem.updated_at}`"
    >
      <div class="order_detail-order_item">
        <div
          v-if="fulfilledItem.variant"
          class="flex items-center q-py-sm"
          style="min-height: 90px"
        >
          <div class="flex items-center">
            <img
              v-if="fulfilledItem.variant?.variant_image"
              decoding="async"
              class="order-detail-product-img"
              :src="fulfilledItem.variant?.variant_image"
            />
          </div>
          <div class="q-pl-lg flex items-center">
            <div>
              <div class="text-thirteen text-weight-7">
                {{ fulfilledItem?.title }}
              </div>
              <div class="text-eleven">
                <div class="q-mt-xs" v-if="fulfilledItem.variant?.title != 'Default Title'">
                  <span>Variant: </span>
                  {{ fulfilledItem.variant?.title ?? '' }}
                </div>
                <div class="q-mt-xs" v-if="fulfilledItem.variant?.weight">
                  <span>Weight: </span
                  >{{
                    `${fulfilledItem.variant?.weight} ${fulfilledItem.variant?.weight_unit ?? ''}`
                  }}
                </div>
                <div class="q-mt-xs" v-if="fulfilledItem.variant?.sku">
                  <span>SKU: </span>
                  {{ `${fulfilledItem.variant?.sku ?? ''}` }}
                </div>
                <div v-if="isCancelled">
                  <div class="q-mt-xs"><span>Reason:</span> {{ fulfillment[0]?.reason }}</div>
                  <div class="q-mt-xs"><span>Comment:</span> {{ fulfillment[0]?.comment }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="flex items-center q-py-sm" style="min-height: 90px">
          <div class="flex items-center">
            <img
              v-if="fulfilledItem.product?.image_url"
              decoding="async"
              class="order-detail-product-img"
              :src="fulfilledItem.product.image_url"
            />
          </div>
          <div class="q-pl-lg flex items-center">
            <div>
              <div class="text-thirteen text-weight-7">{{ fulfilledItem.product?.title }}</div>
              <div class="text-eleven q-mt-xs">
                <span>Weight: </span>{{ fulfilledItem.product?.weight }}
                {{ fulfilledItem.grams ? 'g' : 'kg' }}
              </div>
              <div class="text-eleven q-mt-xs" v-if="fulfilledItem.product?.sku">
                <span>SKU: </span>
                {{ `${fulfilledItem.product?.sku}` }}
              </div>

              <div class="text-eleven" v-if="isCancelled">
                <div class="q-mt-xs"><span>Reason:</span> {{ fulfillment[0]?.reason }}</div>
                <div class="q-mt-xs"><span>Comment:</span> {{ fulfillment[0]?.comment }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="orders_page-modal-order_modal-fulfillments-header-prices flex items-center">
          <div>
            {{
              PriceService.formatInternationalPrice(findLocalMoney(fulfilledItem.price_set) || 0)
            }}
            <span class="q-mx-sm">x</span>
            {{ fulfilledItem.quantity }}
          </div>
          <div class="text-weight-7">
            {{ findTotalLocalMoney(fulfilledItem) }}
          </div>
        </div>
      </div>
      <div class="q-mx-md" v-if="index < fulfillmentItems.length - 1">
        <shipvagoo-separator />
      </div>
    </div>
    <template v-if="!isCancelled">
      <shipvagoo-separator />
      <shipvagoo-card-actions>
        <shipvagoo-button
          v-if="!fulfillment[0].shipment_code"
          @click="onClickCancelFulfillment(fulfillment[0].fulfillment_code)"
          label="Cancel fulfillment"
          outline
        >
        </shipvagoo-button>

        <shipvagoo-button
          v-if="!fulfillment[0].shipment_code"
          @click="onClickCreateShipment(order.order_code, fulfillment[0].fulfillment_code)"
          label="Create shipment"
          outline
        >
        </shipvagoo-button>

        <shipvagoo-button-dropdown-v1
          style="min-width: 80px !important"
          color="#243E90"
          bg-color="#FFFFFF"
          dropdown-icon="fas fa-caret-down"
          label="More"
          v-if="fulfillment[0].shipment_code && fulfillment[0].shipment"
          outline
          no-caps
        >
          <shipvagoo-list>
            <shipvagoo-menu-item
              v-close-popup
              clickable
              @click="onClickCancelFulfillment(fulfillment[0].fulfillment_code)"
            >
              Cancel fulfillment
            </shipvagoo-menu-item>
            <shipvagoo-separator />
            <shipvagoo-menu-item
              v-if="fulfillment[0].fulfillment_code && showCreateReturnButton"
              v-close-popup
              clickable
              @click="onClickCreateReturn"
            >
              Create return
            </shipvagoo-menu-item>
          </shipvagoo-list>
        </shipvagoo-button-dropdown-v1>
        <shipvagoo-button
          style="height: 40px"
          v-if="fulfillment[0].shipment_code"
          @click="
            fulfillment[0].shipment?.label
              ? onClickShipmentDetail(fulfillment[0].shipment_code)
              : onClickContinueFromDraft(fulfillment[0].shipment)
          "
          :label="
            fulfillment[0].shipment?.label ? 'Shipment details' : 'Continue shipment from draft'
          "
          outline
        >
        </shipvagoo-button>
        <shipvagoo-button-dropdown-v1
          color="#243E90"
          bg-color="#FFFFFF"
          dropdown-icon="fas fa-caret-down"
          label="Packing list"
          v-if="fulfillment[0].shipment_code && fulfillment[0].shipment"
          outline
          no-caps
        >
          <shipvagoo-list>
            <shipvagoo-separator />
            <shipvagoo-menu-item
              v-close-popup
              clickable
              @click="onClickDownloadPackingList(order.order_code)"
            >
              Download packing list
            </shipvagoo-menu-item>
            <shipvagoo-menu-item clickable has-caret>
              Print packing list at

              <shipvagoo-menu anchor="top end" style="width: 160px">
                <shipvagoo-menu-item v-if="$store.state.printerStore.printers.length <= 0">
                  {{ $t('common.no_printer_is_set') }}
                </shipvagoo-menu-item>
                <shipvagoo-menu-item
                  v-for="printer in $store.state.printerStore.printers"
                  :key="`printer-${printer.printer_code}`"
                  clickable
                  @click="onClickPrintPackingSlip(printer, order.order_code)"
                >
                  {{ printer.name }}
                </shipvagoo-menu-item>
              </shipvagoo-menu>
            </shipvagoo-menu-item>
          </shipvagoo-list>
        </shipvagoo-button-dropdown-v1>
        <shipvagoo-button-dropdown-v1
          class="q-mr-sm"
          color="#243E90"
          bg-color="#FFFFFF"
          dropdown-icon="fas fa-caret-down"
          label="Shipping label"
          v-if="
            fulfillment[0].shipment_code &&
            fulfillment[0].shipment &&
            fulfillment[0].shipment?.label
          "
          outline
          no-caps
        >
          <shipvagoo-list>
            <shipvagoo-menu-item
              v-if="fulfillment[0].shipment_code && fulfillment[0].shipment?.label"
              v-close-popup
              clickable
              @click="onClickDownloadLabel(shipment?.fulfillment_code ?? null)"
            >
              Download shipping label
            </shipvagoo-menu-item>
            <shipvagoo-menu-item clickable has-caret>
              Print shipping label at
              <shipvagoo-menu anchor="top end" style="width: 160px">
                <shipvagoo-menu-item v-if="$store.state.printerStore.printers.length <= 0">
                  {{ $t('common.no_printer_is_set') }}
                </shipvagoo-menu-item>
                <shipvagoo-menu-item
                  v-for="printer in $store.state.printerStore.printers"
                  :key="`printer-${printer.printer_code}`"
                  clickable
                  @click="onClickPrintLabel(printer, fulfillment[0].fulfillment_code)"
                >
                  {{ printer.name }}
                </shipvagoo-menu-item>
              </shipvagoo-menu>
            </shipvagoo-menu-item>
          </shipvagoo-list>
        </shipvagoo-button-dropdown-v1>
      </shipvagoo-card-actions>
    </template>
  </div>

  <q-dialog v-if="shipment" v-model="isShipmentModelOpen">
    <shipment-detail
      :shipment="shipment"
      @printLabel="(value:any)=>onClickPrintLabel(value.printer, (shipment?.fulfillment_code ?? null))"
      @downloadLabel="onClickDownloadLabel(shipment?.fulfillment_code ?? null)"
    />
  </q-dialog>
  <q-dialog v-model="isOrderFulfillmentReturnModalOpen">
    <order-fulfillment-return-modal
      :order="order"
      :fulfillment="fulfillment"
      :fulfillment-items="fulfillmentItems"
      @close="onCloseOrderFulfillmentsReturnModal"
      @on-created-fulfillments="emit('refresh-order')"
    />
  </q-dialog>
  <q-dialog v-model="isEditDeliveryMethodModalOpen" persistent>
    <change-shipment-template
      v-if="selectedShipment"
      :shipment-code="selectedShipment.shipment_code"
      :delivery-template-code="selectedShipment.delivery_template_code ?? null"
      :return-template-code="order.return_template_code"
      :shipping-address="order.shipping_address"
      :shipping-line-code="
        order.shipping_lines.length
          ? order.shipping_lines[order.shipping_lines.length - 1].code
          : null
      "
      @close="onCloseDeliveryMethodModal"
    />
  </q-dialog>
</template>
shipment

<script setup lang="ts">
  import dayjs from 'dayjs';
  import { computed, Ref, ref, onBeforeMount } from 'vue';
  import { IFulfillment } from '../../../../model/fulfillment.model';
  import { IOrder, IOrderProduct } from '../../../../model/order.model';
  import PriceService from '../../../../services/price.service';
  import { useRouter, useRoute } from 'vue-router';
  import {
    carrierService,
    loadingService,
    loggingService,
    notificationService,
    orderService,
    printJobService,
    shipmentService,
    templateService,
  } from '../../../../services/_singletons';
  import { useStore } from '../../../../store';
  import CarrierService from '../../../../services/carrier.service';
  import { useHelper } from '../../../../composeable/Helper';
  import { IPrinter } from '../../../../model/printer.model';
  import { ICreateNewShipmentForm, IParcelShop, IShipment } from '../../../../model/shipment.model';
  import { ICountry } from '../../../../model/country.model';
  import { ICarrier, IShippingProduct, IWeightClass } from '../../../../model/carrier.model';
  import * as _ from 'lodash-es';
  import { inRange } from 'lodash-es';
  import { AxiosError } from 'axios';

  const $router = useRouter();
  const $route = useRoute();
  const $store = useStore();
  const { showPdf, handleBeforeUnload, findLocalMoney, validateReceiverMobile } = useHelper();

  interface Props {
    fulfillment: IFulfillment[];
    order: IOrder;
    isCancelled?: boolean;
    fulfillmentItems: IOrderProduct[];
  }

  const findTotalLocalMoney = (unfulfilled: any) => {
    let itemPrice = findLocalMoney(unfulfilled.price_set);
    let totalPrice = Number(itemPrice.amount ?? 0) * Number(unfulfilled.quantity);

    let final = PriceService.formatInternationalPrice({
      currency: itemPrice.currency,
      amount: totalPrice,
    });
    return final;
  };

  const onCloseOrderFulfillmentsReturnModal = () => {
    isOrderFulfillmentReturnModalOpen.value = false;
    $router.replace({ name: 'Orders' });
  };
  const isOrderFulfillmentReturnModalOpen = ref<boolean>(false);
  const onClickCreateReturn = async () => {
    isOrderFulfillmentReturnModalOpen.value = true;
  };

  const props = withDefaults(defineProps<Props>(), {
    isCancelled: false,
  });
  const emit = defineEmits(['refresh-order']);
  const isShipmentModelOpen: Ref<boolean> = ref(false);
  const shippingProduct = ref<IShippingProduct | null>(null);
  const isEditDeliveryMethodModalOpen = ref<boolean>(false);
  const selectedShipment = ref<IShipment | null>(null);
  const _fulfillmentCode = ref<number | null>(null);

  const showCreateReturnButton = computed(() => {
    const returnOrderCode = $route.query.return_order_code;
    return props.fulfillmentItems.some(
      (item) =>
        item.quantity -
          (returnOrderCode ? item.returned_quantity ?? 0 : item.calculated_returned_quantity ?? 0) >
        0,
    );
  });

  /*const showCreateReturnButton = computed(() => {
    return props.fulfillmentItems.some(item => (item.quantity - (item.returned_quantity ?? 0)) > 0);
  });*/
  const shipment = computed<IShipment | null>(() => {
    if (!props.fulfillment[0]?.shipment_code || !props.fulfillment[0]?.shipment) {
      return null;
    }

    return props.fulfillment[0].shipment;
  });

  const carrier = computed(() => {
    if (!shipment.value) {
      return null;
    }

    return CarrierService.getCarrierFromShippingProductCode(
      $store.state.courierStore.carriers,
      $store.state.courierStore.carrierProducts,
      shipment.value.carrier_product_code,
    );
  });
  const carriers = ref<ICarrier[]>([]);
  const productWithClientIds = ref<string>('{}');
  const refreshOrder = () => {
    emit('refresh-order');
  };

  const setCarrierProduct = (): void => {
    const carrierProductCode = props.order.delivery_template?.carrier_product_code;
    if (carrierProductCode) {
      shippingProduct.value =
        $store.state.courierStore.carrierProducts?.find(
          (p) => p.carrier_product_code == carrierProductCode,
        ) ?? null;
    }
  };

  const onClickCreateShipment = async (orderCode: number, fulfillmentCode: number) => {
    if (!props.order.delivery_template_code) {
      return notificationService.showError(
        `Shipping rule is not set for order ${props.order.order_number}. Please set a shipping rule before attempting to create a shipment.`,
      );
    }

    const responseMessage = validateReceiverMobile(
      shippingProduct.value?.product_id,
      props.order.shipping_address.phone,
      props.order.order_number,
    );

    console.log('ResponseMessage:', responseMessage);
    console.log('montyProduct:', shippingProduct.value?.product_id);

    if (responseMessage) {
      return notificationService.showError(responseMessage);
    }

    try {
      window.addEventListener('beforeunload', handleBeforeUnload);
      loadingService.startLoading('main-loader:create-shipment-from-fulfillment');

      const response = await orderService.createShipmentFromFulfillment(orderCode, fulfillmentCode);
      if (response && response.fulfillments) {
        const fulfillment = selectedFulfillment(response.fulfillments, fulfillmentCode);
        if (fulfillment) {
          selectedShipment.value = fulfillment.shipment;
          _fulfillmentCode.value = fulfillmentCode;
        }
      }
      await generateShipmentLabel(fulfillmentCode);
    } catch (e: any) {
      handleShipmentError(e);
    } finally {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      loadingService.stopLoading('main-loader:create-shipment-from-fulfillment');
    }
  };
  const generateShipmentLabel = async (fulfillmentCode: number) => {
    try {
      loadingService.startLoading('main-loader:create-shipment-label');
      const response = await orderService.generateLabelForFulfillmentsShipment(fulfillmentCode);
      if (response && response.fulfillments) {
        $store.dispatch('companyStore/syncCompanyData');
        const fulfillment = selectedFulfillment(response.fulfillments, fulfillmentCode);
        if (fulfillment?.shipment_code) {
          notificationService.showSuccess(
            `Shipment created successfully for fulfillment ${fulfillment.name}`,
          );
        }
      }
      _fulfillmentCode.value = null;
      selectedShipment.value = null;
      console.log('Order Refreshing L480');
      emit('refresh-order');
    } catch (error) {
      if (error instanceof AxiosError) {
        if (
          error.response?.data?.error &&
          error.response?.data?.error != 'partial_shipping_rule_validation_error'
        ) {
          notificationService.showError(error.response?.data?.error);
          emit('refresh-order');
        }
        if (error.response?.data?.invalid_shipping_template) {
          isEditDeliveryMethodModalOpen.value = true;
        }
      }
    } finally {
      loadingService.stopLoading('main-loader:create-shipment-label');
    }
  };
  const selectedFulfillment = (fulfillments: IFulfillment[], fulfillmentCode: number) => {
    return fulfillments.find((f) => f.fulfillment_code === fulfillmentCode);
  };
  const handleShipmentError = (error: any) => {
    console.error('Shipment Error:', error);
    loggingService.error('Error while creating shipment from fulfillment', error);

    const errorMessage = error.response?.data?.error?.toLowerCase();
    if (errorMessage) {
      if (
        ['timeout', 'timedout', 'timed out', 'timed'].some((word) => errorMessage.includes(word))
      ) {
        return notificationService.showError('Timeout creating shipping label. Please try again.');
      }
      return notificationService.showError(errorMessage);
    }

    const fallbackMessage =
      error.response?.data?.message ||
      'Error while creating shipment from fulfillment. Please try again';
    notificationService.showError(fallbackMessage);
  };
  const onCloseDeliveryMethodModal = async (value: { isSaved: boolean }) => {
    console.log('onClose ', value);
    if (value.isSaved && _fulfillmentCode.value) {
      await generateShipmentLabel(_fulfillmentCode.value);
    }
    refreshOrder();
    isEditDeliveryMethodModalOpen.value = false;
  };

  const onClickCancelFulfillment = async (fulfillmentCode: number) => {
    if (!props.order.fulfillments) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:cancel-fulfillment');
      await orderService.cancelFullfillment(props.order.order_code, fulfillmentCode);
      notificationService.showSuccess('Fulfillment cancelled successfully');
      refreshOrder();
    } catch (e) {
      loggingService.error('Error while canceling fulfillment', e);
      notificationService.showError('Error while canceling fulfillment');
    } finally {
      loadingService.stopLoading('main-loader:cancel-fulfillment');
    }
  };

  const onClickPrintLabel = async (printer: IPrinter, fulfillmentCode: number | null) => {
    if (!props.order || !fulfillmentCode) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:print-label');
      const response = await orderService.downloadLabelForFulfillmentsShipment(fulfillmentCode);

      let label: string | null = null;
      label = response.label ?? null;

      if (!label) {
        throw new Error('No label found');
      }

      await printJobService.queuePrintJob({
        company_code: $store.state.companyStore.company_code,
        document_base64: label,
        format: printer.format,
        reference: `Shipment #${shipment.value?.shipment_code}`,
        printer: printer.linked_printer,
        printer_app_code: printer.printer_app_code,
      });
      notificationService.showSuccess('Print job queued successfully');
    } catch (e) {
      loggingService.error('Error printing label', e);
      notificationService.showError('Error printing label');
    } finally {
      loadingService.stopLoading('main-loader:print-label');
    }
  };

  const onClickPrintPackingSlip = async (printer: IPrinter, orderCode: number) => {
    if (!props.order) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:print-label');
      const response = await orderService.downloadPackingList(orderCode);
      let label: string | null = null;
      label = response.file ?? null;

      if (!label) {
        throw new Error('No label found');
      }

      await printJobService.queuePrintJob({
        company_code: $store.state.companyStore.company_code,
        document_base64: label,
        format: printer.format,
        reference: `Packing list #${props.order.name ?? props.order.order_number}`,
        printer: printer.linked_printer,
        printer_app_code: printer.printer_app_code,
      });
      notificationService.showSuccess('Print job queued successfully');
    } catch (e) {
      loggingService.error('Error printing packing list', e);
      notificationService.showError('Error printing packing list');
    } finally {
      loadingService.stopLoading('main-loader:print-label');
    }
  };

  /*const onClickCancelFulfillmentAndShipment = async (
    fulfillmentCode: number,
    shipmentCode: number | null,
  ) => {
    if (!fulfillmentCode || !shipmentCode) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:cancel-fulfillment-and-shipment');
      await orderService.cancelFullfillment(props.order.order_code, fulfillmentCode);
      await shipmentService.cancelShipment(shipmentCode, 'Cancel fulfillment');
      $store.dispatch('companyStore/syncCompanyData');
      notificationService.showSuccess('Fulfillment and shipment cancelled successfully');
      refreshOrder();
    } catch (e) {
      loggingService.error('Error while canceling fulfillment and shipment', e);
      notificationService.showError('Error while canceling fulfillment and shipment');
    } finally {
      loadingService.stopLoading('main-loader:cancel-fulfillment-and-shipment');
    }
  };*/

  const onClickShipmentDetail = async (shipmentCode: number | null) => {
    if (!shipmentCode) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:shipment-detail');
      isShipmentModelOpen.value = true;
    } catch (e) {
      loggingService.error('Error while getting shipment detail', e);
      notificationService.showError('Error while getting shipment detail');
    } finally {
      loadingService.stopLoading('main-loader:shipment-detail');
    }
  };

  const downloadLabel = async (fulfillmentCode: number) => {
    if (!fulfillmentCode) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:download-pdf');

      const response = await orderService.downloadLabelForFulfillmentsShipment(fulfillmentCode);
      await showPdf(response.label);
    } catch (error) {
      loggingService.error('Error while downloading PDF', error);
      notificationService.showError('Error while downloading PDF');
    } finally {
      loadingService.stopLoading('main-loader:download-pdf');
    }
  };

  const downloadPackingList = async (orderCode: number) => {
    if (!orderCode) {
      return;
    }
    try {
      loadingService.startLoading('main-loader:download-pdf');
      const response = await orderService.downloadPackingList(orderCode);
      await showPdf(response.file);
    } catch (error) {
      loggingService.error('Error while downloading PDF', error);
      notificationService.showError('Error while downloading PDF');
    } finally {
      loadingService.stopLoading('main-loader:download-pdf');
    }
  };
  const onClickDownloadPackingList = async (orderCode: number | null) => {
    if (!orderCode) {
      return;
    }
    await downloadPackingList(orderCode);
  };

  const onClickDownloadLabel = async (shipmentCode: number | null) => {
    if (!shipmentCode) {
      return;
    }

    await downloadLabel(shipmentCode);
  };

  const getCarriers = async (senderCountry: number, receiverCountry: number) => {
    try {
      loadingService.startLoading('main-loader:carriers');
      const response = await carrierService.getShippingProducts(senderCountry, receiverCountry);
      carriers.value = _.get(response, 'carriers', []);
      productWithClientIds.value = _.get(response, 'productWithClientIds', []) as any;
    } catch (e) {
      notificationService.showError('Error getting carriers');
      loggingService.error('Error getting carriers', e);
    } finally {
      loadingService.stopLoading('main-loader:carriers');
    }
  };

  const onClickContinueFromDraft = async (row: IShipment | null) => {
    try {
      if (row == null) {
        return new Error('Shipment not found');
      } /*  else if (row.delivery_template_code == null) {
          return new Error('Shipment delivery template not found');
        } */
      // console.log("Countries ",JSON.stringify($store.state.countryStore.countries))
      loadingService.startLoading('main-loader:continue-from-draft');
      await getCarriers(row.sender_country_code, row.receiver_country_code);
      const senderCountry = $store.state.countryStore.countries.find(
        (c: ICountry) => c.country_code === row.sender_country_code,
      );
      const receiverCountry = $store.state.countryStore.countries.find(
        (c: ICountry) => c.country_code === row.receiver_country_code,
      );
      const carrier: ICarrier | null = CarrierService.getCarrierFromShippingProductCode(
        $store.state.courierStore.carriers,
        $store.state.courierStore.carrierProducts,
        row.carrier_product_code,
      );
      const shippingProduct: IShippingProduct | undefined =
        $store.state.courierStore.carrierProducts.find(
          (p) => p.carrier_product_code === row.carrier_product_code,
        );

      let ubsend_client_id = null;
      if (row.delivery_template_code) {
        const deliverTemplate = await templateService.getTemplate(row.delivery_template_code);
        ubsend_client_id = deliverTemplate.ubsend_client_id;
        console.log('ShippingProduct ', shippingProduct);
        console.log('ubSendClientID: ', ubsend_client_id);
      } else {
        /*** start here. this code was done to fix issue no. spv-363 ***/
        const product_with_client_ids = JSON.parse(JSON.stringify(productWithClientIds.value));
        console.log('Client IDs: ', product_with_client_ids);
        ubsend_client_id =
          product_with_client_ids[shippingProduct!.product_id] !== undefined
            ? product_with_client_ids[shippingProduct!.product_id]
            : '';
        console.log('Client iD: ', ubsend_client_id);
        /*** this code was commented before ***/
      }
      /* if (!ubsend_client_id) {
        notificationService.showError('Ubsend client id not found');
        return;
      } */
      let weightClasses: IWeightClass[] = [];
      if (ubsend_client_id) {
        weightClasses = await shipmentService.getWeightClasses(
          row.carrier_product_code,
          row.sender_country_code,
          row.receiver_country_code,
          ubsend_client_id,
        );
      }
      /*** end here ***/
      console.log('Weight Class: ', weightClasses);
      const parcel = row.parcels[0];
      let parcelWeightClass = weightClasses.find((weightClass) =>
        inRange(
          parcel.weight ?? 1,
          parseFloat(weightClass.min_weight.toString()),
          parseFloat(weightClass.max_weight.toString()) + 0.1,
        ),
      ) as IWeightClass;

      if (!parcelWeightClass) {
        parcelWeightClass = weightClasses[0];
      }
      if (!carrier || !shippingProduct || !senderCountry || !receiverCountry) {
        notificationService.showError('Carrier not found');
        return;
      }

      let servicePoint: IParcelShop | null = null;

      if (row.recipient.parcelShopId) {
        servicePoint = await shipmentService.getParcelShop(
          row.recipient.parcelShopId,
          carrier.carrier_id,
        );
      }

      const additionalServices = await getAdditionalServices(
        shippingProduct,
        senderCountry,
        receiverCountry,
      );

      const additionalServicesForCreateShipmentData: { [key: number]: boolean } = {};

      if (row.additional_services) {
        row.additional_services.forEach((asChargeCode) => {
          const additionalService = additionalServices.find(
            (as) => as.as_charge_code === asChargeCode,
          );

          if (!additionalService) return;

          additionalServicesForCreateShipmentData[additionalService.as_charge_code] = true;
        });
      }

      let productWeight = 0;
      props.fulfillmentItems.forEach((item) => {
        console.log('Item ', item.variant);
        if (item.variant?.weight_unit?.toLocaleLowerCase() == 'grams') {
          productWeight = (item.variant?.weight ?? 0) / 1000 + productWeight;
        } else {
          productWeight = (item.variant?.weight ?? 0) + productWeight;
        }
      });

      console.log('productWeight ', productWeight);

      await $store.dispatch('shipmentStore/setCreateShipmentData', {
        one: {
          sender: {
            senderCountry: senderCountry,
            name: `${row.sender.firstName} ${row.sender.lastName}`,
            email: row.sender.contactInfo.email,
            phone: row.sender.contactInfo.telephone,
            zipCode: row.sender.postCode,
            address1: row.sender.addressLine1,
            address2: row.sender.addressLine2 || null,
            city: row.sender.city,
          },
          recipient: {
            receiverCountry: receiverCountry,
            name: props.order.shipping_address?.name,
            email: props.order.shipping_address?.email,
            phone: props.order.shipping_address?.phone,
            zipCode: props.order.shipping_address?.zip,
            address1: props.order.shipping_address?.address1,
            address2: props.order.shipping_address?.address2,
            city: props.order.shipping_address?.city,
          },
        },
        two: {
          carrier: carrier,
          shippingProduct: shippingProduct,
          weightClass: parcelWeightClass ?? null,
          isChooseClosestServicePointChecked: !servicePoint,
          servicePoint: servicePoint,
          productWeight: productWeight,
        },
        three: {
          additionalServices: additionalServicesForCreateShipmentData,
        },
        four: { isTermsAndConditionsChecked: false, payWith: 0 },
      } as ICreateNewShipmentForm);
      await $store.dispatch('shipmentStore/setShipmentCode', row.shipment_code);
      await $store.dispatch('shipmentStore/openCreateShipmentModal');
    } catch (e) {
      console.log('Error ', e);
      notificationService.showError('Error continuing from draft');
      loggingService.error('Error continuing from draft', e);
    } finally {
      loadingService.stopLoading('main-loader:continue-from-draft');
    }
  };
  const getAdditionalServices = async (
    shippingProduct: IShippingProduct,
    senderCountry: ICountry,
    receiverCountry: ICountry,
  ) => {
    return shipmentService.getAdditionalServiceCharges(
      senderCountry.country_code,
      receiverCountry.country_code,
      shippingProduct.carrier_product_code,
    );
  };
  onBeforeMount(() => {
    setCarrierProduct();
  });
</script>
