<template>
  <shipvagoo-modal
    size="md"
    separator-class="q-mx-md"
    :title="`Create fulfillment for order ${order.name ?? order.order_number}`"
  >
    <div class="q-px-md dark-text">
      <div v-for="(item, index) in unfulfilledItems" :key="item.line_item_code">
        <div v-if="item.variant" class="flex justify-between q-py-sm">
          <div class="flex items-center q-py-xs" style="height: 70px">
            <div class="flex items-center order-detail-product-border">
              <img
                v-if="item.variant?.variant_image"
                decoding="async"
                class="order-detail-product-modal-img"
                :src="item.variant?.variant_image"
              />
            </div>
            <div class="q-pl-lg flex items-center">
              <div>
                <div class="text-thirteen text-weight-7">
                  {{ item?.title }}
                </div>

                <div class="text-eleven">
                  <div
                    class="q-mt-xs"
                    v-if="item.variant && item.variant?.title != 'Default Title'"
                  >
                    <span>Variant: </span>{{ item.variant?.title ?? '' }}
                  </div>
                  <div class="q-mt-xs">
                    <span>Weight: </span
                    >{{ `${item.variant?.weight} ${item.variant?.weight_unit ?? ''}` }}
                  </div>
                  <div class="q-mt-xs" v-if="item.variant?.sku">
                    <span>SKU: </span>
                    {{ `${item.variant?.sku ?? ''}` }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="flex items-center" style="width: 100px; height: 70px">
            <div class="text-weight-7">
              <shipvagoo-input-v1
                label="Quantity"
                v-model="quantities[item.line_item_code]"
                type="number"
                :suffix="`of ${item.quantity}`"
                @change="onChangeQuantity(item.line_item_code, $event, item.quantity)"
              />
            </div>
          </div>
        </div>
        <div v-else class="flex justify-between q-py-sm">
          <div class="flex items-center q-py-xs" style="height: 70px">
            <div class="flex items-center order-detail-product-border">
              <img
                v-if="item.product?.image_url"
                decoding="async"
                class="order-detail-product-modal-img"
                :src="item.product.image_url"
              />
            </div>
            <div class="q-pl-lg flex items-center">
              <div>
                <div class="text-thirteen">
                  {{ item.product?.title }}
                  {{ item.product?.sku && `- ${item.product?.sku}` }}
                </div>
                <div class="text-eleven q-mt-xs">
                  <span class="text-weight-7">Weight: </span>{{ item.product?.weight }}
                  {{ item.grams ? 'g' : 'kg' }}
                </div>
              </div>
            </div>
          </div>
          <div class="flex items-center" style="width: 100px; height: 70px">
            <div class="text-weight-7">
              <shipvagoo-input-v1
                label="Quantity"
                v-model="quantities[item.line_item_code]"
                type="number"
                :suffix="`of ${item.quantity}`"
                @change="onChangeQuantity(item.line_item_code, $event, item.quantity)"
              />
            </div>
          </div>
        </div>
        <div v-if="index < unfulfilledItems.length - 1">
          <shipvagoo-separator />
        </div>
      </div>
    </div>

    <shipvagoo-card-actions>
      <shipvagoo-button
        label="Fulfill items"
        min-width="120px"
        :disable="getValidFulfillmentItems().length <= 0"
        @click="onClickCreate"
      />
    </shipvagoo-card-actions>
  </shipvagoo-modal>
</template>

<script setup lang="ts">
  import * as _ from 'lodash-es';
  import { onBeforeMount, ref } from 'vue';
  import { IFulfillmentRequestSingle } from '../../../../model/fulfillment.model';
  import { IPagination } from '../../../../model/http.model';
  import { IOrder, IOrderProduct } from '../../../../model/order.model';
  import { IPackaging } from '../../../../model/packaging.model';
  import {
    loadingService,
    loggingService,
    notificationService,
    orderService,
    packagingService,
  } from '../../../../services/_singletons';

  const packagings = ref<IPackaging[]>();
  const quantities = ref<{ [key: number]: string }>({});
  const selectedPackaging = ref<IPackaging | null>(null);
  const isSelectPackagingDisabled = ref<boolean>(true);

  interface Props {
    order: IOrder;
    unfulfilledItems: IOrderProduct[];
  }

  const emit = defineEmits<{
    (event: 'close'): void;
    (event: 'on-created-fulfillments'): void;
  }>();

  const props = withDefaults(defineProps<Props>(), {
    unfulfilledItems: () => [],
  });

  const getValidFulfillmentItems = () =>
    props.unfulfilledItems
      .filter((item: IOrderProduct) => parseInt(quantities.value[item.line_item_code], 10) > 0)
      .map(
        (item: IOrderProduct): IFulfillmentRequestSingle => ({
          line_item_code: item.line_item_code,
          quantity: parseInt(quantities.value[item.line_item_code], 10),
        }),
      );

  const hydrateQuantities = () => {
    props.unfulfilledItems.forEach((item: IOrderProduct) => {
      quantities.value[item.line_item_code] = item.quantity.toString();
    });
  };

  const onChangeQuantity = (lineItemCode: number, quantity: string, maxQuantity: number) => {
    const parsedQuantity = parseInt(quantity, 10);

    if (parsedQuantity < 0) {
      quantities.value[lineItemCode] = '0';
    }

    if (parsedQuantity > maxQuantity) {
      quantities.value[lineItemCode] = maxQuantity.toString();
    }
  };

  const getPackagings = async () => {
    try {
      loadingService.startLoading('main-loader:packagings');
      const response: IPagination<IPackaging> = await packagingService.getPackagings(1, 1000);
      packagings.value = [
        {
          packaging_code: -1,
          name: 'None',
          barcode: '',
          weight: 0,
          length: 0,
          width: 0,
          height: 0,
        },
        ...response.entities,
      ];
      const [defaultPackaging] = packagings.value;
      selectedPackaging.value = defaultPackaging;

      if (packagings.value.length > 1) {
        isSelectPackagingDisabled.value = false;
      }
    } catch (e) {
      notificationService.showError('Error while fetching packagings');
      loggingService.error('Error while fetching packagings', e);
    } finally {
      loadingService.stopLoading('main-loader:packagings');
    }
  };

  const onClickCreate = async () => {
    try {
      loadingService.startLoading('main-loader:create-fulfillment');

      const packagingCode: number | null = selectedPackaging.value?.packaging_code || null;

      const promises: Promise<unknown>[] = [];

      if (props.order.order_status === 'OPEN') {
        _.attempt(() => orderService.setOrderProcessing(props.order.order_code));
      }

      await promises.push(
        orderService
          .createFulfillment(
            props.order.order_code,
            packagingCode === -1 ? null : packagingCode,
            getValidFulfillmentItems(),
          )
          .then(async (response) => {
            notificationService.showSuccess('Fulfillment created');
            emit('on-created-fulfillments');
            emit('close');
          })
          .catch((error) => {
            //throw Error(error.response.data?.error)
            notificationService.showError(error.response.data?.error);
          }),
      );
      await Promise.all(promises);
    } catch (e) {
      if (typeof e === 'string') {
        notificationService.showError(e);
      } else if (e instanceof Error) {
        notificationService.showError(e.message);
      } else {
        //notificationService.showError('Error while creating fulfillment')
      }

      loggingService.error('Error while creating fulfillment', e);
    } finally {
      loadingService.stopLoading('main-loader:create-fulfillment');
    }
  };

  onBeforeMount(() => {
    getPackagings();
    hydrateQuantities();
  });
</script>
