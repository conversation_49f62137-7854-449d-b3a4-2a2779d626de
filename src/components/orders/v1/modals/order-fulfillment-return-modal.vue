<template>
  <shipvagoo-modal
    separator-class="q-mx-md"
    size="lg"
    :title="`Create return for order ${order.name?? order.order_number}`"
  >
    <shipvagoo-card-section>
      <template v-for="item in fulfillmentItems" :key="item.line_item_code">
        <div
          v-if="
            item.quantity -
              (returnOrderCode
                ? item.returned_quantity ?? 0
                : item.calculated_returned_quantity ?? 0) >
            0
          "
          class="row"
        >
          <div v-if="item.variant" class="col-md-4 flex">
            <div class="flex items-center order-detail-product-border">
              <img
                v-if="item.variant?.variant_image"
                decoding="async"
                class="order-detail-product-modal-img"
                :src="item.variant?.variant_image"
              />
            </div>
            <div class="q-pl-lg flex items-center">
              <div>
                <div class="text-thirteen text-weight-7">
                  {{ item.title }}
                </div>

                <div class="text-eleven">
                  <div
                    class="q-mt-xs"
                    v-if="item.variant && item.variant?.title != 'Default Title'"
                  >
                    <span>Variant: </span>{{ item.variant?.title ?? '' }}
                  </div>
                  <div class="q-mt-xs">
                    <span>Weight: </span
                    >{{ `${item.variant?.weight} ${item.variant?.weight_unit}` }}
                  </div>
                  <div class="q-mt-xs" v-if="item.variant?.sku">
                    <span>SKU: </span>
                    {{ `${item.variant?.sku ?? ''}` }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="col-md-4 flex">
            <div class="flex items-center order-detail-product-border">
              <img
                v-if="item.product?.image_url"
                decoding="async"
                class="order-detail-product-modal-img"
                :src="item.product.image_url"
              />
            </div>
            <div class="q-pl-lg flex items-center">
              <div>
                <div class="text-thirteen">
                  {{ item.product?.title }}
                  {{ item.product?.sku && `- ${item.product?.sku}` }}
                </div>
                <div class="text-eleven q-mt-xs">
                  <span class="text-weight-7">Weight: </span>{{ item.product?.weight }}
                  {{ item.grams ? 'g' : 'kg' }}
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-8 q-mt-sm">
            <div class="row">
              <div class="col-md-2">
                <shipvagoo-input-v1
                  placeholder="Quantity"
                  v-model="quantities[item.line_item_code]"
                  type="text"
                  label="Quantity"
                  :suffix="`of ${
                    item.quantity -
                    (returnOrderCode
                      ? item.returned_quantity ?? 0
                      : item.calculated_returned_quantity ?? 0)
                  }`"
                  mask="####"
                  @update:modelValue="
                    onChangeQuantity(
                      item.line_item_code,
                      item.quantity -
                        (returnOrderCode
                          ? item.returned_quantity ?? 0
                          : item.calculated_returned_quantity ?? 0),
                    )
                  "
                >
                  <template v-slot:append>
                    <div class="flex column justify-center q-mt-lg">
                      <q-icon
                        @click="onClickIncrementQuantity(item)"
                        name="keyboard_arrow_up"
                        class="text-eighteen cursor-pointer"
                        style="color: #7d8398 !important"
                      />
                      <q-icon
                        @click="onClickDecrementQuantity(item.line_item_code)"
                        name="keyboard_arrow_down"
                        class="text-eighteen cursor-pointer"
                        style="color: #7d8398 !important"
                      />
                    </div>
                  </template>
                </shipvagoo-input-v1>
              </div>
              <div class="col-md-5 q-pl-sm">
                <shipvagoo-select-v1
                  placeholder="Select"
                  label="Reason"
                  :options="reasons"
                  v-model="reason[item.line_item_code]"
                />
              </div>
              <div class="col-md-5 q-pl-sm">
                <shipvagoo-input-v1
                  v-if="reason[item.line_item_code] == 'Other (comment required)'"
                  placeholder="Comment"
                  label="Comment"
                  v-model="comment[item.line_item_code]"
                />
              </div>
            </div>
          </div>
          <div class="col-md-12 q-my-sm">
            <shipvagoo-separator />
          </div>
        </div>
      </template>
    </shipvagoo-card-section>

    <shipvagoo-card-actions>
      <shipvagoo-button
        label="Create return"
        min-width="120px"
        :disable="getValidFulfillmentItems().length <= 0"
        @click="onClickCreate"
      />
    </shipvagoo-card-actions>
  </shipvagoo-modal>
</template>

<script setup lang="ts">
  import * as _ from 'lodash-es';
  import { onBeforeMount, ref, computed } from 'vue';
  import {
    IFulfillment,
    IFulfillmentReturnRequestSingle,
  } from '../../../../model/fulfillment-return.model';
  import { IPagination } from '../../../../model/http.model';
  import { IOrder, IOrderProduct } from '../../../../model/order.model';
  import { IPackaging } from '../../../../model/packaging.model';
  import {
    loadingService,
    loggingService,
    notificationService,
    orderService,
    packagingService,
  } from '../../../../services/_singletons';
  import { useRoute, useRouter } from 'vue-router';

  /** ref properties **/
  const packagings = ref<IPackaging[]>();
  const quantities = ref<{ [key: number]: string }>({});
  const reasons = ref<string[]>([
    'Arrived damaged or defective',
    'Arrived too late',
    'Changed my mind',
    'Did not fit',
    'Did not match the description',
    'Did not meet expectations',
    'Found a better price somewhere else',
    'No reason',
    'Size too big',
    'Size too small',
    'Wrong item was sent',
    'Wrong size ordered',
    'Other (comment required)',
  ]);
  const reason = ref<{ [key: string]: string }>({});
  const comment = ref<{ [key: string]: string }>({});
  // const resolution = ref<{ [key: string]: string }>({});
  const selectedPackaging = ref<IPackaging | null>(null);
  const isSelectPackagingDisabled = ref<boolean>(true);
  const $route = useRoute();
  const $router = useRouter();

  interface Props {
    order: IOrder;
    fulfillment: IFulfillment[];
    fulfillmentItems: IOrderProduct[];
  }

  const emit = defineEmits<{
    (event: 'close'): void;
    (event: 'on-created-fulfillments'): void;
  }>();

  const props = withDefaults(defineProps<Props>(), {
    fulfillmentItems: () => [],
  });

  /** computed properties **/
  const returnOrderCode = computed(() => {
    return $route.query.return_order_code;
  });

  const onClickIncrementQuantity = (item: IOrderProduct) => {
    const maxQuantity = Number(
      `${
        item.quantity -
        (returnOrderCode.value
          ? item.returned_quantity ?? 0
          : item.calculated_returned_quantity ?? 0)
      }`,
    );
    const quantity = Number(quantities.value[item.line_item_code]);
    if (quantity < maxQuantity) {
      quantities.value[item.line_item_code] = String(quantity + 1);
    }
  };
  const onClickDecrementQuantity = (line_item_code: number) => {
    const quantity = Number(quantities.value[line_item_code]);
    if (quantity > 0) {
      quantities.value[line_item_code] = String(quantity - 1);
    }
  };
  const getValidFulfillmentItems = () =>
    props.fulfillment
      .filter((item: IFulfillment) => parseInt(quantities.value[item.line_item_code], 10) > 0)
      .map(
        (item: IFulfillment): IFulfillmentReturnRequestSingle => ({
          line_item_code: item.line_item_code,
          name: item.name,
          quantity: parseInt(quantities.value[item.line_item_code], 10),
          comment: comment.value[item.line_item_code],
          reason: reason.value[item.line_item_code],
          fulfillment_type: 'RETURN',
          reference: item.fulfillment_code,
          return_order_code: $route.query.return_order_code,
        }),
      );

  const hydrateQuantities = () => {
    props.fulfillmentItems.forEach((item: IOrderProduct) => {
      if (returnOrderCode.value) {
        quantities.value[item.line_item_code] = (
          item.quantity - (item.returned_quantity ?? 0)
        ).toString();
      } else {
        quantities.value[item.line_item_code] = (
          item.quantity - (item.calculated_returned_quantity ?? 0)
        ).toString();
      }
    });
  };

  const onChangeQuantity = (lineItemCode: number, maxQuantity: number) => {
    const quantity = quantities.value[lineItemCode];
    const parsedQuantity = parseInt(quantity, 10);

    if (parsedQuantity < 0 || quantity == '') {
      quantities.value[lineItemCode] = '0';
    }
    /* setTimeout(() => {
     quantities.value[lineItemCode] = quantity.replace(/\./g, '');
   }, 10)
 
  if (parsedQuantity > maxQuantity) {
    quantities.value[lineItemCode] = maxQuantity.toString();
  }*/
  };

  const getPackagings = async () => {
    try {
      loadingService.startLoading('main-loader:packagings');
      const response: IPagination<IPackaging> = await packagingService.getPackagings(1, 1000);
      packagings.value = [
        {
          packaging_code: -1,
          name: 'None',
          barcode: '',
          weight: 0,
          length: 0,
          width: 0,
          height: 0,
        },
        ...response.entities,
      ];
      const [defaultPackaging] = packagings.value;
      selectedPackaging.value = defaultPackaging;

      if (packagings.value.length > 1) {
        isSelectPackagingDisabled.value = false;
      }
    } catch (e) {
      notificationService.showError('Error while fetching packagings');
      loggingService.error('Error while fetching packagings', e);
    } finally {
      loadingService.stopLoading('main-loader:packagings');
    }
  };

  const onClickCreate = async () => {
    try {
      let isValidQty = true;
      props.fulfillmentItems.forEach((item: IOrderProduct) => {
        const totalQuantity =
          item.quantity -
          (returnOrderCode.value
            ? item.returned_quantity ?? 0
            : item.calculated_returned_quantity ?? 0);
        if (Number(quantities.value[item.line_item_code]) > totalQuantity) {
          notificationService.showError('Invalid quantity entered.');
          isValidQty = false;
          return;
        }
        if (
          reason.value[item.line_item_code] == 'Other (comment required)' &&
          (comment.value[item.line_item_code] == null || comment.value[item.line_item_code] == '')
        ) {
          isValidQty = false;
          notificationService.showError('Comment is required.');
          return;
        }
      });
      if (!isValidQty) {
        return;
      }
      loadingService.startLoading('main-loader:create-fulfillment');

      const packagingCode: number | null = selectedPackaging.value?.packaging_code || null;

      const promises: Promise<unknown>[] = [];

      if (props.order.order_status === 'OPEN') {
        _.attempt(() => orderService.setOrderProcessing(props.order.order_code));
      }

      promises.push(
        orderService.createFulfillmentReturn(
          props.order.order_code,
          packagingCode === -1 ? null : packagingCode,
          getValidFulfillmentItems(),
        ),
      );

      await Promise.all(promises);
      notificationService.showSuccess('Fulfillment Return created');
      emit('on-created-fulfillments');
      emit('close');
      $router.replace({ name: 'Orders' });
    } catch (e) {
      notificationService.showError('Error while creating fulfillment return');
      loggingService.error('Error while creating fulfillment return', e);
    } finally {
      loadingService.stopLoading('main-loader:create-fulfillment');
    }
  };
  const hydrateReasonsAndComments = () => {
    const return_orders = props.order.return_orders;
    if (return_orders?.length) {
      return_orders.forEach((return_order) => {
        const return_order_items = return_order.return_order_items;
        return_order_items?.forEach((item) => {
          reason.value[item.line_item_code] = item.return_reason ?? '';
          comment.value[item.line_item_code] = item.comments ?? '';
          // quantities.value[item.line_item_code] = item.quantity.toString() ?? '0'
        });
      });
    }
  };
  onBeforeMount(() => {
    getPackagings();
    hydrateQuantities();
    hydrateReasonsAndComments();
    //hydrateComments();
  });
</script>
