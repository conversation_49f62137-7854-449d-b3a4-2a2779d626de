<template>
  <shipvagoo-modal separator-class="q-mx-lg" size="lg" title="Refund">
    <div v-if="isDiscounted">
      <DashboardTaskAlert
        type="info"
        label="The price incl. VAT is the discounted amount"
        class="q-my-sm q-pl-lg q-pr-sm"
        :show-heading="false"
      ></DashboardTaskAlert>
    </div>
    <div class="q-px-sm dark-text">
      <div class="row">
        <div class="col-8">
          <template
            v-for="(item, index) in fulfillmentItems"
            :key="`fulfillment-items-${item.line_item_code}`"
          >
            <div>
              <div class="order_detail-order_item">
                <div v-if="item.variant" class="flex items-start q-py-sm" style="min-height: 90px">
                  <div class="flex items-center justify-center order-detail-product-border">
                    <img
                      v-if="item.variant?.variant_image"
                      decoding="async"
                      class="order-detail-product-modal-img"
                      :src="item.variant?.variant_image"
                    />
                  </div>
                  <div class="q-pl-lg flex items-start">
                    <div>
                      <div class="text-thirteen text-weight-7">
                        {{ item?.title }}
                      </div>

                      <div class="text-eleven">
                        <div
                          class="q-mt-xs"
                          v-if="item.variant && item.variant?.title != 'Default Title'"
                        >
                          <span>Variant: </span>{{ item.variant?.title ?? '' }}
                        </div>
                        <div class="q-mt-xs" v-if="item.variant?.weight">
                          <span>Weight: </span
                          >{{ `${item.variant?.weight} ${item.variant?.weight_unit}` }}
                        </div>
                        <div class="q-mt-xs" v-if="item.variant?.sku">
                          <span>SKU: </span>
                          {{ `${item.variant?.sku ?? ''}` }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else class="flex items-center q-py-sm">
                  <div class="flex items-center content-center order-detail-product-border">
                    <img
                      v-if="item.product?.image_url"
                      decoding="async"
                      class="order-detail-product-modal-img"
                      :src="item.product.image_url"
                    />
                  </div>
                  <div class="q-pl-lg flex items-center">
                    <div>
                      <div class="text-thirteen">
                        {{ item.product?.title }}
                        {{ item.product?.sku && `- ${item.product?.sku}` }}
                      </div>
                      <div class="text-eleven q-mt-xs">
                        <span class="text-weight-7">Weight: </span>{{ item.product?.weight }}
                        {{ item.grams ? 'g' : 'kg' }}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="flex items-start gap-10" style="max-width: 280px; min-height: 90px">
                  <shipvagoo-input-v1
                    style="width: 120px"
                    label="Quantity"
                    v-model="quantities[item.line_item_code]"
                    mask="####"
                    type="text"
                    :suffix="`of ${getRemainingQuantity(item)}`"
                  >
                    <template v-slot:append>
                      <div class="flex column justify-center q-mt-lg">
                        <q-icon
                          @click="onClickIncrementQuantity(item)"
                          name="keyboard_arrow_up"
                          class="text-eighteen cursor-pointer"
                          style="color: #7d8398 !important"
                        />
                        <q-icon
                          @click="onClickDecrementQuantity(item.line_item_code)"
                          name="keyboard_arrow_down"
                          class="text-eighteen cursor-pointer"
                          style="color: #7d8398 !important"
                        />
                      </div>
                    </template>
                  </shipvagoo-input-v1>
                  <shipvagoo-input-v1
                    disable
                    style="width: 150px"
                    label="Price incl. VAT"
                    :modelValue="prices[item.line_item_code]"
                    type="text"
                    :suffix="getLocalCurrency(item)"
                  >
                  </shipvagoo-input-v1>
                </div>
              </div>
              <shipvagoo-separator
                v-if="index + 1 != fulfillmentItems.length"
                class="q-ml-md q-mr-md"
              />
            </div>
          </template>
          <div class="q-px-md q-pb-md" v-if="quantitySum > 0">
            <shipvagoo-checkbox-v1 :label="restockItemLabel" v-model="restockItems" size="sm" />
          </div>
          <shipvagoo-separator
            v-if="fulfillmentItems.length && _compShippingCharges?.amount?.toFixed(2) > 0"
            class="q-ml-md q-mr-md"
          />
          <div
            class="order_detail-order_item q-pt-md"
            v-if="_compShippingCharges?.amount?.toFixed(2) > 0"
          >
            <div class="flex items-start" style="min-height: 90px; width: 350px">
              <!-- <div
                class="flex items-center q-pa-md"
                style="width: 80px; height: 80px; border: 1px solid #ccc; border-radius: 4px"
              >
                <img decoding="async" :src="TruckCargo" style="width: 40px; height: 40px" />
              </div> -->
              <div class="items-start text-thirteen">
                <strong>Shipping</strong>
                <div class="q-mt-xs">
                  {{ order.shipping_lines?.[0]?.title ?? '' }}
                </div>
              </div>
            </div>
            <div class="flex items-start gap-10" style="width: 280px !important; min-height: 90px">
              <shipvagoo-input-v1
                style="width: 120px"
                label="Shipping"
                v-model="shippingCharges"
                type="text"
                :error="summary.shipping_error"
                :error-message="`${priceValue(_compShippingCharges?.amount) ?? '0,00'} ${
                  _compShippingCharges?.currency ?? 'DKK'
                } available to refund`"
              />
              <shipvagoo-input-v1
                disable
                style="width: 150px"
                :label="order.taxes_included ? 'Price incl. VAT' : 'Price excl. VAT'"
                :modelValue="priceValue(_compShippingCharges?.amount) ?? '0,00'"
                type="text"
                :suffix="_compShippingCharges?.currency ?? 'DKK'"
              />
            </div>
          </div>
        </div>
        <div class="col-4 q-pt-sm q-pb-md">
          <refund-summary :summary="summary" @create="onClickCreate"></refund-summary>
        </div>
      </div>
    </div>
    <!--  <shipvagoo-card-actions>
      <shipvagoo-button
        color="brand"
        label="Create refund"
        min-width="120px"
        @click="onClickCreate"
      />
    </shipvagoo-card-actions> -->
  </shipvagoo-modal>
</template>

<script setup lang="ts">
  import * as _ from 'lodash-es';
  import { onBeforeMount, ref, computed } from 'vue';
  import PriceService from '../../../../services/price.service';
  import {
    IFulfillment,
    IFulfillmentRefundRequestSingle,
  } from '../../../../model/fulfillment-return.model';
  import { IOrder, IOrderProduct } from '../../../../model/order.model';
  //import TruckCargo from '../../../../assets/img/truck_cargo.png';
  import {
    loadingService,
    loggingService,
    notificationService,
    orderService,
  } from '../../../../services/_singletons';

  import { useHelper } from '../../../../composeable/Helper';

  const { getShippingCharges } = useHelper();
  const restockItems = ref(true);
  const quantities = ref<{ [key: number]: string }>({});
  const prices = ref<{ [key: number]: string }>({});
  const shippingCharges = ref<string>('0,00');
  const isDiscounted = ref<boolean>(false);

  interface Props {
    order: IOrder;
    fulfillment: IFulfillment[];
    fulfillmentItems: IOrderProduct[];
  }
  const emit = defineEmits<{
    (event: 'close'): void;
    (event: 'on-created-fulfillments'): void;
  }>();

  const props = withDefaults(defineProps<Props>(), {
    fulfillmentItems: () => [],
  });

  /** computed properties */
  const fulfillmentItems = computed<IOrderProduct[]>(() =>
    props.fulfillmentItems.filter((item: IOrderProduct) => getRemainingQuantity(item) > 0),
  );

  const _compShippingCharges = computed(() => {
    const shippingLine = props.order.shipping_lines?.[0];
    if (!shippingLine) return getShippingCharges(props.order);

    return shippingLine.discount_allocations?.length
      ? { amount: 0, currency: 'DKK' }
      : getShippingCharges(props.order);
  });

  const totalShippingTaxAmount = computed(() => {
    const shippingLine = props.order.shipping_lines?.[0];
    if (!shippingLine || props.order.taxes_included) return 0;

    const isAnyProductTaxable = props.fulfillmentItems.some(
      (item) => item.taxable && item.tax_lines.length,
    );

    return isAnyProductTaxable
      ? shippingLine.tax_lines.reduce(
          (sum, tax) => sum + Number(tax?.price_set?.presentment_money?.amount || 0),
          0,
        )
      : 0;
  });

  const shippingTaxAmount = computed(() => {
    const shippingLine = props.order.shipping_lines?.[0];
    if (!shippingLine) return 0;

    const shippingLineAmount = Number(shippingLine.price_set?.presentment_money?.amount || 0);
    if (shippingLineAmount === 0) return 0;

    const shippingChargeAmount = parseFreightAmount(shippingCharges.value);
    const totalChargeAmount = _compShippingCharges.value.amount;
    return (
      (Math.min(shippingChargeAmount, totalChargeAmount) / shippingLineAmount) *
      totalShippingTaxAmount.value
    );
  });

  const quantitySum = computed(() =>
    Object.values(quantities.value).reduce((sum, value) => sum + Number(value), 0),
  );

  const restockItemLabel = computed(() =>
    quantitySum.value > 1 ? 'Restock items' : 'Restock item',
  );

  const getValidRefundItems = computed(() =>
    props.fulfillment
      .filter(
        (item: IFulfillment) =>
          prices.value[item.line_item_code] &&
          parseInt(quantities.value[item.line_item_code], 10) > 0,
      )
      .map(
        (item: IFulfillment): IFulfillmentRefundRequestSingle => ({
          quantity: parseInt(quantities.value[item.line_item_code], 10),
          price: prices.value[item.line_item_code].replace(/[,.]/g, (match) =>
            match === ',' ? '.' : '',
          ),
          line_item_code: item.line_item_code,
          fulfillment_code: item.fulfillment_code,
          restock_type: restockItems.value ? 'RETURN' : 'NO_RESTOCK',
        }),
      ),
  );

  // ** Helper Functions **
  const parseFreightAmount = (freight: string) =>
    Number(freight?.replace(/[,.]/g, (match) => (match === ',' ? '.' : '')) || 0);

  const formatPrice = (value: number, currency: string) => `${priceValue(value)} ${currency}`;

  const calculateFreightTaxAmount = (
    freightAmount: number,
    shippingLineAmount: number,
    totalShippingTax: number,
  ) =>
    shippingLineAmount === 0
      ? 0
      : (Math.min(freightAmount, shippingLineAmount) / shippingLineAmount) * totalShippingTax;

  const summary = computed(() => {
    const subTotal = getValidRefundItems.value.reduce(
      (sum, item) => sum + Number(item.price) * parseInt(quantities.value[item.line_item_code]),
      0,
    );

    const currency = fulfillmentItems.value.length
      ? getLocalCurrency(fulfillmentItems.value[0])
      : 'DKK';

    const freightAmount = parseFreightAmount(shippingCharges.value);
    const _shippingTaxAmount = shippingTaxAmount.value;

    const actualFreightAmount = _compShippingCharges.value?.amount;
    const actualSubtotal = Object.entries(prices.value).reduce((sum, [lineItemCode, value]) => {
      const item = fulfillmentItems.value.find(
        (item) => String(item.line_item_code) === lineItemCode,
      );
      return item ? sum + parseFreightAmount(value) * getRemainingQuantity(item) : sum;
    }, 0);

    const actualShippingTaxAmount = totalShippingTaxAmount.value;
    const shippingLineAmount = Number(
      props.order.shipping_lines?.[0]?.price_set?.presentment_money?.amount || 0,
    );
    const actualFreightTaxAmount = calculateFreightTaxAmount(
      actualFreightAmount,
      shippingLineAmount,
      actualShippingTaxAmount,
    );

    const total = subTotal + freightAmount + _shippingTaxAmount;
    const actualTotal = (actualSubtotal + actualFreightAmount + actualFreightTaxAmount).toFixed(2);

    return {
      itemSubtotal: formatPrice(subTotal, currency),
      shippingCost: formatPrice(freightAmount, currency),
      refundTotal: formatPrice(total, currency),
      availableForRefund: formatPrice(Number(actualTotal), currency),
      disabled: total > Number(actualTotal),
      shipping_error: roundAmount(freightAmount) > roundAmount(actualFreightAmount),
      tax_amount: formatPrice(_shippingTaxAmount, currency),
    };
  });

  const onClickIncrementQuantity = (item: IOrderProduct) => {
    const quantity = Number(quantities.value[item.line_item_code]);
    if (quantity < item.quantity) {
      quantities.value[item.line_item_code] = String(quantity + 1);
    }
  };
  const onClickDecrementQuantity = (line_item_code: number) => {
    const quantity = Number(quantities.value[line_item_code]);
    if (quantity > 0) {
      quantities.value[line_item_code] = String(quantity - 1);
    }
  };
  const hydrateQuantities = () => {
    fulfillmentItems.value.forEach((item: IOrderProduct) => {
      quantities.value[item.line_item_code] = '1';
    });
  };

  const getLocalCurrency = (item: any) => {
    return item.price_set.presentment_money.currency_code;
  };

  const priceValue = (price: any) => {
    return PriceService.formatPriceTextField(price);
  };

  const getRemainingQuantity = (item: IOrderProduct) => {
    return item.quantity - (item.returned_quantity ?? 0);
  };

  const hydratePrices = () => {
    fulfillmentItems.value.forEach((item: IOrderProduct) => {
      const presentmentAmount = parseFloat(item.price_set?.presentment_money?.amount ?? '0');
      let discountAmount = 0;
      if (item.total_discount_set && item.total_discount_set.length > 0) {
        item.total_discount_set.forEach((discount) => {
          discountAmount += parseFloat(discount.presentment_money.amount);
        });
      }
      const quantity = item.total_quantity ?? 1;
      const actualDiscount = discountAmount / quantity;
      let finalPrice = presentmentAmount - roundAmount(actualDiscount); //this is to fix the rounding issue
      if (actualDiscount > 0) {
        isDiscounted.value = true;
      }
      console.log('Quantity ', item.total_quantity);
      console.log('discountAmount ', discountAmount);
      console.log('actualDiscount ', actualDiscount);
      console.log('finalPrice ', finalPrice);
      console.log('presentmentAmount ', presentmentAmount);

      console.log('Taxable ', item.taxable);
      console.log('TaxLines', item.tax_lines);
      console.log('Tax Included ', props.order.taxes_included);

      let taxAmount = 0;
      if (!props.order.taxes_included && item.tax_lines?.length && item.taxable) {
        item.tax_lines.forEach((taxLine) => {
          const taxPresentmentMoney = taxLine.price_set.presentment_money?.amount ?? '0';
          taxAmount = taxAmount + Number(taxPresentmentMoney) / (quantity ?? 1);
        });
      }

      finalPrice = finalPrice + taxAmount;
      finalPrice = finalPrice;
      if (!isNaN(finalPrice)) {
        prices.value[item.line_item_code] = priceValue(finalPrice);
      }
    });
  };

  const roundAmount = (amount: number) => {
    return Math.round(amount * 100) / 100;
  };
  const hydrateShippingCharges = () => {
    shippingCharges.value = '0,00';
  };
  /*
       const totalRefund = () => {
         let totalAmount = 0;

         fulfillmentItems.value.forEach((item: IOrderProduct) => {
           totalAmount +=
             parseFloat(prices.value[item.line_item_code]) *
             parseInt(quantities.value[item.line_item_code], 10);
         });

         return totalAmount + parseInt(shippingCharges.value.toString(), 10);
       }; */

  /** don't need to remove it */
  /* const isInvalidQuantity = () => {
         return (
           fulfillmentItems.value.filter((fItem: IOrderProduct) => {
             return (
               parseInt(quantities.value[fItem.line_item_code], 10) > fItem.quantity ||
               quantities.value[fItem.line_item_code] == ''
             );
           }).length > 0
         );
       }; */

  /** if products are more than 1, then 1 quantity should be greater than 0 **/
  /* const hasValidQuantities = () => {
         let isValid = false;
         if (fulfillmentItems.value.length > 1) {
           for (const fItem of fulfillmentItems.value) {
             const qty = parseInt(quantities.value[fItem.line_item_code], 10);
             if (fItem.quantity > 0 && qty > 0) {
               isValid = true;
               break;
             }
           }
         } else if (fulfillmentItems.value.length == 1) {
           for (const fItem of fulfillmentItems.value) {
             const qty = parseInt(quantities.value[fItem.line_item_code], 10);
             if (fItem.quantity > 0 && qty > 0) {
               isValid = true;
             }
           }
         }
         return isValid;
       }; */
  const isInvalidPriceFormat = () => {
    let isInvalidPriceFormat = false;
    for (const fItem of fulfillmentItems.value) {
      const regex = /^(\d{1,3}(\.\d{3})*|\d+)(,\d{2})?$/;
      const amount = prices.value[fItem.line_item_code];
      if (!regex.test(amount)) {
        isInvalidPriceFormat = true;
        break;
      }
    }
    return isInvalidPriceFormat;
  };
  /*  const isInvalidPrice = () => {
     const records = fulfillmentItems.value.filter((fItem: IOrderProduct) => {
       return (
         (fItem.price_set?.presentment_money
           ? parseFloat(prices.value[fItem.line_item_code]) >
             parseFloat(fItem.price_set?.presentment_money?.amount)
           : false) || prices.value[fItem.line_item_code] == ''
       );
     });

     console.log('Records ', records);
     return records.length > 0;
   }; */

  const isInvalidFreightFormat = () => {
    const regex = /^(\d{1,3}(\.\d{3})*|\d+)(,\d{2})?$/;
    return !regex.test(shippingCharges.value) || shippingCharges.value == '';
  };
  /* const isInvalidFreight = () => {
     const totalShippingCharges = getShippingCharges(props.order, 'money');
     const freightAmount = parseFloat(
       shippingCharges.value.replace(/[,.]/g, (match) => {
         return match === ',' ? '.' : '';
       }),
     );
     return Number(freightAmount.toFixed(2)) > Number(totalShippingCharges.toFixed(2));
   }; */
  const onClickCreate = async () => {
    try {
      /*props.fulfillment.forEach(function (item) {
           console.log(item);
         });*/
      /*  if (isInvalidQuantity() || !hasValidQuantities()) {
             notificationService.showError('Incorrect quantity.');
             return;
           } */
      /* if (isInvalidPrice()) {
         notificationService.showError('Incorrect product amount.');
         return;
       } */

      if (isInvalidPriceFormat()) {
        notificationService.showError('Incorrect product amount format.');
        return;
      }
      if (isInvalidFreightFormat()) {
        notificationService.showError('Incorrect shipping amount format.');
        return;
      }

      /* if (isInvalidFreight()) {
         notificationService.showError('Incorrect shipping amount.');
         return;
       }
  */
      loadingService.startLoading('main-loader:create-fulfillment-refund');
      const promises: Promise<unknown>[] = [];
      promises.push(
        orderService.createFulfillmentRefund(
          props.order.order_code,
          getValidRefundItems.value,
          String(parseFreightAmount(shippingCharges.value)),
          /*  shippingCharges.value.replace(/[,.]/g, (match) => {
            return match === ',' ? '.' : '';
          }), */
          String(shippingTaxAmount.value),
        ),
      );

      await Promise.all(promises);

      notificationService.showSuccess('Fulfillment Refund created');
      emit('on-created-fulfillments');
      emit('close');
    } catch (e) {
      notificationService.showError('Error while creating fulfillment refund');
      loggingService.error('Error while creating fulfillment return', e);
    } finally {
      loadingService.stopLoading('main-loader:create-fulfillment-refund');
    }
  };

  onBeforeMount(() => {
    hydrateQuantities();
    hydratePrices();
    hydrateShippingCharges();
  });
</script>
