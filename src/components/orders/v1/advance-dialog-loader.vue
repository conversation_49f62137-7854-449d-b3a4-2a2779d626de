<template>
  <div class="bg-white advance-loading-dialog dark-text">
    <div class="row items-center q-px-md q-py-sm header-bg">
      <div class="text-white text-weight-7 text-sixteen">
        Processing...
      </div>
      <q-space />
      <slot name="icon-right" />
      <q-btn v-close-popup icon="close" color="white" flat round dense @click="onClickClose" />
    </div>
    <div class="slot-container q-pa-md">
      <div class="text-fourteen text-weight-7">
        <slot name="label"></slot>
      </div>
      <div style=" margin-top: 3px;" class="light-gray-text">
        Currently processing your request. Refreshing the page may lead to data loss
      </div>
      <div class="q-mt-lg">
        <div class="default-slider-bg">
          <div class="progress-slider-bg" :style="{width: _progress+'%'}" />
        </div>
        <div class="light-gray-text q-mt-xs">
          {{ _progress }}% Completed
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { watch, ref } from "vue";

const emit = defineEmits(["close", "update:progress"]);
const props = defineProps(["progress"]);
const _progress = ref(props.progress);

watch(() => props.progress, (newValue, oldValue) => {
  const step = 1;
  const targetProgress = newValue;
  let currentProgress = oldValue;

  const intervalId = setInterval(() => {
    if (currentProgress < targetProgress) {
      currentProgress += step;
      _progress.value = currentProgress;
    } else {
      clearInterval(intervalId);
    }
  }, 50);
});

const onClickClose = () => {
  emit("close");
};
</script>

<style scoped lang="scss">
.header-bg {
  background-color: #243E90;
}

.advance-loading-dialog {
  width: 550px;
  max-width: 550px !important;
  margin: 0 auto;
  overflow: hidden;
  border-radius: 15px;
}

.slot-container {
  max-height: calc(100vh - 48px - 67px);
  overflow: auto;
}

.default-slider-bg {
  width: 100%;
  height: 12px;
  background: #E5E5E5;
}

.progress-slider-bg {
  height: 12px;
  background: #243E90;
  transition: width 0.3s ease; 
}

.light-gray-text {
  font-size: 12px;
  font-weight: 400;
  color: #757575;
}
</style>