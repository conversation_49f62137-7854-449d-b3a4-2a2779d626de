<template>
  <shipvagoo-card-section no-padding style="padding: 5px 24px">
    <div class="flex" @click="show = !show">
      <img :src="listIcon" style="width: 20px; height: 20px" />
      <div class="q-ml-sm item-detail-label text-fourteen" style="margin-top: 2px">
        {{ label }} ({{ successResults.length }}/{{ results.length }})
      </div>
      <div class="q-ml-sm">
        <q-icon class="cursor-pointer text-twelve q-mt-xs" name="fas fa-chevron-down"> </q-icon>
      </div>
    </div>
    <Transition name="slide-fade">
      <div v-if="show" class="text-twelve text-weight-bold">
        <div
          :key="n"
          v-for="(result, n) in results"
          class="flex justify-between q-my-sm"
          style="margin-left: 33px"
        >
          <div class="">{{ result.description }}</div>
          <div class="flex items-center">
            <img
              :src="result.success ? SuccessIcon : DangerIcon"
              style="width: 16px; height: 16px"
            />
            <div class="q-ml-sm">
              {{
                result.success && result.already
                  ? 'Already completed'
                  : result.success
                  ? 'Completed'
                  : 'Incomplete'
              }}
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </shipvagoo-card-section>
</template>
<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { IOrderMultiActionResult } from '../../../model/order.model';
  import DangerIcon from '../../../assets/img/danger.svg';
  import SuccessIcon from '../../../assets/img/success_rounded.svg';
  import WarningIcon from '../../../assets/img/warning.svg';

  interface Props {
    results: IOrderMultiActionResult[];
    resultType: string;
    label: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    resultType: 'success',
  });
  const show = ref<boolean>(false);

  const successResults = computed(() => {
    return props.results.filter((item) => item.success);
  });
  /*const failedResults = computed(() => {
  return props.results.filter(item => !item.success)
})*/
  const listIcon = computed(() => {
    return props.resultType === 'danger'
      ? DangerIcon
      : props.resultType === 'success'
      ? SuccessIcon
      : WarningIcon;
  });
</script>
<style scoped lang="scss">
  .item-detail-label {
    font-weight: 600;
  }

  .slide-fade-enter-active {
    transition: all 0.3s ease-out;
  }

  .slide-fade-enter-from,
  .slide-fade-leave-to {
    opacity: 0;
    transform: translateX(20px);
  }
</style>
