<template>
  <q-card class="pick_order start_pick_path_modal-pick_order">
    <shipvagoo-card-section class="orders_page-modal-header row items-center">
      <q-btn
        v-if="!($q.platform.is.mobile && selectedOrderProduct)"
        icon="close"
        flat
        round
        dense
        @click="cancelPickModal = true"
      />
      <!-- <q-btn
          v-if="selectedOrder"
          class="show-on-tablet"
          icon="chevron_left"
          flat
          round
          dense
          @click="selectedOrder = null"
        /> -->
      <q-space />
      <div class="start_pick_path_modal-pick_order-header">
        <div v-if="orders.length === 1" class="start_pick_path_modal-pick_order-header-title">
          Pick Order {{ orders[0].order_number }}
        </div>
        <div v-if="orders.length > 1" class="start_pick_path_modal-pick_order-header-title">
          Pick {{ orders.length }} orders
        </div>
        <div class="start_pick_path_modal-pick_order-header-picked_info">
          Picked {{ getOrderCountDonePicking() }} / {{ orders.length }}
        </div>
      </div>
      <q-space />
      <q-btn icon="check" flat round dense @click="finishPacking" />
    </shipvagoo-card-section>
    <shipvagoo-separator />
    <shipvagoo-card-section horizontal class="row" no-padding>
      <shipvagoo-card-section class="flex-1 start_pick_path_modal-pick_order-orders">
        <shipvagoo-card-section
          v-if="width <= 1024"
          class="start_pick_path_modal-pick_order-camera show-on-tablet"
          @click="onClickCamera()"
        >
          <div>Click to start the camera</div>
          <q-icon size="80px" name="fas fa-barcode"></q-icon>
        </shipvagoo-card-section>
        <shipvagoo-separator v-if="width <= 1024" />
        <!-- <shipvagoo-card-section class="orders_page-modal-pick_order-orders-section_title"
            >Bin is missing
          </shipvagoo-card-section> -->
        <shipvagoo-card-section
          v-for="product of startPickPathItems"
          :key="`${product.order_code}-${product.product?.title}-${startPickPathItems.indexOf(
            product,
          )}`"
          v-ripple
          class="start_pick_path_modal-pick_order-orders-order_item cursor-pointer"
          :class="{
            'start_pick_path_modal-pick_order-orders-order_item--selected':
              selectedOrderProduct?.order_code === product.order_code &&
              selectedOrderProduct?.line_item_code === product.line_item_code,
          }"
          :style="{
            display: isItemFulfilled(product) ? 'none' : 'flex',
          }"
          @click="onClickOrderProduct(product)"
        >
          <div class="start_pick_path_modal-pick_order-orders-order_item-info">
            <div class="start_pick_path_modal-pick_order-orders-order_item-title">
              {{ product.product?.title }}
            </div>
          </div>
          <div class="start_pick_path_modal-pick_order-orders-order_item-pick_info">
            <div class="start_pick_path_modal-pick_order-orders-order_item-pick_info-pick_text">
              Pick
            </div>
            <div class="start_pick_path_modal-pick_order-orders-order_item-pick_info-pick_number">
              {{ getRemainingUnpickedCount(product) }}
            </div>
          </div>
        </shipvagoo-card-section>

        <shipvagoo-card-section
          v-for="[key, box] of Object.entries(boxes)"
          :key="
            box
              .map((product) => `${product.order_code}-${product.product?.title}-${product.picked}`)
              .join('-')
          "
          style="padding: 0"
        >
          <shipvagoo-card-section class="start_pick_path_modal-pick_order-orders-section_title">
            Picked - box #{{ key }}
          </shipvagoo-card-section>
          <shipvagoo-separator />
          <shipvagoo-card-section
            v-for="product of box"
            :key="product.line_item_code"
            v-ripple
            class="start_pick_path_modal-pick_order-orders-order_item start_pick_path_modal-pick_order-orders-order_item--picked cursor-pointer"
            @click="onClickOrderProduct(product)"
          >
            <div
              style="
                position: absolute;
                top: 0;
                left: 0;
                width: 6px;
                height: 100%;
                background-color: #389b99;
              "
            ></div>
            <div class="start_pick_path_modal-pick_order-orders-order_item-info">
              <div class="start_pick_path_modal-pick_order-orders-order_item-title">
                {{ product.product?.title }}
              </div>
              <div class="start_pick_path_modal-pick_order-orders-order_item-picked_status">
                Picked: {{ product.picked }}
              </div>
            </div>
            <div class="start_pick_path_modal-pick_order-orders-order_item-pick_info">
              <div class="start_pick_path_modal-pick_order-orders-order_item-pick_info-pick_text">
                Pick
              </div>
              <div class="start_pick_path_modal-pick_order-orders-order_item-pick_info-pick_number">
                {{ product.quantity - product.picked }}
              </div>
            </div>
          </shipvagoo-card-section>
        </shipvagoo-card-section>
      </shipvagoo-card-section>
      <shipvagoo-separator vertical class="show-on-desktop" />
      <shipvagoo-card-section
        v-if="!selectedOrderProduct"
        class="flex-1 start_pick_path_modal-pick_order-order_detail show-on-desktop"
      >
        <div style="display: flex; align-items: center; justify-content: center; height: 100%">
          Select or scan item
        </div>
      </shipvagoo-card-section>
      <shipvagoo-card-section
        v-if="selectedOrderProduct"
        class="flex-1 start_pick_path_modal-pick_order-order_detail show-on-desktop"
      >
        <div class="start_pick_path_modal-pick_order-order_detail-container">
          <div
            v-if="!selectedOrderProduct.product?.image_url"
            class="start_pick_path_modal-pick_order-order_detail-no_image"
          >
            <q-icon name="img:/assets/img/image.svg" size="29px"></q-icon>
            <div>No Image</div>
          </div>
          <img
            v-if="selectedOrderProduct.product?.image_url"
            decoding="async"
            style="width: 100%; max-height: 300px; margin-bottom: 20px; object-fit: contain"
            :src="selectedOrderProduct.product.image_url"
          />
          <div class="start_pick_path_modal-pick_order-order_detail-add_to_box">
            <div class="start_pick_path_modal-pick_order-order_detail-add_to_box-head">
              <q-icon name="fas fa-box"></q-icon>
            </div>
            <div class="start_pick_path_modal-pick_order-order_detail-add_to_box-text">
              Add to box #{{ selectedOrderProductsBoxNumber }}
            </div>
          </div>

          <div class="start_pick_path_modal-pick_order-order_detail-order_name">
            {{ selectedOrderProduct.product?.title }}
          </div>

          <div class="start_pick_path_modal-pick_order-order_detail-count">
            <shipvagoo-button
              icon="ion-remove"
              color="shipvagoo_primary-three"
              size="12px"
              @click="onClickPickCounter('decrement')"
            ></shipvagoo-button>
            <div class="start_pick_path_modal-pick_order-order_detail-count-info">
              <div></div>
              <div>{{ selectedOrderProductCount }}</div>
              <div>of {{ selectedOrderProduct.quantity }}</div>
            </div>
            <shipvagoo-button
              color="shipvagoo_primary-three"
              icon="ion-ios-add"
              @click="onClickPickCounter('increment')"
            >
            </shipvagoo-button>
          </div>
          <shipvagoo-button
            color="shipvagoo_primary-three"
            class="start_pick_path_modal-pick_order-order_detail-confirm"
            label="Confirm pick"
            @click="onConfirmPick()"
          />
        </div>
      </shipvagoo-card-section>
    </shipvagoo-card-section>
  </q-card>

  <q-dialog v-model="pickOrderModalMobile" class="orders_page-modal">
    <q-card>
      <shipvagoo-card-section class="orders_page-modal-header row items-center">
        <q-btn
          v-if="selectedOrderProduct"
          class="show-on-tablet"
          icon="chevron_left"
          flat
          round
          dense
          @click="
            pickOrderModalMobile = false;
            selectedOrderProduct = null;
          "
        />
        <q-space />
        <div class="start_pick_path_modal-pick_order-header">
          <div
            v-if="selectedOrderCodes.length === 1"
            class="start_pick_path_modal-pick_order-header-title"
          >
            Pick Order {{ selectedOrderCodes[0] }}
          </div>
          <div
            v-if="selectedOrderCodes.length > 1"
            class="start_pick_path_modal-pick_order-header-title"
          >
            Pick {{ selectedOrderCodes.length }} orders
          </div>
          <div class="start_pick_path_modal-pick_order-header-picked_info">
            Picked 0 / {{ selectedOrderCodes.length }}
          </div>
        </div>
        <q-space />
        <q-btn v-close-popup icon="check" flat round dense @click="selectedOrderProduct = null" />
      </shipvagoo-card-section>

      <shipvagoo-card-section class="flex-1 start_pick_path_modal-pick_order-order_detail">
        <div class="start_pick_path_modal-pick_order-order_detail-container">
          <div
            v-if="!selectedOrderProduct?.product?.image_url"
            class="start_pick_path_modal-pick_order-order_detail-no_image"
          >
            <q-icon name="img:/src/assets/img/image.svg" size="29px"></q-icon>
            <div>No Image</div>
          </div>
          <img
            v-if="selectedOrderProduct?.product?.image_url"
            decoding="async"
            style="width: 100%; margin-bottom: 20px"
            :src="selectedOrderProduct.product.image_url"
          />
          <div class="start_pick_path_modal-pick_order-order_detail-add_to_box">
            <div class="start_pick_path_modal-pick_order-order_detail-add_to_box-head">
              <q-icon name="fas fa-box"></q-icon>
            </div>
            <div class="start_pick_path_modal-pick_order-order_detail-add_to_box-text">
              Add to box #{{ selectedOrderProductsBoxNumber }}
            </div>
          </div>

          <div class="start_pick_path_modal-pick_order-order_detail-order_name">
            {{ selectedOrderProduct?.product?.title ?? '' }}
          </div>

          <div class="start_pick_path_modal-pick_order-order_detail-count">
            <shipvagoo-button
              icon="ion-remove"
              size="12px"
              @click="onClickPickCounter('decrement')"
            />
            <div class="start_pick_path_modal-pick_order-order_detail-count-info">
              <div></div>
              <div>{{ selectedOrderProductCount }}</div>
              <div>of {{ selectedOrderProduct?.quantity }}</div>
            </div>
            <shipvagoo-button icon="ion-ios-add" @click="onClickPickCounter('increment')" />
          </div>
          <shipvagoo-button
            color="shipvagoo_primary-three"
            class="start_pick_path_modal-pick_order-order_detail-confirm"
            label="Confirm pick"
            :disabled="selectedOrderProductCount <= 0"
            @click="onConfirmPick()"
          />
        </div>
      </shipvagoo-card-section>
    </q-card>
  </q-dialog>

  <q-dialog v-model="finishPackingModal" class="orders_page-modal">
    <shipvagoo-modal title="Finish packing" size="sm">
      <shipvagoo-card-section>
        <div class="orders_page-success-container">
          <div class="orders_page-success">
            <img src="/assets/img/success.svg" decoding="async" />
          </div>
          <div class="orders_page-success-info">
            <p>
              Orders processed {{ selectedOrderCodes.length }} of
              {{ selectedOrderCodes.length }}
            </p>
          </div>
        </div>
      </shipvagoo-card-section>

      <shipvagoo-card-section class="orders_page-modal-footer">
        <shipvagoo-button label="Close" @click="emit('onClose')"></shipvagoo-button>
      </shipvagoo-card-section>
    </shipvagoo-modal>
  </q-dialog>

  <q-dialog v-model="cancelPickModal" class="orders_page-modal">
    <shipvagoo-modal title="Cancel pick path" size="sm">
      <shipvagoo-card-section label="Cancel pick">
        Are you sure you want to abort?
      </shipvagoo-card-section>
      <shipvagoo-separator />
      <shipvagoo-card-actions>
        <shipvagoo-button
          class="flex-1"
          @click="
            cancelPickPath();
            cancelPickModal = false;
          "
        >
          Yes
        </shipvagoo-button>
        <shipvagoo-button class="flex-1" outline @click="cancelPickModal = false">
          No
        </shipvagoo-button>
      </shipvagoo-card-actions>
    </shipvagoo-modal>
  </q-dialog>

  <q-dialog v-model="partiallyPickedModal" class="orders_page-modal">
    <shipvagoo-modal title="Items partially picked" size="sm">
      <shipvagoo-card-section label="Cancel pick">
        Not all items are picked. Do you want to continue anyway?
      </shipvagoo-card-section>
      <shipvagoo-separator />
      <shipvagoo-card-actions>
        <shipvagoo-button
          class="flex-1"
          @click="
            onConfirmPick(true);
            partiallyPickedModal = false;
          "
        >
          Yes
        </shipvagoo-button>
        <shipvagoo-button class="flex-1" outline @click="partiallyPickedModal = false">
          No
        </shipvagoo-button>
      </shipvagoo-card-actions>
    </shipvagoo-modal>
  </q-dialog>
</template>

<script setup lang="ts">
  import { computed, onBeforeMount, ref, Ref, watch } from 'vue';
  import { find } from 'lodash-es';
  import { QVueGlobals, useQuasar } from 'quasar';
  import { useStore } from 'vuex';
  import { IOrder, IOrderProduct } from '../../model/order.model';
  import useWidth from '../../hooks/useWidth';
  import { IRootState } from '../../model/store.model';
  import OrderService from '../../services/order.service';

  interface Props {
    orders: IOrder[];
  }

  interface IStartPickPathItem extends IOrderProduct {
    order_code: number;
    picked: number;
  }

  const $q: QVueGlobals = useQuasar();
  defineExpose({
    $q,
  });
  const $store = useStore<IRootState>();
  const width = useWidth();

  const pickOrderModalMobile = ref(false);
  const selectedOrderProductCount = ref<number>(0);
  const selectedOrderProduct = ref<IStartPickPathItem | null>(null);
  const finishPackingModal: Ref<boolean> = ref(false);
  const cancelPickModal: Ref<boolean> = ref(false);
  const partiallyPickedModal: Ref<boolean> = ref(false);
  const boxes = ref<{ [key: number]: IStartPickPathItem[] }>({});

  const props = defineProps<Props>();
  const emit = defineEmits(['onClose']);

  const getBoxNumberOfItem = (item: IStartPickPathItem) => {
    return props.orders.findIndex((order: IOrder) => order.order_code === item.order_code) + 1;
  };

  const getTotalPickedCountOfTheBox = (boxNumber: number) => {
    return boxes.value?.[boxNumber]?.reduce((acc, item) => acc + item.picked, 0) || 0;
  };

  const getTotalQuantityOfTheOrder = (order: IOrder) => {
    return order.line_items.reduce((acc, item) => acc + item.quantity, 0);
  };

  const getOrderCountDonePicking = () => {
    return props.orders.filter((order: IOrder) => {
      const totalQuantity = getTotalQuantityOfTheOrder(order);
      const totalPicked = getTotalPickedCountOfTheBox(props.orders.indexOf(order) + 1);

      return totalPicked === totalQuantity;
    }).length;
  };

  const isItemFulfilled = (item: IStartPickPathItem) => {
    return (
      find(boxes.value[getBoxNumberOfItem(item)], {
        order_code: item.order_code,
        line_item_code: item.line_item_code,
      })?.picked === item.quantity
    );
  };

  const getRemainingUnpickedCount = (item: IStartPickPathItem): number => {
    const boxedItems = find(boxes.value[getBoxNumberOfItem(item)], {
      order_code: item.order_code,
      line_item_code: item.line_item_code,
    });

    if (!boxedItems) {
      return item.quantity;
    }

    return item.quantity - boxedItems.picked;
  };

  const selectedOrderProductsBoxNumber = computed(() => {
    if (!selectedOrderProduct.value) {
      return 0;
    }

    return getBoxNumberOfItem(selectedOrderProduct.value);
  });

  const selectedOrderCodes = computed(() => props.orders.map((order: IOrder) => order.order_code));

  const startPickPathItems = computed((): IStartPickPathItem[] =>
    props.orders
      .map((order: IOrder) =>
        OrderService.getUnfulfilledItems(order.fulfillments, order.line_items)
          .flat()
          .map((item: IOrderProduct) => ({ ...item, order_code: order.order_code, picked: 0 })),
      )
      .flat(),
  );

  const onClickOrderProduct = (product: IStartPickPathItem) => {
    selectedOrderProductCount.value = 0;
    selectedOrderProduct.value = product;
  };

  const finishPacking = () => {};

  const resetSelectedProductFields = () => {
    selectedOrderProductCount.value = 0;
    selectedOrderProduct.value = null;
  };

  const onConfirmPick = (allowPartialPick: boolean = false) => {
    if (!selectedOrderProduct.value || selectedOrderProductCount.value < 1) {
      return;
    }

    if (!boxes.value[selectedOrderProductsBoxNumber.value]) {
      boxes.value[selectedOrderProductsBoxNumber.value] = [];
    }

    if (
      !allowPartialPick &&
      selectedOrderProductCount.value < selectedOrderProduct.value.quantity
    ) {
      partiallyPickedModal.value = true;
      return;
    }

    boxes.value[selectedOrderProductsBoxNumber.value]?.push({
      ...selectedOrderProduct.value,
      picked: selectedOrderProductCount.value,
    });

    if ($store.state.pickSettingsStore.go_to_next_item) {
      const currentItemIndex = startPickPathItems.value.findIndex(
        (item: IStartPickPathItem) =>
          item.order_code === selectedOrderProduct.value?.order_code &&
          item.line_item_code === selectedOrderProduct.value?.line_item_code,
      );

      if (currentItemIndex === -1) {
        resetSelectedProductFields();
        return;
      }

      const nextItem = startPickPathItems.value[currentItemIndex + 1];

      if (nextItem) {
        onClickOrderProduct(nextItem);
      }
    } else {
      resetSelectedProductFields();
    }
  };

  const onClickPickCounter = (type: 'increment' | 'decrement') => {
    if (type === 'increment') {
      selectedOrderProductCount.value += 1;
    }

    if (type === 'decrement') {
      selectedOrderProductCount.value -= 1;
    }
  };

  const cancelPickPath = () => {
    emit('onClose');
  };

  const onClickCamera = () => {};

  watch(
    () => selectedOrderProductCount.value,
    (value: number) => {
      if (value < 0) {
        selectedOrderProductCount.value = 0;
      }

      if (selectedOrderProduct.value && value > selectedOrderProduct.value.quantity) {
        selectedOrderProductCount.value = selectedOrderProduct.value.quantity;
      }
    },
  );

  watch(width, (widthValue: number) => {
    if (selectedOrderProduct.value && widthValue <= 1024) {
      pickOrderModalMobile.value = true;
    }

    if (selectedOrderProduct.value && widthValue > 1024 && pickOrderModalMobile.value) {
      pickOrderModalMobile.value = false;
    }
  });

  onBeforeMount(() => {
    if ($store.state.pickSettingsStore.start_at_first_item) {
      onClickOrderProduct(startPickPathItems.value[0]);
    }
  });
</script>

<style scoped lang="scss">
  .start_pick_path_modal {
    &-pick_order {
      border-radius: 10px;

      &-header {
        display: flex;
        flex-direction: column;
        align-items: center;

        &-picked_info {
          font-size: 13px;
        }
      }

      &-camera {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding-top: 37px;
        padding-bottom: 56px;
        background: #000000;

        & > *:not(:first-child) {
          margin-top: 7px;
        }
      }

      &-orders {
        max-height: 600px;
        padding: 0 !important;
        overflow-y: auto;

        &-section_title {
          padding: 14px 20px;
          font-size: 13px;
          background-color: #000000;
        }

        &-order_item {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          border-bottom: 1px solid #788795;
          transition: background-color 150ms ease-in-out;

          &--selected {
            background-color: #131393;
          }

          &--picked {
            background-color: #1c303d;
          }

          &-pick_info {
            display: flex;
            flex-direction: column;
            align-items: center;

            &-pick_text {
              font-size: 13px;
              color: #9eb2c6;
            }

            &-pick_number {
              font-size: 20px;
              color: #ffffff;
            }
          }

          &-order_item-info {
            display: flex;
            flex-direction: column;
          }

          &-title {
            margin-bottom: 13px;
            font-size: 15px;
            line-height: 22px;
          }

          &-picked_status {
            font-size: 11px;
            line-height: 24px;
          }

          &:hover {
            @media (hover: hover) {
              background: #2929c8;
            }
          }
        }
      }

      &-order_detail {
        height: 600px;
        padding: 20px;

        &-container {
          display: flex;
          flex-direction: column;
        }

        &-order_name {
          margin-top: 20px;
          font-size: 15px;
        }

        &-confirm {
          display: flex;
          width: 100%;
          margin-top: 20px;
          margin-bottom: 20px;
        }

        &-count {
          display: flex;
          width: 100%;
          height: 40px;
          margin-top: 20px;
          border: 1px solid #2929c8;
          border-radius: 5px;

          button {
            background: #2929c8;
          }

          &-info {
            display: flex;
            flex: 1;
            align-items: center;
            justify-content: space-between;
            padding: 0 14px;
          }
        }

        &-no_image {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 140px 0;
          font-size: 15px;

          & > *:not(:first-child) {
            margin-top: 13px;
          }
        }

        &-add_to_box {
          display: flex;
          flex-direction: row;
          align-items: center;
          height: 40px;
          border: 1px solid #2929c8;
          border-radius: 5px;

          &-head {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: #2929c8;
            border-radius: 5px;
          }

          &-text {
            padding-left: 10px;
            font-size: 15px;
            line-height: 25px;
          }
        }
      }
    }
  }
</style>
