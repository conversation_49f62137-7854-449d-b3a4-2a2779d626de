<template>
  <shipvagoo-modal separator-class="q-mx-lg" title="Capture payment" size="md">
    <shipvagoo-card-section>
      <shipvagoo-input-v1
        v-model="amount"
        type="text"
        label="Amount"
        placeholder="Enter amount"
        :suffix="remainingBalance?.currency"
        :error="$v.amount.$error"
        :error-message="String($v.amount.$errors?.[0]?.$message ?? null)"
      >
      </shipvagoo-input-v1>
    </shipvagoo-card-section>
    <shipvagoo-card-actions>
      <shipvagoo-button color="brand" outline min-width="80px" v-close-popup>
        Cancel
      </shipvagoo-button>
      <shipvagoo-button color="brand" min-width="80px" @click="onClickCapturePayment">
        Accept {{ `${amount} ${remainingBalance.currency}` }}
      </shipvagoo-button>
    </shipvagoo-card-actions>
  </shipvagoo-modal>
</template>
<script lang="ts" setup>
  import useVuelidate from '@vuelidate/core';
  import { helpers, required } from '@vuelidate/validators';

  import { ref, computed, onMounted } from 'vue';
  import { useHelper } from '../../composeable/Helper';
  import {
    loadingService,
    loggingService,
    notificationService,
    orderService,
  } from '../../services/_singletons';
  import { IIntCurrency, IOrder, IOrderTransaction } from '../../model/order.model';
  import OrderService from '../../services/order.service';
  import PriceService from '../../services/price.service';

  const { mustBeValidInput, mustBeValidLength, mustBeValidMinValue } = useHelper();

  interface IProps {
    order: IOrder;
  }

  const emit = defineEmits(['refresh-order']);
  const props = defineProps<IProps>();
  const price = ref<IIntCurrency>(OrderService.getPrice(props.order));
  const amount = ref<string>('0,00');

  const parseIntlAmount = (freight: string) =>
    Number(freight?.replace(/[,.]/g, (match) => (match === ',' ? '.' : '')) || 0);
  const parseDanishAmount = (freight: string) =>
    freight?.replace(/[.,]/g, (match) => (match === '.' ? ',' : '')) || '0,00';

  const authorizedAmount = computed(() => {
    const amount =
      props.order.transactions?.reduce((acc: number, transaction: IOrderTransaction) => {
        if (
          transaction.kind.toLowerCase() === 'authorization' &&
          transaction.status.toLowerCase() == 'success'
        ) {
          return acc + Number(transaction.amount);
        }
        return acc;
      }, 0) ?? 0;
    return amount;
  });

  const captureAmount = computed(() => {
    const amount =
      props.order.transactions?.reduce((acc: number, transaction: IOrderTransaction) => {
        if (
          transaction.kind.toLowerCase() === 'capture' &&
          transaction.status.toLowerCase() == 'success'
        ) {
          return acc + Number(transaction.amount);
        }
        return acc;
      }, 0) ?? 0;
    return amount;
  });
  const remainingBalance = computed(() => {
    const currency =
      props.order.transactions?.find(
        (transaction) =>
          transaction.kind.toLowerCase() == 'capture' ||
          transaction.kind.toLowerCase() == 'authorization',
      )?.currency ?? price.value.currency;
    return {
      amount: parseFloat((authorizedAmount.value - captureAmount.value).toFixed(2)),
      currency: currency,
    };
  });

  const mustValidMaxLength = helpers.withParams({ type: 'maxValue' }, (value: any) => {
    console.log('Value ', value);
    console.log('Remaining balance ', remainingBalance.value.amount);
    console.log('Parsed value ', parseIntlAmount(value));
    return (parseIntlAmount(value) ?? 0) <= remainingBalance.value.amount;
  });
  const rules = {
    amount: {
      required: helpers.withMessage('This field is required.', required),
      mustBeValidInput: helpers.withMessage('Only valid input is required.', mustBeValidInput),
      mustBeValidLength: helpers.withMessage(
        'Amount value can not be greater than 6 digits.',
        mustBeValidLength,
      ),
      minValue: helpers.withMessage(
        'Amount value should be greater than 0.',
        mustBeValidMinValue(0.000001),
      ),
      maxValue: helpers.withMessage(
        `${PriceService.formatInternationalPrice(
          remainingBalance.value,
        )} available to capture payment`,
        mustValidMaxLength,
      ),
    },
  };

  const $v = useVuelidate(rules, { amount });

  const onClickCapturePayment = async () => {
    await $v.value.$validate();
    if ($v.value.$error || !amount.value) {
      return;
    }
    try {
      loadingService.startLoading('main-loader:capture-payment');

      const response = await orderService.capturePayment(props.order.order_code, {
        amount: parseIntlAmount(amount.value),
        currency_code: remainingBalance.value.currency,
      });
      if (response.data == 'SUCCESS') {
        notificationService.showSuccess('Payment captured successfully.');
      }
    } catch (e: any) {
      console.log('ERROR: ', e);
      loggingService.error('Error while capturing payment', e);
      notificationService.showError(e.response?.data?.error ?? 'Error while capturing payment');
    } finally {
      setTimeout(() => {
        loadingService.stopLoading('main-loader:capture-payment');
        emit('refresh-order');
      }, 2000);
    }
  };

  onMounted(() => {
    amount.value = parseDanishAmount(String(remainingBalance.value.amount.toFixed(2))) ?? '0,00';
  });
</script>
