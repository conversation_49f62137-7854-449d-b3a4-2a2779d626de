<template>
  <shipvagoo-modal
    :header-separator="false"
    size="md"
    title="Change shipping rule"
    @close="onClickClose"
  >
    <div class="q-mx-lg">
      <shipvagoo-separator />
    </div>
    <shipvagoo-card-section>
      <div class="change_shipment_template__select_container">
        <shipvagoo-select-v1
          v-model="selectedDeliveryTemplate"
          @update:model-value="debouncedOnChangeTemplate"
          class="change_shipment_template__select"
          label="Shipment rule"
          :options="templates"
          @filter="filterShipmentTemplates"
          use-input
          hide-selected
          fill-input
          input-debounce="0"
          option-value="template_code"
          option-label="name"
        >
          <template #prepend>
            <q-icon name="search" size="sm"></q-icon>
          </template>
        </shipvagoo-select-v1>
      </div>
      <div>
        <shipvagoo-select-v1
          placeholder="Select"
          class="q-mt-md"
          v-if="actualServicePoints.length > 0"
          v-model="service_point"
          :options="servicePoints"
          label="Parcel shop"
          option-label="label"
          option-value="id"
          use-input
          hide-selected
          fill-input
          input-debounce="0"
          @filter="filterParcelShops"
        >
          <template #prepend>
            <q-icon name="search" size="sm"></q-icon>
          </template>
        </shipvagoo-select-v1>
      </div>
      <!--      <div class="change_shipment_template__select_container">
              <shipvagoo-select
                v-model="selectedReturnTemplate"
                class="change_shipment_template__select"
                label="Return shipment template:"
                :options="templates"
                option-value="template_code"
                option-label="name"
              />
            </div>-->
    </shipvagoo-card-section>
    <shipvagoo-card-actions>
      <shipvagoo-button min-width="80px" @click="onClickCreateTemplate">
        Create shipping rule
      </shipvagoo-button>
      <shipvagoo-button
        min-width="80px"
        @click="onClickSave"
        :disable="selectedDeliveryTemplate == null || selectedDeliveryTemplate.id == -1"
      >
        {{ shipmentCode ? 'Create shipment' : 'Save' }}</shipvagoo-button
      >
    </shipvagoo-card-actions>
    <q-dialog v-model="isTemplateDetailOpen" @before-hide="onCloseTemplateDetail()">
      <template-modal @close="onCloseTemplateDetail" />
    </q-dialog>
  </shipvagoo-modal>
</template>

<script setup lang="ts">
  import { useStore } from '../../store';
  import { debounce } from 'lodash';
  import { computed, onBeforeMount, ref, watch } from 'vue';
  import { ITemplate } from '../../model/template.model';
  import {
    loadingService,
    loggingService,
    notificationService,
    orderService,
    templateService,
    shipmentService,
  } from '../../services/_singletons';
  import * as _ from 'lodash-es';
  import { IParcelShop } from '../../model/shipment.model';
  import CarrierService from '../../services/carrier.service';
  import { ICarrier } from '../../model/carrier.model';
  import { IOrderAddress } from '../../model/order.model';

  const none: ITemplate = {
    id: -1,
    template_code: -1,
    name: 'None',
    additional_services: null,
    carrier_product_code: -1,
    created_at: '',
    is_default: 0,
    parcels: [],
    receiver_country_code: -1,
    sender_country_code: -1,
    updated_at: '',
    receiver_country: null,
    sender_country: null,
  };
  const $store = useStore();
  // eslint-disable-next-line @typescript-eslint/naming-convention, no-underscore-dangle
  const _templates = ref<ITemplate[]>([]);

  const templates = ref<ITemplate[]>([]);
  const servicePoints = ref<(IParcelShop & { label: string })[]>([]);
  const actualServicePoints = ref<(IParcelShop & { label: string })[]>([]);
  const service_point = ref<IParcelShop | null>(null);
  const carrier = ref<ICarrier | null>(null);
  const isTemplateDetailOpen = ref<boolean>(false);
  const selectedDeliveryTemplate = ref<ITemplate | null>(null);
  const selectedReturnTemplate = ref<ITemplate | null>(null);

  interface Props {
    orderCode?: number | null;
    deliveryTemplateCode: number | null;
    returnTemplateCode: number | null;
    shippingAddress?: IOrderAddress;
    shippingLineCode?: string | null;
    shipmentCode?: number | null;
  }

  const props = defineProps<Props>();
  const emit = defineEmits(['close']);

  const onClickCreateTemplate = () => {
    isTemplateDetailOpen.value = true;
  };
  const onCloseTemplateDetail = () => {
    isTemplateDetailOpen.value = false;
    getTemplates();
  };
  const getTemplates = async () => {
    try {
      loadingService.startLoading('main-loader:get-templates');
      const receiverCountry =
        $store.state.countryStore.countriesIndexedByIso[
          props.shippingAddress?.country_code ?? ''
        ] || null;
      let payload = [{ key: 'with', value: 'carrierProduct' }];
      if (receiverCountry) {
        payload = [
          ...payload,
          {
            key: 'conditions[receiver_country_code:equal]',
            value: String(receiverCountry.country_code),
          },
          {
            key: 'conditions[sender_country_code:equal]',
            value: String($store.state.companyStore.country_code),
          },
        ];
      }

      const result = await templateService.getTemplates(1, 1000, payload);
      _templates.value = _.orderBy(result.entities, ['name'], ['asc']);
      templates.value = actualTemplates.value;
      setDeliveryTemplateAndReturnTemplate();
    } catch (e) {
      loggingService.error('Error getting templates', e);
      notificationService.showError('Error getting templates');
    } finally {
      loadingService.stopLoading('main-loader:get-templates');
    }
  };

  const actualTemplates = computed((): ITemplate[] => {
    return [none, ..._templates.value];
  });
  const onClickClose = () => {
    emit('close', { isSaved: false });
  };
  const onClickSave = async () => {
    try {
      loadingService.startLoading('main-loader:change-shipment-template');
      let deliveryTemplateCode: number | null = null;
      let returnTemplateCode: number | null = null;

      if (selectedDeliveryTemplate.value) {
        deliveryTemplateCode =
          selectedDeliveryTemplate.value.template_code !== -1
            ? selectedDeliveryTemplate.value.template_code
            : null;
      }

      if (selectedReturnTemplate.value) {
        returnTemplateCode =
          selectedReturnTemplate.value.template_code !== -1
            ? selectedReturnTemplate.value.template_code
            : null;
      }
      //($data['parcelShop']['carrier'] ?? '') . " PakkeShop  $pakkeshops " . '-' . ($data['parcelShop']['name'] ?? '')
      const payload = {
        delivery_template_code: deliveryTemplateCode,
        return_template_code: returnTemplateCode,
        parcel_shop: service_point.value
          ? {
              code: `${selectedDeliveryTemplate.value?.carrier_product_code}#${service_point.value.id}`,
              title: `${service_point.value.carrier} PakkeShop ${service_point.value.id} -${service_point.value.name}`,
            }
          : null,
      };
      if (props.shipmentCode) {
        await orderService.setDeliveryMethodForShipment(props.shipmentCode, payload);
      } else if (props.orderCode) {
        await orderService.setDeliveryMethod(props.orderCode, payload);
      }
      notificationService.showSuccess('Shipping rule updated successfully.');
      emit('close', { isSaved: true });
    } catch (e: any) {
      loggingService.error('Error saving templates', e);
      notificationService.showError(
        e.response.data.error ? e.response.data.error : 'Error saving templates',
      );
    } finally {
      loadingService.stopLoading('main-loader:change-shipment-template');
    }
  };

  const setFirstServicePoint = () => {
    const [firstServicePoint] = servicePoints.value;
    console.log(firstServicePoint);
    if (firstServicePoint) {
      service_point.value = firstServicePoint;
    } else {
      service_point.value = null;
    }
    return service_point.value;
  };

  const fetchServicePoints = async (carrier: ICarrier, address: IOrderAddress) => {
    try {
      loadingService.startLoading('main-loader:service-points');
      const response = await shipmentService.getParcelShops(
        carrier.carrier_id,
        address.country_code,
        address.address1,
        address.zip,
        address.city,
      );
      return response.map((parcelShop) => ({
        ...parcelShop.parcelShop,
        distance: `(${(parcelShop.distance / 1000).toFixed(1)} km)`,
        label: `${parcelShop.parcelShop.name} - ${parcelShop.parcelShop.addressLine1}, Pakkeshop:${
          parcelShop.parcelShop.id
        }, ${parcelShop.parcelShop.country}-${parcelShop.parcelShop.postCode}, ${
          parcelShop.parcelShop.city
        } (${(parcelShop.distance / 1000).toFixed(1)} km)`,
      }));
    } catch (e) {
      notificationService.showError('No service points found');
      return [];
    } finally {
      loadingService.stopLoading('main-loader:service-points');
    }
  };

  const onChangeTemplate = async (template: ITemplate) => {
    selectedReturnTemplate.value = template;
    const shippingAddress = props.shippingAddress;
    if (template?.carrier_product?.hasMultiParcelShop && shippingAddress) {
      const carrierProductCode = template.carrier_product_code;
      if (carrierProductCode) {
        const carrierProducts = $store.state.courierStore.carrierProducts;
        carrier.value = CarrierService.getCarrierFromShippingProductCode(
          $store.state.courierStore.carriers,
          carrierProducts,
          carrierProductCode,
        );
        if (carrier.value) {
          servicePoints.value = await fetchServicePoints(carrier.value, shippingAddress);
          actualServicePoints.value = servicePoints.value;
          const shippingLineCode = props.shippingLineCode;

          if (shippingLineCode) {
            const servicePointId = shippingLineCode.split('#')[1];
            const servicePoint = servicePoints.value.find((sp) => sp.id == servicePointId);
            service_point.value = servicePoint || setFirstServicePoint();
          } else {
            setFirstServicePoint();
          }
        }
      }
    } else {
      resetServicePoints();
    }
  };

  const resetServicePoints = () => {
    service_point.value = null;
    servicePoints.value = [];
    actualServicePoints.value = [];
  };

  const debouncedOnChangeTemplate = debounce(onChangeTemplate, 300);

  const setDeliveryTemplate = async () => {
    const [none] = templates.value;
    if (!props.deliveryTemplateCode) {
      selectedDeliveryTemplate.value = none;
      return;
    }
    const template = templates.value.find((t) => t.template_code === props.deliveryTemplateCode);
    if (!template) {
      selectedDeliveryTemplate.value = none;
      return;
    } else {
      await onChangeTemplate(template);
    }
    selectedDeliveryTemplate.value = template;

    /** added on request of QA */
    selectedReturnTemplate.value = template;
  };
  /** commented on request of QA */
  /* 
  const setReturnTemplate = () => {
    const [none] = templates.value;
    if (!props.returnTemplateCode) {
      selectedReturnTemplate.value = none;
      return;
    }
    const template = templates.value.find((t) => t.template_code === props.returnTemplateCode);
    if (!template) {
      selectedReturnTemplate.value = none;
      return;
    }
    selectedReturnTemplate.value = template;
  }; */
  const setDeliveryTemplateAndReturnTemplate = () => {
    setDeliveryTemplate();
    //setReturnTemplate();
  };

  watch(
    () => templates.value,
    () => {
      const [none] = templates.value;

      selectedDeliveryTemplate.value =
        templates.value.find((t) => t.template_code === props.deliveryTemplateCode) || none;

      /* selectedReturnTemplate.value =
        templates.value.find((t) => t.template_code === props.returnTemplateCode) || none; */
      selectedReturnTemplate.value = selectedDeliveryTemplate.value;
    },
    { deep: true },
  );

  const filterList = (list: any, value: any, key: string) => {
    if (value === '') return list;
    const needle = value.toLowerCase();
    return list.filter((item: any) => item[key].toLowerCase().includes(needle));
  };
  const filterParcelShops = (val: any, update: any) => {
    update(() => {
      servicePoints.value = filterList(actualServicePoints.value, val, 'label');
    });
  };
  const filterShipmentTemplates = (val: any, update: any) => {
    update(() => {
      templates.value = filterList(actualTemplates.value, val, 'name');
    });
  };

  onBeforeMount(() => {
    getTemplates();
  });
</script>

<style scoped lang="scss">
  .change_shipment_template__select_container {
    display: flex;
    gap: 20px;

    .change_shipment_template__select {
      flex: 1;
    }

    align-items: flex-end;
    width: 100%;
  }

  .change_shipment_template__select_container + .change_shipment_template__select_container {
    margin-top: 20px;
  }
</style>
