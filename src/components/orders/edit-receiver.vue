<template>
  <shipvagoo-modal
    separator-class="q-mx-lg"
    title="Edit recipient / shipping address"
    size="md"
    @close="onClose"
  >
    <shipvagoo-card-section>
      <div class="two-columns-responsive gap-20">
        <shipvagoo-input-v1
          v-model="receiver.name"
          class="flex-1"
          label="Name"
          placeholder="Enter name"
          :error="$v.name.$error"
          :error-message="$v.name.$errors?.[0]?.$message.toString()"
        />
        <shipvagoo-input-v1
          v-model="receiver.email"
          class="flex-1"
          label="Email"
          placeholder="Enter email"
          :error="$v.email.$error"
          :error-message="$v.email.$errors?.[0]?.$message.toString()"
        />
      </div>
      <div class="two-columns-responsive gap-20 q-mt-lg">
        <shipvagoo-input-v1
          v-model="receiver.phone"
          class="flex-1"
          :label="`Mobile ${getDialCode(receiver.country?.iso)}`"
          placeholder="Enter phone/mobile"
          :error="$v.phone.$error"
          :error-message="$v.phone.$errors?.[0]?.$message.toString()"
        />
        <shipvagoo-input-v1
          v-model="receiver.address1"
          class="flex-1"
          label="Address"
          placeholder="Enter address"
          :error="$v.address1.$error"
          :error-message="$v.address1.$errors?.[0]?.$message.toString()"
        />
      </div>
      <div class="two-columns-responsive gap-20 q-mt-lg">
        <shipvagoo-input-v1
          v-model="receiver.address2"
          class="flex-1"
          label="Address 2"
          placeholder="Enter address 2"
          :error="$v.address2.$error"
          :error-message="$v.address2.$errors?.[0]?.$message.toString()"
        />
        <shipvagoo-input-v1
          v-model="receiver.zip"
          class="flex-1"
          label="Postal code"
          placeholder="Enter postal code"
          :error="$v.zip.$error"
          :error-message="$v.zip.$errors?.[0]?.$message.toString()"
          @update:model-value="onChangeZipCode($event)"
        />
      </div>
      <div class="two-columns-responsive gap-20 q-mt-lg">
        <shipvagoo-input-v1
          v-model="receiver.city"
          class="flex-1"
          label="City"
          placeholder="Enter city"
          :error="$v.city.$error"
          :error-message="$v.city.$errors?.[0]?.$message.toString()"
        />
        <shipvagoo-select-v1
          v-model="receiver.country"
          :options="$store.state.countryStore.countries"
          class="flex-1"
          label="Country"
          placeholder="Select country"
          option-value="country_code"
          option-label="default_name"
          :error="$v.country.$error"
          :error-message="$v.country.$errors?.[0]?.$message.toString()"
        >
          <template #prepend>
            <shipvagoo-country-icon v-if="receiver.country" :country="receiver.country" />
          </template>
          <template #option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section side>
                <shipvagoo-country-icon v-if="scope.opt" :country="scope.opt" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ scope.opt.default_name }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </shipvagoo-select-v1>
      </div>
    </shipvagoo-card-section>
    <shipvagoo-card-actions>
      <shipvagoo-button color="brand" outline min-width="80px" v-close-popup>
        Cancel
      </shipvagoo-button>
      <shipvagoo-button color="brand" min-width="80px" @click="onClickUpdateReceiver">
        Save
      </shipvagoo-button>
    </shipvagoo-card-actions>
  </shipvagoo-modal>
</template>

<script setup lang="ts">
  import useVuelidate from '@vuelidate/core';
  import { helpers, required, requiredIf, maxLength } from '@vuelidate/validators';
  import { onBeforeMount, ref } from 'vue';
  import * as _ from 'lodash-es';
  import { ICountry } from '../../model/country.model';
  import { IOrder, IOrderAddress, IUpdateReceiverRequest } from '../../model/order.model';
  import {
    loadingService,
    loggingService,
    mapService,
    notificationService,
    orderService,
  } from '../../services/_singletons';
  import { useStore } from '../../store';
  import postCodes from '../../assets/json/postcodes.json';
  import { ICarrier, IShippingProduct } from '../../model/carrier.model';
  import { useHelper } from '../../composeable/Helper';
  import CarrierService from '../../services/carrier.service';
  import HelperService from '../../services/helper.service';

  interface Props {
    order: IOrder;
    shippingAddress: IOrderAddress;
  }

  const $store = useStore();

  const props = defineProps<Props>();
  const emit = defineEmits(['close', 'refresh']);
  const {
    mustBeValidMobileLength,
    minFourAndMaxFourteenDigitsMobileForMontyProducts,
    nineDigitsMobileForMontyProducts,
    phoneRequiredForMontyProducts,
    mustBeValidEmail,
    getDialCode,
    replaceDialCode,
  } = useHelper();

  interface IForm {
    name: string | null;
    email: string | null;
    phone: string | null;
    mobile: string | null;
    address1: string | null;
    address2: string | null;
    zip: string | null;
    city: string | null;
    country: ICountry | null;
    delivery_instruction: string | null;
    latitude: string | null;
    longitude: string | null;
    province: string | null;
    province_code: string | null;
  }

  const receiver = ref<IForm>({
    name: null,
    email: null,
    phone: null,
    mobile: null,
    address1: null,
    address2: null,
    zip: null,
    city: null,
    country: null,
    delivery_instruction: null,
    province_code: '',
    province: '',
    latitude: '',
    longitude: '',
  });

  const shippingProduct = ref<IShippingProduct | null>(null);
  const carrier = ref<ICarrier | null>(null);
  const mustBeValidInput = helpers.withParams({ type: 'validNumber' }, (value: any) => {
    if (value && value.length > 0) {
      const regex = /^[0-9]*$/;
      return regex.test(value as string);
    } else {
      return true;
    }
  });

  const isRequiredReceiverPhone = () => {
    const montyProduct = shippingProduct.value?.product_id;
    let isRequiredForMontyProduct = false;
    if (montyProduct) {
      isRequiredForMontyProduct = phoneRequiredForMontyProducts[montyProduct ?? ''] ?? false;
    }
    return isRequiredForMontyProduct;
  };
  const $v = useVuelidate(
    {
      name: {
        required: helpers.withMessage('This field is required', required),
      },
      address1: {
        required: helpers.withMessage('This field is required', required),
        maxLength: maxLength(60),
      },
      address2: {
        maxLength: maxLength(60),
      },
      zip: {
        required: helpers.withMessage('This field is required', required),
      },
      city: {
        required: helpers.withMessage('This field is required', required),
      },
      country: {
        required: helpers.withMessage('This field is required', required),
      },
      phone: {
        required: helpers.withMessage(
          'Mobile number is mandatory.',
          requiredIf(() => {
            return isRequiredReceiverPhone();
          }),
        ),
        mustBeValidInput: helpers.withMessage(
          'Enter a valid mobile number only with digits.',
          mustBeValidInput,
        ),
        mustBeValidMobileLength: helpers.withMessage(
          () => {
            const montyProduct = shippingProduct.value?.product_id;
            if (nineDigitsMobileForMontyProducts[montyProduct ?? '']) {
              return 'Mobile number must consist of 9 digits';
            }

            if (minFourAndMaxFourteenDigitsMobileForMontyProducts[montyProduct ?? '']) {
              return 'Mobile number must consist of 4 to 40 digits.';
            }

            return 'Invalid mobile number length.';
          },
          (value: any) => mustBeValidMobileLength(value, shippingProduct.value?.product_id),
        ),
      },
      mobile: {},
      email: {
        required: helpers.withMessage('This field is required', required),
        email: helpers.withMessage('This field must be a valid email.', (value: any) =>
          mustBeValidEmail(value, carrier.value?.carrier_id),
        ),
        //  email: helpers.withMessage('Enter a valid email address.', email),
      },
      delivery_instruction: {},
    },
    receiver,
  );

  const onClose = () => {
    emit('close');
  };

  const onClickUpdateReceiver = async () => {
    await $v.value.$validate();

    if (
      $v.value.$error ||
      !receiver.value.name ||
      !receiver.value.address1 ||
      !receiver.value.zip ||
      !receiver.value.city ||
      !receiver.value.country
    ) {
      return;
    }

    // eslint-disable-next-line @typescript-eslint/naming-convention, no-underscore-dangle
    const name = HelperService.parseFullName(receiver.value.name);

    const _receiver: IUpdateReceiverRequest['shipping_address'] = {
      name: receiver.value.name,
      email: receiver.value.email,
      phone: receiver.value.phone,
      mobile: receiver.value.mobile,
      address1: receiver.value.address1,
      address2: receiver.value.address2,
      zip: receiver.value.zip,
      city: receiver.value.city,
      country: receiver.value.country.default_name,
      country_code: receiver.value.country.iso,
      delivery_introduction: receiver.value.delivery_instruction,
      dialCode: getDialCode(receiver.value.country.iso, false),

      first_name: `${name.first} ${name.middle}`.trim() ?? null,
      last_name: name.last ?? null,
      latitude: receiver.value.latitude,
      longitude: receiver.value.longitude,
      province: receiver.value.province,
      province_code: receiver.value.province_code,
    };

    try {
      loadingService.startLoading('main-loader:update-receiver');
      await orderService.updateReceiver(props.order.order_code, _receiver);
      notificationService.showSuccess(
        `Receiver updated successfully for order ${props.order.name ?? props.order.order_number}`,
      );
      onClose();
      emit('refresh');
    } catch (e) {
      notificationService.showError('Error while updating receiver');
      loggingService.error('Error while updating receiver', e);
    } finally {
      loadingService.stopLoading('main-loader:update-receiver');
    }
  };

  const onChangeZipCode = async (zip: string | number | null) => {
    if (!receiver.value.country || !zip || typeof zip !== 'string') {
      return;
    }

    const re = new RegExp(postCodes[receiver.value.country.iso].regex);

    if (!re.test(zip)) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:get-city-from-zip-code');
      const geo = await mapService.getMapInfo(receiver.value.country.iso, zip);

      if (_.size(geo.results) > 0) {
        const results = _.head(Object.values(geo.results));
        const result = _.head(results);

        receiver.value.city = result?.city || '';

        receiver.value.province = result?.province || receiver.value.province;
        receiver.value.province_code = result?.province_code || receiver.value.province_code;
        receiver.value.latitude = result?.latitude || receiver.value.latitude;
        receiver.value.longitude = result?.longitude || receiver.value.longitude;
      }
    } catch (e) {
      notificationService.showError('Error getting city from zip code');
      loggingService.error('Error getting city from zip code', e);
    } finally {
      loadingService.stopLoading('main-loader:get-city-from-zip-code');
    }
  };
  const setCarrierAndProduct = (): void => {
    const carrierProductCode = props.order.delivery_template?.carrier_product_code;
    if (carrierProductCode) {
      const carrierProducts = $store.state.courierStore.carrierProducts;
      shippingProduct.value =
        carrierProducts?.find((p) => p.carrier_product_code == carrierProductCode) ?? null;

      carrier.value = CarrierService.getCarrierFromShippingProductCode(
        $store.state.courierStore.carriers,
        carrierProducts,
        carrierProductCode,
      );
    }
  };
  onBeforeMount(() => {
    const { shippingAddress } = props;
    const defaultValues = {
      name: '',
      email: '',
      phone: '',
      mobile: '',
      address1: '',
      address2: '',
      zip: '',
      city: '',
      country_code: '',

      province_code: '',
      province: '',
      latitude: '',
      longitude: '',
    };
    const country = $store.state.countryStore.countries.find(
      (c) => c.iso === shippingAddress?.country_code,
    ) as ICountry;

    //const mobile = shippingAddress.mobile?.replace(getDialCode(country.iso), '');
    receiver.value = {
      ...defaultValues,
      ...shippingAddress,
      country: country,
      delivery_instruction: '',
      phone: replaceDialCode(shippingAddress.phone, shippingAddress.dialCode ?? ''),
    };

    setCarrierAndProduct();
  });
</script>
