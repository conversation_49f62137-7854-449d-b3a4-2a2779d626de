import {
  createRouter,
  create<PERSON>ebHistory,
  NavigationGuardNext,
  NavigationGuardWithThis,
  RouteLocationNormalized,
  Router,
  RouteRecordRaw,
} from 'vue-router';
import AppLayout from '../layouts/AppLayout.vue';
import ReturnLayout from '../layouts/ReturnLayout.vue';
import Layout from '../layouts/DefaultLayout.vue';
import { loadingService } from '../services/_singletons';
import NotFound from '../pages/NotFound.vue';

const AuthGuard: NavigationGuardWithThis<undefined> = (
  to: RouteLocationNormalized,
  _from: RouteLocationNormalized,
  next: NavigationGuardNext,
): void => {
  const authLocal: string | null = localStorage.getItem('token');
  const authSession: string | null = sessionStorage.getItem('token');

  const isAuthorized = Boolean(authLocal || authSession);

  if (isAuthorized && (to.path === '/login' || to.path === '/sign')) {
    return next('/');
  }

  if (!isAuthorized && to.path !== '/login' && to.path !== '/sign') {
    return next('/login?redirect_to=' + to.fullPath);
  }

  return next();
};

const routes: RouteRecordRaw[] = [
  // AppLayout using page
  {
    path: '/return/:webshopName',
    component: ReturnLayout,
    children: [
      {
        name: 'Return',
        path: '',
        component: () => import('../pages/CustomerReturnPage.vue'),
      },
    ],
  },
  {
    path: '/',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Dashboard',
        path: '',
        component: () => import('../pages/v1/DashboardPage.vue'),
      },
    ],
  },
  {
    path: '/help',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Help',
        path: '',
        component: () => import('../pages/HelpPage.vue'),
      },
    ],
  },
  {
    path: '/help-center',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'help-center',
        path: '',
        component: () => import('../pages/v1/HelpCenterPage.vue'),
      },
    ],
  },
  {
    path: '/login',
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Login',
        path: '',
        component: () => import('../pages/LoginPage.vue'),
      },
    ],
  },
  {
    path: '/sign',
    component: Layout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Sign',
        path: '',
        component: () => import('../pages/SignUpPage.vue'),
      },
    ],
  },
  {
    path: '/agreement',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Agreement',
        path: '',
        component: () => import('../pages/AgreementPage.vue'),
      },
    ],
  } /* {
    path: '/prices',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Prices',
        path: '',
        component: () => import('../pages/PricesPage.vue'),
      },
    ],
  },*/,
  {
    path: '/templates',
    component: AppLayout,
    beforeEnter: [AuthGuard],

    children: [
      {
        name: 'Templates',
        path: '',
        component: () => import('../pages/TemplatesPage.vue'),
      },
    ],
  },
  {
    path: '/contacts',
    component: AppLayout,
    beforeEnter: [AuthGuard],

    children: [
      {
        name: 'Contacts',
        path: '',
        component: () => import('../pages/ContactsPage.vue'),
      },
    ],
  },
  {
    path: '/terms',
    name: 'Terms & Condition',
    component: () => import('../pages/TermsConditionPage.vue'),
  },
  {
    path: '/registration-verified',
    name: 'Registration Verified',
    component: () => import('../pages/RegistrationVerifiedPage.vue'),
  },
  {
    path: '/shipped',
    component: AppLayout,
    beforeEnter: [AuthGuard],

    children: [
      {
        name: 'Shipped',
        path: '',
        component: () => import('../pages/ShippedPage.vue'),
      },
    ],
  },
  {
    path: '/returns',
    component: AppLayout,
    beforeEnter: [AuthGuard],

    children: [
      {
        name: 'Returns',
        path: '',
        component: () => import('../pages/ReturnsPage.vue'),
      },
    ],
  },
  {
    path: '/drafts',
    component: AppLayout,
    beforeEnter: [AuthGuard],

    children: [
      {
        name: 'Drafts',
        path: '',
        component: () => import('../pages/DraftsPage.vue'),
      },
    ],
  },
  {
    path: '/products',
    component: AppLayout,
    beforeEnter: [AuthGuard],

    children: [
      {
        name: 'Products',
        path: '',
        component: () => import('../pages/ProductsPage.vue'),
      },
    ],
  },
  {
    path: '/company',
    component: AppLayout,
    beforeEnter: [AuthGuard],

    children: [
      {
        name: 'Company',
        path: '',
        component: () => import('../pages/CompanyPage.vue'),
      },
    ],
  },
  {
    path: '/order-integration',
    component: AppLayout,
    beforeEnter: [AuthGuard],

    children: [
      {
        name: 'Order Integration',
        path: '',
        component: () => import('../pages/OrderIntegrationPage.vue'),
      },
    ],
  },
  {
    path: '/payment-gateway-integration',
    component: AppLayout,
    beforeEnter: [AuthGuard],

    children: [
      {
        name: 'Payment Gateway Integration',
        path: '',
        component: () => import('../pages/PaymentGatewayIntegrationPage.vue'),
      },
    ],
  },
  {
    path: '/forgot-password',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Forgot Password',
        component: () => import('../pages/v1/ForgotPasswordPage.vue'),
      },
    ],
  },
  {
    path: '/reset-password',
    component: Layout,
    children: [
      {
        name: 'Reset Password',
        path: '',
        component: () => import('../pages/v1/ResetPasswordPage.vue'),
      },
    ],
  },
  {
    path: '/terms',
    component: Layout,
    children: [
      {
        name: 'Terms & Condition',
        path: '',
        component: () => import('../pages/TermsConditionPage.vue'),
      },
    ],
  },
  {
    path: '/orders',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Orders',
        path: '',
        component: () => import('../pages/OrdersPage.vue'),
      },
    ],
  },
  {
    path: '/payment-settings',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Payment Settings',
        path: '',
        component: () => import('../pages/PaymentSettingsPage.vue'),
      },
    ],
  },
  {
    path: '/pick-settings',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Pick Settings',
        path: '',
        component: () => import('../pages/PickSettingsPage.vue'),
      },
    ],
  },
  {
    path: '/personalized-messages',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Personalized Messages',
        path: '',
        component: () => import('../pages/v1/TrackingMessagePage.vue'),
      },
    ],
  },
  {
    path: '/product-shelves',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Product Shelves',
        path: '',
        component: () => import('../pages/ProductShelfLabelsPage.vue'),
      },
    ],
  },
  {
    path: '/product-labels',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Product Labels',
        path: '',
        component: () => import('../pages/ProductLabelsPage.vue'),
      },
    ],
  },
  {
    path: '/add-funds',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Add funds',
        path: '',
        component: () => import('../pages/BalancePage.vue'),
      },
    ],
  },
  {
    path: '/credit-notes',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Credit Notes',
        path: '',
        component: () => import('../pages/CreditNotesPage.vue'),
      },
    ],
  },
  {
    path: '/invoices',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Invoices',
        path: '',
        component: () => import('../pages/InvoicesPage.vue'),
      },
    ],
  },
  {
    path: '/monthly-invoice/:monthlyInvoiceCode/specifications',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'MonthlyInvoiceSpecifications',
        path: '',
        component: () => import('../pages/SpecificationsPage.vue'),
      },
    ],
  },
  {
    path: '/billing',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Billing',
        path: '',
        component: () => import('../pages/BillingPage.vue'),
      },
    ],
  },
  {
    path: '/order-invoices',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Order Invoice',
        path: '',
        component: () => import('../pages/OrderInvoicePage.vue'),
      },
    ],
  },
  {
    path: '/transactions',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Transactions',
        path: '',
        component: () => import('../pages/TransactionsPage.vue'),
      },
    ],
  },
  {
    path: '/staff-invitations',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Staff Invitations',
        path: '',
        component: () => import('../pages/StaffInvitationsPage.vue'),
      },
    ],
  },
  {
    path: '/staff-accounts',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Staff Accounts',
        path: '',
        component: () => import('../pages/StaffAccountsPage.vue'),
      },
    ],
  },
  {
    path: '/return-portals',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Return Portals',
        path: '',
        component: () => import('../pages/ReturnPortalsPage.vue'),
      },
    ],
  },
  {
    path: '/packaging',
    component: AppLayout,
    beforeEnter: [AuthGuard],
    children: [
      {
        name: 'Packaging',
        path: '',
        component: () => import('../pages/PackagingPage.vue'),
      },
    ],
  },
  {
    path: '/pdf-settings',
    component: AppLayout,
    beforeEnter: [AuthGuard],

    children: [
      {
        name: 'PDF Settings',
        path: '',
        component: () => import('../pages/PdfSettingsPage.vue'),
      },
    ],
  },
  {
    path: '/printers',
    component: AppLayout,
    beforeEnter: [AuthGuard],

    children: [
      {
        name: 'Printers',
        path: '',
        component: () => import('../pages/PrintersPage.vue'),
      },
    ],
  },
  {
    path: '/shipping-methods/:integration_code',
    component: AppLayout,
    beforeEnter: [AuthGuard],

    children: [
      {
        name: 'Shipping Methods',
        path: '',
        component: () => import('../pages/ShippingMethodsPage.vue'),
      },
    ],
  },
  {
    path: '/api-access',
    component: AppLayout,
    beforeEnter: [AuthGuard],

    children: [
      {
        name: 'ApiAccessPage',
        path: '',
        component: () => import('../pages/ApiAccessPage.vue'),
      },
    ],
  },
  { path: '/:pathMatch(.*)*', component: NotFound },
];

const router: Router = createRouter({
  history: createWebHistory(),
  routes,
});

router.beforeEach((_to, _from, next) => {
  loadingService.startLoading('main-loader:lazy-loading');
  next();
  /* if (!_to.query.v) {
    let query = {
      ..._to.query,
      v: new Date().getTime()
    };
    next({ path: _to.path, query: query });
  } else {
    next();
  } */
});

router.afterEach(() => {
  loadingService.stopLoading('main-loader:lazy-loading');
});

export default router;
