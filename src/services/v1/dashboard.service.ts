import {
  IDashboardCarriers,
  IDashboardOrderStats,
  IDashboardSalesStats,
  IDashboardTasks
} from '../../model/v1/dashboard.model';
//import {IRequestParam} from '../../model/http.model';
import HttpService from './../http.service';

interface OrderParams {
  year: number | string
}

interface SalesStatsParams {
  filter: string,
  carriers: Array<string>,
  shop: string
}

class DashboardService {
  private readonly ROUTES = {
    GET_DASHBOARD_SALES_STATS: '/dashboard/stats/sales',
    GET_DASHBOARD_ORDER_STATS: '/dashboard/stats/orders',
    GET_DASHBOARD_TASKS: '/dashboard/tasks',
    GET_DASHBOARD_USER_CARRIERS: '/setting/company/agreement/carriers/',
  } as const;

  constructor(private readonly httpService: HttpService) {
  }

  public async getDashboardOrderStats(year: number | string): Promise<IDashboardOrderStats> {
    const params: OrderParams = {year: year};
    return this.httpService.post<IDashboardOrderStats, OrderParams>(this.ROUTES.GET_DASHBOARD_ORDER_STATS, params);
  }

  public async getDashboardSalesStats(params: SalesStatsParams): Promise<IDashboardSalesStats> {
    return this.httpService.post<IDashboardSalesStats, SalesStatsParams>(this.ROUTES.GET_DASHBOARD_SALES_STATS, params);
  }

  public async getDashboardTasks(): Promise<IDashboardTasks[]> {
    return this.httpService.get<IDashboardTasks[]>(this.ROUTES.GET_DASHBOARD_TASKS);
  }

  public async getUserCarriers(user_code: string | number): Promise<IDashboardCarriers[]> {
    return this.httpService.get<IDashboardCarriers[]>(this.ROUTES.GET_DASHBOARD_USER_CARRIERS + user_code);
  }
}

export default DashboardService;
