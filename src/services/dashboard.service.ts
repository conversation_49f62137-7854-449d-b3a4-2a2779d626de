import { IDashboardOverview, IIntegrationIssues } from '../model/dashboard.model';
import { IRequestParam } from '../model/http.model';
import HttpService from './http.service';

class DashboardService {
  private readonly ROUTES = {
    INACTIVE_INTEGRATIONS: '/dashboard/integration-issues',
    GET_DASHBOARD_OVERVIEW: '/dashboard/overview',
  } as const;

  constructor(private readonly httpService: HttpService) {}

  public async getIntegrationIssues(): Promise<IIntegrationIssues> {
    return this.httpService.get<IIntegrationIssues>(this.ROUTES.INACTIVE_INTEGRATIONS);
  }

  public async getDashboardOverview(days: number): Promise<IDashboardOverview> {
    const params: IRequestParam[] = [{ key: 'days', value: days }];
    return this.httpService.get<IDashboardOverview>(this.ROUTES.GET_DASHBOARD_OVERVIEW, params);
  }
}

export default DashboardService;
