import { IPdfSettings, IPdfSettingsUpdate } from '../model/pdf-settings.model';
import HttpService from './http.service';

class PdfSettingsService {
  private readonly ROUTES = {
    GET_PDF_SETTINGS: '/setting/pdf-settings',
    UPDATE_PDF_SETTINGS: '/setting/pdf-settings',
  } as const;

  constructor(private readonly httpService: HttpService) {}

  public async getPdfSettings(): Promise<IPdfSettings> {
    return this.httpService.get<IPdfSettings>(this.ROUTES.GET_PDF_SETTINGS);
  }

  public async updatePdfSettings(payload: IPdfSettingsUpdate): Promise<IPdfSettings> {
    return this.httpService.put<IPdfSettings, IPdfSettingsUpdate>(
      this.ROUTES.UPDATE_PDF_SETTINGS,
      payload,
    );
  }
}

export default PdfSettingsService;
