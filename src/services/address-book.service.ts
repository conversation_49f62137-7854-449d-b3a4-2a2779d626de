import {
  IAddressBookCreate,
  IAddressBookRecord,
  IAddressBookUpdate,
} from '../model/address-book.model';
import { IPagination, IRequestParam } from '../model/http.model';
import HttpService from './http.service';

export default class AddressBookService {
  private readonly ROUTES = {
    ADDRESS_BOOK_RECORDS: `/shipments/address-books`,
    ADDRESS_BOOK: `/shipments/address-book`,
  };

  constructor(private readonly httpService: HttpService) {}

  public async getAddressBookRecords(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPagination<IAddressBookRecord>> {
    const params: IRequestParam[] = [
      { key: 'page', value: page },
      { key: 'per_page', value: limit },
      ...requestParams,
    ];

    return this.httpService.get<IPagination<IAddressBookRecord>>(
      this.ROUTES.ADDRESS_BOOK_RECORDS,
      params,
    );
  }

  public async deleteAddressBookRecord(addressBookId: number): Promise<IAddressBookRecord> {
    return this.httpService.delete<IAddressBookRecord>(
      `${this.ROUTES.ADDRESS_BOOK}/${addressBookId}`,
    );
  }

  public async createAddressBookRecord(
    addressBookRecord: IAddressBookCreate,
  ): Promise<IAddressBookRecord> {
    return this.httpService.post<IAddressBookRecord, IAddressBookCreate>(
      this.ROUTES.ADDRESS_BOOK,
      addressBookRecord,
    );
  }

  public async updateAddressBookRecord(
    addressBookCode: number,
    addressBookRecord: IAddressBookUpdate,
  ): Promise<IAddressBookRecord> {
    return this.httpService.put<IAddressBookRecord, IAddressBookUpdate>(
      `${this.ROUTES.ADDRESS_BOOK}/${addressBookCode}`,
      addressBookRecord,
    );
  }
}
