import HttpService from './http.service';
import {IAddCardLinkResposne, ICardResponse, IPaymentSetting} from "~/model/payment.model";

export default class CardService {

  private readonly ROUTES = {
    CARDS: `/payment-settings/cards`,
    ADD_CARD_LINK: `/payment-settings/getAddCardLink`,
    GET_PAYMENT_SETTINGS: `/payment-settings/getPaymentSettings`,
    CHANGE_CARD_STATUS: `/payment-settings/:card_code/changeCardStatus`,
    DELETE_CARD: `/payment-settings/:card_code/delete`,
    UPDATE_PAYMENT_SETTINGS: `/payment-settings/update`,
  } as const;
  
  constructor(protected httpService: HttpService) {}

  public async getCards(): Promise<any> {
    const result = await this.httpService.get<any>('cards');

    return result.resource[0];
  }
  
  public async getAddCardLink(): Promise<IAddCardLinkResposne> {
    const result = await this.httpService.post<IAddCardLinkResposne, void>(this.ROUTES.ADD_CARD_LINK);
    return result;
  }
  
  public async getPaymentSettings(): Promise<IPaymentSetting> {
    const result = await this.httpService.get<IPaymentSetting>(this.ROUTES.GET_PAYMENT_SETTINGS);
    return result;
  }
  
  public async changeCardStatus(card_code: number): Promise<ICardResponse> {
    const result = await this.httpService.post<ICardResponse, number>(this.ROUTES.CHANGE_CARD_STATUS.replace(':card_code', card_code.toString()));
    return result;
  }
  
  public async deleteCard(card_code: number): Promise<ICardResponse> {
    const result = await this.httpService.delete<ICardResponse>(this.ROUTES.DELETE_CARD.replace(':card_code', card_code.toString()));
    return result;
  }
  
  public async update_payment_settings(): Promise<IPaymentSetting> {
    const result = await this.httpService.post<IPaymentSetting, void>(this.ROUTES.UPDATE_PAYMENT_SETTINGS);
    return result;
  }
}
