import { reactive } from "vue";
import { TLoading<PERSON>ey } from "../model/loading.model";

export default class LoadingService {
  public readonly loadings: Set<TLoadingKey> = reactive(new Set<TLoadingKey>());
  public message: string | null = null;

  private readonly loadingKeyRegex = /^[a-zA-Z0-9-]+:[a-zA-Z0-9-]+$/;

  public startLoading(key: TLoading<PERSON><PERSON>, message: string | null = null): void {
    if (message) {
      this.message = message;
    }
    if (!key.match(this.loadingKeyRegex)) {
      throw new Error(
        `Invalid loading key: ${key}. Key must follow the convention '<prefix>:<name>'.`
      );
    }

    this.loadings.add(key);
  }

  public stopLoading(key: TLoading<PERSON>ey, reset_message: boolean = false): void {
    this.loadings.delete(key);
    if (reset_message) {
      this.message = null;
    }
  }

  public isLoading(key: TLoadingKey): boolean {
    return this.loadings.has(key);
  }

  public isAnyLoadingWithPrefix(prefix: string): boolean {
    return Array.from(this.loadings).some((key) => key.startsWith(prefix));
  }

  public loadingMessage(): string | null {
    return this.message;
  }
}
