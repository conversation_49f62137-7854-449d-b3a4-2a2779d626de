import {inRange} from 'lodash-es';
import {v4 as uuid} from '@lukeed/uuid';
import {IWeightClass} from '../model/carrier.model';
import {IBase64File} from '../model/common.model';
import {IPagination, IRequestParam} from '../model/http.model';
import {IInvoice} from '../model/invoice.model';
import {
  IAdditionalServiceCharge,
  ICalculateChargeResponse, ICheckTrackingEmail,
  ICreateShipmentRequest,
  IFormParcel,
  IParcel,
  IParcelShop,
  IParcelShopSingleResponse, IPaymentShipment,
  ISendShipmentLabelEmailRequest,
  IShipment,
  IShipmentLabelEmail,
  IShipmentUpdateRequest
} from "../model/shipment.model";
import HelperService from './helper.service';
import HttpService from './http.service';
import PriceService from './price.service';
import {IOrder} from "../model/order.model";

export default class ShipmentService {
  private readonly ROUTES = {
    SHIPMENTS: `/shipments`,
    LABEL: `/shipment/:shipmentCode/generate-label`,
    EMAIL_LABELS: `/email/labellings`,
    CREATE_SHIPMENT: '/shipments/shipment',
    PARCEL_SHOPS: '/shipments/parcelshops',
    PARCEL_SHOPS_BY_RETURN_PORTAL_CODE: '/shipments/parcelshops/return-portal/:id',
    CALCULATE_CHARGE: '/shipment/:id/calculate-charge',
    CALCULATE_FINAL_CHARGE: '/shipment/:id/calculate-final-charge',
    MAKE_PAYMENT: '/shipment/:id/make-payment',
    MAKE_RETURN_SHIPPING_PAYMENT: '/shipment/:id/make-payment-for-return-shipping',
    GENERATE_LABEL: '/shipment/:id/generate-label',
    GET_LABEL: '/shipment/:id/getLabel',
    GET_SHIPMENT_INVOICE: '/shipment/:id/get-invoice',
    DELETE_SHIPMENT: '/shipment/:id',
    MARK_AS_DELIVERED: '/shipment/:id/mark-as-delivered',
    STOP_MONITORING: '/shipment/:id/stop-monitoring',
    GET_SHIPMENT: '/shipment/:id',
    GET_ORDER_FROM_SHIPMENT: '/shipment/:id/get-order',
    GET_ADDITIONAL_SERVICE_CHARGES: '/as-charges',
    GET_WEIGHT_CLASSES: '/weight-classes/list',
    UPDATE_SHIPMENT: '/shipment/:id',
    GET_PARCEL_SHOP: '/parcel-shop/:id',
    REPURCHASE_SHIPMENT: '/shipment/:id/repurchase',
    CREATE_RETURN_SHIPMENT: '/shipment/:id/create-return',
    CANCEL_SHIPMENT: '/shipment/:id/cancel',
    GET_RETURN_SHIPMENTS: '/shipments/returns',
    CHECK_TRACKING_EMAIL: 'shipment/:shipmentCode/check/tracking-email'
  } as const;

  constructor(private readonly httpService: HttpService) {
  }

  public async getShipments(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPagination<IShipment>> {
    const params: IRequestParam[] = [
      {key: 'page', value: page},
      {key: 'per_page', value: limit},
      ...requestParams,
    ];

    return this.httpService.get<IPagination<IShipment>>(this.ROUTES.SHIPMENTS, params);
  }

  public async getBookedShipments(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPagination<IShipment>> {
    const params: IRequestParam[] = [
      {key: 'conditions[status:not_equal]', value: 'DRAFT'},
      {key: 'conditions[return_order_code:equal]', value: 'null'},
      ...requestParams,
    ];

    return this.getShipments(page, limit, params);
  }

  public async getShipment(shipmentCode: number | string): Promise<IShipment> {
    const url = this.ROUTES.GET_SHIPMENT.replace(':id', shipmentCode.toString());
    return this.httpService.get<IShipment>(url);
  }

  public async checkTrackingEmail(shipmentCode: number | string, payload:ICheckTrackingEmail): Promise<IShipment> {
    const url = this.ROUTES.CHECK_TRACKING_EMAIL.replace(':shipmentCode', shipmentCode.toString());
    return this.httpService.post<IShipment,ICheckTrackingEmail>(url, payload);
  }

  public async getOrderFromShipment(shipmentCode: number | string): Promise<IOrder> {
    const url = this.ROUTES.GET_ORDER_FROM_SHIPMENT.replace(':id', shipmentCode.toString());
    return this.httpService.get<IOrder>(url);
  }

  public async getShipmentDrafts(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPagination<IShipment>> {
    const params: IRequestParam[] = [
      {key: 'conditions[status:equal]', value: 'DRAFT'},
      ...requestParams,
    ];

    return this.getShipments(page, limit, params);
  }

  public async getReturnPortalShipments(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPagination<IShipment>> {
    const params: IRequestParam[] = [
      {key: 'page', value: page},
      {key: 'per_page', value: limit},
      ...requestParams,
    ];

    return this.httpService.get<IPagination<IShipment>>(this.ROUTES.GET_RETURN_SHIPMENTS, params);
  }

  public async generateShipmentLabelBlob(shipmentCode: number): Promise<Blob> {
    const url = this.ROUTES.LABEL.replace(':shipmentCode', shipmentCode.toString());
    const response = await this.httpService.post<IBase64File, unknown>(url);

    return HelperService.toBlob(`data:application/pdf;base64,${response.file}`);
  }

  public async sendShipmentLabelEmail(
    payload: ISendShipmentLabelEmailRequest,
  ): Promise<IShipmentLabelEmail> {
    const response = await this.httpService.post<[IShipmentLabelEmail],
      ISendShipmentLabelEmailRequest>(this.ROUTES.EMAIL_LABELS, {
      name: payload.name,
      email: payload.email,
      pdf_type: payload.pdf_type,
      shipment_id: payload.shipment_id,
    });

    return response[0];
  }

  public async createShipment(payload: ICreateShipmentRequest): Promise<IShipment> {
    return this.httpService.post<IShipment, ICreateShipmentRequest>(
      this.ROUTES.CREATE_SHIPMENT,
      payload,
    );
  }

  public async getParcelShops(
    carrier: string,
    country: string,
    address: string,
    postCode: string,
    city: any = null
  ): Promise<IParcelShopSingleResponse[]> {
    const params: IRequestParam[] = [
      {key: 'carrier', value: carrier},
      {key: 'country', value: country},
      {key: 'address', value: address},
      {key: 'postCode', value: postCode},
      {key: 'city', value: city},
    ];

    return this.httpService.get<IParcelShopSingleResponse[]>(this.ROUTES.PARCEL_SHOPS, params);
  }

  public async getParcelShopsByReturnPortalCode(
    returnPortalCode: number,
    templateCode: number
  ): Promise<IParcelShopSingleResponse[]> {
    const params: IRequestParam[] = [
      {key: 'templateCode', value: templateCode}
    ];
    const url = this.ROUTES.PARCEL_SHOPS_BY_RETURN_PORTAL_CODE.replace(':id', returnPortalCode.toString());
    return this.httpService.get<IParcelShopSingleResponse[]>(url, params);
  }

  public async calculateShipmentCharge(shipmentId: number): Promise<ICalculateChargeResponse> {
    const url = this.ROUTES.CALCULATE_CHARGE.replace(':id', shipmentId.toString());
    return this.httpService.get<ICalculateChargeResponse>(url);
  }

  public async calculateShipmentFinalCharge(shipmentId: number): Promise<ICalculateChargeResponse> {
    const url = this.ROUTES.CALCULATE_FINAL_CHARGE.replace(':id', shipmentId.toString());
    return this.httpService.get<ICalculateChargeResponse>(url);
  }

  public async makePayment(shipmentId: number, amount: any): Promise<IShipment> {
    const url = this.ROUTES.MAKE_PAYMENT.replace(':id', shipmentId.toString());
    return this.httpService.post<IShipment, { amount: number }>(url, {amount});
  }

  public async makeReturnShippingPayment(shipmentId: number, amount: number): Promise<IShipment> {
    const url = this.ROUTES.MAKE_RETURN_SHIPPING_PAYMENT.replace(':id', shipmentId.toString());
    return this.httpService.post<IShipment, { amount: number }>(url, {amount});
  }

  public async generateLabel(shipmentCode: number): Promise<IShipment> {
    const url = this.ROUTES.GENERATE_LABEL.replace(':id', shipmentCode.toString());
    return this.httpService.post<IShipment, unknown>(url);
  }
  public async getLabel(shipmentCode: number): Promise<IPaymentShipment> {
    const url = this.ROUTES.GET_LABEL.replace(':id', shipmentCode.toString());
    return this.httpService.post<IPaymentShipment, unknown>(url);
  }

  public async getInvoiceFromShipment(shipmentCode: number): Promise<IInvoice> {
    const url = this.ROUTES.GET_SHIPMENT_INVOICE.replace(':id', shipmentCode.toString());
    return this.httpService.get<IInvoice>(url);
  }

  public async deleteShipment(shipmentCode: number): Promise<void> {
    const url = this.ROUTES.DELETE_SHIPMENT.replace(':id', shipmentCode.toString());
    return this.httpService.delete(url);
  }

  public async markAsDelivered(shipmentCode: number): Promise<IShipment> {
    const url = this.ROUTES.MARK_AS_DELIVERED.replace(':id', shipmentCode.toString());
    return this.httpService.post<IShipment, unknown>(url);
  }

  public async stopMonitoring(shipmentCode: number): Promise<IShipment> {
    const url = this.ROUTES.STOP_MONITORING.replace(':id', shipmentCode.toString());
    return this.httpService.post<IShipment, unknown>(url);
  }

  public async repurchaseShipment(shipmentCode: number): Promise<IShipment> {
    const url = this.ROUTES.REPURCHASE_SHIPMENT.replace(':id', shipmentCode.toString());
    return this.httpService.post<IShipment, unknown>(url);
  }

  public async createReturnShipment(shipmentCode: number): Promise<IShipment> {
    const url = this.ROUTES.CREATE_RETURN_SHIPMENT.replace(':id', shipmentCode.toString());
    return this.httpService.post<IShipment, unknown>(url);
  }

  public async cancelShipment(shipmentCode: number, cancelReason: string): Promise<IShipment> {
    const url = this.ROUTES.CANCEL_SHIPMENT.replace(':id', shipmentCode.toString());
    return this.httpService.post<IShipment, { cancel_reason: string }>(url, {
      cancel_reason: cancelReason,
    });
  }

  public async getAdditionalServiceCharges(
    senderCountryCode: number,
    receiverCountryCode: number,
    carrierProductCode: number,
  ): Promise<IAdditionalServiceCharge[]> {
    const params: IRequestParam[] = [
      {key: 'sender_country_code', value: senderCountryCode},
      {key: 'receiver_country_code', value: receiverCountryCode},
      {key: 'carrier_product_code', value: carrierProductCode},
    ];

    return this.httpService.get<IAdditionalServiceCharge[]>(
      this.ROUTES.GET_ADDITIONAL_SERVICE_CHARGES,
      params,
    );
  }

  public async getWeightClasses(
    carrierProductCode: number,
    senderCountryCode: number,
    receiverCountryCode: number,
    ubsend_client_id: number
  ): Promise<IWeightClass[]> {
    const params: IRequestParam[] = [
      {key: 'carrier_product_code', value: carrierProductCode},
      {key: 'sender_country_code', value: senderCountryCode},
      {key: 'receiver_country_code', value: receiverCountryCode},
      {key: 'ubsend_client_id', value: ubsend_client_id},
    ];

    return this.httpService.get<IWeightClass[]>(this.ROUTES.GET_WEIGHT_CLASSES, params);
  }

  public static parcelToFormParcel(
    parcels: IParcel[],
    weightClasses: IWeightClass[],
  ): IFormParcel[] {
    return parcels.map(
      (parcel: IParcel): IFormParcel => ({
        id: uuid(),
        quantity: parcel.count,
        weightClass: weightClasses.find((weightClass) =>
          inRange(
            parcel.weight,
            parseFloat(weightClass.min_weight.toString()),
            parseFloat(weightClass.max_weight.toString()) + 0.1,
          ),
        ) as IWeightClass,
      }),
    );
  }

  public async updateShipment(
    shipmentCode: number,
    payload: IShipmentUpdateRequest,
  ): Promise<IShipment> {
    const url: string = this.ROUTES.UPDATE_SHIPMENT.replace(':id', shipmentCode.toString());
    return this.httpService.put<IShipment, IShipmentUpdateRequest>(url, payload);
  }

  public async getShipmentErrors(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPagination<IShipment>> {
    const params: IRequestParam[] = [
      {key: 'conditions[status:equal]', value: 'ERROR'},
      ...requestParams,
    ];

    return this.getShipments(page, limit, params);
  }

  public async getParcelShop(parcelShopId: string, carrier: string): Promise<IParcelShop> {
    const url: string = this.ROUTES.GET_PARCEL_SHOP.replace(':id', parcelShopId);
    const params: IRequestParam[] = [{key: 'carrier', value: carrier}];

    return this.httpService.get<IParcelShop>(url, params);
  }

  public static formatAdditionalServicePrice(price: number) {
    if (price === 0) {
      return '';
    }

    return `(${PriceService.formatPrice(price)})`;
  }

  public static formatAdditionalServiceLabel(additionalServiceCharge: IAdditionalServiceCharge) {
    return `${
      additionalServiceCharge.additional_service.name
    } ${ShipmentService.formatAdditionalServicePrice(parseFloat(additionalServiceCharge.price))}`;
  }
}
