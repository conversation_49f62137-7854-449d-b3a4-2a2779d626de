import { ICountry } from '../model/country.model';
import { IRequestParam } from '../model/http.model';
import HttpService from './http.service';

export default class CountryService {
  constructor(protected readonly httpService: HttpService) {}

  public async getCountries(params: IRequestParam[] = []): Promise<ICountry[]> {
    return this.httpService.get<ICountry[]>('shipments/countries', [{ key: 'limit', value: 100 }, ...params]);
  }
}
