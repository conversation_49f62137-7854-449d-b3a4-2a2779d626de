import {IBase64File} from '../model/common.model';
import {IPagination, IRequestParam} from '../model/http.model';
import {ISalesInvoice} from '../model/sales-invoice.model';
import HttpService from './http.service';

class SalesInvoiceService {
  public routes = {
    GET_SALES_INVOICES: '/sales-invoices',
    GET_SALES_INVOICE_PDF: '/sales-invoice/:id/invoice-pdf',
  };

  constructor(private readonly httpService: HttpService) {
  }

  public getSalesInvoices(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPagination<ISalesInvoice>> {
    const params: IRequestParam[] = [
      {key: 'page', value: page},
      {key: 'per_page', value: limit},
      ...requestParams,
    ];

    return this.httpService.get<IPagination<ISalesInvoice>>(this.routes.GET_SALES_INVOICES, params);
  }

  public downloadSalesInvoicePdf(invoiceCode: number, invoiceType: string): Promise<IBase64File> {
    const url = this.routes.GET_SALES_INVOICE_PDF.replace(':id', invoiceCode.toString());
    return this.httpService.get<IBase64File>(`${url}?invoice_type=${invoiceType}`);
  }
}

export default SalesInvoiceService;
