import { IPagination, IRequestParam } from '../model/http.model';
import {
  IMessageHook,
  IPersonalizedMessage,
  IPersonalizedMessageRequest,
  IPersonalizedMessageTemplate,
  TMessagingType,
} from '../model/personalized-message.model';
import HttpService from './http.service';

class PersonalizedMessageService {
  private readonly ROUTES = {
    GET_MESSAGE_HOOKS: '/setting/message-hooks',
    GET_PERSONALIZED_MESSAGES: '/setting/personalized-messages',
    GET_PERSONALIZED_MESSAGE_TEMPLATE: '/setting/message-template/default',
    CREATE_PERSONALIZED_MESSAGE: '/setting/personalized-message',
    UPDATE_PERSONALIZED_MESSAGE: '/setting/personalized-message/:id',
    DELETE_PERSONALIZED_MESSAGE: '/setting/personalized-message/:id',
  } as const;

  constructor(private readonly httpService: HttpService) {}

  public getMessageHooks(): Promise<IMessageHook[]> {
    return this.httpService.get<IMessageHook[]>(this.ROUTES.GET_MESSAGE_HOOKS);
  }

  public getPersonalizedMessages(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPagination<IPersonalizedMessage>> {
    const params: IRequestParam[] = [
      { key: 'page', value: page },
      { key: 'per_page', value: limit },
      { key: 'with', value: ['messageHook', 'receiverCountry', 'integration'].join(',') },
      ...requestParams,
    ];

    return this.httpService.get<IPagination<IPersonalizedMessage>>(
      this.ROUTES.GET_PERSONALIZED_MESSAGES,
      params,
    );
  }

  public async getPersonalizedMessageTemplate(
    type: TMessagingType,
    messageHookCode: number,
  ): Promise<IPersonalizedMessageTemplate> {
    const params: IRequestParam[] = [
      { key: 'type', value: type },
      { key: 'message_hook_code', value: messageHookCode },
    ];

    return this.httpService.get<IPersonalizedMessageTemplate>(
      this.ROUTES.GET_PERSONALIZED_MESSAGE_TEMPLATE,
      params,
    );
  }

  public createPersonalizedMessage(
    payload: IPersonalizedMessageRequest,
  ): Promise<IPersonalizedMessage> {
    return this.httpService.post<IPersonalizedMessage, IPersonalizedMessageRequest>(
      this.ROUTES.CREATE_PERSONALIZED_MESSAGE,
      payload,
    );
  }

  public updatePersonalizedMessage(
    personalizedMessageCode: number,
    payload: IPersonalizedMessageRequest,
  ): Promise<IPersonalizedMessage> {
    const url: string = this.ROUTES.UPDATE_PERSONALIZED_MESSAGE.replace(
      ':id',
      personalizedMessageCode.toString(),
    );
    return this.httpService.put<IPersonalizedMessage, IPersonalizedMessageRequest>(url, payload);
  }

  public deletePersonalizedMessage(personalizedMessageCode: number): Promise<void> {
    const url: string = this.ROUTES.DELETE_PERSONALIZED_MESSAGE.replace(
      ':id',
      personalizedMessageCode.toString(),
    );
    return this.httpService.delete<void>(url);
  }
}

export default PersonalizedMessageService;
