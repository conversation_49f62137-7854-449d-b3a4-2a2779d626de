import { IRequestParam } from '../model/http.model';
import { IZipCodeBaseResult } from '../model/map.model';
import HttpService from './http.service';

export default class MapService {
  private ROUTES = {
    GEOCODE: '/map',
  } as const;

  constructor(protected readonly httpService: HttpService) {}

  public async getMapInfo(countryIso: string, zipCode: string): Promise<IZipCodeBaseResult> {
    const params: IRequestParam[] = [
      { key: 'codes[]', value: `${zipCode}` },
      { key: 'country', value: `${countryIso}` },
    ];

    return this.httpService.get<IZipCodeBaseResult>(this.ROUTES.GEOCODE, params);
  }
}
