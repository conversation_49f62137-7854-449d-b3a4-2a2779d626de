import { IPagination } from '../model/http.model';
import { IPaymentGateway } from '../model/payment.model';
import HttpService from './http.service';

export default class PaymentService {
  private readonly ROUTES = {
    GATEWAY: '/setting/payment/gateways',
  };

  constructor(private readonly httpService: HttpService) {}

  public async getPaymentGateways(): Promise<IPaymentGateway[]> {
    const result = await this.httpService.get<IPagination<IPaymentGateway>>(this.ROUTES.GATEWAY);

    return result.entities;
  }
}
