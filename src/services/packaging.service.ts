import { IPagination, IRequestParam } from '../model/http.model';
import { IPackaging, IPackagingCreate, IPackagingUpdate } from '../model/packaging.model';
import HttpService from './http.service';

export default class PackagingService {
  private readonly ROUTES = {
    PACKAGINGS: '/packagings',
    PACKAGING: '/packaging',
  } as const;

  constructor(private readonly httpService: HttpService) {}

  public async getPackagings(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPagination<IPackaging>> {
    const params: IRequestParam[] = [
      { key: 'page', value: page },
      { key: 'per_page', value: limit },
      ...requestParams,
    ];

    return this.httpService.get<IPagination<IPackaging>>(this.ROUTES.PACKAGINGS, params);
  }

  public async createPackaging(packaging: IPackagingCreate): Promise<IPackaging> {
    return this.httpService.post<IPackaging, IPackagingCreate>(this.ROUTES.PACKAGING, packaging);
  }

  public async updatePackaging(
    packagingCode: number,
    packaging: IPackagingUpdate,
  ): Promise<IPackaging> {
    return this.httpService.put<IPackaging, IPackagingUpdate>(
      `${this.ROUTES.PACKAGING}/${packagingCode}`,
      packaging,
    );
  }

  public async deletePackaging(packagingCode: number): Promise<void> {
    return this.httpService.delete<void>(`${this.ROUTES.PACKAGING}/${packagingCode}`);
  }
}
