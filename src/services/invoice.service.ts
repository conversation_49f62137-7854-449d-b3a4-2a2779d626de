import { IBase64File } from '../model/common.model';
import { IPagination, IRequestParam } from '../model/http.model';
import { IInvoice, IInvoiceItem } from '../model/invoice.model';
import HttpService from './http.service';

class InvoiceService {
  public ROUTES = {
    GET_INVOICES: '/invoices',
    GET_MONTHLY_INVOICES: '/invoice/monthly',
    GET_BILLING_INVOICES: '/economic-invoices',
    DOWNLOAD_BILLING_INVOICE_PDF: '/economic-invoices/:invoice-code',
    GET_INVOICE_PDF: '/invoice/:id/pdf',
    MONTHLY_INVOICE_DOWNLOAD: '/invoice/monthly/download/:id',
    GET_MONTHLY_INVOICE_SPECIFICATIONS: '/invoice/monthly/:id/specifications',
    EXPORT_SPECIFICATIONS_TO_EXCEL: '/export-data/:id',
  };

  constructor(private readonly httpService: HttpService) {}

  public getInvoices(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPagination<IInvoice>> {
    const params: IRequestParam[] = [
      { key: 'page', value: page },
      { key: 'per_page', value: limit },
      ...requestParams,
    ];

    return this.httpService.get<IPagination<IInvoice>>(this.ROUTES.GET_INVOICES, params);
  }

  public getMonthlyInvoices(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPagination<IInvoice>> {
    const params: IRequestParam[] = [
      { key: 'page', value: page },
      { key: 'per_page', value: limit },
      ...requestParams,
    ];

    return this.httpService.get<IPagination<IInvoice>>(this.ROUTES.GET_MONTHLY_INVOICES, params);
  }
  public getBillingInvoices(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPagination<IInvoice>> {
    const params: IRequestParam[] = [
      { key: 'page', value: page },
      { key: 'per_page', value: limit },
      ...requestParams,
    ];

    return this.httpService.get<IPagination<IInvoice>>(this.ROUTES.GET_BILLING_INVOICES, params);
  }

  public getInvoicePdf(invoiceCode: number): Promise<IBase64File> {
    const url = this.ROUTES.GET_INVOICE_PDF.replace(':id', invoiceCode.toString());
    return this.httpService.get<IBase64File>(url);
  }

  public static getTotalExclVAT(invoice: IInvoice): number {
    // TODO: Needs to be refactored
    return invoice.invoice_items.reduce(
      (acc, item: IInvoiceItem) =>
        acc +
        item.items.reduce(
          (_acc, _item) => _acc + Number(_item.count) * Number(_item.unit_price),
          0,
        ),
      0,
    );
  }

  public async downloadMonthlyInvoice(
    id: number | string,
  ): Promise<{ file: string; file1?: string }> {
    const url: string = this.ROUTES.MONTHLY_INVOICE_DOWNLOAD.replace(':id', id.toString());
    return this.httpService.get<{ file: string }>(url);
  }

  public getMonthlyInvoiceSpecifications(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
    monthlyInvoiceId: string,
  ): Promise<IPagination<IInvoice>> {
    const params: IRequestParam[] = [
      { key: 'page', value: page },
      { key: 'per_page', value: limit },
      ...requestParams,
    ];

    const url: string = this.ROUTES.GET_MONTHLY_INVOICE_SPECIFICATIONS.replace(
      ':id',
      monthlyInvoiceId.toString(),
    );
    return this.httpService.get<IPagination<IInvoice>>(url, params);
  }

  public exportSpecficationsToExcel(monthlyInvoiceId: string): Promise<IBase64File> {
    const url: string = this.ROUTES.EXPORT_SPECIFICATIONS_TO_EXCEL.replace(
      ':id',
      monthlyInvoiceId.toString(),
    );
    return this.httpService.get<IBase64File>(url);
  }

  public async downloadBillingInvoicePdf(invoice_code: number | string): Promise<{ file: string }> {
    const url: string = this.ROUTES.DOWNLOAD_BILLING_INVOICE_PDF.replace(
      ':invoice-code',
      invoice_code.toString(),
    );
    return this.httpService.get<{ file: string }>(`${url}`);
  }
}

export default InvoiceService;
