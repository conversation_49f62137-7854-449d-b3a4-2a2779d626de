import { IPrintJob, IPrintJobCreate } from '../model/print-job.model';
import HttpService from './http.service';

class PrintJobService {
  public ROUTES = {
    QUEUE_PRINT_JOB: '/print-job/print',
    QUEUE_LABEL_PRINT_JOB: '/print-job/print/:shipmentCode',
  } as const;

  constructor(private readonly httpService: HttpService) {}

  public queuePrintJob(payload: IPrintJobCreate): Promise<IPrintJob> {
    return this.httpService.post<IPrintJob, IPrintJobCreate>(this.ROUTES.QUEUE_PRINT_JOB, payload);
  }
  
  public queueLabelPrintJob(shipmentCode: number): Promise<IPrintJob> {
    const url: string = this.ROUTES.QUEUE_LABEL_PRINT_JOB.replace(':shipmentCode', shipmentCode.toString());
    return this.httpService.get<IPrintJob>(url);
  }
}

export default PrintJobService;
