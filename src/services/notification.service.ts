import { Notify, QNotifyCreateOptions } from "quasar";

export default class NotificationService {
  private readonly notify: Notify = Notify;
  private activeMessages: Set<string> = new Set();

  constructor() {
    this.notify.setDefaults({
      position: "top-right",
      textColor: "white",
      attrs: {
        style: {
          width: "400px"
        }
      }
    });
  }

  public showInfo(message: string, options: QNotifyCreateOptions = {}) {
    return this.notify.create({
      message,
      color: "negative",
      attrs: {
        style: {
          width: "400px",
          "background-color": "#efaa02 !important",
          "border-radius": "5px",
          "font-weight": "700"
        }
      },
      ...options
    });
  }

  public showError(message: string, options: QNotifyCreateOptions = {}) {
    if (!this.activeMessages.has(message)) {
      this.activeMessages.add(message);
      return this.notify.create({
        message,
        color: "negative",
        attrs: {
          style: {
            width: "400px",
            "background-color": "#ff3d24 !important",
            "border-radius": "5px",
            "font-weight": "700"
          }
        },
        actions: [
          {
            icon: "close", color: "white", handler: () => { /* ... */
            }
          }
        ],
        onDismiss: () => {
          this.activeMessages.delete(message);
        },
        ...options
      });
    }
  }

  public showSuccess(message: string, options: QNotifyCreateOptions = {}) {
    if (!this.activeMessages.has(message)) {
      this.activeMessages.add(message);
      return this.notify.create({
        message,
        color: "positive",
        attrs: {
          style: {
            width: "400px",
            "background-color": "#01bc9a !important",
            "border-radius": "5px",
            "font-weight": "700"
          }
        },
        actions: [
          {
            icon: "close", color: "white", handler: () => { /* ... */
            }
          }
        ],
        onDismiss: () => {
          this.activeMessages.delete(message);
        },
        ...options
      });
    }
  }
}
