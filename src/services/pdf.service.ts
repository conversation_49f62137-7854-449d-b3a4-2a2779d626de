import { jsPDF, TextOptionsLight } from 'jspdf';
import autoTable, { CellHookData } from 'jspdf-autotable';
import * as _ from 'lodash-es';
import DecimalService from './decimal.service';

class PdfService {
  public PAGE_PADDING_PX: number = 150;

  public A4_WIDTH_MM: number = 210;

  public DESIGN_WIDTH: number = 2480;

  public SCALE_FACTOR: number = this.A4_WIDTH_MM / this.DESIGN_WIDTH;

  public TABLE_WIDTH: number = this.A4_WIDTH_MM - this.PAGE_PADDING_PX * 2 * this.SCALE_FACTOR;

  public drawShipvagooInformationUnderPage(doc: jsPDF): void {
    doc.setFontSize(10);
    doc.setTextColor(0, 0, 0);

    const row1: string = 'ShipVagoo ApS• Laplandsgade 4A • DK-2300 København S';
    const row2: string = 'DK1234567890 • <EMAIL> • +45 12 345 678';

    const row1HeightAsMM: number = doc.getTextDimensions(row1).h;
    // row2HeightAsMM not needed as baseline is set to 'top'.
    const pagePaddingAsMM: number = this.PAGE_PADDING_PX * this.SCALE_FACTOR;
    const marginBetweenBaselinesAsMM: number = 80 * this.SCALE_FACTOR;
    const projectedTotalHeight = row1HeightAsMM + marginBetweenBaselinesAsMM;

    const x: number = doc.internal.pageSize.getWidth() / 2;
    const y: number = doc.internal.pageSize.getHeight() - pagePaddingAsMM - projectedTotalHeight;
    const textOptions: TextOptionsLight = { align: 'center', baseline: 'top' };
    doc.text(row1, x, y, textOptions);
    doc.text(row2, x, y + marginBetweenBaselinesAsMM, textOptions);
  }

  public drawPageNumber(doc: jsPDF, pageNumber: number): void {
    doc.setFontSize(10);
    doc.setTextColor(0, 0, 0);

    const row: string = `Page ${pageNumber}`;
    const rowHeightAsMM: number = doc.getTextDimensions(row).h;
    const pagePaddingAsMM: number = this.PAGE_PADDING_PX * this.SCALE_FACTOR;
    const x: number = doc.internal.pageSize.getWidth() - pagePaddingAsMM;
    const y: number = doc.internal.pageSize.getHeight() - pagePaddingAsMM - rowHeightAsMM;
    const textOptions: TextOptionsLight = { align: 'right', baseline: 'top' };
    doc.text(row, x, y, textOptions);
  }

  public drawLineUnderHead(doc: jsPDF, cell: CellHookData): void {
    doc.setFillColor(0, 0, 0);
    doc.rect(
      150 * this.SCALE_FACTOR,
      cell.settings.startY + 60 * this.SCALE_FACTOR,
      doc.internal.pageSize.getWidth() - this.PAGE_PADDING_PX * this.SCALE_FACTOR * 2,
      8 * this.SCALE_FACTOR,
      'F',
    );
  }

  public drawVATInformation(
    doc: jsPDF,
    shipping: number,
    amountExclVAT: number,
    VATPercent: number,
    VAT: number,
    total: number,
    // Temporary
    t: (s: string) => string,
  ): void {
    autoTable(doc, {
      startY: _.get(doc, 'lastAutoTable.finalY', 0) + 40 * this.SCALE_FACTOR,
      margin: {
        left: this.PAGE_PADDING_PX * this.SCALE_FACTOR,
        right: this.PAGE_PADDING_PX * this.SCALE_FACTOR,
        bottom: 335 * this.SCALE_FACTOR,
      },
      head: [
        [
          t('common.this_includes'),
          t('common.delivery'),
          t('common.amount'),
          t('common.vat'),
          t('common.vat_amount'),
          t('common.total'),
        ],
      ],
      body: [
        [
          '',
          DecimalService.toDecimal(shipping),
          DecimalService.toDecimal(amountExclVAT),
          new Intl.NumberFormat('en-US', {
            style: 'percent',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(VATPercent),
          DecimalService.toDecimal(VAT),
          DecimalService.toDecimal(total),
        ],
      ],
      headStyles: {
        fontSize: 9.5,
        textColor: [0, 0, 0],
        fillColor: [255, 255, 255],
        cellPadding: {
          left: 20 * this.SCALE_FACTOR,
          right: 20 * this.SCALE_FACTOR,
          bottom: 30 * this.SCALE_FACTOR,
        },
      },
      bodyStyles: {
        fontSize: 9.5,
        textColor: [0, 0, 0],
        fillColor: [255, 255, 255],
      },
      alternateRowStyles: {
        fillColor: [255, 255, 255],
      },
      columnStyles: {
        0: { cellWidth: this.TABLE_WIDTH * 0.167},
        1: { cellWidth: this.TABLE_WIDTH * 0.167 },
        2: { cellWidth: this.TABLE_WIDTH * 0.167 },
        3: { cellWidth: this.TABLE_WIDTH * 0.167 },
        4: { cellWidth: this.TABLE_WIDTH * 0.167 },
        5: { cellWidth: this.TABLE_WIDTH * 0.167 },
      },
      didDrawCell: (cell) => {
        // TODO: -- Get index from columns when moved to separate component
        if (cell.section === 'head' && cell.column.index === 5) {
          this.drawLineUnderHead(doc, cell);
        }
      },
      willDrawCell: (data) => {
        if (data.column.index !== 0) {
          // eslint-disable-next-line no-param-reassign
          data.cell.styles.halign = 'right';
        }
      },
    });
  }

  public drawTotalRow(doc: jsPDF, total: number, t: (s: string) => string): void {
    doc.setFillColor(0, 0, 0);
    doc.rect(
      150 * this.SCALE_FACTOR,
      _.get(doc, 'lastAutoTable.finalY', 0),
      doc.internal.pageSize.getWidth() - this.PAGE_PADDING_PX * this.SCALE_FACTOR * 2,
      8 * this.SCALE_FACTOR,
      'F',
    );

    autoTable(doc, {
      startY: _.get(doc, 'lastAutoTable.finalY', 0) + 40 * this.SCALE_FACTOR,
      margin: {
        left: this.PAGE_PADDING_PX * this.SCALE_FACTOR,
        right: this.PAGE_PADDING_PX * this.SCALE_FACTOR,
        bottom: 335 * this.SCALE_FACTOR,
      },
      showHead: 'never',
      body: [[`${t('common.total_incl_vat')} (DKK)`, DecimalService.toDecimal(total)]],
      headStyles: {
        fontSize: 9.5,
        textColor: [0, 0, 0],
        fillColor: [255, 255, 255],
        cellPadding: {
          left: 20 * this.SCALE_FACTOR,
          right: 20 * this.SCALE_FACTOR,
          bottom: 30 * this.SCALE_FACTOR,
        },
      },
      bodyStyles: {
        fontSize: 9.5,
        textColor: [0, 0, 0],
        fillColor: [255, 255, 255],
        fontStyle: 'bold',
      },
      alternateRowStyles: {
        fillColor: [255, 255, 255],
      },
      didDrawCell: (cell) => {
        // TODO: -- Get index from columns when moved to separate component
        if (cell.section === 'head' && cell.column.index === 1) {
          this.drawLineUnderHead(doc, cell);
        }
      },
      willDrawCell: (data) => {
        if (data.column.index !== 0) {
          // eslint-disable-next-line no-param-reassign
          data.cell.styles.halign = 'right';
        }
      },
    });
  }
  
  public drawShipping(doc: jsPDF, total: number, t: (s: string) => string): void {
    doc.setFillColor(0, 0, 0);
    doc.rect(
      150 * this.SCALE_FACTOR,
      _.get(doc, 'lastAutoTable.finalY', 0),
      doc.internal.pageSize.getWidth() - this.PAGE_PADDING_PX * this.SCALE_FACTOR * 2,
      8 * this.SCALE_FACTOR,
      'F',
    );

    autoTable(doc, {
      startY: _.get(doc, 'lastAutoTable.finalY', 0) + 40 * this.SCALE_FACTOR,
      margin: {
        left: this.PAGE_PADDING_PX * this.SCALE_FACTOR,
        right: this.PAGE_PADDING_PX * this.SCALE_FACTOR,
        bottom: 335 * this.SCALE_FACTOR,
      },
      showHead: 'never',
      body: [[`${t('common.delivery')} (DKK)`, DecimalService.toDecimal(total)]],
      headStyles: {
        fontSize: 9.5,
        textColor: [0, 0, 0],
        fillColor: [255, 255, 255],
        cellPadding: {
          left: 20 * this.SCALE_FACTOR,
          right: 20 * this.SCALE_FACTOR,
        },
      },
      bodyStyles: {
        fontSize: 9.5,
        textColor: [0, 0, 0],
        fillColor: [255, 255, 255],
        fontStyle: 'bold',
      },
      alternateRowStyles: {
        fillColor: [255, 255, 255],
      },
      didDrawCell: (cell) => {
        // TODO: -- Get index from columns when moved to separate component
        if (cell.section === 'head' && cell.column.index === 1) {
          this.drawLineUnderHead(doc, cell);
        }
      },
      willDrawCell: (data) => {
        if (data.column.index !== 0) {
          // eslint-disable-next-line no-param-reassign
          data.cell.styles.halign = 'right';
        }
      },
    });
  }

  public drawFooters(doc: jsPDF): void {
    for (let i: number = 1; i <= doc.getNumberOfPages(); i += 1) {
      doc.setPage(i);

      this.drawShipvagooInformationUnderPage(doc);
      this.drawPageNumber(doc, i);
    }
  }
}

export default PdfService;
