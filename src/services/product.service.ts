import { IBase64File } from '../model/common.model';
import { IPagination, IRequestParam } from '../model/http.model';
import {
  IProduct,
  IProductLabelOptions,
  IProductShelfOptions,
  IProductUpdate,
} from '../model/product.model';
import HttpService from './http.service';

export default class ProductService {
  private readonly ROUTES = {
    UPDATE_PRODUCT: '/product/:id',
    PRODUCTS: '/products',
    PRODUCT_LABEL: '/products/barcode-label',
    SHELF_LABELS: '/products/barcode-labels',
    IMPORT_PRODUCTS: '/products/import',
  } as const;

  constructor(private readonly httpService: HttpService) {}

  public generateProductLabel(options: IProductLabelOptions): Promise<IBase64File> {
    const params: IRequestParam[] = [
      { key: 'sku', value: options.sku },
      { key: 'barcode', value: options.barcode },
      { key: 'name', value: options.name },
    ];

    if (options.bin) {
      params.push({ key: 'bin', value: options.bin });
    }

    return this.httpService.get<IBase64File>(this.ROUTES.PRODUCT_LABEL, params);
  }

  public generateShelfLabels(options: IProductShelfOptions): Promise<IBase64File> {
    const params: IRequestParam[] = [
      { key: 'section_from', value: options.sectionFrom },
      { key: 'section_to', value: options.sectionTo },
      { key: 'number_from', value: options.numberFrom },
      { key: 'number_to', value: options.numberTo },
      { key: 'levels', value: options.levels },
      { key: 'placements', value: options.placements },
    ];

    return this.httpService.get<IBase64File>(this.ROUTES.SHELF_LABELS, params);
  }

  public async getProducts(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPagination<IProduct>> {
    const params: IRequestParam[] = [
      { key: 'page', value: page },
      { key: 'per_page', value: limit },
      { key: 'with', value: 'integration' },
      ...requestParams,
    ];

    return this.httpService.get<IPagination<IProduct>>(this.ROUTES.PRODUCTS, params);
  }

  public async updateProduct(productCode: number, payload: IProductUpdate): Promise<IProduct> {
    const url = this.ROUTES.UPDATE_PRODUCT.replace(':id', productCode.toString());
    return this.httpService.put<IProduct, IProductUpdate>(url, payload);
  }

  public async importProducts(): Promise<unknown> {
    return this.httpService.post<unknown, never>(this.ROUTES.IMPORT_PRODUCTS);
  }
}
