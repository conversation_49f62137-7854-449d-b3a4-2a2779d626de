import AuthService from './auth.service';
import CardService from './card.service';
import CompanyService from './company.service';
import CountryService from './country.service';
import HelperService from './helper.service';
import HttpService from './http.service';
import LoadingService from './loading.service';
import MapService from './map.service';
import OrderService from './order.service';
import PaymentService from './payment.service';
import PickSettingsService from './pick-settings.service';
import TransactionService from './transaction.service';
import UpdateService from './update.service';
import UserService from './user.service';
import OrderIntegrationService from './order-integration.service';
import ShipmentService from './shipment.service';
import ModuleService from './module.service';
import AddressBookService from './address-book.service';
import TemplateService from './template.service';
import LoggingService from './logging.service';
import LanguageService from './language.service';
import NotificationService from './notification.service';
import ProductService from './product.service';
import ReturnPortalService from './return-portal.service';
import PackagingService from './packaging.service';
import CustomerService from './customer.service';
import FileService from './file.service';
import CarrierService from './carrier.service';
import InvoiceService from './invoice.service';
import SalesInvoiceService from './sales-invoice.service';
import PersonalizedMessageService from './personalized-message.service';
import DashboardService from './dashboard.service';
import DashboardServiceV1 from './v1/dashboard.service';
import PriceService from './price.service';
import CreditNoteService from './credit-note.service';
import PdfService from './pdf.service';
import PdfSettingsService from './pdf-settings.service';
import PrinterService from './printer.service';
import PrintJobService from './print-job.service';
import ApiAccessService from './api-access.service';
import ShippingMethodService from "./shipping-method.service";
import AgreementService from "./agreement.service";

export const httpService: HttpService = new HttpService(import.meta.env.VITE_baseUrl);
export const loadingService: LoadingService = new LoadingService();
export const helperService: HelperService = new HelperService();
export const authService: AuthService = new AuthService(httpService);
export const userService: UserService = new UserService(httpService);
export const companyService: CompanyService = new CompanyService(httpService);
export const orderService: OrderService = new OrderService(httpService);
export const pickSettingsService: PickSettingsService = new PickSettingsService(httpService);
export const loggingService: LoggingService = new LoggingService();
export const updateService: UpdateService = new UpdateService(loggingService);
export const transactionService: TransactionService = new TransactionService(
  httpService,
  loadingService,
  loggingService,
);
export const cardService: CardService = new CardService(httpService);
export const paymentService: PaymentService = new PaymentService(httpService);
export const countryService: CountryService = new CountryService(httpService);
export const mapService: MapService = new MapService(httpService);
export const orderIntegrationService: OrderIntegrationService = new OrderIntegrationService(
  httpService,
);
export const shipmentService: ShipmentService = new ShipmentService(httpService);
export const moduleService: ModuleService = new ModuleService(httpService);
export const addressBookService: AddressBookService = new AddressBookService(httpService);
export const templateService: TemplateService = new TemplateService(httpService);
export const carrierService: CarrierService = new CarrierService(httpService);
export const languageService: LanguageService = new LanguageService(httpService);
export const notificationService: NotificationService = new NotificationService();
export const productService: ProductService = new ProductService(httpService);
export const returnPortalService: ReturnPortalService = new ReturnPortalService(httpService);
export const packagingService: PackagingService = new PackagingService(httpService);
export const customerService: CustomerService = new CustomerService(httpService);
export const fileService: FileService = new FileService();
export const invoiceService: InvoiceService = new InvoiceService(httpService);
export const salesInvoiceService: SalesInvoiceService = new SalesInvoiceService(httpService);
export const personalizedMessageService: PersonalizedMessageService =
  new PersonalizedMessageService(httpService);
export const dashboardService: DashboardService = new DashboardService(httpService);
export const dashboardServiceV1: DashboardServiceV1 = new DashboardServiceV1(httpService);
export const priceService: PriceService = new PriceService();
export const creditNoteService: CreditNoteService = new CreditNoteService(httpService);
export const pdfService: PdfService = new PdfService();
export const pdfSettingsService: PdfSettingsService = new PdfSettingsService(httpService);
export const printerService: PrinterService = new PrinterService(httpService);
export const shippingMethodService: ShippingMethodService = new ShippingMethodService(httpService);
export const printJobService: PrintJobService = new PrintJobService(httpService);
export const apiAccessService: ApiAccessService = new ApiAccessService(httpService);
export const agreementService: AgreementService = new AgreementService(httpService);