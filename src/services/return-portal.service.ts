import { IPagination, IRequestParam } from '../model/http.model';
import {
  IReturnPortal,
  IReturnPortalCreate,
  IReturnPortalMethod,
  IReturnPortalMethodCreate,
  IReturnPortalReason,
  IReturnPortalReasonCreate,
  IReturnPortalReasonUpdate,
  IReturnPortalReceiverUpdate,
  IReturnPortalResolution,
  IReturnPortalResolutionCreate,
  IReturnPortalResolutionUpdate,
  IReturnPortalTextUpdate,
} from '../model/return-portal.model';
import HttpService from './http.service';

export default class ReturnPortalService {
  private readonly ROUTES = {
    GET_RETURN_PORTALS: '/setting/return-portals',
    CREATE_RETURN_PORTAL: '/setting/return-portal',
    DELETE_RETURN_PORTAL: '/setting/return-portal/:id',
    UPDATE_RETURN_PORTAL: '/setting/return-portal/:id',
    UPDATE_RECEIVER: '/setting/return-portal/:id/update-receiver',
    UPDATE_TEXT: '/setting/return-portal/:id/update-text',
    GET_RETURN_PORTAL_REASONS: '/setting/return-portal/:id/return-reasons',
    CREATE_RETURN_PORTAL_REASON: '/setting/return-portal/:id/return-reason',
    UPDATE_RETURN_PORTAL_REASON: '/setting/return-portal/:return-portal-id/return-reason/:id',
    DELETE_RETURN_PORTAL_REASON: '/setting/return-portal/:return-portal-id/return-reason/:id',
    GET_RETURN_PORTAL_RESOLUTIONS: '/setting/return-portal/:id/resolutions',
    CREATE_RETURN_PORTAL_RESOLUTION: '/setting/return-portal/:id/resolution',
    UPDATE_RETURN_PORTAL_RESOLUTION: '/setting/return-portal/:return-portal-id/resolution/:id',
    DELETE_RETURN_PORTAL_RESOLUTION: '/setting/return-portal/:return-portal-id/resolution/:id',
    GET_RETURN_PORTAL_METHODS: '/setting/return-portal/:id/methods',
    CREATE_RETURN_PORTAL_METHOD: '/setting/return-portal/:id/method',
    UPDATE_RETURN_PORTAL_METHOD: '/setting/return-portal/:return-portal-id/method/:id',
    DELETE_RETURN_PORTAL_METHOD: '/setting/return-portal/:return-portal-id/method/:id',
  } as const;

  constructor(private readonly httpService: HttpService) {}

  public static getReturnPortalLink(portalName: string | null): string | null {
    if (!portalName || portalName === '') {
      return null;
    }

    return `${import.meta.env.VITE_appUrl}/return/${portalName}`;
  }

  public async createReturnPortal(returnPortal: IReturnPortalCreate): Promise<IReturnPortal> {
    return this.httpService.post<IReturnPortal, IReturnPortalCreate>(
      this.ROUTES.CREATE_RETURN_PORTAL,
      returnPortal,
    );
  }

  public async updateReturnPortal(returnPortalCode: number, returnPortal: IReturnPortalCreate): Promise<IReturnPortal> {
    const url = this.ROUTES.UPDATE_RETURN_PORTAL.replace(':id', returnPortalCode.toString());
    return this.httpService.put<IReturnPortal, IReturnPortalCreate>(
      url,
      returnPortal,
    );
  }

  public async getReturnPortals(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPagination<IReturnPortal>> {
    const params: IRequestParam[] = [
      { key: 'page', value: page },
      { key: 'per_page', value: limit },
      ...requestParams,
    ];

    return this.httpService.get<IPagination<IReturnPortal>>(this.ROUTES.GET_RETURN_PORTALS, params);
  }

  public async getReturnPortalReasons(returnPortalCode: number): Promise<IReturnPortalReason[]> {
    const url = this.ROUTES.GET_RETURN_PORTAL_REASONS.replace(':id', returnPortalCode.toString());
    return this.httpService.get<IReturnPortalReason[]>(url);
  }

  public async createReturnPortalReason(
    returnPortalCode: number,
    payload: IReturnPortalReasonCreate,
  ): Promise<IReturnPortalReason> {
    const url = this.ROUTES.CREATE_RETURN_PORTAL_REASON.replace(':id', returnPortalCode.toString());
    return this.httpService.post<IReturnPortalReason, IReturnPortalReasonCreate>(url, payload);
  }

  public async updateReturnPortalReason(
    returnPortalCode: number,
    returnReasonCode: number,
    payload: IReturnPortalReasonUpdate,
  ): Promise<IReturnPortalReason> {
    const url = this.ROUTES.UPDATE_RETURN_PORTAL_REASON.replace(
      ':return-portal-id',
      returnPortalCode.toString(),
    ).replace(':id', returnReasonCode.toString());
    return this.httpService.put<IReturnPortalReason, IReturnPortalReasonUpdate>(url, payload);
  }

  public async deleteReturnPortalReason(
    returnPortalCode: number,
    returnReasonCode: number,
  ): Promise<void> {
    const url = this.ROUTES.DELETE_RETURN_PORTAL_REASON.replace(
      ':return-portal-id',
      returnPortalCode.toString(),
    ).replace(':id', returnReasonCode.toString());

    return this.httpService.delete<void>(url);
  }

  public async deleteReturnPortal(returnPortalCode: number): Promise<void> {
    const url = this.ROUTES.DELETE_RETURN_PORTAL.replace(':id', returnPortalCode.toString());
    return this.httpService.delete(url);
  }

  public async getReturnPortalResolutions(
    returnPortalCode: number,
  ): Promise<IReturnPortalResolution[]> {
    const url = this.ROUTES.GET_RETURN_PORTAL_RESOLUTIONS.replace(
      ':id',
      returnPortalCode.toString(),
    );
    return this.httpService.get<IReturnPortalResolution[]>(url);
  }

  public async createReturnPortalResolution(
    returnPortalCode: number,
    payload: IReturnPortalResolutionCreate,
  ): Promise<IReturnPortalResolution> {
    const url = this.ROUTES.CREATE_RETURN_PORTAL_RESOLUTION.replace(
      ':id',
      returnPortalCode.toString(),
    );

    return this.httpService.post<IReturnPortalResolution, IReturnPortalResolutionCreate>(
      url,
      payload,
    );
  }

  public async updateReturnPortalResolution(
    returnPortalCode: number,
    resolutionCode: number,
    payload: IReturnPortalResolutionUpdate,
  ): Promise<IReturnPortalResolution> {
    const url = this.ROUTES.UPDATE_RETURN_PORTAL_RESOLUTION.replace(
      ':return-portal-id',
      returnPortalCode.toString(),
    ).replace(':id', resolutionCode.toString());

    return this.httpService.put<IReturnPortalResolution, IReturnPortalResolutionUpdate>(
      url,
      payload,
    );
  }

  public async deleteReturnPortalResolution(
    returnPortalCode: number,
    resolutionCode: number,
  ): Promise<void> {
    const url = this.ROUTES.DELETE_RETURN_PORTAL_RESOLUTION.replace(
      ':return-portal-id',
      returnPortalCode.toString(),
    ).replace(':id', resolutionCode.toString());

    return this.httpService.delete<void>(url);
  }

  public async getReturnPortalMethods(
    returnPortalCode: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IReturnPortalMethod[]> {
    const url = this.ROUTES.GET_RETURN_PORTAL_METHODS.replace(':id', returnPortalCode.toString());

    const params: IRequestParam[] = [{ key: 'with', value: 'template' }, ...requestParams];

    return this.httpService.get<IReturnPortalMethod[]>(url, params);
  }

  public async createReturnPortalMethod(
    returnPortalCode: number,
    payload: IReturnPortalMethodCreate,
  ): Promise<IReturnPortalMethod> {
    const url = this.ROUTES.CREATE_RETURN_PORTAL_METHOD.replace(':id', returnPortalCode.toString());

    return this.httpService.post<IReturnPortalMethod, IReturnPortalMethodCreate>(url, payload);
  }

  public async updateReturnPortalMethod(
    returnPortalCode: number,
    methodCode: number,
    payload: IReturnPortalMethodCreate,
  ): Promise<IReturnPortalMethod> {
    const url = this.ROUTES.UPDATE_RETURN_PORTAL_METHOD.replace(
      ':return-portal-id',
      returnPortalCode.toString(),
    ).replace(':id', methodCode.toString());

    return this.httpService.put<IReturnPortalMethod, IReturnPortalMethodCreate>(url, payload);
  }

  public async deleteReturnPortalMethod(
    returnPortalCode: number,
    methodCode: number,
  ): Promise<void> {
    const url = this.ROUTES.DELETE_RETURN_PORTAL_METHOD.replace(
      ':return-portal-id',
      returnPortalCode.toString(),
    ).replace(':id', methodCode.toString());

    return this.httpService.delete<void>(url);
  }

  public async updateReturnPortalReceiver(
    returnPortalCode: number,
    payload: IReturnPortalReceiverUpdate,
  ): Promise<IReturnPortal> {
    const url = this.ROUTES.UPDATE_RECEIVER.replace(':id', returnPortalCode.toString());
    return this.httpService.put<IReturnPortal, IReturnPortalReceiverUpdate>(url, payload);
  }

  public async updateReturnPortalText(
    returnPortalCode: number,
    payload: IReturnPortalTextUpdate,
  ): Promise<IReturnPortal> {
    const url = this.ROUTES.UPDATE_TEXT.replace(':id', returnPortalCode.toString());
    return this.httpService.put<IReturnPortal, IReturnPortalTextUpdate>(url, payload);
  }
}
