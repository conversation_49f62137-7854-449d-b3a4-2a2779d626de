/* eslint-disable prefer-template */
export default class WindowService {
  public static openWindow(name?: string, url?: string): Window {
    const windowRef = window.open(url) as Window;

    if (!url) {
      const style: HTMLStyleElement = windowRef.document.createElement('style');
      style.innerHTML = WindowService.getLoadingStyles();
      windowRef.document.head.appendChild(style);
      windowRef.document.body.innerHTML = WindowService.getLoadingHTML(name);
    }

    if (!url) {
      const titleEl: HTMLTitleElement = windowRef.document.createElement('title');
      titleEl.innerText = name || 'Loading...';
      windowRef.document.head.appendChild(titleEl);
    }

    return windowRef;
  }

  public static downloadFromBlobUrl(blobUrl: string, filename: string): void {
    const link = document.createElement('a');
    link.href = blobUrl;
    link.setAttribute('download', filename); // Set the file name
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
  private static getLoadingHTML(name?: string): string {
    const loadingText: string = name ? `Loading ${name}...` : 'Loading...';

    return `
      <div class="loading-animation">
        <div class="block">
          <div class="item"></div>
          <div class="item"></div>
          <div class="item"></div>
          <div class="item"></div>
          <div class="item"></div>
          <div class="item"></div>
          <div class="item"></div>
          <div class="item"></div>
        </div>
      </div>
      <div class="loading-text">${loadingText}</div>
    `;
  }

  private static getLoadingStyles(): string {
    return `
    body {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      background-color: #fff;
      color: #313131;
      max-height: -webkit-fill-available;
      max-width: -webkit-fill-available;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: geometricPrecision;
    }

    .loading-text {
      margin-top: 10rem;
      font-size: 1.25rem;
      font-weight: bold;

    }

    .loading-animation {
      position: fixed;
      top: 50%;
      left: 50%;
      height: 2rem;
      width: 2rem;
      transform: translateX(-50%) translateY(-50%);
    }
    .container {
      position: absolute;
      top: 0;
      left: 0;
      height: 2rem;
      width: 2rem;
    }
    .block {
      position: absolute;
      top: 0;
      left: 0;
      height: 2rem;
      width: 2rem;
    }
    .block > .item {
      position: absolute;
      height: 2rem;
      width: 2rem;
      background: #fff;
      -webkit-animation: move 2s linear infinite;
              animation: move 2s linear infinite;
    }
    .block > .item:nth-of-type(1) {
      top: -2rem;
      left: -2rem;
      -webkit-animation-delay: -1.75s;
              animation-delay: -1.75s;
    }
    .block > .item:nth-of-type(2) {
      top: -2rem;
      left: 0;
      -webkit-animation-delay: -1.5s;
              animation-delay: -1.5s;
    }
    .block > .item:nth-of-type(3) {
      top: -2rem;
      left: 2rem;
      -webkit-animation-delay: -1.25s;
              animation-delay: -1.25s;
    }
    .block > .item:nth-of-type(4) {
      top: 0;
      left: 2rem;
      -webkit-animation-delay: -1s;
              animation-delay: -1s;
    }
    .block > .item:nth-of-type(5) {
      top: 2rem;
      left: 2rem;
      -webkit-animation-delay: -0.75s;
              animation-delay: -0.75s;
    }
    .block > .item:nth-of-type(6) {
      top: 2rem;
      left: 0;
      -webkit-animation-delay: -0.5s;
              animation-delay: -0.5s;
    }
    .block > .item:nth-of-type(7) {
      top: 2rem;
      left: -2rem;
      -webkit-animation-delay: -0.25s;
              animation-delay: -0.25s;
    }
    .block > .item:nth-of-type(8) {
      top: 0;
      left: -2rem;
      -webkit-animation-delay: 0s;
              animation-delay: 0s;
    }
    @-webkit-keyframes move {
      0% {
        transform: rotate(0) scale(1);
        -webkit-animation-timing-function: ease-in;
                animation-timing-function: ease-in;
      }
      10% {
        transform: rotate(90deg) scale(0);
      }
      50% {
        transform: rotate(90deg) scale(0);
        -webkit-animation-timing-function: ease-out;
                animation-timing-function: ease-out;
      }
      60% {
        transform: rotate(180deg) scale(1);
      }
      100% {
        transform: rotate(180deg) scale(1);
      }
    }
    @keyframes move {
      0% {
        transform: rotate(0) scale(1);
        -webkit-animation-timing-function: ease-in;
                animation-timing-function: ease-in;
      }
      10% {
        transform: rotate(90deg) scale(0);
      }
      50% {
        transform: rotate(90deg) scale(0);
        -webkit-animation-timing-function: ease-out;
                animation-timing-function: ease-out;
      }
      60% {
        transform: rotate(180deg) scale(1);
      }
      100% {
        transform: rotate(180deg) scale(1);
      }
    }
    `;
  }
}
