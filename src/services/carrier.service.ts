import { ICarrier, IPricesResponse, IShippingProduct } from '../model/carrier.model';
import { IRequestParam } from '../model/http.model';
import HttpService from './http.service';
import { IOrder } from '../model/order.model';

export default class CarrierService {
  private readonly ROUTES = {
    GET_CARRIERS: '/shipments/carriers',
    GET_CARRIER_PRODUCTS: '/shipments/carrier-products',
    PRICES: `/shipments/prices`,
  };

  constructor(private readonly httpService: HttpService) {}

  public async getCarriers(): Promise<ICarrier[]> {
    return this.httpService.get<ICarrier[]>(this.ROUTES.GET_CARRIERS);
  }

  public async getCarrierProducts(): Promise<IShippingProduct[]> {
    return this.httpService.get<IShippingProduct[]>(this.ROUTES.GET_CARRIER_PRODUCTS);
  }

  public async getShippingProducts(
    senderCountry: number,
    receiverCountry: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPricesResponse | []> {
    const params: IRequestParam[] = [
      { key: 'sender_country_code', value: senderCountry },
      { key: 'receiver_country_code', value: receiverCountry },
      ...requestParams,
    ];

    return this.httpService.get<IPricesResponse | []>(this.ROUTES.PRICES, params);
  }

  public static getCarrierFromShippingProductCode(
    carriers: ICarrier[],
    shippingProducts: IShippingProduct[],
    shippingProductCode: number | any,
  ): ICarrier | null {
    const shippingProduct = shippingProducts.find(
      (product) => product.carrier_product_code === shippingProductCode,
    );

    if (!shippingProduct) {
      return null;
    }

    const carrier = carriers.find((c) => c.carrier_code === shippingProduct.carrier_code);

    if (!carrier) {
      return null;
    }

    return carrier;
  }

  public getShippingProductFromOrder(
    order: IOrder,
    shippingProducts: IShippingProduct[],
  ): IShippingProduct | null {
    const shippingProductCode = order.delivery_template?.carrier_product_code;
    const shippingProduct = shippingProducts.find(
      (product) => product.carrier_product_code === shippingProductCode,
    );

    if (!shippingProduct) {
      return null;
    }
    return shippingProduct;
  }

  public static getCarrierProduct(
    shippingProductCode: number | string,
    shippingProducts: IShippingProduct[],
  ): IShippingProduct | null {
    const shippingProduct = shippingProducts.find(
      (product) => product.carrier_product_code === shippingProductCode,
    );

    if (!shippingProduct) {
      return null;
    }
    return shippingProduct;
  }
}
