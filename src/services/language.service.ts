import { ILanguage } from '../model/language.model';
import HttpService from './http.service';

export default class LanguageService {
  private readonly ROUTES = {
    GET_LANGUAGES: '/languages',
  } as const;

  constructor(private readonly httpService: HttpService) {}

  public static setLanguage(language: 'en' | 'dk'): void {
    localStorage.setItem('LANGUAGE', language);
  }

  public static getLanguage(): 'en' | 'dk' {
    return (localStorage.getItem('LANGUAGE') as 'en' | 'dk') || 'en';
  }

  public async getLanguages(): Promise<ILanguage[]> {
    return this.httpService.get<ILanguage[]>(this.ROUTES.GET_LANGUAGES);
  }
}
