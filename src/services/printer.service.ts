import { IPagination, IRequestParam } from '../model/http.model';
import { IPrinter, IPrinterApp, IPrinterCreate } from '../model/printer.model';
import HttpService from './http.service';

class PrinterService {
  private readonly ROUTES = {
    GET_PRINTERS: '/setting/printers',
    CREATE_PRINTER: '/setting/printer',
    GET_PRINTER_APPS: '/setting/printer-apps',
    DELETE_PRINTER: '/setting/printer/:id',
    UPDATE_PRINTER: '/setting/printer/:id',
  } as const;

  constructor(private readonly httpService: HttpService) {}

  public async getPrinters(
    page: number,
    limit: number,
    requestParams: IRequestParam[],
  ): Promise<IPagination<IPrinter>> {
    const params: IRequestParam[] = [
      { key: 'page', value: page },
      { key: 'per_page', value: limit },
      ...requestParams,
    ];

    return this.httpService.get<IPagination<IPrinter>>(this.ROUTES.GET_PRINTERS, params);
  }

  public async getPrinterApps(): Promise<IPrinterApp[]> {
    return this.httpService.get<IPrinterApp[]>(this.ROUTES.GET_PRINTER_APPS);
  }

  public async deletePrinter(id: number): Promise<void> {
    const url: string = this.ROUTES.DELETE_PRINTER.replace(':id', id.toString());
    return this.httpService.delete<void>(url);
  }

  public async createPrinter(payload: IPrinterCreate): Promise<IPrinter> {
    return this.httpService.post<IPrinter, IPrinterCreate>(this.ROUTES.CREATE_PRINTER, payload);
  }

  public async updatePrinter(id: number, payload: IPrinterCreate): Promise<IPrinter> {
    const url: string = this.ROUTES.UPDATE_PRINTER.replace(':id', id.toString());
    return this.httpService.put<IPrinter, IPrinterCreate>(url, payload);
  }
}

export default PrinterService;
