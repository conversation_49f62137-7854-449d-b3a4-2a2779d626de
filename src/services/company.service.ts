import {
  ICompany,
  ICompanyUpdateRequest,
  ICompanyUser,
  ICompanyUserUpdate,
  IStaffInvitation,
  IStaffInvitationUpdatePayload,
} from '../model/company.model';
import { IPagination, IRequestParam } from '../model/http.model';
import { IModule } from '../model/module.model';
import HttpService from './http.service';

export default class CompanyService {
  private readonly ROUTES = {
    COMPANY: '/setting/company',
    BALANCE: '/setting/company/balance',
    INVITE: '/setting/invite',
    INVITES: '/setting/invites',
    COMPANY_STAFFS: '/setting/company/staffs',
    COMPANY_STAFF: '/setting/company/staff',
    UPDATE_INVITE: '/setting/invite/:id',
  };

  constructor(private readonly httpService: HttpService) {}

  public async getCurrentCompany(): Promise<ICompany> {
    const params: IRequestParam[] = [{ key: 'with', value: 'country' }];
    return this.httpService.get<ICompany>(this.ROUTES.COMPANY, params);
  }

  // eslint-disable-next-line class-methods-use-this
  public async getCurrentBalance(): Promise<number> {
    // const result = await this.httpService.get<[IBalance]>(this.ROUTES.BALANCE);

    // return result.resource[0].balance;

    return 0;
  }

  public async updateCompany(company: ICompanyUpdateRequest): Promise<ICompany> {
    const id: number = company.company_code;

    return this.httpService.put<ICompany, ICompanyUpdateRequest>(
      `${this.ROUTES.COMPANY}/${id}`,
      company,
    );
  }

  public async getStaffInvitations(
    page: number,
    limit: number,
    requestParams: IRequestParam[],
  ): Promise<IPagination<IStaffInvitation>> {
    const params: IRequestParam[] = [
      { key: 'page', value: page },
      { key: 'per_page', value: limit },
      ...requestParams,
    ];

    return this.httpService.get<IPagination<IStaffInvitation>>(this.ROUTES.INVITES, params);
  }

  public async inviteStaffMember(
    email: string,
    name: string,
    modules: IModule[],
  ): Promise<IStaffInvitation> {
    return this.httpService.post<
      IStaffInvitation,
      Pick<IStaffInvitation, 'email' | 'name' | 'permitted_modules'>
    >(this.ROUTES.INVITE, {
      email,
      name,
      permitted_modules: modules.map<number>((m) => m.module_code),
    });
  }

  public async updateStaffInvitation(
    inviteCode: number,
    payload: IStaffInvitationUpdatePayload,
  ): Promise<IStaffInvitation> {
    const url = this.ROUTES.UPDATE_INVITE.replace(':id', inviteCode.toString());
    return this.httpService.put<IStaffInvitation, IStaffInvitationUpdatePayload>(url, payload);
  }

  public async deleteStaffInvitation(invitationId: number): Promise<IStaffInvitation> {
    return this.httpService.delete<IStaffInvitation>(`${this.ROUTES.INVITE}/${invitationId}`);
  }

  public async getCompanyUsers(
    page: number,
    limit: number,
    requestParams: IRequestParam[],
  ): Promise<IPagination<ICompanyUser>> {
    const params: IRequestParam[] = [
      { key: 'page', value: page },
      { key: 'per_page', value: limit },
      ...requestParams,
    ];

    return this.httpService.get<IPagination<ICompanyUser>>(this.ROUTES.COMPANY_STAFFS, params);
  }

  public async deleteCompanyUser(userId: number): Promise<ICompanyUser> {
    return this.httpService.delete<ICompanyUser>(`${this.ROUTES.COMPANY_STAFF}/${userId}`);
  }

  public async updateCompanyUser(userId: number, user: ICompanyUserUpdate): Promise<ICompanyUser> {
    return this.httpService.put<ICompanyUser, ICompanyUserUpdate>(
      `${this.ROUTES.COMPANY_STAFF}/${userId}`,
      user,
    );
  }
}
