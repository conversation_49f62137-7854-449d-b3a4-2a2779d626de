import axios from 'axios';
import { validate as uuidValidate } from 'uuid';
import LoggingService from './logging.service';

export default class UpdateService {
  private readonly INTERVAL_UPDATE_CHECK: number = 30000;

  private readonly BYPASS_ENV_CHECK: boolean = false;

  private currentVersionNumber = '';

  constructor(private readonly loggingService: LoggingService) {}

  private static validateUuid(uuid: string): boolean {
    return uuidValidate(uuid);
  }

  public async initializeAutoUpdater(): Promise<void> {
    if (!this.BYPASS_ENV_CHECK && import.meta.env.DEV) {
      return;
    }

    try {
      const response = await axios.get('/version', { headers: { 'Cache-Control': 'no-cache' } });
      this.currentVersionNumber = response.data;
    } catch {
      this.loggingService.log('Auto update is disabled');
      return;
    }

    if (!UpdateService.validateUuid(this.currentVersionNumber)) {
      this.loggingService.log('Auto update is disabled');
      return;
    }

    setInterval(this.checkForUpdate.bind(this), this.INTERVAL_UPDATE_CHECK);
  }

  private async checkForUpdate(): Promise<void> {
    this.loggingService.log('Checking for update...');
    const response = await axios.get('/version', { headers: { 'Cache-Control': 'no-cache' } });

    if (UpdateService.validateUuid(response.data) && response.data !== this.currentVersionNumber) {
      this.loggingService.log('Updating...');
      window.location.reload();
    }
  }
}
