import {IPagination, IRequestParam} from '../model/http.model';
import HttpService from './http.service';
import {
  IShippingMethodCreate,
  IShippingMethodResponse
} from "~/model/shipping-method.model";

class ShippingMethod {
  private readonly ROUTES = {
    // code for adding with carrier
    GET_SHIPPING_METHODS: '/setting/shipping-methods',
    CREATE_SHIPPING_METHOD: '/setting/shipping-method',
    DELETE_SHIPPING_METHOD: '/setting/shipping-method/:id',
    UPDATE_SHIPPING_METHOD: '/setting/shipping-method/:id',
  } as const;

  constructor(private readonly httpService: HttpService) {
  }

  public async getShippingMethods(
    page: number,
    limit: number,
    requestParams: IRequestParam[],
    integration_code: string
  ): Promise<IPagination<IShippingMethodResponse>> {
    const params: IRequestParam[] = [
      {key: 'with', value: 'carrier'},
      {key: 'page', value: page},
      {key: 'per_page', value: limit},
      {key: 'conditions[integration_code:equal]', value: integration_code},
      ...requestParams,
    ];
    console.log("Params ", JSON.stringify(params))
    return this.httpService.get<IPagination<IShippingMethodResponse>>(this.ROUTES.GET_SHIPPING_METHODS, params);
  }

  public async deleteShippingMethod(id: number): Promise<void> {
    const url: string = this.ROUTES.DELETE_SHIPPING_METHOD.replace(':id', id.toString());
    return this.httpService.delete<void>(url);
  }

  public async createShippingMethod(payload: IShippingMethodCreate): Promise<IShippingMethodResponse> {
    return this.httpService.post<IShippingMethodResponse, IShippingMethodCreate>(this.ROUTES.CREATE_SHIPPING_METHOD, payload);
  }

  public async updateShippingMethod(id: number, payload: IShippingMethodCreate): Promise<IShippingMethodResponse> {
    const url: string = this.ROUTES.UPDATE_SHIPPING_METHOD.replace(':id', id.toString());
    return this.httpService.put<IShippingMethodResponse, IShippingMethodCreate>(url, payload);
  }
}

export default ShippingMethod;
