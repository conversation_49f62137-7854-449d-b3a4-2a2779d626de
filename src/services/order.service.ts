import * as _ from 'lodash-es';
import { IBase64File } from '../model/common.model';
import { useHelper } from '../composeable/Helper';

const { findStoreMoney, findLocalMoney } = useHelper();

import {
  IFulfillmentRequestSingle,
  IFulfillmentRequest,
  ICancelFulfillRequest,
  IFulfillment,
} from '../model/fulfillment.model';
import { IPagination, IRequestParam } from '../model/http.model';
import {
  IOrder,
  IOrderProduct,
  IRefundPaymentRequest,
  IShippingMethodPayload,
  IUpdateCustomerRequest,
  IUpdateNoteRequest,
  IUpdateReceiverRequest,
  IDeliveryTemplatePayload,
  IIntCurrency,
  IUpdateBillingAddress,
  ICapturePaymentRequest,
  IFulfillmentHolds,
  OrderShipmentStatus,
} from '../model/order.model';
import HttpService from './http.service';
import MockOrder from '../assets/json/mock-order.json';
import {
  IFulfillmentRefundRequest,
  IFulfillmentRefundRequestSingle,
  IFulfillmentReturnRequest,
  IFulfillmentReturnRequestSingle,
} from '~/model/fulfillment-return.model';

export default class OrderService {
  private readonly ROUTES = {
    ORDERS: '/orders',
    OPEN_ORDERS: '/orders/open',
    ONGOING_ORDERS: '/orders/ongoing',
    SENT_ORDERS: '/orders/sent',
    ORDER: '/order',
    SET_ORDER_PROCESSING: '/order/:id/set_order_processing',
    SET_ORDER_OPEN: '/order/:id/set-open',
    CREATE_FULFILLMENT: '/order/:id/create-fulfillment',
    CREATE_FULFILLMENT_Return: '/order/:id/create-fulfillment-return',
    CREATE_FULFILLMENT_Refund: '/order/:id/create-fulfillment-refund',
    CANCEL_FULFILLMENT: '/order/:id/cancel-fulfillment/:fulfillmentId',
    CAPTURE_PAYMENT: '/order/:id/capture-payment',
    REFUND_PAYMENT: '/order/:id/refund-payment',
    GET_PICK_LISTS: '/print_documents',
    MARK_AS_PAID: '/order/:id/mark-paid',
    UPDATE_NOTE: '/order/:id/update-note',
    ADD_COMMENT: '/order/:id/add-event',
    SET_ORDER_READY_FOR_PICKUP: '/order/:id/ready-for-pickup',
    SET_ORDER_PICKED_UP: '/order/:id/picked-up',
    UPDATE_RECEIVER_ADDRESS: '/order/:id/update-receiver',
    UPDATE_BILLING_ADDRESS: '/order/:id/update-billing',
    UPDATE_CUSTOMER_ADDRESS: '/order/:id/update-customer',
    GENERATE_SHIPMENT_LABEL: '/order/{orderCode}/shipment-label',
    CREATE_SHIPMENT_FROM_FULFILLMENT: '/order/:orderCode/create-shipment/:fulfillmentCode',
    SET_DELIVERY_METHOD: '/order/:id/shipping-templates',
    SET_DELIVERY_METHOD_FOR_SHIPMENT: '/shipment/:id/shipping-templates',
    ACCEPT_DELIVERY_METHOD: '/order/:id/accept-shipping-template',
    ACCEPT_ALL_DELIVERY_METHOD: '/order/accept-all-shipping-template',
    ACCEPT_DENY_DELIVERY_METHOD: '/order/deny-all-shipping-template',
    DENY_DELIVERY_METHOD: '/order/:id/deny-shipping-template',
    CREATE_LABEL_FOR_FULFILLMENTS_SHIPMENT: '/fulfillment/:fulfillmentCode/generate-label',
    DOWNLOAD_LABEL_FOR_FULFILLMENTS_SHIPMENT: '/fulfillment/:fulfillmentCode/shipment-label',
    GET_ORDER_COUNT: '/orders/unprocessed-count',
    DELETE_ORDERS: '/orders/delete',
    CREATE_PICK_LIST: '/order/:id/create-pick-list',
    DOWNLOAD_PACKING_LIST: '/order/:order-code/create-packing-slip',
    GET_RECIPIENT_COUNTRIES: '/orders/recipient/countries',
  } as const;

  private readonly ordersDefaultParams: IRequestParam[] = [
    {
      key: 'with',
      value: 'customer,fulfillments,salesInvoices,deliveryTemplate,lineItems,fulfillmentReturns',
    },
  ];

  constructor(private readonly httpService: HttpService) {}

  // public static findDkkPrice = (item: any) => {
  //   if (item.shop_money.currency_code == 'DKK') {
  //     return item.shop_money.amount;
  //   } else if (item.presentment_money.currency_code == 'DKK') {
  //     return item.presentment_money.amount;
  //   } else {
  //     return item.shop_money.amount;
  //   }
  // };
  public static getTotalShopMoney(order: IOrder): IIntCurrency {
    let result = findStoreMoney(order.total_price_set);
    return result;
  }

  public static getPrice(order: IOrder): IIntCurrency {
    let result = findLocalMoney(order.total_price_set);
    return result;
  }

  public static getShippingPrice(order: IOrder): IIntCurrency {
    const result = order.shipping_lines[0];
    return result
      ? findLocalMoney(result.price_set)
      : {
          currency: order.total_price_set.presentment_money.currency_code,
          amount: 0.0,
        };
  }

  public static getSubtotal(order: IOrder): IIntCurrency {
    let result = findLocalMoney(order.subtotal_price_set);
    return result;
  }
  public static getDiscount(order: IOrder): IIntCurrency {
    // let result = findLocalMoney(order.total_discounts_set);
    const lineItems = order.line_items;
    let discountEntry = {
      amount: 0,
      currency: 'DKK',
    };
    lineItems.forEach((element) => {
      const allocations = element.discount_allocations ?? [];
      allocations.forEach((discount_allocation) => {
        const localMoney = findLocalMoney(discount_allocation.amount_set);
        const amount = Number(localMoney?.amount || 0);

        discountEntry.amount += amount;
        discountEntry.currency = localMoney.currency || discountEntry.currency;
      });
    });
    return discountEntry;
  }

  public static getRefundAmount(order: IOrder): number {
    return order.refund_amount ?? 0.0;
  }

  public static getVat(order: IOrder): IIntCurrency {
    const taxLine = order?.tax_lines[0];
    return taxLine
      ? findLocalMoney(taxLine.price_set)
      : {
          currency: order.total_price_set.presentment_money.currency_code,
          amount: 0.0,
        };
  }

  public static getCurrency(order: IOrder): string {
    return (
      order.current_total_price_set?.presentment_money?.currency_code ??
      order.total_price_set?.presentment_money?.currency_code
    );
  }

  public static getStoreCurrency(order: IOrder): string {
    return order.total_price_set?.shop_money?.currency_code;
  }

  public static getLocalCurrency(order: IOrder): string {
    return order.total_price_set?.presentment_money?.currency_code;
  }

  public async getOrders(requestParams: IRequestParam[] = []): Promise<IPagination<IOrder>> {
    const params: IRequestParam[] = [...this.ordersDefaultParams, ...requestParams];

    return this.httpService.get<IPagination<IOrder>>(this.ROUTES.ORDERS, params);
  }

  public async getOpenOrders(requestParams: IRequestParam[] = []): Promise<IPagination<IOrder>> {
    const params: IRequestParam[] = [...this.ordersDefaultParams, ...requestParams];

    return this.httpService.get<IPagination<IOrder>>(this.ROUTES.OPEN_ORDERS, params);
  }

  public async getOngoingOrders(requestParams: IRequestParam[] = []): Promise<IPagination<IOrder>> {
    const params: IRequestParam[] = [...this.ordersDefaultParams, ...requestParams];

    return this.httpService.get<IPagination<IOrder>>(this.ROUTES.ONGOING_ORDERS, params);
  }

  public async getSentOrders(requestParams: IRequestParam[] = []): Promise<IPagination<IOrder>> {
    const params: IRequestParam[] = [...this.ordersDefaultParams, ...requestParams];

    return this.httpService.get<IPagination<IOrder>>(this.ROUTES.SENT_ORDERS, params);
  }

  public async getOrder(orderCode: number): Promise<IOrder> {
    return this.httpService.get<IOrder>(`${this.ROUTES.ORDER}/${orderCode}`);
  }

  public async setOrderOpen(orderCode: number): Promise<IOrder> {
    const url: string = this.ROUTES.SET_ORDER_OPEN.replace(':id', orderCode.toString());
    return this.httpService.post<IOrder, string>(url);
  }

  public async setOrderProcessing(orderCode: number): Promise<IOrder> {
    const url: string = this.ROUTES.SET_ORDER_PROCESSING.replace(':id', orderCode.toString());
    return this.httpService.post<IOrder, string>(url);
  }

  public async setReadyForPickup(orderCode: number): Promise<IOrder> {
    const url: string = this.ROUTES.SET_ORDER_READY_FOR_PICKUP.replace(':id', orderCode.toString());
    return this.httpService.post<IOrder, string>(url);
  }

  public async setPickedUp(orderCode: number): Promise<IOrder> {
    const url: string = this.ROUTES.SET_ORDER_PICKED_UP.replace(':id', orderCode.toString());
    return this.httpService.post<IOrder, string>(url);
  }

  public async createFulfillment(
    orderCode: number,
    packagingCode: number | null,
    fulfillments: IFulfillmentRequestSingle[],
  ): Promise<IOrder> {
    const url: string = this.ROUTES.CREATE_FULFILLMENT.replace(':id', orderCode.toString());
    return this.httpService.post<IOrder, IFulfillmentRequest>(url, {
      fulfillments,
      packaging_code: packagingCode,
    });
  }

  public async createFulfillmentReturn(
    orderCode: number,
    packagingCode: number | null,
    fulfillments: IFulfillmentReturnRequestSingle[],
  ): Promise<IOrder> {
    const url: string = this.ROUTES.CREATE_FULFILLMENT_Return.replace(':id', orderCode.toString());
    return this.httpService.post<IOrder, IFulfillmentReturnRequest>(url, {
      fulfillments,
      packaging_code: packagingCode,
    });
  }

  public async createFulfillmentRefund(
    orderCode: number,
    fulfillments: IFulfillmentRefundRequestSingle[],
    shipping_charges: string,
    tax_amount: string,
  ): Promise<IOrder> {
    const url: string = this.ROUTES.CREATE_FULFILLMENT_Refund.replace(':id', orderCode.toString());
    return this.httpService.post<IOrder, IFulfillmentRefundRequest>(url, {
      fulfillments,
      shipping_charges,
      tax_amount,
    });
  }

  public async cancelFullfillment(orderCode: number, fulfillmentCode: number): Promise<[]> {
    const url: string = this.ROUTES.CANCEL_FULFILLMENT.replace(':id', orderCode.toString()).replace(
      ':fulfillmentId',
      fulfillmentCode.toString(),
    );
    return this.httpService.post<[], ICancelFulfillRequest>(url);
  }

  public static getQuantitySumOfFulfillment = (
    fulfillment: IFulfillment[],
    lineItemCode: number,
  ): number => {
    return _.sumBy(
      fulfillment.filter((f) => f.line_item_code === lineItemCode),
      'quantity',
    );
  };

  public static getReturnedQuantity = (
    fulfillment: IFulfillment[],
    lineItemCode: number,
  ): number => {
    return _.sumBy(
      fulfillment.filter((f) => f.line_item_code === lineItemCode),
      'returned_quantity',
    );
  };
  public static getCalculatedReturnedQuantity = (
    fulfillment: IFulfillment[],
    lineItemCode: number,
  ): number => {
    const item = _.find(fulfillment, { line_item_code: lineItemCode });
    return item ? item?.calculated_returned_quantity ?? 0 : 0;
  };

  public static getFulfilledItems(
    fulfillment: IFulfillment[] | null,
    products: IOrderProduct[],
  ): IOrderProduct[] {
    if (!fulfillment) {
      return [];
    }
    return products
      .map((p) => ({
        ...p,
        total_quantity: p.quantity,
        quantity: OrderService.getQuantitySumOfFulfillment(fulfillment, p.line_item_code),
        returned_quantity: OrderService.getReturnedQuantity(fulfillment, p.line_item_code),
        calculated_returned_quantity: OrderService.getCalculatedReturnedQuantity(
          fulfillment,
          p.line_item_code,
        ),
      }))
      .filter((p) => p.quantity > 0);
  }

  public static groupFulfillmentsById(fulfillments: IFulfillment[] | null): {
    [key: number]: IFulfillment[];
  } {
    if (!fulfillments) {
      return {};
    }

    return _.groupBy(fulfillments, 'fulfillment_code');
  }

  public static getUnfulfilledItems(
    fulfillment: IFulfillment[] | null,
    products: IOrderProduct[],
  ): IOrderProduct[] {
    return products
      .map((p) => {
        return {
          ...p,
          quantity:
            p.quantity -
            p.returned_quantity -
            (!fulfillment
              ? 0
              : OrderService.getQuantitySumOfFulfillment(fulfillment, p.line_item_code)) -
            OrderService.getSumOfHoldItemsQuantity(p.fulfillment_holds),
        };
      })
      .filter((p) => p.quantity > 0);
  }

  public static getOnholdItems(products: IOrderProduct[]): IOrderProduct[] {
    return products
      .filter((product) => {
        return (
          product.fulfillment_holds.length > 0 &&
          product.fulfillment_holds.filter((hold) => {
            return hold.quantity > 0;
          })
        );
      })
      .map((p) => {
        return {
          ...p,
          quantity: OrderService.getSumOfHoldItemsQuantity(p.fulfillment_holds),
        };
      })
      .filter((p) => p.quantity > 0);
  }

  public static getSumOfHoldItemsQuantity(fulfillmentHolds: IFulfillmentHolds[]): number {
    return _.sumBy(fulfillmentHolds, 'quantity');
  }

  public static mapLineItemsByFulfillmentOrder(lineItems: IOrderProduct[]): IOrderProduct[][] {
    const fulfillmentOrderMap: Map<string, IOrderProduct[]> = new Map();

    lineItems.forEach((lineItem) => {
      lineItem.fulfillment_holds.forEach((hold) => {
        const fulfillmentOrderId = hold.fulfillment_order_id;
        const newLineItem: IOrderProduct = {
          ...lineItem,
          fulfillment_holds: [hold],
          quantity: hold.quantity,
        };

        if (fulfillmentOrderMap.has(fulfillmentOrderId)) {
          fulfillmentOrderMap.get(fulfillmentOrderId)!.push(newLineItem);
        } else {
          fulfillmentOrderMap.set(fulfillmentOrderId, [newLineItem]);
        }
      });
    });

    const result: IOrderProduct[][] = [];
    fulfillmentOrderMap.forEach((items) => {
      result.push(items);
    });

    return result;
  }

  public static getFulfillmentStatus(order: IOrder) {
    const fulfillment = OrderService.getUnfulfilledItems(order.fulfillments, order.line_items);
    const totalItemsQuantity = order.line_items.reduce(
      (accumulator: number, orderProduct: IOrderProduct) =>
        accumulator + orderProduct.quantity - orderProduct.returned_quantity,
      0,
    );

    if (fulfillment.length <= 0) {
      return {
        color: '#0C9247',
        bgColor: '#BEF3D7',
        text: 'Fulfilled',
        status: 'fulfilled',
      };
    }

    const fulfilledItemsTotalQuantity = fulfillment.reduce(
      (accumulator: number, item: IOrderProduct) => accumulator + item.quantity,
      0,
    );

    if (fulfilledItemsTotalQuantity < totalItemsQuantity) {
      return {
        color: '#6158F5',
        bgColor: '#E1DDFF',
        text: 'Partially fulfilled',
        status: 'partially_fulfilled',
      };
    }

    return {
      color: '#8E7200',
      bgColor: '#FEECA1',
      text: 'Unfulfilled',
      status: 'un_fulfilled',
    };
  }

  public async getPickLists(orderCodes: number[]): Promise<IBase64File> {
    const params: IRequestParam[] = [
      {
        key: 'order_codes',
        value: orderCodes.join(','),
      },
    ];
    return this.httpService.get(this.ROUTES.GET_PICK_LISTS, params);
  }
  public async getRecipientCountries(): Promise<{ country: string; country_code: string }[]> {
    return this.httpService.get(this.ROUTES.GET_RECIPIENT_COUNTRIES, []);
  }

  public async capturePayment(
    orderCode: number,
    data: ICapturePaymentRequest,
  ): Promise<{ data: string }> {
    const url: string = this.ROUTES.CAPTURE_PAYMENT.replace(':id', orderCode.toString());
    return this.httpService.post<{ data: string }, ICapturePaymentRequest>(url, data);
  }

  public async refundPayment(orderCode: number, refundPaymentAmount: string): Promise<[]> {
    const url: string = this.ROUTES.REFUND_PAYMENT.replace(':id', orderCode.toString());
    return this.httpService.post<[], IRefundPaymentRequest>(url, {
      refundPaymentAmount,
    });
  }

  public async markAsPaid(orderCode: number): Promise<IOrder> {
    const url: string = this.ROUTES.MARK_AS_PAID.replace(':id', orderCode.toString());
    return this.httpService.post<IOrder, string>(url);
  }

  public async updateNote(orderCode: number, note: string): Promise<IOrder> {
    const url: string = this.ROUTES.UPDATE_NOTE.replace(':id', orderCode.toString());
    return this.httpService.post<IOrder, IUpdateNoteRequest>(url, { note });
  }

  public async addComment(orderCode: number, message: string): Promise<IOrder> {
    const url: string = this.ROUTES.ADD_COMMENT.replace(':id', orderCode.toString());
    return this.httpService.post<IOrder, { message: string }>(url, { message });
  }

  public async updateReceiver(
    orderCode: number,
    receiver: IUpdateReceiverRequest['shipping_address'],
  ): Promise<IOrder> {
    const url: string = this.ROUTES.UPDATE_RECEIVER_ADDRESS.replace(':id', orderCode.toString());
    return this.httpService.post<IOrder, IUpdateReceiverRequest>(url, {
      shipping_address: receiver,
    });
  }
  public async updateBilling(
    orderCode: number,
    billing_address: IUpdateBillingAddress['billing_address'],
  ): Promise<IOrder> {
    const url: string = this.ROUTES.UPDATE_BILLING_ADDRESS.replace(':id', orderCode.toString());
    return this.httpService.post<IOrder, IUpdateBillingAddress>(url, {
      billing_address: billing_address,
    });
  }

  public async updateCustomer(
    orderCode: number,
    customer: IUpdateCustomerRequest['customer'],
  ): Promise<IOrder> {
    const url: string = this.ROUTES.UPDATE_CUSTOMER_ADDRESS.replace(':id', orderCode.toString());
    return this.httpService.post<IOrder, IUpdateCustomerRequest>(url, { customer });
  }

  public async generateShipmentLabel(orderCode: number): Promise<IBase64File> {
    const url: string = this.ROUTES.GENERATE_SHIPMENT_LABEL.replace(':id', orderCode.toString());
    return this.httpService.post<IBase64File, string>(url);
  }

  public async createShipmentFromFulfillment(
    orderCode: number,
    fulfillmentCode: number,
  ): Promise<IOrder> {
    const url: string = this.ROUTES.CREATE_SHIPMENT_FROM_FULFILLMENT.replace(
      ':orderCode',
      orderCode.toString(),
    ).replace(':fulfillmentCode', fulfillmentCode.toString());

    return this.httpService.post<IOrder, unknown>(url);
  }

  public async generateLabelForFulfillmentsShipment(fulfillmentCode: number): Promise<IOrder> {
    const url = this.ROUTES.CREATE_LABEL_FOR_FULFILLMENTS_SHIPMENT.replace(
      ':fulfillmentCode',
      fulfillmentCode.toString(),
    );
    return this.httpService.post<IOrder, unknown>(url);
  }

  public async downloadLabelForFulfillmentsShipment(
    fulfillmentCode: number,
  ): Promise<{ label: string }> {
    const url = this.ROUTES.DOWNLOAD_LABEL_FOR_FULFILLMENTS_SHIPMENT.replace(
      ':fulfillmentCode',
      fulfillmentCode.toString(),
    );
    return this.httpService.get<{ label: string }>(url);
  }

  public async acceptShipmentTemplate(
    orderCode: number,
    payload: IDeliveryTemplatePayload,
  ): Promise<IOrder> {
    const url: string = this.ROUTES.ACCEPT_DELIVERY_METHOD.replace(':id', orderCode.toString());
    return this.httpService.post<IOrder, IDeliveryTemplatePayload>(url, payload);
  }

  public async acceptAllShipmentTemplate(payload: IDeliveryTemplatePayload): Promise<IOrder> {
    const url: string = this.ROUTES.ACCEPT_ALL_DELIVERY_METHOD;
    return this.httpService.post<IOrder, IDeliveryTemplatePayload>(url, payload);
  }

  public async denyAllShipmentTemplate(payload: IDeliveryTemplatePayload): Promise<IOrder> {
    const url: string = this.ROUTES.ACCEPT_DENY_DELIVERY_METHOD;
    return this.httpService.post<IOrder, IDeliveryTemplatePayload>(url, payload);
  }

  public async denyShipmentTemplate(
    orderCode: number,
    payload: IDeliveryTemplatePayload,
  ): Promise<IOrder> {
    const url: string = this.ROUTES.DENY_DELIVERY_METHOD.replace(':id', orderCode.toString());
    return this.httpService.post<IOrder, IDeliveryTemplatePayload>(url, payload);
  }

  public async setDeliveryMethod(
    orderCode: number,
    payload: IShippingMethodPayload,
  ): Promise<IOrder> {
    const url: string = this.ROUTES.SET_DELIVERY_METHOD.replace(':id', orderCode.toString());
    return this.httpService.post<IOrder, IShippingMethodPayload>(url, payload);
  }
  public async setDeliveryMethodForShipment(
    shipmentCode: number,
    payload: IShippingMethodPayload,
  ): Promise<IOrder> {
    const url: string = this.ROUTES.SET_DELIVERY_METHOD_FOR_SHIPMENT.replace(
      ':id',
      shipmentCode.toString(),
    );
    return this.httpService.post<IOrder, IShippingMethodPayload>(url, payload);
  }

  public async getUnprocessedOrderCount(): Promise<{ count: number }> {
    return this.httpService.get<{ count: number }>(this.ROUTES.GET_ORDER_COUNT);
  }

  public async deleteOrders(orderCodes: number[]): Promise<[]> {
    return this.httpService.post<[], { order_codes: number[] }>(this.ROUTES.DELETE_ORDERS, {
      order_codes: orderCodes,
    });
  }

  public async createPickList(orderCode: number): Promise<{ file: string }> {
    const url: string = this.ROUTES.CREATE_PICK_LIST.replace(':id', orderCode.toString());
    return this.httpService.post<{ file: string }, string>(url);
  }

  public static getMockOrder(): IOrder {
    return MockOrder as unknown as IOrder;
  }

  public async downloadPackingList(orderCode: number): Promise<{ file: string }> {
    const url = this.ROUTES.DOWNLOAD_PACKING_LIST.replace(':order-code', orderCode.toString());
    return this.httpService.post<{ file: string }, string>(url);
  }

  public static isOrderFulfilled(order: IOrder) {
    const fulfillment = OrderService.getUnfulfilledItems(order.fulfillments, order.line_items);
    if (fulfillment) {
      return fulfillment.length <= 0;
    }
    return false;
  }

  public static isOrderShipmentCreated(order: IOrder) {
    if (!OrderService.isOrderFulfilled(order)) {
      return false;
    }

    const fulfillments = order.fulfillments;
    let isShipmentCreated = false;
    if (fulfillments && fulfillments.length) {
      const nullShipments = fulfillments.filter((item) => {
        return item.shipment?.label == null;
      });
      if (nullShipments && nullShipments.length) {
        isShipmentCreated = false;
      } else {
        isShipmentCreated = true;
      }
    }
    return isShipmentCreated;
  }

  /**
   * Determines the shipment status of an order based on its fulfillments and line items
   * @param order The order to evaluate
   * @returns The shipment status: NOT_CREATED, PARTIAL, or COMPLETE
   */
  public static getOrderShipmentStatus(order: IOrder): OrderShipmentStatus {
    if (!order.line_items || order.line_items.length === 0) {
      return OrderShipmentStatus.NOT_CREATED;
    }

    const totalOrderedQuantity = order.line_items.reduce(
      (total, item) => total + (item.quantity || 0),
      0,
    );

    if (!order.fulfillments || order.fulfillments.length === 0) {
      return OrderShipmentStatus.NOT_CREATED;
    }

    let quantityWithShipments = 0;

    for (const fulfillment of order.fulfillments) {
      if (fulfillment.shipment && fulfillment.shipment.label !== null) {
        quantityWithShipments += fulfillment.quantity || 0;
      }
    }

    if (quantityWithShipments === 0) {
      return OrderShipmentStatus.NOT_CREATED;
    }

    if (quantityWithShipments < totalOrderedQuantity) {
      return OrderShipmentStatus.PARTIAL;
    }

    return OrderShipmentStatus.COMPLETE;
  }

  /**
   * Determines if all items in an order have been fulfilled
   * @param order The order to check
   * @returns True if all items are fulfilled, false otherwise
   */
  public static isOrderFullyFulfilled(order: IOrder): boolean {
    if (!order.line_items || order.line_items.length === 0) {
      return false;
    }
    if (!order.fulfillments || order.fulfillments.length === 0) {
      return false;
    }
    const totalOrderedQuantity = order.line_items.reduce(
      (total, item) => total + (item.quantity || 0),
      0,
    );
    const totalFulfilledQuantity = order.fulfillments.reduce(
      (total, fulfillment) => total + (fulfillment.quantity || 0),
      0,
    );

    return totalFulfilledQuantity >= totalOrderedQuantity;
  }
}
