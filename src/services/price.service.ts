class PriceService {
  // Denmark
  public vat: number = 0.00;

  public getPriceExclVAT(priceInclVAT: number): number {
    return priceInclVAT / (1 + this.vat);
  }

  public getVAT(totalPrice: number): number {
    return Number(totalPrice) - this.getPriceExclVAT(Number(totalPrice));
  }

  // eslint-disable-next-line class-methods-use-this
  public getTaxPercentage(totalPrice: number, totalTax: number): number {

    if (totalTax === 0.00)
      return Number(0.00)

    return Number((totalTax / totalPrice) * 100);
  }

  public static formatPrice(price: number, locales: string | string[] = 'de-DE'): string {
    return new Intl.NumberFormat(locales, {
      maximumFractionDigits: 2,
      minimumFractionDigits: 2,
      style: 'currency',
      currency: 'DKK',
    }).format(price);
  }

  public static formatInternationalPrice({currency, amount}: any, locales: string | string[] = 'de-DE'): string {
    return new Intl.NumberFormat(locales, {
      maximumFractionDigits: 2,
      minimumFractionDigits: 2,
      style: 'currency',
      currency: currency,
      currencyDisplay: 'code',
    }).format(amount);
  }

  public static formatPriceTextField(price: number, locales: string | string[] = 'de-DE'): string {
    return new Intl.NumberFormat(locales, {
      maximumFractionDigits: 2,
      minimumFractionDigits: 2,
    }).format(price);
  }

  public static shippingPriceFormat(price: number, locales: string | string[] = 'dk-DK'): string {
    return new Intl.NumberFormat(locales, {
      maximumFractionDigits: 2,
      minimumFractionDigits: 2,
      
    }).format(price);
  }
}


export default PriceService;
