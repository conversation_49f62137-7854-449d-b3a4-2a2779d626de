import { IPagination, IRequestParam } from '../model/http.model';
import {
  IDeliveryMethod,
  IOrderIntegration,
  IOrderIntegrationCreateRequest,
  IOrderIntegrationPlatform, IOrderIntegrationStatus,
  IOrderIntegrationUpdateRequest,
  IUpdateDeliveryMethodRequest,
} from '../model/order-integration.model';
import HttpService from './http.service';

export default class OrderIntegrationService {
  private readonly ROUTES = {
    INTEGRATIONS: '/setting/integrations',
    INTEGRATION: '/setting/integration',
    ACTIVATEINTEGRATION: '/setting/activate-integration',
    DEACTIVATEINTEGRATION: '/setting/deactivate-integration',
    PLATFORM: '/setting/platforms',
    DELIVERY_METHODS: '/setting/delivery-methods',
    DELIVERY_METHOD: '/setting/delivery-method/:id',
    IMPORT_SETTINGS: '/setting/integration/:id/import-settings',
  };

  constructor(private readonly httpService: HttpService) {}

  public async getOrderIntegrations(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPagination<IOrderIntegration>> {
    const params: IRequestParam[] = [
      { key: 'page', value: page },
      { key: 'per_page', value: limit },
      { key: 'with', value: 'deliveryMethods' },
      ...requestParams,
    ];

    return this.httpService.get<IPagination<IOrderIntegration>>(this.ROUTES.INTEGRATIONS, params);
  }

  public async getOrderIntegrationPlatforms(): Promise<IOrderIntegrationPlatform[]> {
    const result = await this.httpService.get<IPagination<IOrderIntegrationPlatform>>(
      this.ROUTES.PLATFORM,
    );

    return result.entities;
  }

  public async createOrderIntegration(
    payload: IOrderIntegrationCreateRequest,
  ): Promise<IOrderIntegration> {
    return this.httpService.post<IOrderIntegration, IOrderIntegrationCreateRequest>(
      this.ROUTES.INTEGRATION,
      payload,
    );
  }

  public async activateOrderIntegration(
    payload: IOrderIntegrationStatus,
  ): Promise<IOrderIntegrationStatus> {
    return this.httpService.post<IOrderIntegrationStatus, IOrderIntegrationStatus>(
      this.ROUTES.ACTIVATEINTEGRATION,
      payload,
    );
  }
  public async deactivateOrderIntegration(
    payload: IOrderIntegrationStatus,
  ): Promise<IOrderIntegrationStatus> {
    return this.httpService.post<IOrderIntegrationStatus, IOrderIntegrationStatus>(
      this.ROUTES.DEACTIVATEINTEGRATION,
      payload,
    );
  }

  public async updateOrderIntegration(
    integrationCode: number,
    payload: IOrderIntegrationUpdateRequest,
  ): Promise<IOrderIntegration> {
    return this.httpService.put<IOrderIntegration, IOrderIntegrationUpdateRequest>(
      `${this.ROUTES.INTEGRATION}/${integrationCode}`,
      payload,
    );
  }

  public async deleteOrderIntegration(integrationCode: number): Promise<IOrderIntegration> {
    return this.httpService.delete<IOrderIntegration>(
      `${this.ROUTES.INTEGRATION}/${integrationCode}`,
    );
  }

  public async getDeliveryMethods(integrationCode: number): Promise<IDeliveryMethod[]> {
    const params: IRequestParam[] = [
      { key: 'integration_code', value: integrationCode },
      { key: 'sorting[created_at]', value: 'desc' },
      { key: 'with', value: 'deliveryTemplate,returnTemplate' },
    ];

    return this.httpService.get<IDeliveryMethod[]>(this.ROUTES.DELIVERY_METHODS, params);
  }

  public async deleteDeliveryMethod(deliveryMethodCode: number): Promise<boolean> {
    const url = this.ROUTES.DELIVERY_METHOD.replace(':id', deliveryMethodCode.toString());
    return this.httpService.delete<boolean>(url);
  }

  public async updateDeliveryMethod(
    deliveryMethodCode: number,
    payload: IUpdateDeliveryMethodRequest,
  ): Promise<IDeliveryMethod> {
    const url = this.ROUTES.DELIVERY_METHOD.replace(':id', deliveryMethodCode.toString());
    return this.httpService.put<IDeliveryMethod, IUpdateDeliveryMethodRequest>(url, payload);
  }

  public async importIntegrationSettings(integrationCode: number): Promise<boolean> {
    const url = this.ROUTES.IMPORT_SETTINGS.replace(':id', integrationCode.toString());
    return this.httpService.post<boolean, unknown>(url);
  }
}
