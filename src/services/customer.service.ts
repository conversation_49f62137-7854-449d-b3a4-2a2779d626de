import { ICustomer, ICustomerRequest } from '../model/customer.model';
import { IPagination, IRequestParam } from '../model/http.model';
import HttpService from './http.service';

export default class CustomerService {
  private readonly ROUTES = {
    CUSTOMERS: '/customers',
    CUSTOMER: '/customer/:id',
  } as const;

  constructor(private readonly httpService: HttpService) {}

  public async getCustomers(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPagination<ICustomer>> {
    const params: IRequestParam[] = [
      { key: 'page', value: page },
      { key: 'per_page', value: limit },
      ...requestParams,
    ];
    return this.httpService.get<IPagination<ICustomer>>(this.ROUTES.CUSTOMERS, params);
  }

  public async deleteCustomer(customerCode: number): Promise<void> {
    return this.httpService.delete(`${this.ROUTES.CUSTOMER}/${customerCode}`);
  }

  public async updateCustomer(customerCode: number, payload: ICustomerRequest): Promise<ICustomer> {
    const url = this.ROUTES.CUSTOMER.replace(':id', customerCode.toString());
    return this.httpService.put<ICustomer, ICustomerRequest>(url, payload);
  }
}
