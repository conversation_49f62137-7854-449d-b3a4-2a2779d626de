import { IApiAccess } from '../model/api-access.model';
import HttpService from './http.service';

class ApiAccessService {
  public ROUTES = {
    GET_API_ACCESS: '/setting/api-access',
    GENERATE_API_ACCESS: '/setting/generate-api-access',
  } as const;

  constructor(private readonly httpService: HttpService) {}

  public getApiAccess(): Promise<IApiAccess | ''> {
    return this.httpService.get<IApiAccess | ''>(this.ROUTES.GET_API_ACCESS);
  }

  public generateApiAccess(): Promise<IApiAccess> {
    return this.httpService.post<IApiAccess, unknown>(this.ROUTES.GENERATE_API_ACCESS);
  }
}

export default ApiAccessService;
