import { IPagination, IRequestParam } from '../model/http.model';
import { ITemplate, ITemplateRequest } from '../model/template.model';
import HttpService from './http.service';

export default class TemplateService {
  private readonly ROUTES = {
    TEMPLATES: '/shipments/templates',
    UPDATE_TEMPLATE: '/shipments/template/:id',
    DELETE_TEMPLATE: '/shipments/template/:id',
    CREATE_TEMPLATE: '/shipments/template',
    GET_DEFAULT_TEMPLATE: '/shipments/template/default-template',
    GET_TEMPLATE: '/shipments/template/:id',
  } as const;

  constructor(private readonly httpService: HttpService) {}

  public async getTemplates(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPagination<ITemplate>> {
    const params: IRequestParam[] = [
      { key: 'page', value: page },
      { key: 'per_page', value: limit },
      ...requestParams,
    ];

    return this.httpService.get<IPagination<ITemplate>>(this.ROUTES.TEMPLATES, params);
  }

  public async updateTemplate(templateCode: number, payload: ITemplateRequest): Promise<ITemplate> {
    const url: string = this.ROUTES.UPDATE_TEMPLATE.replace(':id', templateCode.toString());
    return this.httpService.put<ITemplate, ITemplateRequest>(url, payload);
  }

  public async createTemplate(payload: ITemplateRequest): Promise<ITemplate> {
    return this.httpService.post<ITemplate, ITemplateRequest>(this.ROUTES.CREATE_TEMPLATE, payload);
  }

  public async deleteTemplate(templateCode: number): Promise<void> {
    const url: string = this.ROUTES.DELETE_TEMPLATE.replace(':id', templateCode.toString());
    return this.httpService.delete(url);
  }

  public async getDefaultTemplate(): Promise<ITemplate | ''> {
    return this.httpService.get<ITemplate | ''>(this.ROUTES.GET_DEFAULT_TEMPLATE);
  }

  public async getTemplate(templateCode: number): Promise<ITemplate> {
    const url: string = this.ROUTES.GET_TEMPLATE.replace(':id', templateCode.toString());
    return this.httpService.get<ITemplate>(url);
  }
}
