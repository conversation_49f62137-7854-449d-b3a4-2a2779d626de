import { ICreditNote, ICreditNoteItem, ICreditNoteItemGroup } from '../model/credit-note.model';
import { IPagination, IRequestParam } from '../model/http.model';
import HttpService from './http.service';

class CreditNoteService {
  private readonly ROUTES = {
    CREDIT_NOTES: '/credit-notes',
  } as const;

  constructor(private httpService: HttpService) {}

  public async getCreditNotes(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = [],
  ): Promise<IPagination<ICreditNote>> {
    const params: IRequestParam[] = [
      { key: 'page', value: page },
      { key: 'per_page', value: limit },
      ...requestParams,
    ];
    return this.httpService.get<IPagination<ICreditNote>>(this.ROUTES.CREDIT_NOTES, params);
  }

  public static getTotalExclVAT(creditNote: ICreditNote): number {
    // TODO: Needs to be refactored
    return creditNote.credit_note_items.reduce(
      (acc: number, item: ICreditNoteItemGroup) =>
        acc +
        item.items.reduce(
          (_acc: number, _item: ICreditNoteItem) =>
            _acc + Number(_item.count) * Number(_item.unit_price),
          0,
        ),
      0,
    );
  }
}

export default CreditNoteService;
