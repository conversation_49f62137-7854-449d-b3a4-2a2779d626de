import { AxiosError } from 'axios';
import router from '../router';
import * as _ from 'lodash-es';
import {
  IForgotPasswordRequest,
  IForgotPasswordResponse,
  ILoginRequest,
  ILoginResponse,
  ILogoutRequest,
  IResetPasswordRequest,
  ISignUpRequest,
  ISignUpResponse,
} from '../model/auth.model';
import HttpService from './http.service';

export default class AuthService {
  protected readonly routes = {
    LOGIN: 'login',
    SIGNUP: 'register',
    LOGOUT: 'logout',
    FORGOT_PASSWORD: 'forgot-password',
    RESET_PASSWORD: 'reset-password',
  };

  constructor(protected readonly httpService: HttpService) {
    httpService.axios.interceptors.response.use((response) => response, this.onRejected.bind(this));
  }

  private static setLocalStorageToken(isRememberMe: boolean, token: string): void {
    if (isRememberMe) {
      localStorage.setItem('token', token);
    } else {
      sessionStorage.setItem('token', token);
    }
  }

  public setSessionStorageToken(token: string): void {
    this.httpService.setToken(token);
    AuthService.setLocalStorageToken(false, token);
  }

  public async onRejected(error: AxiosError | unknown): Promise<void> {
    if (error instanceof AxiosError && error?.response?.status && error.response.status === 401) {
      await this.logout();
      router.push({ path: 'login' });
      return;
    }

    return Promise.reject(error);
  }

  public async login(email: string, password: string, isRememberMe: boolean): Promise<void> {
    const result = await this.httpService.post<ILoginResponse, ILoginRequest>(this.routes.LOGIN, {
      email,
      password,
    });
    const { token } = result;

    this.httpService.setToken(token);
    AuthService.setLocalStorageToken(isRememberMe, token);
  }

  public async signUp(payload: ISignUpRequest): Promise<ISignUpResponse> {
    const response: ISignUpResponse = await this.httpService.post<ISignUpResponse, ISignUpRequest>(
      this.routes.SIGNUP,
      payload,
    );

    this.httpService.setToken(response.user.token);
    AuthService.setLocalStorageToken(true, response.user.token);

    return response;
  }

  public async logout(): Promise<void> {
    const token = HttpService.getToken();
    if (!token) {
      router.push({ path: 'login' });
      return;
    }
    sessionStorage.removeItem('token');
    localStorage.removeItem('token');
    await _.attempt(async () =>
      this.httpService.post<[false], ILogoutRequest>(this.routes.LOGOUT, {
        token,
      }),
    );
  }

  public async forgotPassword(email: string): Promise<IForgotPasswordResponse> {
    return this.httpService.post<IForgotPasswordResponse, IForgotPasswordRequest>(
      this.routes.FORGOT_PASSWORD,
      { email },
    );
  }

  public async resetPassword(
    token: string,
    password: string,
    password_confirmation: string,
  ): Promise<void> {
    return this.httpService.post<void, IResetPasswordRequest>(this.routes.RESET_PASSWORD, {
      token,
      password,
      password_confirmation,
    });
  }
}
