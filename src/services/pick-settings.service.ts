import { IPickSettings, IPickSettingsSaveRequest } from '../model/pick-settings.model';
import HttpService from './http.service';

export default class PickSettingsService {
  constructor(private readonly httpService: HttpService) {}

  private readonly ROUTES = { self: '/setting/pick-setting' } as const;

  public async savePickSettings(pickSettings: IPickSettingsSaveRequest): Promise<IPickSettings> {
    return this.httpService.post<IPickSettings, IPickSettingsSaveRequest>(
      this.ROUTES.self,
      pickSettings,
    );
  }

  public async getPickSettings(): Promise<IPickSettings> {
    const result = await this.httpService.get<[IPickSettings]>(this.ROUTES.self);

    return result[0];
  }
}
