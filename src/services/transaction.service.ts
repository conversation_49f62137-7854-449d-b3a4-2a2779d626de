import { Notify } from "quasar";
import { IBase64File } from "../model/common.model";
import { IPagination, IRequestParam } from "../model/http.model";
import {
  IAddFundRequest,
  IAddFundResponse,
  IAddFundWithSavedCardResponse,
  ITransaction, IVerifyPayment, IVerifyPaymentResponse
} from "../model/transactions.model";
import HelperService from "./helper.service";
import HttpService from "./http.service";
import LoadingService from "./loading.service";
import LoggingService from "./logging.service";
import { IInvoice } from "~/model/invoice.model";

export default class TransactionService {
  private readonly ROUTES = {
    TRANSACTIONS: "transactions",
    TRANSACTION: "transaction",
    INVOICE: "transaction/:transaction_code/invoice",
    REFILLS: "transactions/refills",
    ADD_FUND: "add-funds",
    ADD_FUND_WITH_SAVED_CARD: "add-funds-with-saved-card",
    VERIFY_ADD_FUND_WITH_SAVED_CARD: "add-funds-with-saved-card/verify",
    VERIFY_PAYMENT: "verify-payment",
    DOWNLOAD_RECEIPT_PDF: "download-receipt-pdf/:transaction_code",
    DOWNLOAD_INVOICE_PDF: "transactions-download/:transaction_code"
  };

  private printWindowRef: Window | null = null;

  constructor(
    protected readonly httpService: HttpService,
    protected readonly loadingService: LoadingService,
    protected readonly loggingService: LoggingService
  ) {
  }

  public async getTransactions(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = []
  ): Promise<IPagination<ITransaction>> {
    const params: IRequestParam[] = [
      { key: "page", value: page },
      { key: "per_page", value: limit },
      ...requestParams
    ];
    return this.httpService.get<IPagination<ITransaction>>(this.ROUTES.TRANSACTIONS, params);
  }

  public async getFundsTransactions(
    page: number,
    limit: number,
    requestParams: IRequestParam[] = []
  ): Promise<IPagination<ITransaction>> {
    const params: IRequestParam[] = [
      { key: "page", value: page },
      { key: "per_page", value: limit },
      ...requestParams
    ];

    return this.httpService.get<IPagination<ITransaction>>(this.ROUTES.REFILLS, params);
  }

  public async addFund(payload: IAddFundRequest): Promise<IAddFundResponse> {
    return this.httpService.post<IAddFundResponse, IAddFundRequest>(this.ROUTES.ADD_FUND, payload);
  }

  public async addFundWithSavedCard(payload: IAddFundRequest): Promise<IAddFundWithSavedCardResponse> {
    return this.httpService.post<IAddFundWithSavedCardResponse, IAddFundRequest>(this.ROUTES.ADD_FUND_WITH_SAVED_CARD, payload);
  }
  
  public async verifyPayment(payload: IVerifyPayment): Promise<IVerifyPaymentResponse> {
    return this.httpService.post<IVerifyPaymentResponse, IVerifyPayment>(this.ROUTES.VERIFY_PAYMENT, payload);
  }

  public async invoice(transaction_code: number): Promise<IInvoice> {
    const url = this.ROUTES.INVOICE.replace(":transaction_code", transaction_code.toString());
    return this.httpService.get<IInvoice>(url);
  }

  public printReceipt(transactionCode: number): void {
    const windowRef: Window | null = window.open();

    if (windowRef) {
      this.printWindowRef = windowRef;
    }

    this.asyncPrintReceipt(transactionCode);
  }

  public async asyncPrintReceipt(transactionCode: number): Promise<void> {
    try {
      this.loadingService.startLoading("main-loader:print-transaction-receipt");
      const request = `/transaction/receipt/${transactionCode}`;
      const response = await this.httpService.get<IBase64File>(request);
      const blob: Blob = await HelperService.toBlob(
        `data:application/pdf;base64,${encodeURI(response.file)}`
      );

      if (this.printWindowRef) {
        this.printWindowRef.location = URL.createObjectURL(blob);
      } else {
        throw new Error("Printing window is not available");
      }
    } catch (error) {
      this.loggingService.error("Print receipt", error);

      if (this.printWindowRef) {
        this.printWindowRef.close();
      }

      Notify.create({ message: "Error downloading PDF", color: "red", icon: "error" });
    } finally {
      this.loadingService.stopLoading("main-loader:print-transaction-receipt");
    }
  }

  public async downloadFundsPdf(transaction_code: number | string): Promise<{ file: string }> {
    const url: string = this.ROUTES.DOWNLOAD_RECEIPT_PDF.replace(":transaction_code", transaction_code.toString());
    return this.httpService.get<{ file: string }>(url);
  }

  public async downloadInvoicePdf(transaction_code: number | string): Promise<{ file: string }> {
    const url: string = this.ROUTES.DOWNLOAD_INVOICE_PDF.replace(":transaction_code", transaction_code.toString());
    return this.httpService.get<{ file: string }>(`${url}?type=INVOICE`);
  }
}
