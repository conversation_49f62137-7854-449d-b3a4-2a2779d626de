import { computed, onMounted, onUnmounted, Ref, ref } from 'vue';

export default () => {
  const windowWidth: Ref<number> = ref(window.innerWidth);

  const onWidthChange = () => {
    windowWidth.value = window.innerWidth;
  };

  onMounted(() => window.addEventListener('resize', onWidthChange));
  onUnmounted(() => window.removeEventListener('resize', onWidthChange));

  return computed(() => windowWidth.value);
};
