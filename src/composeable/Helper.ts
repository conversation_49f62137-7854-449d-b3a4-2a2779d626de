import WindowService from '../services/window.service';
import HelperService from '../services/helper.service';
import { ICountry } from '../model/country.model';
import { ICurrency } from '../model/shipping-method.model';
import { useStore } from '../store';
import { helpers } from '@vuelidate/validators';
import PriceService from '../services/price.service';
import { IOrder } from '../model/order.model';

interface IStringBoolean {
  [key: string]: boolean;
}

export function useHelper() {
  let windowRef: Window | null;
  const showPdf = async (file: string) => {
    windowRef = WindowService.openWindow('PDF Preview');
    if (!windowRef) {
      throw new Error('Window reference is not available');
    }
    try {
      const blob: Blob = await HelperService.toBlob(
        `data:application/pdf;base64,${encodeURI(file)}`,
      );
      windowRef.location = URL.createObjectURL(blob);
    } catch (e) {
      windowRef.close();
    } finally {
      windowRef = null;
    }
  };
  const handleBeforeUnload = (event: any) => {
    event.preventDefault();
    const confirmationMessage = 'Are you sure you want to leave?';
    event.returnValue = 'Are you sure you want to leave?';
    return confirmationMessage;
  };

  const denmark: ICountry = {
    country_code: 3421137111,
    iso: 'DK',
    name: 'DENMARK',
    default_name: 'Denmark',
    iso3: 'DNK',
    num_code: 208,
    phone_code: 45,
    created_at: '',
    is_deleted: false,
    created_by: 123,
    status: true,
    updated_at: '',
  };

  const currencies: ICurrency[] = [
    {
      currency: 'DKK',
      currency_code: 3744202329,
    },
    {
      currency: 'EUR',
      currency_code: 5716914006853,
    },
    {
      currency: 'SEK',
      currency_code: 3520543250353,
    },
  ];

  // function to return product price in either dk or another currency
  const findDkkPrice = (item: any) => {
    if (item.shop_money.currency_code == 'DKK') {
      return item.shop_money.amount;
    } else if (item.presentment_money.currency_code == 'DKK') {
      return item.presentment_money.amount;
    } else {
      return item.shop_money.amount;
    }
  };

  const findStoreMoney = (item: any) => {
    return { amount: item.shop_money.amount, currency: item.shop_money.currency_code };
  };

  const findLocalMoney = (item: any) => {
    return {
      amount: item.presentment_money.amount,
      currency: item.presentment_money.currency_code,
    };
  };

  const carrierInfoLink = (carrier_id: any) => {
    let link: string = '';
    if (carrier_id == 'GLS_DK') {
      link = 'https://shipvagoo.com/virksomhed/kundecenter/gls-danmark/';
    } else if (carrier_id == 'DAO') {
      link = 'https://shipvagoo.com/virksomhed/kundecenter/dao/';
    } else if (carrier_id == 'DHL_SE') {
      link = 'https://shipvagoo.com/virksomhed/kundecenter/dhl-sweden/';
    } else if (carrier_id == 'GLS_EU') {
      link = 'https://shipvagoo.com/virksomhed/kundecenter/gls-international/';
    } else if (carrier_id == 'DHL_PARCEL_EUROPE') {
      link = 'https://shipvagoo.com/virksomhed/kundecenter/dhl-netherlands/';
    } else if (carrier_id == 'MONDIAL_RELAY') {
      link = 'https://shipvagoo.com/virksomhed/kundecenter/mondial-relay/';
    } else if (carrier_id == 'POSTI') {
      link = 'https://shipvagoo.com/virksomhed/kundecenter/posti/';
    }
    return link;
  };

  const phoneRequiredForMontyProducts: IStringBoolean = {
    DHL_SE_SERVICE_POINT: true,
    DHL_SE_SERVICE_POINT_DOORSTEP: true,
    POSTI_POSTAL_PARCEL: true,
    POSTI_HOME_PARCEL: true,
    GLS_DK_PARCEL_SHOP: true,
    GLS_DK_BUSINESS_PARCEL: false,
    GLS_DK_FLEX: true,
    GLS_DK_SHOP_RETURN: true,
    GLS_DK_BUSINESS_PARCEL_EURO: true,
    GLS_DK_HOME_PARCEL_EURO: true,
    GLS_DK_SHOP_RETURN_EURO: true,
    GLS_DK_PRIVATE: true,
  };
  const nineDigitsMobileForMontyProducts: IStringBoolean = {
    MONDIAL_RELAY_24R: true,
    MONDIAL_RELAY_LCC: true,
  };

  const minFourAndMaxFourteenDigitsMobileForMontyProducts: IStringBoolean = {
    GLS_DK_PARCEL_SHOP: true,
    GLS_DK_BUSINESS_PARCEL: true,
    GLS_DK_FLEX: true,
    GLS_DK_SHOP_RETURN: true,
    GLS_DK_BUSINESS_PARCEL_EURO: true,
    GLS_DK_HOME_PARCEL_EURO: true,
    GLS_DK_SHOP_RETURN_EURO: true,
    GLS_DK_PRIVATE: true,
  };
  const specialCharValidationForCarriers: IStringBoolean = {
    GLS_DK: true,
  };
  const validateReceiverMobile = (
    montyProduct: string | undefined,
    receiverPhone: string,
    order_number: number,
  ): string | null => {
    let responseMessage = null;
    console.log('Receiver Phone ', receiverPhone);
    if (montyProduct) {
      let isRequiredForMontyProduct = phoneRequiredForMontyProducts[montyProduct ?? ''] ?? false;
      const isNineDigitsProductRequiredProduct =
        nineDigitsMobileForMontyProducts[montyProduct ?? ''] ?? false;
      if (isRequiredForMontyProduct && (receiverPhone == null || receiverPhone == '')) {
        responseMessage = `Order No. ${order_number}: Mobile number is mandatory. Update receiver information`;
      } else if (receiverPhone != null && receiverPhone == '' && !/^[0-9]*$/.test(receiverPhone)) {
        responseMessage = `Order No. ${order_number}: Enter a valid mobile number only with digits.`;
      } else if (
        isNineDigitsProductRequiredProduct &&
        receiverPhone != null &&
        receiverPhone != '' &&
        receiverPhone.length != 9
      ) {
        responseMessage = `Order No. ${order_number}: Mobile number must consist of 9 digits`;
      }
    }
    return responseMessage;
  };
  const getDialCode = (countryIso: string | undefined, withParanthesis = true) => {
    if (!countryIso) {
      return '';
    }
    const $store = useStore();
    const { countries } = $store.state.countryStore;
    const country: ICountry | undefined =
      countryIso === 'DK' ? denmark : countries.find((c) => c.iso === countryIso);

    if (!country) {
      throw new Error('Country not found');
    }

    const dialCode: string = `+${country.phone_code}`;

    return withParanthesis ? `(${dialCode})` : dialCode;
  };

  function extractMinMaxWeight(parcelDescription: string) {
    const pattern = /\d+(\.\d+)?/g;
    const matches = parcelDescription.match(pattern);
    return matches ? matches.map(Number) : [];
  }

  const mustBeValidInput = helpers.withParams({ type: 'validDecimal' }, (value: any) => {
    const regex = /^(\d{1,3}(\.\d{3})*|\d+)(,\d{2})?$/;
    return regex.test(value as string);
  });

  const mustBeValidLength = helpers.withParams({ type: 'validLength' }, (value: any) => {
    return isValidPrice(value);
  });

  const isValidPrice = (value: any) => {
    if (!value) {
      return true; // Value is falsy, return true
    }
    const stringValue = value
      .toString()
      .replace(/[,.]/g, (match: any) => (match === ',' ? '.' : ''));
    const formattedPrice = PriceService.shippingPriceFormat(Number(stringValue), 'en').replace(
      /,/g,
      '',
    );
    if (!formattedPrice) {
      return true;
    }
    const result = formattedPrice.split('.')[0];
    return result.length <= 6;
  };

  const mustBeValidMinValue = (minVal: number) =>
    helpers.withParams({ type: 'minValue' }, (value: any) => {
      if (value == null || value == '') {
        return true;
      }
      const stringValue = value
        .toString()
        .replace(/[,.]/g, (match: any) => (match === ',' ? '.' : ''));
      return Number(stringValue) > minVal;
    });
  const mustBeValidMaxValue = (maxValue: number) =>
    helpers.withParams({ type: 'maxValue' }, (value: any) => {
      if (value == null || value === '') {
        return true;
      }
      const stringValue = value
        .toString()
        .replace(/[,.]/g, (match: any) => (match === ',' ? '.' : ''));
      const numValue = Number(stringValue);
      return numValue <= maxValue && numValue >= 0;
    });

  const mustBeValidName = helpers.withParams({ type: 'validName' }, (value: any) => {
    if (!value || value.length === 0) {
      return false;
    }
    const senderName = HelperService.parseFullName(value);
    if (!senderName.first || !senderName.last) {
      return false;
    }
    return true;
  });

  const mustBeValidEmail = (value: any, carrier_id: string | undefined | null) => {
    if (!value || value.length === 0) {
      return true;
    }
    const utf8EmailRegex =
      /^[\p{L}0-9](?!.*\.\.)[\p{L}0-9._%+-]*(?<![.%+-])@[a-zA-Z0-9]+(?:-[a-zA-Z0-9]+)*(\.[a-zA-Z0-9]+(?:-[a-zA-Z0-9]+)*)*\.[a-zA-Z]{2,63}$/u;
    // const utf8EmailRegex = /^[\p{L}0-9._%+-]+(?<![.%+-])@[a-zA-Z0-9]+(?:-[a-zA-Z0-9]+)*(\.[a-zA-Z0-9]+(?:-[a-zA-Z0-9]+)*)*\.[a-zA-Z]{2,63}$/u
    //const utf8EmailRegex = /^[^\s@]+@[a-zA-Z0-9]+(?:\.[a-zA-Z0-9-]+)*\.[a-zA-Z]{2,}$/;
    const generalEmailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9]+(?:\.[a-zA-Z0-9-]+)*\.[a-zA-Z]{2,}$/;

    if (!carrier_id) {
      return utf8EmailRegex.test(value);
    }

    const isSpecialCharCarrier = specialCharValidationForCarriers[carrier_id ?? ''] ?? false;

    return isSpecialCharCarrier ? utf8EmailRegex.test(value) : generalEmailRegex.test(value);
  };

  const mustBeValidMobileLength = (value: any, product_id: string | undefined | null) => {
    if (!value || value.length === 0) {
      return true;
    }

    //const montyProduct = shippingProduct.value?.product_id;
    if (!product_id) {
      return true;
    }
    const isNineDigitRequired = nineDigitsMobileForMontyProducts[product_id ?? ''] ?? false;
    if (isNineDigitRequired) {
      return value.length === 9;
    }

    const isMinFourMaxFourty =
      minFourAndMaxFourteenDigitsMobileForMontyProducts[product_id ?? ''] ?? false;

    if (isMinFourMaxFourty) {
      return value.length >= 4 && value.length <= 40;
    }

    return true;
  };
  const mustBeValidUrlName = helpers.withParams({ type: 'mustBeValidUrlName' }, (value: any) => {
    const regex = /[/?#]/;
    return !regex.test(value as string);
  });
  const mustBeValidURL = helpers.withParams({ type: 'mustBeValidURL' }, (value: any) => {
    if (!value) {
      return true;
    }
    const regex = /^(https?:\/\/)?(www\.)?[a-zA-Z0-9\-]+\.[a-zA-Z]{2,}$/;
    return regex.test(value as string);
  });

  function replaceDialCode(phone: string, dialCode: string) {
    const escapedDialCode = dialCode?.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') ?? '';
    const regex = new RegExp(`^\\+?${escapedDialCode}`);
    return phone?.replace(regex, '') ?? '';
  }

  const getShippingCharges = (order: IOrder, option: any = 'object'): any => {
    /* let totalFulfillments = 0;

    // Sum up the total fulfillment quantity
    order.line_items.forEach((item: any) => {
      totalFulfillments += item.quantity;
    }); */

    // Get the delivery charge from shipping_lines
    let deliveryCharges: number = 0.0;
    const shippingLine = order?.shipping_lines?.[0];
    if (shippingLine) {
      const res = findLocalMoney(shippingLine.price_set);
      deliveryCharges = res?.amount || 0.0;
    }

    // Calculate refunded amount from sales invoices
    let refundedAmount = 0;
    const creditNotes =
      order.sales_invoices?.filter((invoice: any) => invoice.invoice_type === 'CREDIT_NOTE') || [];

    creditNotes.forEach((creditNote: any) => {
      if (creditNote.shipping_lines?.price) {
        refundedAmount += parseFloat(creditNote.shipping_lines.price);
      }
    });

    // Calculate the final amount
    const finalAmount = deliveryCharges - refundedAmount;

    // Return the result based on the option
    if (option === 'money') {
      return parseFloat(finalAmount.toString());
    } else {
      return {
        currency: shippingLine ? findLocalMoney(shippingLine.price_set)?.currency : undefined,
        amount: parseFloat(finalAmount.toString()),
      };
    }
  };

  return {
    showPdf,
    handleBeforeUnload,
    denmark,
    currencies,
    findDkkPrice,
    findStoreMoney,
    findLocalMoney,
    carrierInfoLink,
    phoneRequiredForMontyProducts,
    nineDigitsMobileForMontyProducts,
    minFourAndMaxFourteenDigitsMobileForMontyProducts,
    mustBeValidMobileLength,
    specialCharValidationForCarriers,
    validateReceiverMobile,
    getDialCode,
    extractMinMaxWeight,
    mustBeValidMinValue,
    mustBeValidMaxValue,
    mustBeValidLength,
    mustBeValidInput,
    mustBeValidName,
    mustBeValidUrlName,
    mustBeValidURL,
    mustBeValidEmail,
    replaceDialCode,
    getShippingCharges,
  };
}
