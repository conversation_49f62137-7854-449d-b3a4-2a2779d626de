/*
Makes backend API call to rasa chatbot and display output to chatbot frontend
*/

function init(botLogoPath) {

    // ---------------------------- Including Jquery ------------------------------

    const script = document.createElement('script');
    script.src = 'https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js';
    script.type = 'text/javascript';
    document.getElementsByTagName('head')[0].appendChild(script);

    // --------------------------- Important Variables----------------------------
    // botLogoPath = "./imgs/bot-logo.png"

    // --------------------------- Chatbot Frontend -------------------------------
    const chatContainer = document.getElementById("chat-container");

    template = ` <button class='chat-btn'><img src = "./icons/comment.png" class = "material-icon" ></button>

    <div class='chat-popup'>
    
		<div class='chat-header'>
			<div class='chatbot-img'>
				<img src='${botLogoPath}' alt='Chat Bot image' class='bot-img'> 
			</div>
			<h3 class='bot-title'>Covid Bot</h3>
			<button class = "expand-chat-window" ><img src="./icons/open_fullscreen.png" class="material-icon" ></button>
		</div>

		<div class='chat-area'>
            <div class='bot-msg'>
                <img class='bot-img' src ='${botLogoPath}' />
				<span class='msg'>Hi, How can i help you?</span>
			</div>

			
			

		</div>


		<div class='chat-input-area'>
			<input type='text' autofocus class='chat-input' onkeypress='return givenUserInput(event)' placeholder='Type a message ...' autocomplete='off'>
			<button class='chat-submit'><i class='material-icons'>send</i></button>
		</div>

	</div>`

    chatContainer.innerHTML = template;

    // --------------------------- Important Variables----------------------------
    const inactiveMessage = "Server is down, Please contact the developer to activate it"


    chatPopup = document.querySelector(".chat-popup")
    chatBtn = document.querySelector(".chat-btn")
    chatSubmit = document.querySelector(".chat-submit")
    chatHeader = document.querySelector(".chat-header")
    chatArea = document.querySelector(".chat-area")
    chatInput = document.querySelector(".chat-input")
    expandWindow = document.querySelector(".expand-chat-window")
    root = document.documentElement;
    chatPopup.style.display = "none"
    const host = ""


    // ------------------------ ChatBot Toggler -------------------------

    chatBtn.addEventListener("click", () => {

        mobileDevice = !detectMob()
        if (chatPopup.style.display == "none" && mobileDevice) {
            chatPopup.style.display = "flex"
            chatInput.focus();
            chatBtn.innerHTML = `<img src = "./icons/close.png" class = "material-icon" >`
        } else if (mobileDevice) {
            chatPopup.style.display = "none"
            chatBtn.innerHTML = `<img src = "./icons/comment.png" class = "material-icon" >`
        } else {
            mobileView()
        }
    })

    chatSubmit.addEventListener("click", () => {
        const userResponse = chatInput.value.trim();
        if (userResponse !== "") {
            setUserResponse();
            send(userResponse)
        }
    })

    expandWindow.addEventListener("click", (e) => {
        // console.log(expandWindow.innerHTML)
        if (expandWindow.innerHTML == '<img src="./icons/open_fullscreen.png" class="material-icon">') {
            expandWindow.innerHTML = `<img src = "./icons/close_fullscreen.png" class = 'material-icon'>`
            root.style.setProperty('--chat-window-height', `${80  }%`);
            root.style.setProperty('--chat-window-total-width', `${85  }%`);
            chatHeader.style.width = "100%";
        } else if (expandWindow.innerHTML == '<img src="./icons/close.png" class="material-icon">') {
            chatPopup.style.display = "none"
            chatBtn.style.display = "block"
        } else {
            expandWindow.innerHTML = `<img src = "./icons/open_fullscreen.png" class = "material-icon" >`
            root.style.setProperty('--chat-window-height', `${500  }px`);
            root.style.setProperty('--chat-window-total-width', `${380  }px`);
        }

    })


}


// to submit user input when he presses enter
function givenUserInput(e) {
    if (e.keyCode == 13) {
        const userResponse = chatInput.value.trim();
        if (userResponse !== "") {
            setUserResponse()
            send(userResponse)
        }
    }
}

// to display user message on UI
function setUserResponse() {
    const userInput = chatInput.value;
    const temp = `<div class="user-msg"><span class = "msg">${userInput}</span></div>`
    chatArea.innerHTML += temp;
    chatInput.value = ""
    scrollToBottomOfResults();
}



function scrollToBottomOfResults() {
    chatArea.scrollTop = chatArea.scrollHeight;
}

/** *************************************************************
Frontend Part Completed
*************************************************************** */

// host = 'http://localhost:5005/webhooks/rest/webhook'
function send(message) {
    chatInput.focus();
    console.log("User Message:", message)
    $.ajax({
        url: host,
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            "message": message,
            "sender": "User"
        }),
        success(data, textStatus) {
            if (data != null) {
                setBotResponse(data);
            }
            console.log("Rasa Response: ", data, "\n Status:", textStatus)
        },
        error(errorMessage) {
            setBotResponse("");
            console.log(`Error${  errorMessage}`);

        }
    });
    chatInput.focus();
}


// ------------------------------------ Set bot response -------------------------------------
function setBotResponse(val) {
    setTimeout(() => {
        if (val.length < 1) {
            // if there is no response from Rasa
            // msg = 'I couldn\'t get that. Let\' try something else!';
            msg = inactiveMessage;

            var BotResponse = `<div class='bot-msg'><img class='bot-img' src ='${botLogoPath}' /><span class='msg'> ${msg} </span></div>`;
            $(BotResponse).appendTo('.chat-area').hide().fadeIn(1000);
            scrollToBottomOfResults();
            chatInput.focus();

        } else {
            // if we get response from Rasa
            for (i = 0; i < val.length; i++) {
                // check if there is text message
                if (val[i].hasOwnProperty("text")) {
                    var BotResponse = `<div class='bot-msg'><img class='bot-img' src ='${botLogoPath}' /><span class='msg'>${val[i].text}</span></div>`;
                    $(BotResponse).appendTo('.chat-area').hide().fadeIn(1000);
                }

                // check if there is image
                if (val[i].hasOwnProperty("image")) {
                    var BotResponse = `<div class='bot-msg'>${  "<img class='bot-img' src ='${botLogoPath}' />"
                    `<img class="msg-image" src="${  val[i].image  }">` 
                        }</div>`
                    $(BotResponse).appendTo('.chat-area').hide().fadeIn(1000);
                }

            }
            scrollToBottomOfResults();
            chatInput.focus();
        }

    }, 500);
}




function mobileView() {
    $('.chat-popup').width($(window).width());

    if (chatPopup.style.display == "none") {
        chatPopup.style.display = "flex"
            // chatInput.focus();
        chatBtn.style.display = "none"
        chatPopup.style.bottom = "0"
        chatPopup.style.right = "0"
            // chatPopup.style.transition = "none"
        expandWindow.innerHTML = `<img src = "./icons/close.png" class = "material-icon" >`
    }
}

function detectMob() {
    return ((window.innerHeight <= 800) && (window.innerWidth <= 600));
}

function chatbotTheme(theme) {
    const gradientHeader = document.querySelector(".chat-header");
    const orange = {
        color: "#FBAB7E",
        background: "linear-gradient(19deg, #FBAB7E 0%, #F7CE68 100%)"
    }

    const purple = {
        color: "#B721FF",
        background: "linear-gradient(19deg, #21D4FD 0%, #B721FF 100%)"
    }



    if (theme === "orange") {
        root.style.setProperty('--chat-window-color-theme', orange.color);
        gradientHeader.style.backgroundImage = orange.background;
        chatSubmit.style.backgroundColor = orange.color;
    } else if (theme === "purple") {
        root.style.setProperty('--chat-window-color-theme', purple.color);
        gradientHeader.style.backgroundImage = purple.background;
        chatSubmit.style.backgroundColor = purple.color;
    }
}

function createChatBot(hostURL, botLogo, title, welcomeMessage, inactiveMsg, theme = "blue") {

    host = hostURL;
    botLogoPath = botLogo;
    inactiveMessage = inactiveMsg;
    init(botLogoPath)
    const msg = document.querySelector(".msg");
    msg.innerText = welcomeMessage;

    const botTitle = document.querySelector(".bot-title");
    botTitle.innerText = title;

    chatbotTheme(theme)
}

console.log("Working!")

createChatBot(host = 'http://localhost:5005/webhooks/rest/webhook',
    botLogo = "img/bot-logo.png",
    title = "UI BOT", welcomeMessage = "Hey, i am UI Bot, I am here to give you an example UI",
    inactiveMsg = "This is inactive message, show casing that the server is down"
)