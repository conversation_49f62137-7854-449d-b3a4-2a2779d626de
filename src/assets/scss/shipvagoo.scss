.body{
  font-family: Helvetica, sans-serif;
}



.create-template-modal-stepper-v2 {
  &.q-stepper--horizontal {
    align-items: center;
    display: flex !important;
    flex-direction: column !important;

    .q-stepper__header {
      min-width: 250px !important;
      justify-items: center !important;
      justify-self: center !important;
    }

    .q-stepper__content {
      width: 100% !important;
    }
  }

  .two-columns-responsive {
    display: flex;
    flex-direction: row;
    @media (max-width: 600px) {
      flex-direction: column;
    }
  }
}

@mixin margin($side, $value) {
  margin-#{$side}: $value !important;
}

@mixin generate-margin-classes($sides, $values) {
  @each $side in $sides {
    @each $value in $values {
      .margin-#{$side}-#{$value} {
        @include margin($side, $value);
      }
    }
  }
}

$sides: left, right, top, bottom;
$values: 5px, 10px, 20px, 30px, 40px;

@include generate-margin-classes($sides, $values);



.dataList{
  position: relative;
  display: flex;
  flex-wrap: wrap;
  margin: 0 0 15px;
  
  &.text-left{
    dd{
      text-align: left;
    }
  }
  
  dt, dd{
    margin: 0;
    padding: 10px 20px;
  }
  
  dt{
    font-weight: 700;
    flex: 0 0 40%;
    padding-left: 0;
  }
  
  dd{
    text-align: right;
    flex: 0 0 60%;
  }
}