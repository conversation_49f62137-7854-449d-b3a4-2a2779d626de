{"AF": {"format": "NNNN", "regex": "^\\d{4}$"}, "AX": {"format": "NNNNN", "regex": "^\\d{5}$"}, "AL": {"format": "NNNN", "regex": "^\\d{4}$"}, "DZ": {"format": "NNNNN", "regex": "^\\d{5}$"}, "AS": {"format": "NNNNN (optionally NNNNN-NNNN or NNNNN-NNNNNN)", "regex": "^\\d{5}(-{1}\\d{4,6})$"}, "AD": {"format": "CCNNN", "regex": "^[Aa][Dd]\\d{3}$"}, "AO": {"format": null, "regex": ""}, "AI": {"format": "AI-2640", "regex": "^[Aa][I][-][2][6][4][0]$"}, "AG": {"format": null, "regex": ""}, "AR": {"format": "1974-1998 NNNN; From 1999 ANNNNAAA", "regex": "^\\d{4}|[A-Za-z]\\d{4}[a-zA-Z]{3}$"}, "AM": {"format": "NNNN", "regex": "^\\d{4}$"}, "AW": {"format": null, "regex": ""}, "AC": {"format": "AAAANAA one code: ASCN 1ZZ", "regex": "^[Aa][Ss][Cc][Nn]\\s{0,1}[1][Zz][Zz]$"}, "AU": {"format": "NNNN", "regex": "^\\d{4}$"}, "AT": {"format": "NNNN", "regex": "^\\d{4}$"}, "AZ": {"format": "CCNNNN", "regex": "^[Aa][Zz]\\d{4}$"}, "BS": {"format": null, "regex": ""}, "BH": {"format": "NNN or NNNN", "regex": "^\\d{3,4}$"}, "BD": {"format": "NNNN", "regex": "^\\d{4}$"}, "BB": {"format": "CCNNNNN", "regex": "^[Aa][Zz]\\d{5}$"}, "BY": {"format": "NNNNNN", "regex": "^\\d{6}$"}, "BE": {"format": "NNNN", "regex": "^\\d{4}$"}, "BZ": {"format": null, "regex": ""}, "BJ": {"format": null, "regex": ""}, "BM": {"format": "AA NN or AA AA", "regex": "^[A-Za-z]{2}\\s([A-Za-z]{2}|\\d{2})$"}, "BT": {"format": "NNNNN", "regex": "^\\d{5}$"}, "BO": {"format": "NNNN", "regex": "^\\d{4}$"}, "BQ": {"format": null, "regex": ""}, "BA": {"format": "NNNNN", "regex": "^\\d{5}$"}, "BW": {"format": null, "regex": ""}, "BR": {"format": "NNNNN-NNN (NNNNN from 1971 to 1992)", "regex": "^\\d{5}-\\d{3}$"}, "": {"format": "BIQQ 1ZZ", "regex": "^[Bb][Ii][Qq]{2}\\s{0,1}[1][Zz]{2}$"}, "IO": {"format": "AAAANAA one code: BBND 1ZZ", "regex": "^[Bb]{2}[Nn][Dd]\\s{0,1}[1][Zz]{2}$"}, "VG": {"format": "CCNNNN", "regex": "^[Vv][Gg]\\d{4}$"}, "BN": {"format": "AANNNN", "regex": "^[A-Za-z]{2}\\d{4}$"}, "BG": {"format": "NNNN", "regex": "^\\d{4}$"}, "BF": {"format": null, "regex": ""}, "BI": {"format": null, "regex": ""}, "KH": {"format": "NNNNN", "regex": "^\\d{5}$"}, "CM": {"format": null, "regex": ""}, "CA": {"format": "ANA NAN", "regex": "^(?=[^DdFfIiOoQqUu\\d\\s])[A-Za-z]\\d(?=[^DdFfIiOoQqUu\\d\\s])[A-Za-z]\\s{0,1}\\d(?=[^DdFfIiOoQqUu\\d\\s])[A-Za-z]\\d$"}, "CV": {"format": "NNNN", "regex": "^\\d{4}$"}, "KY": {"format": "CCN-NNNN", "regex": "^[Kk][Yy]\\d[-\\s]{0,1}\\d{4}$"}, "CF": {"format": null, "regex": ""}, "TD": {"format": "NNNNN", "regex": "^\\d{5}$"}, "CL": {"format": "NNNNNNN (NNN-NNNN)", "regex": "^\\d{7}\\s\\(\\d{3}-\\d{4}\\)$"}, "CN": {"format": "NNNNNN", "regex": "^\\d{6}$"}, "CX": {"format": "NNNN", "regex": "^\\d{4}$"}, "CC": {"format": "NNNN", "regex": "^\\d{4}$"}, "CO": {"format": "NNNNNN", "regex": "^\\d{6}$"}, "KM": {"format": null, "regex": ""}, "CG": {"format": null, "regex": ""}, "CD": {"format": null, "regex": "^[Cc][Dd]$"}, "CK": {"format": null, "regex": ""}, "CR": {"format": "NNNNN (NNNN until 2007)", "regex": "^\\d{4,5}$"}, "CI": {"format": null, "regex": ""}, "HR": {"format": "NNNNN", "regex": "^\\d{5}$"}, "CU": {"format": "NNNNN", "regex": "^\\d{5}$"}, "CW": {"format": null, "regex": ""}, "CY": {"format": "NNNN", "regex": "^\\d{4}$"}, "CZ": {"format": "NNNNN (NNN NN)", "regex": "^\\d{5}\\s\\(\\d{3}\\s\\d{2}\\)$"}, "DK": {"format": "NNNN", "regex": "^\\d{4}$"}, "DJ": {"format": null, "regex": ""}, "DM": {"format": null, "regex": ""}, "DO": {"format": "NNNNN", "regex": "^\\d{5}$"}, "TL": {"format": null, "regex": ""}, "EC": {"format": "NNNNNN", "regex": "^\\d{6}$"}, "SV": {"format": "1101", "regex": "^1101$"}, "EG": {"format": "NNNNN", "regex": "^\\d{5}$"}, "GQ": {"format": null, "regex": ""}, "ER": {"format": null, "regex": ""}, "EE": {"format": "NNNNN", "regex": "^\\d{5}$"}, "ET": {"format": "NNNN", "regex": "^\\d{4}$"}, "FK": {"format": "AAAANAA one code: FIQQ 1ZZ", "regex": "^[Ff][Ii][Qq]{2}\\s{0,1}[1][Zz]{2}$"}, "FO": {"format": "NNN", "regex": "^\\d{3}$"}, "FJ": {"format": null, "regex": ""}, "FI": {"format": "NNNNN", "regex": "^\\d{5}$"}, "FR": {"format": "NNNNN", "regex": "^\\d{5}$"}, "GF": {"format": "973NN", "regex": "^973\\d{2}$"}, "PF": {"format": "987NN", "regex": "^987\\d{2}$"}, "TF": {"format": null, "regex": ""}, "GA": {"format": "NN [city name] NN", "regex": "^\\d{2}\\s[a-zA-Z-_ ]\\s\\d{2}$"}, "GM": {"format": null, "regex": ""}, "GE": {"format": "NNNN", "regex": "^\\d{4}$"}, "DE": {"format": "NNNNN", "regex": "^\\d{5}$"}, "GH": {"format": null, "regex": ""}, "GI": {"format": "GX11 1AA", "regex": "^[Gg][Xx][1]{2}\\s{0,1}[1][Aa]{2}$"}, "GR": {"format": "NNN NN", "regex": "^\\d{3}\\s{0,1}\\d{2}$"}, "GL": {"format": "NNNN", "regex": "^\\d{4}$"}, "GD": {"format": null, "regex": ""}, "GP": {"format": "971NN", "regex": "^971\\d{2}$"}, "GU": {"format": "NNNNN", "regex": "^\\d{5}$"}, "GT": {"format": "NNNNN", "regex": "^\\d{5}$"}, "GG": {"format": "AAN NAA, AANN NAA", "regex": "^[A-Za-z]{2}\\d\\s{0,1}\\d[A-Za-z]{2}$"}, "GN": {"format": null, "regex": ""}, "GW": {"format": "NNNN", "regex": "^\\d{4}$"}, "GY": {"format": null, "regex": ""}, "HT": {"format": "NNNN", "regex": "^\\d{4}$"}, "HM": {"format": "NNNN", "regex": "^\\d{4}$"}, "HN": {"format": "NNNNN", "regex": "^\\d{5}$"}, "HK": {"format": null, "regex": ""}, "HU": {"format": "NNNN", "regex": "^\\d{4}$"}, "IS": {"format": "NNN", "regex": "^\\d{3}$"}, "IN": {"format": "NNNNNN,&#10;NNN NNN", "regex": "^\\d{6}$"}, "ID": {"format": "NNNNN", "regex": "^\\d{5}$"}, "IR": {"format": "NNNNN-NNNNN", "regex": "^\\d{5}-\\d{5}$"}, "IQ": {"format": "NNNNN", "regex": "^\\d{5}$"}, "IE": {"format": null, "regex": ""}, "IM": {"format": "CCN NAA, CCNN NAA", "regex": "^[Ii[Mm]\\d{1,2}\\s\\d\\[A-Z]{2}$"}, "IL": {"format": "NNNNNNN", "regex": "^\\d{7}$"}, "IT": {"format": "NNNNN", "regex": "^\\d{5}$"}, "JM": {"format": "Before suspension: CCAAANN &#10;After suspension: NN", "regex": "^\\d{2}$"}, "JP": {"format": "NNNNNNN (NNN-NNNN)", "regex": "^\\d{7}\\s\\(\\d{3}-\\d{4}\\)$"}, "JE": {"format": "CCN NAA", "regex": "^[Jj][Ee]\\d\\s{0,1}\\d[A-Za-z]{2}$"}, "JO": {"format": "NNNNN", "regex": "^\\d{5}$"}, "KZ": {"format": "NNNNNN", "regex": "^\\d{6}$"}, "KE": {"format": "NNNNN", "regex": "^\\d{5}$"}, "KI": {"format": null, "regex": ""}, "KP": {"format": null, "regex": ""}, "KR": {"format": "NNNNNN (NNN-NNN)", "regex": "^\\d{6}\\s\\(\\d{3}-\\d{3}\\)$"}, "XK": {"format": "NNNNN", "regex": "^\\d{5}$"}, "KW": {"format": "NNNNN", "regex": "^\\d{5}$"}, "KG": {"format": "NNNNNN", "regex": "^\\d{6}$"}, "LV": {"format": "LV-NNNN", "regex": "^[Ll][Vv][- ]{0,1}\\d{4}$"}, "LA": {"format": "NNNNN", "regex": "^\\d{5}$"}, "LB": {"format": "NNNN NNNN", "regex": "^\\d{4}\\s{0,1}\\d{4}$"}, "LS": {"format": "NNN", "regex": "^\\d{3}$"}, "LR": {"format": "NNNN", "regex": "^\\d{4}$"}, "LY": {"format": "NNNNN", "regex": "^\\d{5}$"}, "LI": {"format": "NNNN", "regex": "^\\d{4}$"}, "LT": {"format": "LT-NNNNN", "regex": "^[Ll][Tt][- ]{0,1}\\d{5}$"}, "LU": {"format": "NNNN", "regex": "^\\d{4}$"}, "MO": {"format": null, "regex": ""}, "MK": {"format": "NNNN", "regex": "^\\d{4}$"}, "MG": {"format": "NNN", "regex": "^\\d{3}$"}, "MW": {"format": null, "regex": ""}, "MV": {"format": "NNNN, NNNNN", "regex": "^\\d{4,5}$"}, "MY": {"format": "NNNNN", "regex": "^\\d{5}$"}, "ML": {"format": null, "regex": ""}, "MT": {"format": "AAANNNN (AAA NNNN)", "regex": "^[A-Za-z]{3}\\s{0,1}\\d{4}$"}, "MH": {"format": "NNNNN", "regex": "^\\d{5}$"}, "MR": {"format": null, "regex": ""}, "MU": {"format": null, "regex": ""}, "MQ": {"format": "972NN", "regex": "^972\\d{2}$"}, "YT": {"format": "976NN", "regex": "^976\\d{2}$"}, "FM": {"format": "NNNNN", "regex": "^\\d{5}$"}, "MX": {"format": "NNNNN", "regex": "^\\d{5}$"}, "MD": {"format": "CCNNNN (CC-NNNN)", "regex": "^[Mm][Dd][- ]{0,1}\\d{4}$"}, "MC": {"format": "980NN", "regex": "^980\\d{2}$"}, "MN": {"format": "NNNNN", "regex": "^\\d{5}$"}, "ME": {"format": "NNNNN", "regex": "^\\d{5}$"}, "MS": {"format": "MSR 1110-1350", "regex": "^[Mm][Ss][Rr]\\s{0,1}\\d{4}$"}, "MA": {"format": "NNNNN", "regex": "^\\d{5}$"}, "MZ": {"format": "NNNN", "regex": "^\\d{4}$"}, "MM": {"format": "NNNNN", "regex": "^\\d{5}$"}, "NA": {"format": "NNNNN", "regex": "^\\d{5}$"}, "NR": {"format": null, "regex": ""}, "NP": {"format": "NNNNN", "regex": "^\\d{5}$"}, "NL": {"format": "NNNN AA", "regex": "^\\d{4}\\s{0,1}[A-Za-z]{2}$"}, "NC": {"format": "988NN", "regex": "^988\\d{2}$"}, "NZ": {"format": "NNNN", "regex": "^\\d{4}$"}, "NI": {"format": "NNNNN", "regex": "^\\d{5}$"}, "NE": {"format": "NNNN", "regex": "^\\d{4}$"}, "NG": {"format": "NNNNNN", "regex": "^\\d{6}$"}, "NU": {"format": null, "regex": ""}, "NF": {"format": "NNNN", "regex": "^\\d{4}$"}, "MP": {"format": "NNNNN", "regex": "^\\d{5}$"}, "NO": {"format": "NNNN", "regex": "^\\d{4}$"}, "OM": {"format": "NNN", "regex": "^\\d{3}$"}, "PK": {"format": "NNNNN", "regex": "^\\d{5}$"}, "PW": {"format": "NNNNN", "regex": "^\\d{5}$"}, "PA": {"format": "NNNNNN", "regex": "^\\d{6}$"}, "PG": {"format": "NNN", "regex": "^\\d{3}$"}, "PY": {"format": "NNNN", "regex": "^\\d{4}$"}, "PE": {"format": "NNNNN", "regex": "^\\d{5}$"}, "PH": {"format": "NNNN", "regex": "^\\d{4}$"}, "PN": {"format": "AAAANAA one code: PCRN 1ZZ", "regex": "^[Pp][Cc][Rr][Nn]\\s{0,1}[1][Zz]{2}$"}, "PL": {"format": "NNNNN (NN-NNN)", "regex": "^\\d{2}[- ]{0,1}\\d{3}$"}, "PT": {"format": "NNNN-NNN (NNNN NNN)", "regex": "^\\d{4}[- ]{0,1}\\d{3}$"}, "PR": {"format": "NNNNN", "regex": "^\\d{5}$"}, "QA": {"format": null, "regex": ""}, "RE": {"format": "974NN", "regex": "^974\\d{2}$"}, "RO": {"format": "NNNNNN", "regex": "^\\d{6}$"}, "RU": {"format": "NNNNNN", "regex": "^\\d{6}$"}, "BL": {"format": "97133", "regex": "^97133$"}, "SH": {"format": "TDCU 1ZZ", "regex": "^[Tt][Dd][Cc][Uu]\\s{0,1}[1][Zz]{2}$"}, "KN": {"format": null, "regex": ""}, "LC": {"format": null, "regex": ""}, "MF": {"format": "97150", "regex": "^97150$"}, "PM": {"format": "97500", "regex": "^97500$"}, "VC": {"format": "CCNNNN", "regex": "^[Vv][Cc]\\d{4}$"}, "SM": {"format": "4789N", "regex": "^4789\\d$"}, "ST": {"format": null, "regex": ""}, "SA": {"format": "NNNNN for PO Boxes. NNNNN-NNNN for home delivery.", "regex": "^\\d{5}(-{1}\\d{4})?$"}, "SN": {"format": "NNNNN", "regex": "^\\d{5}$"}, "RS": {"format": "NNNNN", "regex": "^\\d{5}$"}, "SC": {"format": null, "regex": ""}, "SX": {"format": null, "regex": ""}, "SL": {"format": null, "regex": ""}, "SG": {"format": "NNNNNN", "regex": "^\\d{6}$"}, "SK": {"format": "NNNNN (NNN NN)", "regex": "^\\d{5}\\s\\(\\d{3}\\s\\d{2}\\)$"}, "SI": {"format": "NNNN (CC-NNNN)", "regex": "^([Ss][Ii][- ]{0,1}){0,1}\\d{4}$"}, "SB": {"format": null, "regex": ""}, "SO": {"format": null, "regex": ""}, "ZA": {"format": "NNNN", "regex": "^\\d{4}$"}, "GS": {"format": "SIQQ 1ZZ", "regex": "^[Ss][Ii][Qq]{2}\\s{0,1}[1][Zz]{2}$"}, "ES": {"format": "NNNNN", "regex": "^\\d{5}$"}, "LK": {"format": "NNNNN", "regex": "^\\d{5}$"}, "SD": {"format": "NNNNN", "regex": "^\\d{5}$"}, "SR": {"format": null, "regex": ""}, "SZ": {"format": "ANNN", "regex": "^[A-Za-z]\\d{3}$"}, "SE": {"format": "NNNNN (NNN NN)", "regex": "^\\d{3}\\s*\\d{2}$"}, "CH": {"format": "NNNN", "regex": "^\\d{4}$"}, "SJ": {"format": "NNNN", "regex": "^\\d{4}$"}, "SY": {"format": null, "regex": ""}, "TW": {"format": "NNNNN", "regex": "^\\d{5}$"}, "TJ": {"format": "NNNNNN", "regex": "^\\d{6}$"}, "TZ": {"format": null, "regex": ""}, "TH": {"format": "NNNNN", "regex": "^\\d{5}$"}, "TG": {"format": null, "regex": ""}, "TK": {"format": null, "regex": ""}, "TO": {"format": null, "regex": ""}, "TT": {"format": "NNNNNN", "regex": "^\\d{6}$"}, "TN": {"format": "NNNN", "regex": "^\\d{4}$"}, "TR": {"format": "NNNNN", "regex": "^\\d{5}$"}, "TM": {"format": "NNNNNN", "regex": "^\\d{6}$"}, "TC": {"format": "TKCA 1ZZ", "regex": "^[Tt][Kk][Cc][Aa]\\s{0,1}[1][Zz]{2}$"}, "TV": {"format": null, "regex": ""}, "UG": {"format": null, "regex": ""}, "UA": {"format": "NNNNN", "regex": "^\\d{5}$"}, "AE": {"format": null, "regex": ""}, "GB": {"format": "A(A)N(A/N)NAA (A[A]N[A/N] NAA)", "regex": "^[A-Za-z]{1,2}\\d([A-Za-z]|\\d)\\d[A-Za-z]{2}$"}, "US": {"format": "NNNNN (optionally NNNNN-NNNN)", "regex": "^\\b\\d{5}\\b(?:[- ]{1}\\d{4})?$"}, "UY": {"format": "NNNNN", "regex": "^\\d{5}$"}, "VI": {"format": "NNNNN", "regex": "^\\d{5}$"}, "UZ": {"format": "NNN NNN", "regex": "^\\d{3} \\d{3}$"}, "VU": {"format": null, "regex": ""}, "VA": {"format": "120", "regex": "^120$"}, "VE": {"format": "NNNN or NNNN A", "regex": "^\\d{4}(\\s[a-zA-Z]{1})?$"}, "VN": {"format": "NNNNNN", "regex": "^\\d{6}$"}, "WF": {"format": "986NN", "regex": "^986\\d{2}$"}, "YE": {"format": null, "regex": ""}, "ZM": {"format": "NNNNN", "regex": "^\\d{5}$"}, "ZW": {"format": null, "regex": ""}}