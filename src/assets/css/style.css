.title-bar-heading {
  padding: 5px 40px;
}

.text-brand {
  color: #243E90 !important;
}

.bg-brand {
  background: #243E90 !important;
}

.text-green-brand {
  color: #18AF94 !important;
}

.bg-green-brand {
  background: #18AF94 !important;
}

.text-dark-brand {
  color: #212121 !important;
}

.bg-dark-brand {
  background: #212121 !important;
}

.text-gray-brand {
  color: #313131 !important;
}

.bg-gray-brand {
  background: #212121 !important;
}

.text-danger {
  color: #BE4748 !important;
}

.bg-danger {
  background: #BE4748 !important;
}

.bg-modal {
  background: #F5F5F5 !important;
}

.text-dark-gray {
  color: #313131 !important;;
}

.bg-dark-gray {
  background: #313131 !important;;
}

.bg-light-gray {
  background: #7D8398 !important;;
}

.text-light-gray {
  color: #7D8398 !important;;
}

.shpvagoo-carrier-logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  width: 100%;
  height: 100%;
}

.shipvagoo-table-carrier-logo {
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  width: 24px;
  height: 24px;
}

/*body {
  overflow-y: scroll;
  background-color: white; !* Set the desired background color *!
}*/

::-webkit-scrollbar {
  width: 8px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #a5a5a5;
  border-radius: 4px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #898989;
}

.q-table--col-auto-width {
  padding: 0 !important;
}

.dark-text {
  color: #212121 !important;
  font-family: Helvetica, sans-serif;
  font-style: normal;
  line-height: normal;
}

.dark-subtitle-text {
  color: #7D8398 !important;
  font-family: Helvetica, sans-serif;
  font-style: normal;
  line-height: normal;
}

.blue-text {
  color: #243E90 !important;
  font-family: Helvetica, sans-serif;
  font-style: normal;
  line-height: normal;
}

.helvetica-text {
  font-family: Helvetica, sans-serif;
  font-style: normal;
  line-height: normal;
}

.text-ten {
  font-size: 10px;
}

.text-eleven {
  font-size: 11px;
}

.text-twelve {
  font-size: 12px;
}

.text-thirteen {
  font-size: 13px;
}

.text-fourteen {
  font-size: 14px;
}

.text-fifteen {
  font-size: 15px;
}

.text-sixteen {
  font-size: 16px;
}

.text-eighteen {
  font-size: 18px;
}

.text-twenty {
  font-size: 20px;
}

.text-weight-7 {
  font-weight: 700;
}

.text-weight-4 {
  font-weight: 400;
}

.text-twenty-four {
  font-size: 24px;
}

.big_search {
  width: 300px;
}

@media (max-width: 600px) {
  flex-direction: column;

  .big_search {
    margin-bottom: 10px;
  }
}

.flex-1 {
  flex: 1
}

.gap-5 {
  gap: 5px;
}

.gap-10 {
  gap: 10px;
}

.gap-15 {
  gap: 15px;
}

.gap-20 {
  gap: 20px;
}

.two-columns-responsive {
  display: flex;
  flex-direction: row;
  @media (max-width: 600px) {
    flex-direction: column;
  }
}

.orders-country_flag {
  width: 20px;
  height: 20px;
  object-fit: cover;
  border-radius: 50%;
}

.order-detail-box-card {
  border: 1px solid #7D839833;
  width: 100%;
  border-radius: 5px;
  background-color: white;
}

.order-detail-product-img {
  max-height: 80px;
  width: 80px;
  object-fit: contain;
}

.order-detail-product-modal-img {
  max-height: 60px;
  width: 60px;
  object-fit: contain;
}

.order-detail-product-border {
  border-radius: 4px;
  border: 1px solid rgba(125, 131, 152, 0.20);
  width: 65px;
  height: 65px;
}

.order_detail-order_item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px;
}

.margin-top-minus-two {
  margin-top: -2px !important;
}

.margin-top-twenty {
  margin-top: 20px !important;
}