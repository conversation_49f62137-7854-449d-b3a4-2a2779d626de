<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with <PERSON><PERSON><PERSON><PERSON> ("http://www.sodipodi.com/") -->
<svg id="svg378" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="480" width="640" version="1" y="0" x="0" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata3065">
  <rdf:RDF>
   <cc:Work rdf:about="">
    <dc:format>image/svg+xml</dc:format>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs id="defs380">
  <clipPath id="clipPath3058" clipPathUnits="userSpaceOnUse">
   <rect id="rect3060" fill-opacity="0.67" height="512" width="682.67" y="-.0000018715" x="-74"/>
  </clipPath>
 </defs>
 <g id="flag" fill-rule="evenodd" clip-path="url(#clipPath3058)" transform="matrix(.93750 0 0 .93750 69.375 .0000017546)" stroke-width="1pt">
  <rect id="rect149" height="512" width="768" y=".000016585" x="-74" fill="#e80000"/>
  <rect id="rect280" height="256" width="768" y=".000016585" x="-74" fill="#fff"/>
  <path id="path279" d="m-74-7.3297e-7 382.73 255.67-382.73 255.34v-511.01z" fill="#00006f"/>
 </g>
</svg>
