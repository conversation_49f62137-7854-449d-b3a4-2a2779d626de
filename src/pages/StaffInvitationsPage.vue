<template>
  <Title :title="$t('common.staff_invitations')" />
  <div class="staff_invitations_page">
    <div class="staff_invitations_page-actions">
      <div class="big_search-rows_per_page-group">
        <big-search
          v-model="search"
          class="staff_invitations_page-actions-big_search"
          @update:model-value="getInvitations(null)"
        />
      </div>

      <!-- <shipvagoo-button outline label="Download bulk" /> -->
    </div>

    <shipvagoo-table
      v-model:pagination="pagination"
      :columns="columns"
      :rows="rows"
      :total="pagination.rowsNumber"
      @row-click="onClickRow"
      @request="getInvitations($event.pagination)"
    >
      <template #body-cell-more="props">
        <q-td :props="props" class="more">
          <q-btn dense flat round   style="color:#1f2124;" class="more-action" field="edit" icon="more_horiz">
            <shipvagoo-menu>
              <shipvagoo-menu-item clickable @click="onClickDeleteInvitation(props.row)">
                {{ $t('common.delete') }}
              </shipvagoo-menu-item>
            </shipvagoo-menu>
          </q-btn>
        </q-td>
      </template>
    </shipvagoo-table>
  </div>

  <shipvagoo-fab icon="add" @click="onClickInviteStaff" />

  <q-dialog v-model="isInvitationDetailModalOpen" @before-hide="onCloseInvitationDetailModal">
    <shipvagoo-modal :title="staffDetailTitle" @close="onCloseInvitationDetailModal">
      <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
        <div class="input-double input-double-responsive">
          <shipvagoo-input
            v-model="invitationDetailForm.name"
            :label="$t('common.name')"
            :error="$v.name.$error"
            :error-message="$v.name.$errors[0]?.$message.toString()"
          />
          <shipvagoo-input
            v-model="invitationDetailForm.email"
            :label="$t('common.email')"
            :error="$v.email.$error"
            :error-message="$v.email.$errors[0]?.$message.toString()"
          />
        </div>
      </shipvagoo-card-section>
      <shipvagoo-separator />
      <shipvagoo-card-section>
        <shipvagoo-checkbox
          v-model="invitationDetailForm.staffMemberHasAccessToEverything"
          size="sm"
          style="font-size: 12px"
          @update:model-value="onUpdateStaffMemberHasAccessToEverything"
        >
          {{ $t('staff_accounts.staff_member_has_access_to_everything') }}
        </shipvagoo-checkbox>
      </shipvagoo-card-section>
      <shipvagoo-separator />
      <shipvagoo-card-section style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px">
        <shipvagoo-checkbox
          v-for="_module in modules"
          :key="_module.module_code"
          v-model="_module.isChecked"
          size="sm"
          :disabled="_module.module_key === EModukeKey.DASHBOARD"
          :label="_module.name"
          @update:model-value="onUpdateModuleCheckbox"
        />
      </shipvagoo-card-section>
      <shipvagoo-separator />
      <shipvagoo-card-actions>
        <shipvagoo-button
          v-if="currentInvitationThatIsBeingEdited"
          min-width="120px"
          :disable="
            isEmpty(invitationDetailForm.email) ||
            isEmpty(invitationDetailForm.name) ||
            modules.every((m) => !m.isChecked)
          "
          @click="onClickUpdateInvitation"
        >
          {{ $t('common.save') }}
        </shipvagoo-button>
        <shipvagoo-button
          v-if="!currentInvitationThatIsBeingEdited"
          min-width="120px"
          :disable="
            isEmpty(invitationDetailForm.email) ||
            isEmpty(invitationDetailForm.name) ||
            modules.every((m) => !m.isChecked)
          "
          @click="onClickCreateInvite"
        >
          {{ $t('common.create') }}
        </shipvagoo-button>
      </shipvagoo-card-actions>
    </shipvagoo-modal>
  </q-dialog>
</template>

<script setup lang="ts">
  import { computed, onBeforeMount, Ref, ref } from 'vue';
  import { QTableProps } from 'quasar';
  import { omit, isEmpty } from 'lodash-es';
  import useVuelidate, { ValidationArgs } from '@vuelidate/core';
  import { helpers, required, email as emailValidator } from '@vuelidate/validators';
  import { useI18n } from 'vue-i18n';
  import Title from '../components/title-bar.vue';
  import {
    companyService,
    loadingService,
    loggingService,
    moduleService,
    notificationService,
  } from '../services/_singletons';
  import { EModukeKey, IModule } from '../model/module.model';
  import { ICheckable, Pagination } from '../model/common.model';
  import { IStaffInvitation } from '../model/company.model';
  import HelperService from '../services/helper.service';
  import { IRequestParam } from '../model/http.model';

  const isInvitationDetailModalOpen = ref<boolean>();
  const currentInvitationThatIsBeingEdited: Ref<IStaffInvitation | null> = ref(null);

  const { t } = useI18n();
  interface IStaffInvitationForm {
    name: string;
    email: string;
    staffMemberHasAccessToEverything: boolean;
  }

  const invitationDetailForm = ref<IStaffInvitationForm>({
    name: '',
    email: '',
    staffMemberHasAccessToEverything: false,
  });

  const $v = useVuelidate<IStaffInvitationForm, ValidationArgs<IStaffInvitationForm>>(
    {
      name: {
        required: helpers.withMessage('This field is required.', required),
      },
      email: {
        required: helpers.withMessage('This field is required.', required),
        email: helpers.withMessage('This email not using', emailValidator),
      },
      staffMemberHasAccessToEverything: {},
    },
    invitationDetailForm,
  );
  const modules: Ref<ICheckable<IModule>[]> = ref<ICheckable<IModule>[]>([]);

  const search: Ref<string> = ref<string>('');
  const columns = computed<QTableProps['columns']>(() => [
    { name: 'name', field: 'name', label: t('common.name'), align: 'left', sortable: true },
    { name: 'email', field: 'email', label: t('common.email'), align: 'left', sortable: true },
    { name: 'more', field: 'more', label: t('common.more'), align: 'right' },
  ]);
  const rows = ref<IStaffInvitation[]>([]);
  const pagination: Ref<Pagination> = ref({
    page: 1,
    rowsPerPage: 10,
    rowsNumber: 0,
    descending: true,
  });

  const staffDetailTitle = computed<string>(() => {
    if (currentInvitationThatIsBeingEdited.value) {
      return t('staff_invitations.edit_staff_invitation');
    }

    return t('staff_invitations.invite_staff');
  });

  const getInvitations = async (tablePagination: Pagination | null) => {
    try {
      loadingService.startLoading('main-loader:get-company-users');
      const page = tablePagination?.page ?? 1;
      const limit = tablePagination?.rowsPerPage ?? 10;

      const params: IRequestParam[] = [];

      if (search.value && search.value !== '') {
        params.push(
          { key: 'conditions[name:like]', value: search.value },
          { key: 'conditions[email:like]', value: search.value },
        );
      }

      if (tablePagination?.sortBy) {
        const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
        params.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
      } else {
        params.push({ key: 'sorting[created_at]', value: 'desc' });
      }

      const response = await companyService.getStaffInvitations(page, limit, params);
      rows.value = response.entities;
      pagination.value.rowsNumber = response.total;
      pagination.value.page = page;
      pagination.value.rowsPerPage = limit;
      pagination.value.descending = tablePagination?.descending;
      pagination.value.sortBy = tablePagination?.sortBy;
    } catch (e) {
      loggingService.error('Error getting company users', e);
      notificationService.showSuccess('Error getting company users');
    } finally {
      loadingService.stopLoading('main-loader:get-company-users');
    }
  };

  const onClickRow = (event: Event, row: IStaffInvitation) => {
    if (HelperService.containsIgnoredRowElements(event)) {
      return;
    }

    currentInvitationThatIsBeingEdited.value = row;

    invitationDetailForm.value = {
      name: row.name,
      email: row.email,
      staffMemberHasAccessToEverything: false,
    };

    modules.value = modules.value.map((_m) => {
      const m = { ..._m };
      m.isChecked = row.permitted_modules.includes(m.module_code);
      return m;
    });

    invitationDetailForm.value.staffMemberHasAccessToEverything = modules.value.every(
      (m) => m.isChecked,
    );

    isInvitationDetailModalOpen.value = true;
  };

  const onClickInviteStaff = () => {
    isInvitationDetailModalOpen.value = true;
  };

  const onClickCreateInvite = async () => {
    await $v.value.$validate();

    if ($v.value.$error) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:invite-staff-member');
      const { name, email } = invitationDetailForm.value;
      await companyService.inviteStaffMember(
        email,
        name,
        modules.value.map((m): IModule => omit(m, 'isChecked')),
      );

      isInvitationDetailModalOpen.value = false;
      notificationService.showSuccess('Staff member invited successfully');
      getInvitations(null);
    } catch (e) {
      loggingService.error('Error inviting staff member', e);
      notificationService.showError('Error inviting staff member');
    } finally {
      loadingService.stopLoading('main-loader:invite-staff-member');
    }
  };

  const onClickUpdateInvitation = async () => {
    await $v.value.$validate();

    if ($v.value.$error || !currentInvitationThatIsBeingEdited.value) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:update-invitation');
      const { name, email } = invitationDetailForm.value;
      await companyService.updateStaffInvitation(
        currentInvitationThatIsBeingEdited.value.invite_code,
        {
          name,
          email,
          permitted_modules: modules.value
            .filter((m) => m.isChecked)
            .map((m): IModule => omit(m, 'isChecked'))
            .map((m) => m.module_code),
        },
      );

      isInvitationDetailModalOpen.value = false;
      notificationService.showSuccess('Staff member invited successfully');
      getInvitations(null);
    } catch (e) {
      loggingService.error('Error inviting staff member', e);
      notificationService.showError('Error inviting staff member');
    } finally {
      loadingService.stopLoading('main-loader:update-invitation');
    }
  };

  const onClickDeleteInvitation = async (invitation: IStaffInvitation) => {
    try {
      loadingService.startLoading('main-loader:delete-staff-invitation');
      await companyService.deleteStaffInvitation(invitation.invite_code);

      const page = pagination.value?.page ?? 1;

      if (rows.value.length === 1 && page > 1) {
        pagination.value.page = page - 1;
      }

      getInvitations(pagination.value);
    } catch (e) {
      loggingService.error('Error deleting staff invitation', e);
      notificationService.showError('Error deleting staff invitation');
    } finally {
      loadingService.stopLoading('main-loader:delete-staff-invitation');
    }
  };

  const onCloseInvitationDetailModal = () => {
    $v.value.$reset();
    invitationDetailForm.value = {
      name: '',
      email: '',
      staffMemberHasAccessToEverything: false,
    };
    currentInvitationThatIsBeingEdited.value = null;
    modules.value = modules.value.map((m): ICheckable<IModule> => ({ ...m, isChecked: false }));
  };

  const onUpdateStaffMemberHasAccessToEverything = (value: boolean) => {
    modules.value = modules.value.map(
      (_module: IModule): ICheckable<IModule> => ({
        ..._module,
        isChecked: value,
      }),
    );
  };

  const onUpdateModuleCheckbox = () => {
    invitationDetailForm.value.staffMemberHasAccessToEverything = modules.value.every(
      (_module: ICheckable<IModule>) => _module.isChecked,
    );
  };

  const syncModules = async () => {
    loadingService.startLoading('main-loader:sync-modules');

    const allModules: IModule[] = await moduleService.getModules();
    modules.value = allModules.map(
      (_module: IModule): ICheckable<IModule> => ({
        ..._module,
        isChecked: false,
      }),
    );

    loadingService.stopLoading('main-loader:sync-modules');
  };

  onBeforeMount(async () => {
    syncModules();
    getInvitations(null);
  });
</script>

<style scoped lang="scss">
  .staff_invitations_page {
    padding: 20px 40px;

    @media (max-width: 1024px) {
      padding: 20px 10px;
    }
  }
</style>
