<template>
  <div class="flex justify-between items-center title-bar-heading">
    <title-bar style="width: 100%;" title="Account" subtitle="An overview of your account information."/>
  </div>

  <div class="company_page-container">
    <div class="row">
      <div class="col-md-6 col-12">
        <div class="company_page-rounded-border">
          <!--          <div class="company_page-rounded-border-header-bg flex items-center">
                      <div style="color: #313131;font-size: 17px; font-weight: 600;margin-left: 20px;">
                        Company Information
                      </div>
                    </div>-->
          <div class="company_page-inputs_container">
            <div class="input-double input-double-responsive">
              <shipvagoo-input-v1 v-model="form.companyName" 
                                  
                                  label="Company name"/>
              <shipvagoo-input-v1 v-model="form.address" :label="$t('common.address')"/>
            </div>

            <div class="input-double input-double-responsive">
              <shipvagoo-input-v1 v-model="form.vatNo" label="VAT no"/>
              <shipvagoo-input-v1
                v-model="form.zipCode"
                label="Postal code"
                @update:model-value="onChangeZipCode($event)"
              />

            </div>
            <div class="input-double input-double-responsive">
              <shipvagoo-input-v1 v-model="form.phoneNo" :label="$t('common.phone')"/>
              <shipvagoo-input-v1 v-model="form.city" :label="$t('common.city')"/>
            </div>
            <div class="input-double input-double-responsive">
              <shipvagoo-select-v1
                v-model="form.country"
                :options="$store.state.countryStore.countries"
                :label="$t('common.country')"
                disable
                option-value="country_code"
                option-label="default_name"
              >
                <template #prepend>
                  <shipvagoo-country-icon v-if="form.country" :country="form.country"/>
                </template>
                <template #option="scope">
                  <q-item v-bind="scope.itemProps">
                    <q-item-section side>
                      <shipvagoo-country-icon v-if="scope.opt" :country="scope.opt"/>
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ scope.opt.default_name }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
              </shipvagoo-select-v1>
              <div></div>
            </div>

            <div class="company_page-footer">
              <shipvagoo-button
                class="save_button"
                :label="$t('common.update')"
                min-width="100px"
                @click="onClickSave"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {find} from 'lodash-es';
import {onBeforeMount, ref, watch} from 'vue';
import {useStore} from 'vuex';
import * as _ from 'lodash-es';
import {
  companyService,
  loadingService,
  loggingService,
  mapService,
  notificationService,
} from '../services/_singletons';
import {ICountry} from '../model/country.model';
import {IRootState} from '../model/store.model';
import postCodes from '../assets/json/postcodes.json';

interface ICompanyForm {
  companyName: string;
  contactPerson: string;
  address: string;
  zipCode: string;
  city: string;
  phoneNo: string;
  country: ICountry | null;
  vatNo: string;
}

const form = ref<ICompanyForm>({
  companyName: '',
  address: '',
  zipCode: '',
  city: '',
  phoneNo: '',
  country: null,
  vatNo: '',
  contactPerson: '',
});

const companyCode = ref<number>(0);
const languageCode = ref<number>(0);

const $store = useStore<IRootState>();


watch(() => $store.state.countryStore.countries, () => {
  form.value.country = find($store.state.countryStore.countries, {
    country_code: $store.state.companyStore.country_code,
  }) as ICountry;
});
const getCompanyData = async () => {
  try {
    loadingService.startLoading('main-loader:get-company-data');
    await $store.dispatch('companyStore/syncCompanyData');
    companyCode.value = $store.state.companyStore.company_code;
    languageCode.value = $store.state.companyStore.language_code;
    form.value.companyName = $store.state.companyStore.company_name;
    form.value.address = $store.state.companyStore.address;
    form.value.zipCode = $store.state.companyStore.zipcode;
    form.value.country = find($store.state.countryStore.countries, {
      country_code: $store.state.companyStore.country_code,
    }) as ICountry;
    form.value.city = $store.state.companyStore.city;
    form.value.vatNo = $store.state.companyStore.vat_no;
    form.value.phoneNo = $store.state.companyStore.phone_no;
    form.value.address = $store.state.companyStore.address;
    form.value.contactPerson = $store.state.companyStore.contact_person;
  } catch (e) {
    loggingService.error('Error while getting company data', e);
    notificationService.showError('Error while getting company data');
  } finally {
    loadingService.stopLoading('main-loader:get-company-data');
  }
};

const onClickSave = async () => {
  try {
    loadingService.startLoading('main-loader:save-company');
    await companyService.updateCompany({
      company_code: companyCode.value,
      company_name: form.value.companyName,
      address: form.value.address,
      zipcode: form.value.zipCode,
      country_code: form.value.country?.country_code as number,
      city: form.value.city,
      vat_no: form.value.vatNo,
      phone_no: form.value.phoneNo,
      contact_person: form.value.contactPerson,
      language_code: languageCode.value,
    });
    notificationService.showSuccess('Company data saved');
    getCompanyData();
  } catch (error) {
    loggingService.error('Saving company', error);
    notificationService.showError('Error saving company');
  } finally {
    loadingService.stopLoading('main-loader:save-company');
  }
};

const onChangeZipCode = async (zip: string | number | null) => {
  if (!form.value.country || !zip || typeof zip !== 'string') {
    return;
  }

  const re = new RegExp(postCodes[form.value.country.iso].regex);

  if (!re.test(zip)) {
    return;
  }

  try {
    loadingService.startLoading('main-loader:get-city-from-zip-code');
    const geo = await mapService.getMapInfo(form.value.country.iso, zip);

    if (_.size(geo.results) > 0) {
      const results = _.head(Object.values(geo.results));
      const result = _.head(results);

      form.value.city = result?.city || '';
    }
  } catch (e) {
    notificationService.showError('Error getting city from zip code');
    loggingService.error('Error getting city from zip code', e);
  } finally {
    loadingService.stopLoading('main-loader:get-city-from-zip-code');
  }
};

onBeforeMount(() => {
  getCompanyData();
});
</script>

<style lang="scss" scoped>
.company_page {
  &-container {
    padding: 0 40px 10px 40px;

    @media (max-width: 768px) {
      padding: 0 20px 10px 20px;
    }
  }

  &-rounded-border {
    // box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.25);
    border: 1px solid #e1e1e1;
    border-radius: 4px;
  }

  &-rounded-border-header-bg {
    width: 100%;
    height: 55px;
    background-color: #F5F5F5;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }

  &-inputs_container {
    padding: 20px;

    & > *:not(:first-child) {
      margin-top: 20px;
    }
  }

  &-footer {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    padding: 20px;
    margin: 20px -20px -20px;
    border-top: 1px solid #e1e1e1;

    @media (max-width: $tablet) {
      & > * {
        width: 100%;
      }
    }
  }
}

.input-double {
  display: flex;
  flex-direction: row;
  gap: 30px;

  &-responsive {
    @media (max-width: 768px) {
      display: flex;
      flex-direction: column;
      gap: 17px;
    }
  }

  & > * {
    flex: 1;
  }
}
</style>
