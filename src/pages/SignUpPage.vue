<template>
  <div class="sign_up_page">
    <div class="sign_up_page-start_for_free">START FOR FREE</div>
    <h1 class="sign_up_page-heading">Create new account</h1>
    <div class="sign_up_page-not_a_member">
      Already a Member? <shipvagoo-hyperlink label="Login" to="/login" type="router-link" />
    </div>

    <form @submit.prevent="submit">
      <div class="input-double input-double-responsive">
        <shipvagoo-input
          v-model="form.companyName"
          label="Company name"
          :error="$v.companyName.$error"
          :error-message="$v.companyName.$errors[0]?.$message.toString()"
        ></shipvagoo-input>

        <shipvagoo-input
          v-model="form.contactPerson"
          label="Contact person"
          :error="$v.contactPerson.$error"
          :error-message="$v.contactPerson.$errors[0]?.$message.toString()"
        ></shipvagoo-input>
      </div>

      <shipvagoo-input
        v-model="form.address"
        label="Address"
        :error="$v.address.$error"
        :error-message="$v.address.$errors[0]?.$message.toString()"
      ></shipvagoo-input>

      <div class="input-double input-double-responsive">
        <shipvagoo-input
          v-model="form.zipCode"
          label="Zip Code"
          type="tel"
          mask="####"
          :error="$v.zipCode.$error"
          :error-message="$v.zipCode.$errors[0]?.$message.toString()"
        ></shipvagoo-input>

        <shipvagoo-select
          v-model="form.country"
          label="Country"
          :options="$store.state.countryStore.countries.filter((country) => euMember(country.iso))"
          :error="$v.country.$error"
          :error-message="$v.country.$errors?.[0]?.$message.toString()"
          option-label="default_name"
          option-value="country_code"
        >
          <template #prepend>
            <shipvagoo-country-icon v-if="form.country" :country="form.country" />
          </template>
          <template #option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section side>
                <shipvagoo-country-icon v-if="scope.opt" :country="scope.opt" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ scope.opt.default_name }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </shipvagoo-select>
      </div>

      <div class="input-double input-double-responsive">
        <shipvagoo-input
          v-model="form.city"
          label="City"
          :error="$v.city.$error"
          :error-message="$v.city.$errors[0]?.$message.toString()"
        ></shipvagoo-input>

        <shipvagoo-select
          v-model="form.language"
          label="Language"
          :options="$store.state.languageStore.languages"
          :error="$v.language.$error"
          :error-message="$v.language.$errors[0]?.$message.toString()"
          :option-label="(option) => $t(option.name)"
          option-value="language_code"
        ></shipvagoo-select>
      </div>
      <div class="input-double input-double-responsive">
        <shipvagoo-input
          v-model="form.vatNo"
          label="VAT No"
          type="tel"
          mask="########"
          :error="$v.vatNo.$error"
          :error-message="$v.vatNo.$errors[0]?.$message.toString()"
        ></shipvagoo-input>

        <shipvagoo-input
          v-model="form.phone"
          label="Phone"
          type="tel"
          mask="## ## ## ##"
          :error="$v.phone.$error"
          :error-message="$v.phone.$errors[0]?.$message.toString()"
        ></shipvagoo-input>
      </div>

      <shipvagoo-input
        v-model="form.email"
        label="Email"
        type="email"
        :error="$v.email.$error"
        :error-message="$v.email.$errors[0]?.$message.toString()"
      ></shipvagoo-input>

      <div class="input-double input-double-responsive">
        <shipvagoo-input
          v-model="form.password"
          label="Password"
          type="password"
          :error="$v.password.$error"
          :error-message="$v.password.$errors[0]?.$message.toString()"
        ></shipvagoo-input>

        <shipvagoo-input
          v-model="form.confirmPassword"
          label="Confirm Password"
          type="password"
          :error="$v.confirmPassword.$error"
          :error-message="$v.confirmPassword.$errors[0]?.$message.toString()"
        ></shipvagoo-input>
      </div>

      <shipvagoo-checkbox v-model="form.isTermsAndConditionsAccepted">
        I have read and agree to the&nbsp;
        <shipvagoo-hyperlink type="router-link" to="/terms" label="Terms and Conditions" />
      </shipvagoo-checkbox>

      <shipvagoo-button
        class="login-button"
        type="submit"
        label="Create"
        :disable="!form.isTermsAndConditionsAccepted"
      />
    </form>
  </div>
  <img class="sign_up_page-background_banner" :src="LoginImage" alt="" decoding="async" />
</template>
<script setup lang="ts">
  import useValidate, { ValidationArgs } from '@vuelidate/core';
  import { required, helpers, email, minLength } from '@vuelidate/validators';
  import { onBeforeMount, ref } from 'vue';
  import { useStore } from 'vuex';
  import { useRouter } from 'vue-router';
  import { euMember } from 'is-european';
  import {
    authService,
    loadingService,
    loggingService,
    notificationService,
  } from '../services/_singletons';
  import LoginImage from '../assets/img/sign.png';
  import { ICountry } from '../model/country.model';
  import { IRootState } from '../model/store.model';
  import { ILanguage } from '../model/language.model';

  const $store = useStore<IRootState>();
  const $router = useRouter();

  interface ISignUpForm {
    companyName: string;
    contactPerson: string;
    address: string;
    zipCode: string;
    vatNo: string;
    phone: string;
    email: string;
    password: string;
    confirmPassword: string;
    city: string;
    country: ICountry | null;
    language: ILanguage | null;
    isTermsAndConditionsAccepted: boolean;
  }

  const form = ref<ISignUpForm>({
    companyName: '',
    contactPerson: '',
    address: '',
    zipCode: '',
    vatNo: '',
    phone: '',
    email: '',
    password: '',
    confirmPassword: '',
    city: '',
    country: null,
    language: null,
    isTermsAndConditionsAccepted: false,
  });

  const $v = useValidate<ISignUpForm, ValidationArgs<ISignUpForm>>(
    {
      companyName: { required: helpers.withMessage('This field is required.', required) },
      contactPerson: { required: helpers.withMessage('This field is required.', required) },
      address: { required: helpers.withMessage('This field is required.', required) },
      zipCode: {
        required: helpers.withMessage('This field is required.', required),
        minLength: helpers.withMessage('This field minimum value 4 character.', minLength(4)),
      },
      vatNo: {
        required: helpers.withMessage('This field is required.', required),
        minLength: helpers.withMessage('This field minimum value 4 character.', minLength(4)),
      },
      phone: { required: helpers.withMessage('This field is required.', required) },
      email: {
        required: helpers.withMessage('This field is required.', required),
        email: helpers.withMessage('This email not using', email),
      },
      password: {
        required: helpers.withMessage('This field is required.', required),
        minLength: helpers.withMessage('This field minimum value 8 character.', minLength(8)),
      },
      confirmPassword: {
        required: helpers.withMessage('This field is required.', required),
        sameAs: helpers.withMessage(
          'This password not match.',
          () => form.value.password === form.value.confirmPassword,
        ),
      },
      country: { required: helpers.withMessage('This field is required.', required) },
      city: { required: helpers.withMessage('This field is required.', required) },
      language: { required: helpers.withMessage('This field is required.', required) },
      isTermsAndConditionsAccepted: {},
    },
    form,
  );

  const submit = async () => {
    await $v.value.$validate();

    if (
      $v.value.$error ||
      !form.value.isTermsAndConditionsAccepted ||
      !form.value.country ||
      !form.value.language
    ) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:sign-up');
      await authService.signUp({
        company_name: form.value.companyName,
        contact_person: form.value.contactPerson,
        address: form.value.address,
        zipcode: form.value.zipCode,
        vat_no: form.value.vatNo,
        phone_no: form.value.phone,
        email: form.value.email,
        password: form.value.password,
        password_confirmation: form.value.confirmPassword,
        city: form.value.city,
        country_code: form.value.country.country_code,
        language_code: form.value.language.language_code,
      });
      await $router.push('/');
    } catch (e) {
      loggingService.error('Error on sign up', e);
      notificationService.showError('Error on sign up');
    } finally {
      loadingService.stopLoading('main-loader:sign-up');
    }
  };

  onBeforeMount(async () => {
    $store.dispatch('countryStore/syncCountries');
  });
</script>

<style scoped lang="scss">
  .sign_up_page {
    width: 502px;

    transition: transform 0.3s ease-in-out, width 0.3s ease-in-out;
    transform: translateX(78px) translateY(81px);
    will-change: width, transform;

    @media (max-width: 768px) {
      width: calc(100% - 20px);
      max-width: unset;

      transform: translateX(10px) translateY(241px);
    }

    &-start_for_free {
      font-size: 15px;
      font-weight: 500;
      line-height: 17px;
      color: #9eb2c6;
    }

    &-heading {
      margin: 0;
      margin-top: 17px;
      font-size: 35px;
      line-height: 42px;
    }

    &-not_a_member {
      margin-top: 17px;
      font-size: 15px;
      color: #9eb2c6;
    }

    &-forgot_password,
    &-remember_me {
      margin-top: 27px;
    }

    &-login_button {
      min-width: 115px;
      margin-top: 27px;
    }

    &-background_banner {
      position: absolute;
      top: 0;
      right: 0;
      z-index: -1;
      width: calc(100% - 340px - 78px - 23px);
      height: 100%;
      object-fit: cover;
      object-position: center;
      transition: width 300ms ease-in-out, height 300ms ease-in-out;
      will-change: width, height;

      @media (max-width: 768px) {
        width: 100%;
        max-width: unset;
        max-height: 375px;
      }
    }

    form {
      & > * {
        margin-top: 17px;
      }
    }
  }

  .input-double {
    display: flex;
    flex-direction: row;
    gap: 30px;

    &-responsive {
      @media (max-width: 768px) {
        display: flex;
        flex-direction: column;
        gap: 17px;
      }
    }

    & > * {
      flex: 1;
    }
  }
</style>
