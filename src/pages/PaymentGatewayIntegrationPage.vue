<template>
  <Title title="Payment Gateway Integration" />
  <div class="payment_gateway_integration_page">
    <div class="payment_gateway_integration_page-actions">
      <div class="big_search-rows_per_page-group">
        <big-search v-model="search" class="payment_gateway_integration_page-actions-big_search" />
      </div>

      <!-- <shipvagoo-button outline label="Download bulk" /> -->
    </div>

    <shipvagoo-table
      :columns="columns"
      :rows="rows"
      :total="rows.length"
      :pagination="{ rowsPerPage: 10 }"
      @row-click="onClickRow"
    >
      <template #body-cell-more="props">
        <q-td :props="props" class="more">
          <q-btn dense flat round   style="color:#1f2124;" class="more-action" field="edit" icon="more_horiz">
            <shipvagoo-menu>
              <shipvagoo-menu-item
                clickable
                @click="onClickDeletePaymentGatewayIntegration(props.row)"
              >
                Delete
              </shipvagoo-menu-item>
            </shipvagoo-menu>
          </q-btn>
        </q-td>
      </template>
    </shipvagoo-table>
  </div>

  <!-- <shipvagoo-fab icon="add" @click="onClickInviteStaff" /> -->

  <q-dialog
    v-model="isPaymentGatewayIntegrationDetailModalOpen"
    @before-hide="onClosePaymentGatewayIntegrationModal"
  >
    <shipvagoo-modal title="Edit payment gateway">
      <!-- <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
        <div class="input-double input-double-responsive">
          <shipvagoo-input v-model="staffDetailForm.name" label="Name" placeholder="Name..." />
          <shipvagoo-input v-model="staffDetailForm.email" label="E-mail" placeholder="E-mail..." />
        </div>
      </shipvagoo-card-section>
      <shipvagoo-separator />
      <shipvagoo-card-section>
        <shipvagoo-checkbox
          v-model="staffDetailForm.staffMemberHasAccessToEverything"
          size="sm"
          style="font-size: 12px"
          @update:model-value="onUpdateStaffMemberHasAccessToEverything"
        >
          Staff member has access to everything
        </shipvagoo-checkbox>
      </shipvagoo-card-section>
      <shipvagoo-separator />
      <shipvagoo-card-section style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px">
        <shipvagoo-checkbox
          v-for="_module in modules"
          :key="_module.module_code"
          v-model="_module.isChecked"
          size="sm"
          :label="_module.name"
          @update:model-value="onUpdateModuleCheckbox"
        />
      </shipvagoo-card-section> -->
      <shipvagoo-separator />
      <shipvagoo-card-actions>
        <!-- <shipvagoo-button
          :disable="
            isEmpty(staffDetailForm.email) ||
            isEmpty(staffDetailForm.name) ||
            modules.every((m) => !m.isChecked)
          "
          @click="onClickSave"
          >Save</shipvagoo-button
        > -->
      </shipvagoo-card-actions>
    </shipvagoo-modal>
  </q-dialog>
</template>

<script setup lang="ts">
  import { onBeforeMount, Ref, ref } from 'vue';
  import { QTableProps } from 'quasar';
  // import useVuelidate, { ValidationArgs } from '@vuelidate/core';
  // import { email, helpers, required } from '@vuelidate/validators';
  import Title from '../components/title-bar.vue';
  import {
    loadingService,
    loggingService,
    notificationService,
    paymentService,
  } from '../services/_singletons';
  import { ICompanyUser } from '../model/company.model';
  import { IPaymentGateway } from '../model/payment.model';
  import HelperService from '../services/helper.service';

  const isPaymentGatewayIntegrationDetailModalOpen = ref<boolean>();

  // const selectedUserId = ref<number | null>(null);

  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  // interface IStaffAccountForm {}
  const paymentGatewayIntegrationForm = ref({});

  // const $v = useVuelidate<IStaffAccountForm, ValidationArgs<IStaffAccountForm>>(
  //   {},
  //   staffDetailForm,
  // );

  const search: Ref<string> = ref<string>('');
  const columns: QTableProps['columns'] = [
    { name: 'gateway_name', field: 'gateway_name', label: 'Name', align: 'left', sortable: true },
    { name: 'provider', field: 'provider', label: 'Provider', align: 'left', sortable: true },
    {
      name: 'merchant_code',
      field: 'merchant_code',
      label: 'Merchant No.',
      align: 'left',
      sortable: true,
    },
    { name: 'more', field: 'more', label: 'More', align: 'right' },
  ];
  const rows = ref<IPaymentGateway[]>([]);

  const getPaymentGateways = async () => {
    try {
      loadingService.startLoading('main-loader:get-payment-gateways');
      rows.value = await paymentService.getPaymentGateways();
    } catch (e) {
      loggingService.error('Error getting payment gateways', e);
      notificationService.showSuccess('Error getting payment gateways');
    } finally {
      loadingService.stopLoading('main-loader:get-payment-gateways');
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const onClickRow = (event: Event, row: ICompanyUser) => {
    if (HelperService.containsIgnoredRowElements(event)) {
      return;
    }

    // modules.value = modules.value.map((_m) => {
    //   const m = _m;
    //   m.isChecked = row.permitted_modules.includes(m.module_code);
    //   return m;
    // });

    // staffDetailForm.value.staffMemberHasAccessToEverything = modules.value.every(
    //   (m) => m.isChecked,
    // );

    isPaymentGatewayIntegrationDetailModalOpen.value = true;
  };

  // const onClickSave = async () => {
  //   await $v.value.$validate();

  //   if ($v.value.$error) {
  //     return;
  //   }

  //   try {
  //     loadingService.startLoading('main-loader:update-staff-member');
  //     // await companyService.updateCompanyUser(selectedUserId.value ?? 0, {
  //     //   name: staffDetailForm.value.name,
  //     //   email: staffDetailForm.value.email,
  //     //   permitted_modules: modules.value.filter((m) => m.isChecked).map((m) => m.module_code),
  //     // });

  //     isStaffDetailModalOpen.value = false;

  //     notificationService.showSuccess('Staff member updated successfully');
  //     getPaymentGateways();
  //   } catch (e) {
  //     loggingService.error('Error updating staff member', e);
  //     notificationService.showError('Error updating staff member');
  //   } finally {
  //     loadingService.stopLoading('main-loader:update-staff-member');
  //   }
  // };

  const onClosePaymentGatewayIntegrationModal = () => {
    paymentGatewayIntegrationForm.value = {};
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const onClickDeletePaymentGatewayIntegration = async (_user: ICompanyUser) => {
    try {
      loadingService.startLoading('main-loader:delete-payment-gateway-integration');
      // await companyService.deleteCompanyUser(user.user_code);
      getPaymentGateways();
    } catch (e) {
      loggingService.error('Error deleting payment gateway integration', e);
      notificationService.showError('Error deleting payment gateway integration');
    } finally {
      loadingService.stopLoading('main-loader:delete-payment-gateway-integration');
    }
  };

  onBeforeMount(async () => {
    getPaymentGateways();
  });
</script>

<style scoped lang="scss">
  .payment_gateway_integration_page {
    padding: 20px 40px;

    @media (max-width: 1024px) {
      padding: 20px 10px;
    }
  }
</style>