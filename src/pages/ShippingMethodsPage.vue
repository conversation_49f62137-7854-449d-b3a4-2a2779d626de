<template>
  <div class="flex justify-between items-center title-bar-heading">
    <title-bar style="width: 100%;" :title="$t('common.shipping_methods')"/>
  </div>
  <div class="printers_page">
<!--    <shipping-methods-list :integration-code="$route.params.integration_code.toString()"></shipping-methods-list>-->
  </div>
</template>
<script setup lang="ts">
 // import {useRoute} from "vue-router";

  //const $route = useRoute();

</script>
<style scoped lang="scss">
  .printers_page {
    padding: 20px 40px;

    @media (max-width: 1024px) {
      padding: 20px 10px;
    }
  }
</style>