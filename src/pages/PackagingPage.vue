<template>
  <title-bar :title="$t('common.packaging')" />
  <div class="packaging_page">
    <div class="packaging_page-actions">
      <div class="big_search-rows_per_page-group">
        <big-search v-model="search" class="packaging_page-actions-big_search" />
      </div>

      <!-- <shipvagoo-button outline label="Download bulk" /> -->
    </div>

    <shipvagoo-table
      v-model:pagination="pagination"
      :columns="columns"
      :rows="rows"
      :total="pagination.rowsNumber"
      :loading="loadingService.loadings.has('main-loader:get-packagings')"
      @row-click="onClickRow"
    >
      <template #body-cell-more="props">
        <q-td :props="props" class="more">
          <q-btn dense flat round style="color:#1f2124;" class="more-action" field="edit" icon="more_horiz">
            <shipvagoo-menu>
              <shipvagoo-menu-item clickable @click="onClickDeletePackaging(props.row)">
                {{ $t('common.delete') }}
              </shipvagoo-menu-item>
            </shipvagoo-menu>
          </q-btn>
        </q-td>
      </template>
    </shipvagoo-table>
  </div>

  <shipvagoo-fab icon="add" @click="onClickAdd" />

  <q-dialog v-model="isPackagingDetailOpen" @before-hide="onClosePackagingDetail">
    <shipvagoo-modal :title="packagingDetailTitle" size="sm">
      <shipvagoo-card-section style="display: grid; grid-template-columns: 1fr; gap: 20px">
        <shipvagoo-input-group responsive>
          <shipvagoo-input
            v-model="form.name"
            :label="$t('common.name')"
            :error="$v.name.$error"
            :error-message="$v.name.$errors[0]?.$message.toString()"
          />
          <shipvagoo-input
            v-model="form.barcode"
            :label="$t('common.barcode')"
            :error="$v.barcode.$error"
            :error-message="$v.barcode.$errors[0]?.$message.toString()"
          />
        </shipvagoo-input-group>
        <shipvagoo-input-group responsive>
          <shipvagoo-input-group responsive>
            <shipvagoo-input
              v-model="form.length"
              :label="`${$t('common.length')} (cm)`"
              :error="$v.length.$error"
              :error-message="$v.length.$errors[0]?.$message.toString()"
            />
            <shipvagoo-input
              v-model="form.width"
              :label="`${$t('common.width')} (cm)`"
              :error="$v.width.$error"
              :error-message="$v.width.$errors[0]?.$message.toString()"
            />
          </shipvagoo-input-group>
          <shipvagoo-input-group responsive>
            <shipvagoo-input
              v-model="form.height"
              :label="`${$t('common.height')} (cm)`"
              :error="$v.height.$error"
              :error-message="$v.height.$errors[0]?.$message.toString()"
            />
            <shipvagoo-input
              v-model="form.weight"
              :label="`${$t('common.weight')} (gram)`"
              :error="$v.weight.$error"
              :error-message="$v.weight.$errors[0]?.$message.toString()"
            />
          </shipvagoo-input-group>
        </shipvagoo-input-group>
      </shipvagoo-card-section>
      <shipvagoo-separator />
      <shipvagoo-card-actions>
        <shipvagoo-button
          v-if="packagingDetailType === 'create'"
          :label="$t('common.create')"
          min-width="120px"
          @click="onClickCreatePackaging"
        />
        <shipvagoo-button
          v-if="packagingDetailType === 'edit'"
          :label="$t('common.update')"
          min-width="120px"
          @click="onClickUpdatePackaging"
        />
      </shipvagoo-card-actions>
    </shipvagoo-modal>
  </q-dialog>
</template>

<script setup lang="ts">
  import useVuelidate from '@vuelidate/core';
  import { helpers, required, decimal, numeric } from '@vuelidate/validators';
  import { QTableProps } from 'quasar';
  import { onBeforeMount, Ref, ref } from 'vue';
  import { useI18n } from 'vue-i18n';
  import TitleBar from '../components/title-bar.vue';
  import { Pagination } from '../model/common.model';
  import { IRequestParam } from '../model/http.model';
  import { IPackaging } from '../model/packaging.model';
  import HelperService from '../services/helper.service';
  import {
    loadingService,
    loggingService,
    packagingService,
    notificationService,
  } from '../services/_singletons';

  const { t } = useI18n();
  const search = ref<string>('');
  const columns: QTableProps['columns'] = [
    { field: 'name', name: 'name', label: t('common.name'), align: 'left' },
    { field: 'length', name: 'length', label: t('common.length'), align: 'left' },
    { field: 'width', name: 'width', label: t('common.width'), align: 'left' },
    { field: 'height', name: 'height', label: t('common.height'), align: 'left' },
    { field: 'weight', name: 'weight', label: t('common.weight'), align: 'left' },
    { field: 'barcode', name: 'barcode', label: t('common.barcode'), align: 'left' },
    { field: 'more', name: 'more', label: t('common.more'), align: 'right' },
  ];
  const rows = ref<IPackaging[]>([]);
  const isPackagingDetailOpen = ref<boolean>(false);
  const currentPackagingCode = ref<number>(0);
  const packagingDetailType = ref<'create' | 'edit'>('create');
  const packagingDetailTitle = ref<string>('');

  interface IPackagingForm {
    name: string;
    barcode: string;
    length: number | null;
    width: number | null;
    height: number | null;
    weight: number | null;
  }

  const form = ref<IPackagingForm>({
    name: '',
    barcode: '',
    length: null,
    width: null,
    height: null,
    weight: null,
  });

  const pagination: Ref<Pagination> = ref({
    page: 1,
    rowsPerPage: 10,
    rowsNumber: 0,
    descending: true,
  });

  const $v = useVuelidate(
    {
      name: {
        required: helpers.withMessage('This field is required', required),
      },
      barcode: {
        required: helpers.withMessage('This field is required', required),
        numeric: helpers.withMessage('This field must be numeric', numeric),
      },
      length: {
        required: helpers.withMessage('This field is required', required),
        decimal: helpers.withMessage('Must be a decimal value', decimal),
      },
      width: {
        required: helpers.withMessage('This field is required', required),
        decimal: helpers.withMessage('Must be a decimal value', decimal),
      },
      height: {
        required: helpers.withMessage('This field is required', required),
        decimal: helpers.withMessage('Must be a decimal value', decimal),
      },
      weight: {
        required: helpers.withMessage('This field is required', required),
        decimal: helpers.withMessage('Must be a decimal value', decimal),
      },
    },
    form,
  );

  const getPackagings = async (tablePagination: Pagination | null) => {
    try {
      loadingService.startLoading('main-loader:get-packagings');
      const page = tablePagination?.page || 1;
      const limit = tablePagination?.rowsPerPage || 10;

      const params: IRequestParam[] = [];

      if (tablePagination?.sortBy) {
        const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
        params.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
      } else {
        params.push({ key: 'sorting[created_at]', value: 'desc' });
      }

      const result = await packagingService.getPackagings(page, limit);

      pagination.value.page = page;
      pagination.value.rowsPerPage = limit;
      pagination.value.rowsNumber = result.total;
      pagination.value.sortBy = tablePagination?.sortBy;
      pagination.value.descending = tablePagination?.descending;

      rows.value = result.entities;
    } catch (error) {
      loggingService.error('Error getting packagings', error);
      notificationService.showError('Error getting packagings');
    } finally {
      loadingService.stopLoading('main-loader:get-packagings');
    }
  };

  const onClickAdd = () => {
    packagingDetailTitle.value = t('packaging_detail.create_packaging');
    packagingDetailType.value = 'create';
    isPackagingDetailOpen.value = true;
  };

  const onClickRow = (event: Event, row: IPackaging) => {
    if (HelperService.containsIgnoredRowElements(event)) {
      return;
    }

    packagingDetailTitle.value = t('packaging_detail.edit_packaging');
    packagingDetailType.value = 'edit';

    currentPackagingCode.value = row.packaging_code;
    form.value.name = row.name;
    form.value.barcode = row.barcode;
    form.value.length = row.length;
    form.value.width = row.width;
    form.value.height = row.height;
    form.value.weight = row.weight;

    isPackagingDetailOpen.value = true;
  };

  const onClosePackagingDetail = () => {
    isPackagingDetailOpen.value = false;
    currentPackagingCode.value = 0;
    form.value.name = '';
    form.value.barcode = '';
    form.value.length = null;
    form.value.width = null;
    form.value.height = null;
    form.value.weight = null;
    $v.value.$reset();
  };

  const onClickCreatePackaging = async () => {
    await $v.value.$validate();

    if (
      $v.value.$error ||
      !form.value.length ||
      !form.value.width ||
      !form.value.height ||
      !form.value.weight
    ) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:add-packaging');
      await packagingService.createPackaging({
        name: form.value.name,
        barcode: form.value.barcode,
        length: form.value.length,
        width: form.value.width,
        height: form.value.height,
        weight: form.value.weight,
      });
      notificationService.showSuccess('Packaging created successfully');
      getPackagings(null);
      onClosePackagingDetail();
    } catch (error) {
      loggingService.error('Error adding packaging', error);
      notificationService.showError('Error adding packaging');
    } finally {
      loadingService.stopLoading('main-loader:add-packaging');
    }
  };

  const onClickUpdatePackaging = async () => {
    await $v.value.$validate();

    if (
      $v.value.$error ||
      !form.value.length ||
      !form.value.width ||
      !form.value.height ||
      !form.value.weight
    ) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:update-packaging');
      await packagingService.updatePackaging(currentPackagingCode.value, {
        name: form.value.name,
        barcode: form.value.barcode,
        length: form.value.length,
        width: form.value.width,
        height: form.value.height,
        weight: form.value.weight,
      });
      notificationService.showSuccess('Packaging updated successfully');
      getPackagings(pagination.value);
      onClosePackagingDetail();
    } catch (error) {
      loggingService.error('Error updating packaging', error);
      notificationService.showError('Error updating packaging');
    } finally {
      loadingService.stopLoading('main-loader:update-packaging');
    }
  };

  const onClickDeletePackaging = async (row: IPackaging) => {
    try {
      loadingService.startLoading('main-loader:delete-packaging');
      await packagingService.deletePackaging(row.packaging_code);
      getPackagings(pagination.value);
    } catch (error) {
      loggingService.error('Error deleting packaging', error);
      notificationService.showError('Error deleting packaging');
    } finally {
      loadingService.stopLoading('main-loader:delete-packaging');
    }
  };

  onBeforeMount(() => {
    getPackagings(null);
  });
</script>

<style scoped lang="scss">
  .packaging_page {
    padding: 20px 40px;

    @media (max-width: 1024px) {
      padding: 20px 10px;
    }
  }
</style>
