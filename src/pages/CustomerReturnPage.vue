<template>
  <div class="customer-return-page" v-if="verifiedPortal">
    <div v-if="parent_step == 1">
      <div class="start-return-form">
        <div class="row full-height full-width">
          <div class="col-md-6 col-12 start-page-form-left flex items-center justify-end q-pa-lg">
            <div>
              <div
                class="text-white text-weight-7 no-padding"
                style="font-size: 25px; text-align: end"
              >
                {{ verifiedPortal?.company_name }}
              </div>
              <div
                class="text-white text-weight-7 no-padding"
                style="font-size: 30px; text-align: end"
              >
                Return Portal
              </div>
            </div>
          </div>
          <div class="col-md-6 col-12">
            <div class="return-form-grids full-height q-py-md">
              <div class="return-form-child-grids">
                <div class="return-form-grids full-height">
                  <div class="return-form-child-grids flex justify-center items-end">
                    <div class="text-h4 text-weight-medium text-brand" v-if="verifiedPortal">
                      <a
                        v-if="
                          verifiedPortal.logo_full_path != null &&
                          (verifiedPortal.meta?.company_logo_url || verifiedPortal.company?.website)
                        "
                        target="_blank"
                        :href="
                          '//' +
                          (verifiedPortal.meta?.company_logo_url ?? verifiedPortal.company?.website)
                        "
                        class="logo"
                      >
                        <img
                          v-if="
                            verifiedPortal.logo !== null && verifiedPortal.logo_full_path != null
                          "
                          style="width: 100px; max-height: 100px"
                          :src="verifiedPortal?.logo_full_path"
                          :alt="verifiedPortal?.company_name ?? 'Logo'"
                          decoding="async"
                        />
                      </a>
                      <img
                        v-else-if="
                          verifiedPortal.logo !== null && verifiedPortal.logo_full_path != null
                        "
                        style="width: 100px; max-height: 100px"
                        :src="verifiedPortal?.logo_full_path"
                        :alt="verifiedPortal?.company_name ?? 'Logo'"
                        decoding="async"
                      />
                    </div>
                  </div>
                  <div class="return-form-child-grids text-center flex justify-center q-mt-lg">
                    <div style="width: 50%">
                      <div class="text-h5 dark-text text-weight-bold">
                        {{ verifiedPortal?.page_title }}
                      </div>
                      <div
                        class="text-weight-regular text-light-gray q-mt-sm"
                        v-html="verifiedPortal?.content"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="return-form-child-grids flex justify-center items-center">
                <div class="full-width q-px-lg">
                  <shipvagoo-input-v1
                    label="Order number"
                    v-model="form.order_number"
                    placeholder="Enter order number"
                    :error="$v.order_number.$error"
                    :error-message="$v.order_number.$errors?.[0]?.$message.toString()"
                    class=""
                  >
                  </shipvagoo-input-v1>
                  <shipvagoo-input-v1
                    label="Email"
                    v-model="form.email"
                    placeholder="Enter email"
                    :error="$v.email.$error"
                    :error-message="$v.email.$errors?.[0]?.$message.toString()"
                    class="q-mt-md"
                  >
                  </shipvagoo-input-v1>
                  <shipvagoo-button
                    :disable="!form.email || !form.order_number"
                    @click="onClickFindOrder"
                    no-caps
                    class="text-white full-width q-mt-md"
                  >
                    Find my order
                  </shipvagoo-button>
                </div>
              </div>
              <div class="return-form-child-grids"></div>
            </div>
          </div>
          <div></div>
        </div>
      </div>
    </div>
    <div v-else-if="order && parent_step == 2">
      <div class="return-order-steps-container">
        <shipvagoo-stepper-v1 ref="stepper" class="return-order-modal-stepper" v-model="step">
          <q-step :name="1" title="Items" :done="step > 1">
            <div class="return-order-steps">
              <div class="dark-text text-weight-7 text-sixteen q-mb-xs">
                Select items you want to return
              </div>
              <div class="row full-height" v-if="shippedItems.length > 0">
                <div class="col-md-9 col-12">
                  <div
                    v-for="(item, index) of shippedItems"
                    :key="item.line_item_code"
                    class="return-order-item q-mt-sm"
                    :style="{
                      backgroundColor: returnItemsChecks[item.line_item_code] ? '#F7F7F7' : '',
                    }"
                  >
                    <div class="flex justify-between">
                      <div class="flex">
                        <shipvagoo-checkbox-v1
                          v-model="returnItemsChecks[item.line_item_code]"
                          size="md"
                          @click.stop
                          @keypress.stop
                        >
                          <div
                            class="return-order-info-container"
                            style="justify-content: center; margin-left: 10px"
                          >
                            <div v-if="item.variant && item.variant?.variant_image">
                              <img
                                :src="item.variant.variant_image"
                                class="return-order-item-image"
                                alt="product image"
                              />
                            </div>
                            <div v-else-if="item.product?.image_url">
                              <img
                                :src="item.product?.image_url"
                                class="return-order-item-image"
                                alt="product image"
                              />
                            </div>
                          </div>
                        </shipvagoo-checkbox-v1>
                        <div style="margin-left: 25px" @click.stop>
                          <div v-if="item.variant">
                            <div class="text-weight-7">
                              {{ item?.title }}
                            </div>
                            <div class="text-eleven">
                              <div
                                class="q-mt-xs"
                                v-if="item.variant && item.variant?.title != 'Default Title'"
                              >
                                <span>Variant: </span>{{ item.variant?.title ?? '' }}
                              </div>
                              <div class="q-mt-xs">
                                <span>Weight: </span
                                >{{ `${item.variant?.weight} ${item.variant?.weight_unit}` }}
                              </div>
                              <div class="q-mt-xs" v-if="item.variant?.sku">
                                <span>SKU: </span>
                                {{ `${item.variant?.sku ?? ''}` }}
                              </div>
                            </div>
                          </div>

                          <div v-else style="font-weight: bold">
                            {{ item.product?.title }}
                          </div>
                          <q-select
                            dense
                            class="select-quantity q-mt-sm"
                            v-if="item.quantity > 1"
                            v-model="returnItemQuantitySelections[item.line_item_code]"
                            outlined
                            popup-content-style="color: #000000"
                            :option-label="(option: number) => `${option} of ${(item.quantity)- (item.calculated_returned_quantity ?? 0)}`"
                            :options="
                              _.range(
                                1,
                                item.quantity + 1 - (item.calculated_returned_quantity ?? 0),
                              )
                            "
                          />
                        </div>
                      </div>
                      <div class="full-height q-py-xs">
                        <q-select
                          v-model="stepForm.step1.reasons[index].reason"
                          option-label="description"
                          option-value="return_reason_code"
                          outlined
                          dense
                          placeholder="Select reason"
                          :options="reasons"
                          popup-content-style="color: #000000"
                          hide-bottom-space
                          class="reason-select"
                          no-error-icon
                          :error="reason_errors[index]?.is_reason_error"
                          error-message="Field is required."
                        />
                        <textarea
                          v-if="stepForm.step1.reasons[index].reason?.enable_comment === 1"
                          type="textarea"
                          rows="3"
                          v-model="stepForm.step1.reasons[index].comment"
                          class="comment-textarea q-mt-sm"
                          placeholder="Write your comment here..."
                        />
                        <div>
                          <div
                            style="
                              margin-top: -5px;
                              margin-left: 12px;
                              font-size: 11px;
                              color: #c10015;
                            "
                            v-if="
                              stepForm.step1.reasons[index].reason?.enable_comment === 1 &&
                              reason_errors[index]?.is_comment_error
                            "
                            class="no-padding"
                          >
                            Field is required
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-3 col-12">
                  <div class="return-item-details q-my-sm">
                    <div class="dark-text text-sixteen text-weight-bold">Return item</div>
                    <shipvagoo-separator class="q-my-sm" />
                    <div>
                      <table class="full-width">
                        <tr>
                          <th class="dark-text text-twelve text-weight-7 text-left">
                            Product Name
                          </th>
                          <th class="dark-text text-twelve text-weight-7 text-right">Qty</th>
                        </tr>
                        <tr
                          v-for="(itemToBeReturned, index) of itemsToBeReturned"
                          :key="itemToBeReturned.quantity"
                        >
                          <td
                            class="dark-text text-twelve text-left q-pt-sm"
                            :class="index == 0 && 'q-pt-sm'"
                          >
                            <div class="text-thirteen">{{ itemToBeReturned?.title }}</div>
                            <div
                              class="text-eleven q-mt-xs"
                              v-if="
                                itemToBeReturned.variant &&
                                itemToBeReturned.variant?.title != 'Default Title'
                              "
                            >
                              <span>Variant: {{ itemToBeReturned?.variant?.title }}</span>
                            </div>
                            <div
                              class="text-eleven q-mt-xs"
                              v-if="itemToBeReturned.variant && itemToBeReturned.variant?.sku"
                            >
                              <span>SKU: {{ itemToBeReturned?.variant?.sku }}</span>
                            </div>
                          </td>
                          <td
                            class="dark-text text-twelve text-right"
                            :class="index == 0 && 'q-pt-sm'"
                          >
                            {{ returnItemQuantitySelections[itemToBeReturned.line_item_code] }}
                          </td>
                        </tr>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="text-light-gray text-sixteen text-center q-mt-md">
                No items found
              </div>
            </div>
          </q-step>
          <q-step :name="2" title="Return" :done="step > 2">
            <div class="return-order-steps">
              <div class="dark-text text-weight-7 text-sixteen q-mb-xs">
                How will you like to handle your return?
              </div>
              <div class="row full-height" v-if="shippedItems.length > 0">
                <div class="col-md-9 col-12">
                  <div class="return-order-resolution-container q-my-sm">
                    <div
                      v-for="resolution of resolutions"
                      :key="`${resolution.resolution_code}-${resolution.name}-${resolution.description}`"
                      :class="
                        stepForm.step2.resolution == resolution.resolution_code && 'checked-radio'
                      "
                    >
                      <shipvagoo-radio
                        @update:model-value="onChangeResolution"
                        v-model="stepForm.step2.resolution"
                        :name="resolution.name"
                        :radio-value="resolution.resolution_code"
                        style="width: 100%; margin: 16px"
                      >
                        <div class="text-brand text-weight-7">
                          {{ resolution.name }}
                        </div>
                      </shipvagoo-radio>
                      <shipvagoo-input-v1
                        v-if="
                          resolution.resolution_code == stepForm.step2.resolution &&
                          showResolutionComment
                        "
                        bg-color="#FFFFFF"
                        style="background-color: white !important"
                        label="Comment"
                        placeholder="Enter your comment"
                        v-model="stepForm.step2.resolution_comment"
                        class="q-mb-md q-mx-md"
                      />
                      <shipvagoo-separator />
                      <!-- <shipvagoo-separator v-if="resolution !== resolutions.at(-1)" />-->
                    </div>
                  </div>
                </div>
                <div class="col-md-3 col-12">
                  <div class="return-item-details q-my-sm">
                    <div class="dark-text text-sixteen text-weight-bold">Return details</div>
                    <shipvagoo-separator class="q-my-sm" />
                    <div>
                      <table class="full-width">
                        <tr>
                          <th class="dark-text text-twelve text-weight-7 text-left">
                            Product Name
                          </th>
                          <th class="dark-text text-twelve text-weight-7 text-right">Qty</th>
                        </tr>
                        <tr
                          v-for="(itemToBeReturned, index) of itemsToBeReturned"
                          :key="itemToBeReturned.quantity"
                        >
                          <td
                            class="dark-text text-twelve text-left q-pt-sm"
                            :class="index == 0 && 'q-pt-sm'"
                          >
                            <div class="text-thirteen">{{ itemToBeReturned?.title }}</div>
                            <div
                              class="text-eleven q-mt-xs"
                              v-if="
                                itemToBeReturned.variant &&
                                itemToBeReturned.variant?.title != 'Default Title'
                              "
                            >
                              <span>Variant: {{ itemToBeReturned?.variant?.title }}</span>
                            </div>
                            <div
                              class="text-eleven q-mt-xs"
                              v-if="itemToBeReturned.variant && itemToBeReturned.variant?.sku"
                            >
                              <span>SKU: {{ itemToBeReturned?.variant?.sku }}</span>
                            </div>
                          </td>
                          <td
                            class="dark-text text-twelve text-right q-pt"
                            :class="index == 0 && 'q-pt-sm'"
                          >
                            {{ returnItemQuantitySelections[itemToBeReturned.line_item_code] }}
                          </td>
                        </tr>
                      </table>
                    </div>
                    <div class="dark-text text-sixteen text-weight-bold q-mt-md">
                      Resolution method
                    </div>
                    <shipvagoo-separator class="q-my-sm" />
                    <div class="dark-text text-twelve">
                      {{
                        _.find(resolutions, ['resolution_code', stepForm.step2.resolution])?.name
                      }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </q-step>
          <q-step :name="3" title="Return Delivery" :done="step > 3">
            <div class="return-order-steps">
              <div class="dark-text text-weight-7 text-sixteen q-mb-xs">
                How will you like to handle your return?
              </div>
              <div class="row full-height" v-if="shippedItems.length > 0">
                <div class="col-md-9 col-12">
                  <div class="return-order-resolution-container q-my-sm">
                    <div
                      class="q-mt-xs"
                      v-for="method of methods"
                      :key="method.method_code"
                      :class="stepForm.step3.method == method.method_code && 'checked-radio'"
                    >
                      <shipvagoo-radio
                        @update:model-value="onChangeReturnMethod"
                        v-model="stepForm.step3.method"
                        :name="method.name"
                        :radio-value="method.method_code"
                        class="shipvagoo-radio-method"
                        style="width: 100%; margin: 16px"
                      >
                        <div class="flex justify-between full-width">
                          <div class="flex items-center">
                            <img
                              class="q-ml-md"
                              v-if="
                                CarrierService.getCarrierFromShippingProductCode(
                                  $store.state.courierStore.carriers,
                                  $store.state.courierStore.carrierProducts,
                                  method?.template?.carrier_product_code,
                                )?.carrier_id?.toLowerCase()
                              "
                              decoding="async"
                              style="
                                width: 20px;
                                height: 20px;
                                border-radius: 50%;
                                object-fit: cover;
                              "
                              :src="`${
                                CarrierService.getCarrierFromShippingProductCode(
                                  $store.state.courierStore.carriers,
                                  $store.state.courierStore.carrierProducts,
                                  method.template?.carrier_product_code,
                                )?.icon
                              }`"
                            />
                            <div class="text-brand text-weight-7 q-ml-sm">
                              {{ method.name }}
                            </div>
                          </div>
                          <div class="q-mr-lg text-weight-7 dark-text">
                            {{ PriceService.formatPrice(method.price) }}
                          </div>
                        </div>
                      </shipvagoo-radio>
                      <shipvagoo-separator />
                    </div>
                  </div>
                </div>
                <div class="col-md-3 col-12">
                  <div class="return-item-details q-my-sm">
                    <div class="dark-text text-sixteen text-weight-bold">Return details</div>
                    <shipvagoo-separator class="q-my-sm" />
                    <div>
                      <table class="full-width">
                        <tr>
                          <th class="dark-text text-twelve text-weight-7 text-left">
                            Product Name
                          </th>
                          <th class="dark-text text-twelve text-weight-7 text-right">Qty</th>
                        </tr>
                        <tr
                          v-for="(itemToBeReturned, index) of itemsToBeReturned"
                          :key="itemToBeReturned.quantity"
                        >
                          <td
                            class="dark-text text-twelve text-left q-pt-sm"
                            :class="index == 0 && 'q-pt-sm'"
                          >
                            <div class="text-thirteen">{{ itemToBeReturned?.title }}</div>
                            <div
                              class="text-eleven q-mt-xs"
                              v-if="
                                itemToBeReturned.variant &&
                                itemToBeReturned.variant?.title != 'Default Title'
                              "
                            >
                              <span>Variant: {{ itemToBeReturned?.variant?.title }}</span>
                            </div>
                            <div
                              class="text-eleven q-mt-xs"
                              v-if="itemToBeReturned.variant && itemToBeReturned.variant?.sku"
                            >
                              <span>SKU: {{ itemToBeReturned?.variant?.sku }}</span>
                            </div>
                          </td>
                          <td
                            class="dark-text text-twelve text-right q-pt"
                            :class="index == 0 && 'q-pt-sm'"
                          >
                            {{ returnItemQuantitySelections[itemToBeReturned.line_item_code] }}
                          </td>
                        </tr>
                      </table>
                    </div>
                    <div class="dark-text text-sixteen text-weight-bold q-mt-md">
                      Resolution method
                    </div>
                    <shipvagoo-separator class="q-my-sm" />
                    <div class="dark-text text-twelve">
                      {{ _resolution?.name }}
                    </div>
                    <div class="dark-text text-sixteen text-weight-bold q-mt-lg">
                      Return carrier
                    </div>
                    <shipvagoo-separator class="q-my-sm" v-if="_method" />
                    <div class="flex justify-between full-width q-py-xs" v-if="_method">
                      <div class="flex items-center">
                        <img
                          v-if="
                            CarrierService.getCarrierFromShippingProductCode(
                              $store.state.courierStore.carriers,
                              $store.state.courierStore.carrierProducts,
                              _method?.template?.carrier_product_code,
                            )?.carrier_id?.toLowerCase()
                          "
                          decoding="async"
                          style="width: 20px; height: 20px; border-radius: 50%; object-fit: cover"
                          :src="`${
                            CarrierService.getCarrierFromShippingProductCode(
                              $store.state.courierStore.carriers,
                              $store.state.courierStore.carrierProducts,
                              _method?.template?.carrier_product_code,
                            )?.icon
                          }`"
                        />
                        <div class="text-brand text-weight-7 q-ml-sm">
                          {{ _method?.name }}
                        </div>
                      </div>
                      <div class="text-weight-7 dark-text">
                        {{ PriceService.formatPrice(_method?.price) }}
                      </div>
                    </div>
                    <shipvagoo-separator class="q-my-sm" />
                    <shipvagoo-checkbox-v1 dense v-model="isReadTermsAndConditions">
                      <div class="dark-text text-left">
                        I accept the
                        <a
                          href="http://shipvagoo.com/virksomhed/vilkaar-og-betingelser"
                          target="_blank"
                          class="text-brand"
                          style="font-weight: 600"
                          @click.stop
                          >terms and conditions</a
                        >
                      </div>
                    </shipvagoo-checkbox-v1>
                  </div>
                </div>
              </div>
            </div>
          </q-step>
          <q-step :name="4" title="Confirmation" :done="step > 4">
            <div class="return-order-steps">
              <div class="row full-height" v-if="shippedItems.length > 0">
                <div class="col-md-9 col-12">
                  <div class="return-order-resolution-container q-my-sm">
                    <div
                      v-if="isShowSuccessPopup"
                      class="full-width full-height flex justify-center items-center"
                    >
                      <div class="flex items-center column">
                        <q-icon name="fas fa-check-circle" size="xl" color="green-brand "></q-icon>
                        <div
                          class="text-green-brand text-sixteen text-weight-7 q-mt-sm text-center q-mt-md"
                        >
                          Your return has been successfully created
                        </div>
                        <div class="dark-text text-thirteen q-mt-sm text-center">
                          Make sure to download the return label.
                        </div>
                        <div class="dark-text text-thirteen q-mt-sm text-center">
                          The return label has also been sent to {{ form.email }}.
                        </div>
                        <shipvagoo-button
                          height="36px"
                          style="min-height: 36px"
                          no-caps
                          class="q-mt-lg"
                          @click="onClickDownload"
                          label="Download return label"
                        />
                      </div>
                    </div>
                    <div
                      v-if="isShowFailurePopup"
                      class="full-width full-height flex justify-center items-center"
                    >
                      <div class="flex items-center column">
                        <q-icon name="fas fa-check-circle" size="xl" color="danger"></q-icon>
                        <div
                          class="text-danger text-sixteen text-weight-7 q-mt-sm text-center q-mt-md"
                        >
                          Sorry something went wrong
                        </div>
                        <div class="dark-text text-thirteen q-mt-sm text-center">
                          <span v-html="compErrorMessage"></span>
                        </div>
                        <shipvagoo-button
                          height="36px"
                          style="min-width: 140px; min-height: 36px"
                          no-caps
                          class="q-mt-lg"
                          outline
                          @click="onClickOk"
                          label="OK"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-3 col-12">
                  <div class="return-item-details q-my-sm">
                    <div class="dark-text text-sixteen text-weight-bold">Return details</div>
                    <shipvagoo-separator class="q-my-sm" />
                    <div>
                      <table class="full-width">
                        <tr>
                          <th class="dark-text text-twelve text-weight-7 text-left">
                            Product Name
                          </th>
                          <th class="dark-text text-twelve text-weight-7 text-right">Qty</th>
                        </tr>
                        <tr
                          v-for="(itemToBeReturned, index) of itemsToBeReturned"
                          :key="itemToBeReturned.quantity"
                        >
                          <td
                            class="dark-text text-twelve text-left q-pt-sm"
                            :class="index == 0 && 'q-pt-sm'"
                          >
                            <div class="text-thirteen">{{ itemToBeReturned?.title }}</div>
                            <div
                              class="text-eleven q-mt-xs"
                              v-if="
                                itemToBeReturned.variant &&
                                itemToBeReturned.variant?.title != 'Default Title'
                              "
                            >
                              <span>Variant: {{ itemToBeReturned?.variant?.title }}</span>
                            </div>
                            <div
                              class="text-eleven q-mt-xs"
                              v-if="itemToBeReturned.variant && itemToBeReturned.variant?.sku"
                            >
                              <span>SKU: {{ itemToBeReturned?.variant?.sku }}</span>
                            </div>
                          </td>
                          <td
                            class="dark-text text-twelve text-right q-pt"
                            :class="index == 0 && 'q-pt-sm'"
                          >
                            {{ returnItemQuantitySelections[itemToBeReturned.line_item_code] }}
                          </td>
                        </tr>
                      </table>
                    </div>
                    <div class="dark-text text-sixteen text-weight-bold q-mt-md">
                      Resolution method
                    </div>
                    <shipvagoo-separator class="q-my-sm" />
                    <div class="dark-text text-twelve">
                      {{ _resolution?.name }}
                    </div>
                    <div class="dark-text text-sixteen text-weight-bold q-mt-lg">
                      Return carrier
                    </div>
                    <shipvagoo-separator class="q-my-sm" v-if="_method" />
                    <div class="flex justify-between full-width q-py-xs" v-if="_method">
                      <div class="flex items-center">
                        <img
                          v-if="
                            CarrierService.getCarrierFromShippingProductCode(
                              $store.state.courierStore.carriers,
                              $store.state.courierStore.carrierProducts,
                              _method?.template?.carrier_product_code,
                            )?.carrier_id?.toLowerCase()
                          "
                          decoding="async"
                          style="width: 20px; height: 20px; border-radius: 50%; object-fit: cover"
                          :src="`${
                            CarrierService.getCarrierFromShippingProductCode(
                              $store.state.courierStore.carriers,
                              $store.state.courierStore.carrierProducts,
                              _method?.template?.carrier_product_code,
                            )?.icon
                          }`"
                        />
                        <div class="text-brand text-weight-7 q-ml-sm">
                          {{ _method?.name }}
                        </div>
                      </div>
                      <div class="text-weight-7 dark-text">
                        {{ PriceService.formatPrice(_method?.price) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </q-step>
          <template #navigation>
            <q-stepper-navigation class="full-width">
              <shipvagoo-separator class="q-mt-sm" v-if="step < 4" />
              <div class="full-width flex justify-between q-mt-lg">
                <shipvagoo-button
                  style="width: 100px"
                  color="brand"
                  v-if="step <= 3"
                  @click="onClickPrevious"
                  label="Previous"
                  outline
                  no-caps
                />
                <shipvagoo-button
                  style="width: 100px"
                  color="brand"
                  v-if="step <= 3"
                  label="Next"
                  no-caps
                  @click="onClickNext"
                />
              </div>
            </q-stepper-navigation>
          </template>
        </shipvagoo-stepper-v1>
      </div>
    </div>
    <q-dialog
      v-if="link"
      :square="false"
      maximized
      @hide="closePaymentModal"
      :model-value="openMakePaymentModal"
      no-backdrop-dismiss
      persistent
    >
      <make-payment :link="link" title="Make payment"></make-payment>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
  import axios, { AxiosError, AxiosResponse } from 'axios';
  import { computed, onBeforeMount, onBeforeUnmount, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useHelper } from '../composeable/Helper';
  import * as _ from 'lodash-es';
  import {
    IReturnOrder,
    IReturnOrderRequest,
    IReturnPortalMethod,
    IReturnPortalReason,
    IReturnPortalResolution,
    IVerifyPortal,
    IVerifyPortalRequest,
  } from '../model/return-portal.model';
  import {
    loadingService,
    loggingService,
    notificationService,
    authService,
    shipmentService,
    transactionService,
  } from '../services/_singletons';
  import { useStore } from '../store';
  import useVuelidate, { ValidationArgs } from '@vuelidate/core';
  import { helpers, required } from '@vuelidate/validators';
  import OrderService from '../services/order.service';
  import { IOrder, IOrderProduct } from '../model/order.model';
  import HttpService from '../services/http.service';
  import CarrierService from '../services/carrier.service';
  import PriceService from '../services/price.service';
  import HelperService from '../services/helper.service';
  import WindowService from '../services/window.service';
  import { IVerifyPaymentResponse } from '../model/transactions.model';
  import { IShipment } from '../model/shipment.model';

  const $route = useRoute();
  const $router = useRouter();
  const $store = useStore();
  const $axios = axios.create({
    baseURL: import.meta.env.VITE_baseUrl,
  });
  const { mustBeValidEmail } = useHelper();
  interface IForm {
    email: string | null;
    order_number: string | null;
  }

  interface IStepForm {
    step1: {
      reasons: any;
    };
    step2: {
      resolution: number | null;
      resolution_comment: string | null;
    };
    step3: {
      method: number | null;
    };
  }

  /** ref properties **/
  //const ref_label_link = ref<any>(null);
  const errorMessage = ref<string | null>(null);
  const isShowFailurePopup = ref<boolean>(false);
  const isShowSuccessPopup = ref<boolean>(false);
  const openMakePaymentModal = ref<boolean>(false);
  const paymentId = ref<string | null>(null);
  const returnOrderCode = ref<string | null>(null);
  const labelLink = ref<string>('#');
  const interval = ref<any>(null);
  const link = ref<string>('');
  const parent_step = ref<number>(1);
  const form = ref<IForm>({
    email: '',
    order_number: '',
  });
  const order = ref<IOrder | null>(null);
  const reasons = ref<IReturnPortalReason[]>([]);
  const resolutions = ref<IReturnPortalResolution[]>([]);
  const methods = ref<IReturnPortalMethod[]>([]);
  const saved_return_order_code = ref<number>(1);
  const returnItemsChecks = ref<{ [key: number]: boolean }>({});
  const returnItemQuantitySelections = ref<{ [key: number]: number }>({});
  const verifiedPortal = ref<IVerifyPortal | null>(null);
  const reason_errors = ref<any>([]);
  const step = ref<number>(1);
  const isReadTermsAndConditions = ref(false);
  const shipment = ref<IShipment | null>(null);

  const $v = useVuelidate<IForm, ValidationArgs<IForm>>(
    {
      order_number: {
        required: helpers.withMessage('This field is required', required),
        /*  
        integer: helpers.withMessage('This field must be an integer', (value: string) => {
          return Number.isInteger(Number(value));
        }),
        positive: helpers.withMessage('This field must be positive', (value: string) => {
          return Number(value) > 0;
        }), */
      },
      email: {
        required: helpers.withMessage('This field is required', required),
        email: helpers.withMessage('This field must be a valid email.', (value: any) =>
          mustBeValidEmail(value, null),
        ),
      },
    },
    form,
  );

  const stepForm = ref<IStepForm>({
    step1: {
      reasons: [],
    },
    step2: {
      resolution: null,
      resolution_comment: null,
    },
    step3: {
      method: null,
    },
  });

  /** computed properties **/

  /* const disabledNext = computed(() => {
    let disabled = false;
    if (step.value == 1) {
      disabled = itemsToBeReturned.value.length <= 0;
    }
    return disabled;
  }); */
  const compErrorMessage = computed(() => {
    const message = `Return order was not created.<br />Please contact ${verifiedPortal.value?.company_name}`;
    if (errorMessage.value == null) {
      return message;
    } else {
      return errorMessage.value;
    }
  });
  const showResolutionComment = computed(() => {
    let show_comment = false;
    const resolution = resolutions.value.find(
      (resolution) => resolution.resolution_code == stepForm.value.step2.resolution,
    );
    if (resolution && resolution.name.toLowerCase() == 'item exchange') {
      show_comment = true;
    }
    return show_comment;
  });
  const _method = computed<IReturnPortalMethod | null>(() => {
    return _.find(methods.value, ['method_code', stepForm.value.step3.method]) ?? null;
  });
  const _resolution = computed<IReturnPortalResolution | null>(() => {
    return _.find(resolutions.value, ['resolution_code', stepForm.value.step2.resolution]) ?? null;
  });
  /*const totalRemainingQuantity = computed(() => {
  if (!order.value) {
    return 0;
  }

  const fulfilledQuantity = order.value.fulfilled_quantity_sum ?? 0;
  const returnedQuantity = order.value.returned_quantity ?? 0;
  const quantity = fulfilledQuantity - returnedQuantity;
  return quantity > 0 ? quantity : 0;
});*/
  const shippedItems = computed<IOrderProduct[]>(() => {
    if (!order.value || !order.value.fulfillments) {
      return [];
    }

    let fulfilledItems = OrderService.getFulfilledItems(
      order.value.fulfillments.filter((f) => f.shipment_code),
      order.value.line_items,
    );

    fulfilledItems.forEach((fi) => {
      //if (fi.returned_quantity != null) fi.quantity -= fi.returned_quantity
      /*order.value?.return_orders?.forEach(ro => {
      if (ro.return_order_items != null)
        (ro.return_order_items.filter(ri => ri.line_item_code == fi.line_item_code)).forEach(ri => {
          console.log("Line Item ", fi.line_item_code)
          console.log("RO Item ", ri.line_item_code)
          fi.quantity -= ri.quantity
        })
    })*/
      /** manually created by merchant **/
      /** commented 23-2-24 **/
      /*order.value?.fulfillment_returns?.filter((item: any) => item.return_order_code == null);//.forEach((item1: any) => fi.quantity -= item1.quantity);

    const return_order_codes: any = [];
    order.value?.fulfillment_returns?.filter((item: any) => item.return_order_code != null).forEach((item1: any) => {
      return_order_codes.push(item1.return_order_code);
    });

    order.value?.return_orders?.forEach(ro => {
      if (return_order_codes.includes(ro.return_order_items)) {
        /!** commented 23-2-24 **!/
        /!*const quantity = order.value?.fulfillment_returns?.find((item: any) => item.return_order_code == ro.return_order_code)?.quantity ?? 0;
        fi.quantity -= quantity;*!/
      } else {
        if (ro.return_order_items) {
          /!** commented 23-2-24 **!/
          (ro.return_order_items.filter(ri => ri.line_item_code == fi?.line_item_code));//.forEach(ri => {fi.quantity -= ri.quantity})
        }
      }
    });*/
      //   const fulFillmentsWithoutReturnOrder=
      /*order.value?.fulfillment_returns?.forEach(fr => {
      if (fr.line_item_code == fi.line_item_code) {
        fi.quantity -= fr.quantity
        console.log("Quantity ", fi.quantity)
      }
    })*/
    });

    fulfilledItems = fulfilledItems.filter((fi) => fi.quantity > 0);

    /** these lines for multi select reasons **/
    stepForm.value.step1.reasons = fulfilledItems.map(() => {
      return { reason: null, comment: null };
    });
    reason_errors.value = fulfilledItems.map((item, index) => {
      return { is_comment_error: false, is_reason_error: false };
    });
    /** end comment **/

    return fulfilledItems.filter(
      (item: IOrderProduct) => item.quantity - (item.calculated_returned_quantity ?? 0) > 0,
    );
  });

  const itemsToBeReturned = computed<IOrderProduct[]>(() => {
    return shippedItems.value.filter((item) => {
      return returnItemsChecks.value[item.line_item_code];
    });
  });

  /** methods start here **/
  const resetAllForms = () => {
    form.value.email = null;
    form.value.order_number = null;
    stepForm.value.step1.reasons = [];
    stepForm.value.step2.resolution = null;
    stepForm.value.step3.method = null;
    isReadTermsAndConditions.value = false;
    isShowFailurePopup.value = false;
    isShowSuccessPopup.value = false;
  };
  const onClickOk = () => {
    $v.value.$reset();
    resetLinkAndInterval();
    resetAllForms();
    parent_step.value = 1;
    step.value = 1;
  };
  const onChangeReturnMethod = () => {
    resetLinkAndInterval();
  };
  const onChangeResolution = () => {
    stepForm.value.step2.resolution_comment = null;
    resetLinkAndInterval();
  };

  const resetLinkAndInterval = () => {
    link.value = '';
    if (interval.value != null) {
      clearInterval(interval.value);
    }
  };
  const verifyPortal = async () => {
    try {
      loadingService.startLoading('main-loader:verify-portal');
      const response = await $axios.post<IVerifyPortalRequest, AxiosResponse<IVerifyPortal>>(
        '/verify-portal',
        {
          portal_name: $route.params.webshopName,
        },
      );

      verifiedPortal.value = response.data;
      if (verifiedPortal.value.logo !== null)
        verifiedPortal.value.logo =
          import.meta.env.VITE_s3_baseUrl + '/company_logos/' + verifiedPortal.value.logo;
      _.set(
        $axios.defaults,
        'headers.Authorization',
        `Bearer ${verifiedPortal.value.access_token}`,
      );
      authService.setSessionStorageToken(verifiedPortal.value.user_access_token);
    } catch (e) {
      loggingService.error('Error while verifying the portal', e);
      notificationService.showError('Error while verifying the portal');
      $router.push('/not-found');
    } finally {
      loadingService.stopLoading('main-loader:verify-portal');
    }
  };

  const onClickFindOrder = async () => {
    resetLinkAndInterval();
    await $v.value.$validate();
    if ($v.value.$error || !form.value.email || !form.value.order_number) {
      return;
    }
    if (!$axios) {
      throw new Error('Axios is not defined');
    }

    try {
      loadingService.startLoading('main-loader:find-order');

      const response = await $axios.get<IOrder>(
        `/returns/order${HttpService.generateFilter([
          { key: 'order_number', value: encodeURIComponent(form.value.order_number) },
          { key: 'email', value: form.value.email },
        ])}`,
      );

      const reasonsResponse = await $axios.get<IReturnPortalReason[]>(`/returns/return-reasons`);

      order.value = response.data;
      /*if (!order.value.fulfillments) {
      notificationService.showError("Unable to create a return for this order. Please contact the webshop owner");
      return;
    }*/
      if (shippedItems.value.length <= 0) {
        notificationService.showError(
          'Unable to create a return for this order. Please contact the webshop owner',
        );
        return;
      }
      reasons.value = reasonsResponse.data.filter((item) => item.status);

      returnItemsChecks.value = {};
      returnItemQuantitySelections.value = {};

      const lineItemCodes: number[] = shippedItems.value.map((i) => i.line_item_code);

      lineItemCodes.forEach((code: number) => {
        returnItemsChecks.value[code] = false;
        returnItemQuantitySelections.value[code] = 1;
      });
      parent_step.value = 2;
    } catch (e: any) {
      if (e instanceof AxiosError && e.response?.status === 422) {
        if (e.response.data.error.order_number) {
          notificationService.showError(e.response.data.error.order_number);
        } else {
          notificationService.showError(
            'The order could not be found based on the provided information',
          );
        }
      } else {
        if (e.response?.data?.error) {
          notificationService.showError(e.response?.data?.error);
        } else {
          notificationService.showError(
            'The order could not be found based on the provided information',
          );
        }
      }
    } finally {
      loadingService.stopLoading('main-loader:find-order');
    }
  };
  const onClickPrevious = () => {
    resetLinkAndInterval();
    if (step.value == 1) {
      parent_step.value = 1;
    } else {
      step.value = step.value - 1;
    }
  };
  const onClickNext = () => {
    if (step.value == 1) {
      onClickContinue();
    } else if (step.value == 2) {
      if (!stepForm.value.step2.resolution) {
        notificationService.showError('Please select a resolution method.');
      } else if (showResolutionComment.value && !stepForm.value.step2.resolution_comment) {
        notificationService.showError('Please enter a comment for resolution method.');
      } else {
        step.value = 3;
      }
    } else if (step.value == 3) {
      if (!stepForm.value.step3.method) {
        notificationService.showError('Please select a return carrier.');
      } else if (!isReadTermsAndConditions.value) {
        notificationService.showError('Please accept the terms and conditions.');
      } else {
        onClickReturnOrder();
      }
    }
  };
  const returnOrder = async () => {
    if (
      !order.value ||
      !stepForm.value.step2.resolution ||
      !stepForm.value.step3.method ||
      !stepForm.value.step1.reasons
    ) {
      return;
    }

    if (!$axios) {
      throw new Error('Axios is not defined');
    }
    resetLinkAndInterval();
    try {
      loadingService.startLoading('main-loader:return-order');
      // let persistentNotification = _.noop;

      let url: string = '/returns/order/:id/return';
      url = url.replace(':id', order.value.order_code.toString());

      const method = _method.value;
      const resolution = _resolution.value;

      if (!method || !resolution) {
        throw new Error('Method or resolution not found');
      }

      const line_items = itemsToBeReturned.value.map((item) => {
        const shippedItemIndex = shippedItems.value.findIndex(
          (row) => row.line_item_code == item.line_item_code,
        );
        let reason: string | null = null;
        let comment: string | null = null;
        if (shippedItemIndex > -1) {
          reason = stepForm.value.step1.reasons[shippedItemIndex]?.reason?.description;
          comment = stepForm.value.step1.reasons[shippedItemIndex]?.comment;
        }
        return {
          line_item_code: item.line_item_code,
          quantity: returnItemQuantitySelections.value[item.line_item_code],
          return_reason: reason,
          comments: comment,
        };
      });
      const response = await $axios.post<IReturnOrder>(url, {
        order_code: order.value.order_code,
        method_code: method.method_code,
        //  return_reason_code: form.value.step2.returnReason.return_reason_code,
        resolution_code: resolution.resolution_code,
        resolution_comment: stepForm.value.step2.resolution_comment,
        transaction_status: 'PROCESSING',
        line_items: line_items,
      } as IReturnOrderRequest);
      shipment.value = response.data.shipment;
      returnOrderCode.value = response.data.return_order_code.toString();
      if (method.price <= 0) {
        await calculateChargeAndGenerateLabel();
        /*shipmentService.calculateShipmentCharge(response.data.shipment.shipment_code)
        .then(charge => {
          shipmentService.makeReturnShippingPayment(response.data.shipment.shipment_code, charge.totals.after_vat.amount)
            .then(res => {
              saved_return_order_code.value = response.data.return_order_code;
              generateLabel(response.data.return_order_code)
                .finally(() => {
                  loadingService.stopLoading("main-loader:return-order");
                });
            })
            .catch(e => {
              notificationService.showError("Something went wrong. Try Again!");
              loadingService.stopLoading("main-loader:return-order");
            });
        })
        .catch(e => {
          notificationService.showError("Something went wrong. Try Again!");
          loadingService.stopLoading("main-loader:return-order");
        })
        .finally(() => {
          loadingService.stopLoading("main-loader:return-order");
        });*/
      } else {
        loadingService.stopLoading('main-loader:return-order');
        link.value = response.data.payment_url;
        openMakePaymentModal.value = true;
        paymentId.value = response.data.payment_id ?? null;
        /* interval.value = setInterval(async () => {
         const returnOrder = await getReturnOrder();
         if (openMakePaymentModal.value) {
           return;
         } else {
           if (returnOrder?.data.transaction_status == "PROCESSING") {
             return;
           }
         }
         
         loadingService.startLoading("main-loader:return-order");
         clearInterval(interval.value);
 
         if (returnOrder?.data.transaction_status == "CANCELLED") {
           notificationService.showError("Payment Failed. Try Again!");
           loadingService.stopLoading("main-loader:return-order");
           step.value = 3;
         } else if (returnOrder?.data.transaction_status == "COMPLETED") {
           console.log("transactions status completed");
           shipmentService.calculateShipmentCharge(response.data.shipment.shipment_code)
             .then(charge => {
               shipmentService.makeReturnShippingPayment(response.data.shipment.shipment_code, charge.totals.after_vat.amount)
                 .then(res => {
                   saved_return_order_code.value = response.data.return_order_code;
                   generateLabel(response.data.return_order_code)
                     .finally(() => {
                       loadingService.stopLoading("main-loader:return-order");
                     });
                 })
                 .catch(e => {
                   step.value = 4;
                   isShowFailurePopup.value = true;
                   notificationService.showError(e.response?.data?.error);
                   loadingService.stopLoading("main-loader:return-order");
                 });
             })
             .catch(e => {
               step.value = 4;
               isShowFailurePopup.value = true;
               notificationService.showError(e.response?.data?.error);
               loadingService.stopLoading("main-loader:return-order");
             });
         } else {
           step.value = 3;
           isShowFailurePopup.value = true;
           notificationService.showError("Something went wrong. Try Again!");
           loadingService.stopLoading("main-loader:return-order");
         }
         // }
       }, 2000);*/
      }
    } catch (e) {
      /*
    loggingService.error('Error while returning order', e);*/

      loadingService.stopLoading('main-loader:return-order');

      if (e instanceof AxiosError && e.response?.data?.error) {
        notificationService.showError(e.response?.data?.error);
      } else {
        notificationService.showError('Error while returning order');
      }
    } finally {
      loadingService.stopLoading('main-loader:return-order');
      console.log(loadingService.loadings);
    }
  };
  const verifyPayment = async (_payment_id: number | string | null, type: string) => {
    try {
      loadingService.startLoading(`main-loader:get-transactions-add-funds-${type}`);
      const timeInterval = 5;
      const timeOut = 60;
      let currentTime = 0;
      let isApiCallInProgress = false;
      if (type == 'return') {
        notificationService.showError(
          'Loading your page. Please hold on for a moment while we verify if something went wrong.',
        );
      }
      interval.value = setInterval(async () => {
        if (!isApiCallInProgress && _payment_id) {
          if (currentTime % 10 === 0 && currentTime < timeOut && type == 'return') {
            notificationService.showError(
              'Loading your page. Please hold on for a moment while we verify if something went wrong.',
            );
          }
          try {
            isApiCallInProgress = true;
            const verifyPayment: IVerifyPaymentResponse = await transactionService.verifyPayment({
              payment_id: _payment_id,
              type: type,
            });
            isApiCallInProgress = false;
            if (
              verifyPayment.p_status == 'success' ||
              verifyPayment.p_status == 'error' ||
              currentTime >= timeOut
            ) {
              clearInterval(interval.value);
              if (verifyPayment.p_status == 'success') {
                if (type == 'return') {
                  notificationService.showSuccess('Payment made successfully.');
                  await calculateChargeAndGenerateLabel();
                } else if (type == 'refund') {
                  //notificationService.showSuccess(verifyPayment.message ?? "Payment refunded successfully.");
                  errorMessage.value =
                    'We apologize for the inconvenience. Your payment was refunded due to a <br/>label generation error. Please verify your shipment details.';
                  step.value = 4;
                  isShowFailurePopup.value = true;
                }
                loadingService.stopLoading(`main-loader:get-transactions-add-funds-${type}`);
              } else if (verifyPayment.p_status == 'error') {
                if (type == 'return') {
                  notificationService.showError(
                    verifyPayment.message ?? 'Error while making payment.',
                  );
                } else if (type == 'refund') {
                  errorMessage.value =
                    'We regret to inform you that there has been an issue with creating a return<br/>shipment for your order. The return cost has been deducted and we <br/> attempted to refund the amount but unfortunately this was not possible due<br/> to a technical issue.<br/><br/>Please contact us to begin the return process and include the order details.<br/> We understand the inconvenience this may have and sincerely apologize.';
                }
                step.value = 4;
                isShowFailurePopup.value = true;
              } else {
                step.value = 4;
                isShowFailurePopup.value = true;
              }
              loadingService.stopLoading(`main-loader:get-transactions-add-funds-${type}`);
            }
            currentTime = currentTime + timeInterval;
          } catch (error: any) {
            loadingService.stopLoading(`main-loader:get-transactions-add-funds-${type}`);
            step.value = 4;
            isShowFailurePopup.value = true;
            throw new Error(error.response.data.error);
          }
        } else {
          loadingService.stopLoading(`main-loader:get-transactions-add-funds-${type}`);
          clearInterval(interval.value);
        }
      }, timeInterval * 1000);
    } catch (error: any) {
      step.value = 4;
      isShowFailurePopup.value = true;
      loadingService.stopLoading(`main-loader:get-transactions-add-funds-${type}`);
      throw new Error(error.response.data.error);
    } /*finally {loadingService.stopLoading(`main-loader:get-transactions-add-funds-${type}`);
  }*/
  };

  const calculateChargeAndGenerateLabel = async () => {
    try {
      loadingService.startLoading('main-loader:generate-label');

      if (!shipment.value || !returnOrderCode.value) {
        return;
      }
      const shipmentCode = shipment.value.shipment_code;
      const charge = await shipmentService.calculateShipmentCharge(shipmentCode);
      const paymentRes = await shipmentService.makeReturnShippingPayment(
        shipmentCode,
        charge.totals.after_vat.amount,
      );
      if (paymentRes) {
        saved_return_order_code.value = Number(returnOrderCode.value);
        await generateLabel(returnOrderCode.value);
      }
    } catch (error: any) {
      step.value = 4;
      isShowFailurePopup.value = true;
      const errorMsg = error.response?.data?.error || 'An error occurred';
      notificationService.showError(errorMsg);
    } finally {
      loadingService.stopLoading('main-loader:generate-label');
      loadingService.stopLoading('main-loader:return-order');
    }
  };

  /*const calculateChargeAndGenerateLabel = async () => {
  try {
    loadingService.startLoading("main-loader:generate-label");
    if (shipment.value && returnOrderCode.value) {
      await shipmentService.calculateShipmentCharge(shipment.value.shipment_code)
        .then(async charge => {
          if (shipment.value && returnOrderCode.value) {
            await shipmentService.makeReturnShippingPayment(shipment.value.shipment_code, charge.totals.after_vat.amount)
              .then(res => {
                if (res && returnOrderCode.value) {
                  saved_return_order_code.value = Number(returnOrderCode.value);
                  generateLabel(returnOrderCode.value)
                    .finally(() => {
                      loadingService.stopLoading("main-loader:return-order");
                    });
                }
              })
              .catch(e => {
                step.value = 4;
                isShowFailurePopup.value = true;
                notificationService.showError(e.response?.data?.error);
                loadingService.stopLoading("main-loader:return-order");
              });
          }
        })
        .catch(e => {
          step.value = 4;
          isShowFailurePopup.value = true;
          notificationService.showError(e.response?.data?.error);
          loadingService.stopLoading("main-loader:return-order");
        });
    }
  } catch (error) {

  } finally {
    loadingService.stopLoading("main-loader:generate-label");
    loadingService.stopLoading("main-loader:return-order");
  }
};*/
  /*const getReturnOrder = async () => {
  if (returnOrderCode.value !== null) {
    if (!$axios) {
      throw new Error("Axios is not defined");
    }
    let url: string = "/returns/order/:id";
    url = url.replace(":id", returnOrderCode.value);
    return await $axios.get<IReturnOrder>(url);
  } else {
    notificationService.showError("Something went wrong. Reload Page!");
  }
};*/
  const generateLabel = async (responseOrderCode: string) => {
    try {
      if (!$axios) {
        throw new Error('Axios is not defined');
      }

      loadingService.startLoading('main-loader:generate-label');
      let url: string = '/returns/order/:id/generate-label';
      url = url.replace(':id', responseOrderCode);

      const response = await $axios.post<{ label: string }>(url);

      const blob: Blob = await HelperService.toBlob(
        `data:application/pdf;base64,${encodeURI(response.data.label)}`,
      );

      // windowRef.location = URL.createObjectURL(blob);
      step.value = 4;
      labelLink.value = URL.createObjectURL(blob);
      WindowService.openWindow('Return Portal Shipping Label', URL.createObjectURL(blob));
      isShowSuccessPopup.value = true;
    } catch (e) {
      await verifyPayment(paymentId.value, 'refund');
      step.value = 4;
      isShowFailurePopup.value = true;
      loggingService.error('Error while generating label', e);
      notificationService.showError('Error while generating label');
    } finally {
      loadingService.stopLoading('main-loader:generate-label');
    }
  };

  const onClickReturnOrder = () => {
    if (
      !order.value ||
      !stepForm.value.step2.resolution ||
      (showResolutionComment.value && !stepForm.value.step2.resolution_comment) ||
      !stepForm.value.step3.method ||
      !stepForm.value.step1.reasons
    ) {
      return;
    }
    returnOrder();
  };
  const onClickContinue = async () => {
    reason_errors.value = [];
    let error_exist: boolean = false;
    shippedItems.value.forEach((item, index) => {
      const found = itemsToBeReturned.value.find(
        (row) => row.line_item_code == item.line_item_code,
      );
      //form.value.step2.reasons[index].reason?.enable_comment === 1
      //form.value.step2.reasons[index].reason
      if (found) {
        if (stepForm.value.step1.reasons[index].reason == null) {
          error_exist = true;
          reason_errors.value[index] = {
            is_reason_error: true,
            is_comment_error: false,
          };
        } else if (
          stepForm.value.step1.reasons[index].reason?.enable_comment === 1 &&
          stepForm.value.step1.reasons[index].reason.comment_required == 1 &&
          (stepForm.value.step1.reasons[index].comment == '' ||
            stepForm.value.step1.reasons[index].comment == null)
        ) {
          error_exist = true;
          reason_errors.value[index] = {
            is_reason_error: false,
            is_comment_error: true,
          };
        } else {
          reason_errors.value[index] = {
            is_reason_error: false,
            is_comment_error: false,
          };
        }
      } else {
        reason_errors.value[index] = {
          is_reason_error: false,
          is_comment_error: false,
        };
      }
    });
    console.log('error exist ', error_exist);
    if (error_exist) {
      return;
    }

    if (!$axios) {
      throw new Error('Axios is not defined');
    }

    try {
      loadingService.startLoading('main-loader:methods-and-resolutions');

      const resolutionsResponse = await $axios.get<IReturnPortalResolution[]>(
        `/returns/resolutions`,
      );
      const formData = new FormData();
      itemsToBeReturned.value.forEach((item, index) => {
        formData.append(`line_items[${index}][line_item_code]`, item.line_item_code.toString());
        formData.append(
          `line_items[${index}][quantity]`,
          returnItemQuantitySelections.value[item.line_item_code].toString(),
        );
      });
      const methodsResponse = await $axios.post<IReturnPortalMethod[]>(
        `/returns/methods?with=template`,
        formData,
      );

      resolutions.value = resolutionsResponse.data.filter((item) => item.status);
      methods.value = methodsResponse.data;
      step.value = 2;
    } catch (e: any) {
      if (e instanceof AxiosError && e.response?.status !== 422) {
        notificationService.showError(e.response?.data?.error);
      } else {
        loggingService.error('Error while getting methods and resolutions', e);
        notificationService.showError('Error while getting methods and resolutions');
      }
    } finally {
      loadingService.stopLoading('main-loader:methods-and-resolutions');
    }
  };
  const onClickDownload = () => {
    window.open(labelLink.value, '_blank');
  };
  const closePaymentModal = async () => {
    openMakePaymentModal.value = false;
    /* loadingService.startLoading("main-loader:return-order");
   setTimeout(() => {
     loadingService.stopLoading("main-loader:return-order");
   }, 5000);*/
    if (paymentId.value) {
      await verifyPayment(paymentId.value, 'return');
    }
  };
  onBeforeUnmount(() => {
    resetLinkAndInterval();
  });
  onBeforeMount(async () => {
    if (!$route.params.webshopName || $route.params.webshopName === '') {
      return;
    }
    await verifyPortal();
    await $store.dispatch('courierStore/getCarriers');
    await $store.dispatch('courierStore/getCarrierProducts');
  });
</script>

<style scoped lang="css">
  .customer-return-page {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100vh;
  }

  .start-return-form {
    width: 50vw;
    height: 75vh;
    border: 1px solid #7d839833;
    border-radius: 8px;
  }

  .return-order-steps-container {
    min-width: 60vw;
    height: 80vh;
    margin: 10px;
    border: 1px solid #7d839833;
    border-radius: 8px;
  }

  .start-page-form-left {
    background-image: url('../assets/img/return/external_return_bg.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    border-radius: 8px 0 0 8px;
  }

  .return-form-grids {
    display: flex;
    flex-direction: column;
    justify-content: end;
  }

  .return-form-child-grids {
    flex: 1;
    width: 100%;
  }

  .return-order-item {
    width: 100%;
    min-height: 170px;
    padding: 10px;
    border: 1px solid rgba(125, 131, 152, 0.2);
    border-radius: 5px;
  }

  .return-order-item-image {
    max-width: 135px;
    max-height: 135px;
    object-fit: contain;
  }

  .return-item-details {
    height: 95% !important;
    padding: 15px;
    margin-left: 10px;
    background-color: #f7f7f7;
    border: 1px solid #7d839833;
    border-radius: 5px;
  }

  .reason-select {
    width: 250px;
  }

  .comment-textarea {
    box-sizing: border-box;
    width: 250px;
    height: 85px;
    padding: 10px;
    resize: none;
    border-color: #757575;
    border-radius: 5px;
  }

  .return-order-resolution-container {
    height: 95%;
    padding: 15px 20px;
    border: 1px solid rgba(125, 131, 152, 0.2);
    border-radius: 5px;
  }

  .checked-radio {
    background-color: #f7f7f7;
    border-radius: 4px 4px 0 0;
  }

  /* Mobile Devices (Portrait) */
  @media only screen and (max-width: 767px) {
    /* Your styles for small screens go here */
    .start-page-form-left {
      display: none;
    }

    .start-return-form {
      min-width: 95vw;
      min-height: 80vh;
    }

    .return-order-steps-container {
      min-width: 95vw;
      min-height: 90vh;
    }

    .return-item-details {
      margin: 5px 0;
    }

    .comment-textarea {
      width: 300px;
    }

    .reason-select {
      width: 300px;
    }
  }

  /* Mobile Devices (Landscape) */
  @media only screen and (min-width: 768px) and (max-width: 1023px) {
    .start-page-form-left {
      display: none;
    }

    .return-item-details {
      margin: 5px 0;
    }

    .comment-textarea {
      max-width: 400px;
    }

    .reason-select {
      max-width: 400px;
    }

    .start-return-form {
      min-width: 90vw;
      min-height: 80vh;
    }

    .return-order-steps-container {
      min-width: 90vw;
      min-height: 85vh;
    }
  }

  /* Tablets */
  @media only screen and (min-width: 1024px) and (max-width: 1199px) {
    /* Your styles for larger screens (tablets) go here */
    .start-return-form {
      min-width: 80vw;
      min-height: 75vh;
    }

    .return-order-steps-container {
      min-width: 80vw;
      min-height: 85vh;
    }
  }

  /* Desktops */
  @media only screen and (min-width: 1200px) and (max-width: 1439px) {
    /** my laptop **/
    .start-return-form {
      min-width: 65vw;
      min-height: 75vh;
    }

    .return-order-steps-container {
      min-width: 80vw;
      min-height: 85vh;
    }
  }

  /* Large Desktops */
  @media only screen and (min-width: 1440px) {
    /* Your styles for larger desktop screens go here */
  }

  /* High-Resolution Displays (Retina, etc.) */
  @media only screen and (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Your styles for high-resolution displays go here */
  }
</style>
<style lang="scss">
  .return-order-modal-stepper {
    &.q-stepper--horizontal {
      display: flex !important;
      flex-direction: column !important;
      align-content: space-between !important;
      align-items: center;
      height: 97% !important;
      margin: 0 10px 10px 10px !important;

      .q-stepper__tab {
        width: 150px !important;

        .q-stepper__title {
          white-space: nowrap !important;
        }
      }

      .q-stepper__header {
        flex: 0.5 !important;
        justify-items: center !important;
        justify-self: center !important;
        min-width: 600px !important;
      }

      .q-stepper__content {
        flex: 9 !important;
        width: 100% !important;
        padding: 0 !important;

        .q-stepper__step-content {
          height: 100% !important;

          .q-stepper__step-inner {
            height: 100% !important;

            .return-order-steps {
              height: 95% !important;
            }
          }
        }
      }

      .q-stepper__step-inner {
        padding: 0 10px !important;
      }

      .q-stepper__nav {
        flex: 0.5 !important;
        padding: 0 10px 0 10px !important;
      }
    }
  }

  .shipvagoo-radio-method {
    .q-radio__label {
      width: 100% !important;
    }
  }
</style>
