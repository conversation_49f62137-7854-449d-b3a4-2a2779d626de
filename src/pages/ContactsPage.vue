<template>
  <div class="flex justify-between items-center title-bar-heading q-mt-md">
    <title-bar style="color:#212121" title="Contacts" subtitle="An overview of all your saved contacts." />
    <div class="action-container">
      <big-search placeholder="Search for name or address" style="width:300px;" @update:modelValue="searchVal"
        v-model="search" class="address_book_page-actions-big_search q-mr-md" />
      <shipvagoo-button clickable @click="onClickAddNewContact">New contact</shipvagoo-button>
    </div>
  </div>
  <div class="address_book_page q-mt-xs" v-if="!rows.length">
    <shipvagoo-placeholder text="No contacts have been saved">
    </shipvagoo-placeholder>
  </div>
  <div class="address_book_page" v-if="rows.length">
    <shipvagoo-table-v1 v-model:pagination="pagination" :columns="columns" :rows="rows" :total="pagination.rowsNumber"
      :loading="loadingService.loadings.has('main-loader:get-address-book')" @row-click="onClickRow"
      @request="getAddressBook($event.pagination)">
      <template #body-cell-country="props">
        <q-td :props="props">
          <div v-if="props" class="flex items-center no-wrap " style="gap: 8px;">
            <img decoding="async" :src="`/assets/img/${props.row.country.iso.toLowerCase()}.svg`"
              style="width: 24px; height: 24px; border-radius: 50%; object-fit: cover" />
            <div class="text-center full-height">{{
              props.row.country ? (props.row.country.name.toLowerCase().charAt(0).toUpperCase() +
                props.row.country.name.toLowerCase().slice(1)) : ""
            }}
            </div>
          </div>
        </q-td>
      </template>

      <template #body-cell-more="props">
        <q-td :props="props" class="more">
          <q-btn dense flat round style="color:#1f2124;" class="more-action" field="edit" icon="more_horiz">
            <shipvagoo-menu>
              <shipvagoo-menu-item clickable @click="openCreateShipment" class="text-no-wrap">
                New shipment
              </shipvagoo-menu-item>
              <shipvagoo-menu-item clickable @click="deleteAddressBookRecord(props.row.address_book_code)">
                {{ $t("common.delete") }}
              </shipvagoo-menu-item>
            </shipvagoo-menu>
          </q-btn>
        </q-td>
      </template>
    </shipvagoo-table-v1>
  </div>


  <q-dialog v-model="isAddressDetailModalOpen" @before-hide="onCloseModal">
    <shipvagoo-modal :header-separator="false" :title="addressDetailTitle">
      <shipvagoo-separator style="margin:0 20px"></shipvagoo-separator>
      <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
        <div class="input-double input-double-responsive">
          <shipvagoo-select-v1 v-model="form.country" :options="$store.state.countryStore.countries"
            :label="$t('common.country')" :error="$v.country.$error"
            :error-message="String($v.country.$errors?.[0]?.$message ?? null)" placeholder="Select"
            option-label="default_name" option-value="country_code">
            <template #prepend>
              <shipvagoo-country-icon v-if="form.country" :country="form.country" />
            </template>
            <template #option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section side>
                  <shipvagoo-country-icon v-if="scope.opt" :country="scope.opt" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ scope.opt.default_name }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>
          </shipvagoo-select-v1>
          <div>
            <shipvagoo-input-v1 placeholder="Enter first and last name" v-model="form.name" :label="$t('common.name')"
              :error="$v.name.$error" :error-message="String($v.name.$errors?.[0]?.$message ?? null)" />
          </div>
          <!--          <shipvagoo-input
                      v-model="form.attn"
                      label="Att"
                      :error="$v.attn.$error"
                      :error-message="String($v.attn.$errors?.[0]?.$message ?? null)"
                    />-->
        </div>
        <div class="input-double input-double-responsive">
          <div>
            <shipvagoo-input-v1 v-model="form.email" :label="$t('common.email')" placeholder="Enter email"
              autocomplete="email" type="email" :error="$v.email.$error"
              :error-message="String($v.email.$errors?.[0]?.$message ?? null)" />
          </div>
          <div>
            <shipvagoo-input-v1 autocomplete="tel" id="phone" name="phone" v-model="form.mobile"
              :label="$t('common.mobile')" placeholder="Enter mobile" :error="$v.mobile.$error"
              :error-message="String($v.mobile.$errors?.[0]?.$message ?? null)" />
          </div>
        </div>
        <div class="input-double input-double-responsive">
          <div>
            <shipvagoo-input-v1 v-model="form.address" id="address" name="address" autocomplete="street-address"
              :label="$t('common.address')" :error="$v.address.$error" placeholder="Enter address"
              :error-message="String($v.address.$errors?.[0]?.$message ?? null)" />
          </div>
          <div>
            <shipvagoo-input-v1 v-model="form.address2" label="Address 2 (optional)" placeholder="Enter address 2"
              :error="$v.address2.$error" :error-message="String($v.address2.$errors?.[0]?.$message ?? null)" />
          </div>
        </div>
        <div class="input-double input-double-responsive">
          <div>
            <shipvagoo-input-v1 v-model="form.zipCode" placeholder="Enter postal code" label="Postal code"
              :error="$v.zipCode.$error" :error-message="String($v.zipCode.$errors?.[0]?.$message ?? null)"
              @update:model-value="onChangeZipCode($event)" />
          </div>
          <div>
            <shipvagoo-input-v1 v-model="form.city" placeholder="Enter city" :label="$t('common.city')"
              :error="$v.city.$error" :error-message="String($v.city.$errors?.[0]?.$message ?? null)" />
          </div>
        </div>
      </shipvagoo-card-section>
      <shipvagoo-card-actions>
        <shipvagoo-button v-if="addressDetailModalType === 'create'" label="Save" min-width="100px"
          @click="onCreateAddressBookRecord" />
        <div class="flex gap-10" v-if="addressDetailModalType === 'edit'">
          <shipvagoo-button label="Cancel" outline v-close-popup min-width="100px" />
          <shipvagoo-button :label="$t('common.update')" min-width="100px" @click="onUpdateAddressBookRecord" />
        </div>
      </shipvagoo-card-actions>
    </shipvagoo-modal>
  </q-dialog>
</template>

<script setup lang="ts">
import { onBeforeMount, ref } from "vue";
import { QTableProps } from "quasar";
import { useStore } from "vuex";
import useVuelidate, { ValidationArgs } from "@vuelidate/core";
import { helpers, required } from "@vuelidate/validators";
import { useI18n } from "vue-i18n";
import * as _ from "lodash-es";
import {
  addressBookService,
  loadingService,
  loggingService,
  mapService,
  notificationService
} from "../services/_singletons";
import { IAddressBookRecord } from "../model/address-book.model";
import { IRootState } from "../model/store.model";
import { ICountry } from "../model/country.model";
import { IRequestParam } from "../model/http.model";
import { Pagination } from "../model/common.model";
import HelperService from "../services/helper.service";
import postCodes from "../assets/json/postcodes.json";

const { t } = useI18n();
const $store = useStore<IRootState>();

const isAddressDetailModalOpen = ref(false);
const addressDetailModalType = ref<"create" | "edit">("create");

const search = ref<string>("");

interface IAddressForm {
  name: string;
  attn: string | null;
  email: string;
  mobile: string | null;
  address: string;
  address2: string | null;
  zipCode: string;
  city: string;
  country: ICountry | null;
  isDefault: boolean;
}

const pagination = ref<Pagination>({
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0
});

const form = ref<IAddressForm>({
  name: "",
  attn: "",
  email: "",
  mobile: "",
  address: "",
  address2: "",
  zipCode: "",
  city: "",
  country: null,//$store.state.countryStore.countriesIndexedByIso.DK,
  isDefault: false
});

const $v = useVuelidate<IAddressForm, ValidationArgs<IAddressForm>>(
  {
    name: { required: helpers.withMessage("This field is required", required) },
    attn: {},
    email: { required: helpers.withMessage("This field is required", required) },
    address: { required: helpers.withMessage("This field is required", required) },
    address2: {},
    mobile: { required: helpers.withMessage("This field is required", required) },
    zipCode: { required: helpers.withMessage("This field is required", required) },
    city: { required: helpers.withMessage("This field is required", required) },
    country: { required: helpers.withMessage("This field is required", required) },
    isDefault: {}
  },
  form
);

const addressDetailTitle = ref<string>("");

// const rowsPerPageOptions = [10, 25, 50, 100];
const columns: QTableProps["columns"] = [
  {
    name: "name",
    required: true,
    label: t("common.name"),
    align: "left",
    field: (row: IAddressBookRecord) => row.name,
    sortable: false
  },

  {
    name: "address",
    align: "left",
    label: t("common.address"),
    field: "address",
    sortable: false
  },
  {
    name: "address2",
    align: "left",
    label: t("common.address2"),
    field: "address2",
    sortable: false
  },
  {
    name: "zipcode",
    align: "left",
    label: "Postal code",
    field: "zipcode",
    sortable: false
  }, {
    name: "city",
    align: "left",
    label: "City",
    field: "city",
    sortable: false
  },
  {
    name: "country",
    align: "left",
    label: `${t("common.country")}`,
    field: "",
    sortable: false
  },
  {
    name: "mobile",
    align: "left",
    label: "Phone",
    field: "mobile",
    sortable: false
  },
  {
    name: "email",
    align: "left",
    label: t("common.email"),
    field: "email",
    sortable: false
  },
  { name: "more", align: "right", label: "Action", field: "more" }
];
const rows = ref<IAddressBookRecord[]>([]);
const rowThatCurrentlyBeingEdited = ref<IAddressBookRecord | null>(null);

const onClickRow = (event: Event, row: IAddressBookRecord) => {
  if (HelperService.containsIgnoredRowElements(event)) {
    return;
  }

  rowThatCurrentlyBeingEdited.value = row;
  addressDetailModalType.value = "edit";
  addressDetailTitle.value = "Edit";

  form.value.name = row.name;
  form.value.attn = row.attn;
  form.value.email = row.email;
  form.value.mobile = row.mobile;
  form.value.address = row.address;
  form.value.address2 = row.address2;
  form.value.zipCode = row.zipcode;
  form.value.city = row.city;
  form.value.country = row.country;
  form.value.isDefault = Boolean(row.is_default);

  isAddressDetailModalOpen.value = true;
};
const searchVal = (value: string) => {
  search.value = value;
  getAddressBook(pagination.value);
};
const getAddressBook = async (tablePagination: Pagination | null) => {
  try {
    loadingService.startLoading("main-loader:get-address-book");
    const page = tablePagination?.page || 1;
    const limit = tablePagination?.rowsPerPage || 10;

    const requestParams: IRequestParam[] = [{ key: "with", value: "country" }];

    if (tablePagination?.sortBy) {
      const sortingOrder = tablePagination.descending ? "desc" : "asc";
      requestParams.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
    } else {
      requestParams.push({ key: "sorting[created_at]", value: "desc" });
    }

    if (search.value && search.value !== "") {
      requestParams.push(
        { key: "conditions[name:like]", value: search.value },
        { key: "conditions[address:or_like]", value: search.value }

        /*   {key: 'conditions[email:or_like]', value: search.value},
           {key: 'conditions[address2:or_like]', value: search.value},
           {key: 'conditions[zipcode:or_like]', value: search.value},
           {key: 'conditions[city:or_like]', value: search.value},
           {key: 'conditions[mobile:or_like]', value: search.value},*/
      );
    }

    const result = await addressBookService.getAddressBookRecords(page, limit, requestParams);
    rows.value = result.entities;
    pagination.value.rowsNumber = result.total;
    pagination.value.rowsPerPage = result.per_page;
    pagination.value.sortBy = tablePagination?.sortBy;
    pagination.value.descending = tablePagination?.descending;
  } catch (error) {
    loggingService.error("Get address book", error);
    notificationService.showError("Failed to get address book");
  } finally {
    loadingService.stopLoading("main-loader:get-address-book");
  }
};

const onCloseModal = () => {
  $v.value.$reset();
  rowThatCurrentlyBeingEdited.value = null;

  form.value.name = "";
  form.value.attn = "";
  form.value.email = "";
  form.value.mobile = "";
  form.value.address = "";
  form.value.address2 = "";
  form.value.zipCode = "";
  form.value.city = "";
  form.value.country = $store.state.countryStore.countries.find(
    (c) => c.iso === "DK"
  ) as ICountry;
  form.value.isDefault = false;
};

const onCreateAddressBookRecord = async () => {
  await $v.value.$validate();

  if ($v.value.$error) {
    return;
  }

  try {
    loadingService.startLoading("main-loader:create-address-book-record");

    await addressBookService.createAddressBookRecord({
      name: form.value.name,
      attn: form.value.attn,
      email: form.value.email,
      mobile: form.value.mobile,
      address: form.value.address,
      address2: form.value.address2,
      zipcode: form.value.zipCode,
      city: form.value.city,
      is_default: form.value.isDefault,
      country_code: form.value.country?.country_code ?? null
    });

    notificationService.showSuccess("Address book record created successfully");

    isAddressDetailModalOpen.value = false;
    onCloseModal();
    await getAddressBook(null);
  } catch (e) {
    loggingService.error("Error creating address book record", e);
    notificationService.showError("Error creating address book record");
  } finally {
    loadingService.stopLoading("main-loader:create-address-book-record");
  }
};

const onUpdateAddressBookRecord = async () => {
  await $v.value.$validate();

  if ($v.value.$error) {
    return;
  }

  try {
    loadingService.startLoading("main-loader:update-address-book-record");

    if (!rowThatCurrentlyBeingEdited.value) {
      throw new Error("No row currently being edited");
    }

    await addressBookService.updateAddressBookRecord(
      rowThatCurrentlyBeingEdited.value.address_book_code,
      {
        name: form.value.name,
        attn: form.value.attn,
        email: form.value.email,
        mobile: form.value.mobile,
        address: form.value.address,
        address2: form.value.address2,
        zipcode: form.value.zipCode,
        city: form.value.city,
        is_default: form.value.isDefault,
        country_code: form.value.country?.country_code ?? null,
        company_code: $store.state.companyStore.company_code
      }
    );

    isAddressDetailModalOpen.value = false;
    onCloseModal();

    getAddressBook(pagination.value);
    notificationService.showSuccess("Address book record updated successfully");
  } catch (e) {
    loggingService.error("Error updating address book record", e);
    notificationService.showError("Error updating address book record");
  } finally {
    loadingService.stopLoading("main-loader:update-address-book-record");
  }
};

const deleteAddressBookRecord = async (addressBookId: number) => {
  try {
    loadingService.startLoading("main-loader:delete-address-book-record");
    await addressBookService.deleteAddressBookRecord(addressBookId);
    notificationService.showSuccess("Address book record deleted successfully");

    const page = pagination.value.page ?? 1;

    if (rows.value.length === 1 && page > 1) {
      pagination.value.page = page - 1;
    }

    getAddressBook(pagination.value);
  } catch (e) {
    loggingService.error("Error deleting address book record", e);
    notificationService.showError("Error deleting address book record");
  } finally {
    loadingService.stopLoading("main-loader:delete-address-book-record");
  }
};

const onChangeZipCode = async (zip: string | number | null) => {
  if (!form.value.country || !zip || typeof zip !== "string") {
    return;
  }

  const re = new RegExp(postCodes[form.value.country.iso].regex);

  if (!re.test(zip)) {
    return;
  }

  try {
    loadingService.startLoading("main-loader:get-city-from-zip-code");
    const geo = await mapService.getMapInfo(form.value.country.iso, zip);

    if (_.size(geo.results) > 0) {
      const results = _.head(Object.values(geo.results));
      const result = _.head(results);

      form.value.city = result?.city || "";
    }
  } catch (e) {
    notificationService.showError("Error getting city from zip code");
    loggingService.error("Error getting city from zip code", e);
  } finally {
    loadingService.stopLoading("main-loader:get-city-from-zip-code");
  }
};

const onClickAddNewContact = () => {
  addressDetailTitle.value = "Add new contact";
  addressDetailModalType.value = "create";
  isAddressDetailModalOpen.value = true;
};

const openCreateShipment = () => {
  $store.dispatch("shipmentStore/openCreateShipmentModal");
};

onBeforeMount(() => {
  getAddressBook(null);
});
</script>

<style scoped lang="scss">
.address_book_page {
  padding: 0 40px 10px 40px;

  @media (max-width: 1024px) {
    padding: 0 20px 10px 20px;
  }

}

.action-container {
  display: flex !important;
  height: 50px;
}

@media (max-width: 600px) {
  .action-container {
    flex-direction: column;
    height: 120px;
    margin-bottom: 10px;
  }
}
</style>
