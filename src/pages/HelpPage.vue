<template>
  <div class="flex justify-between items-center title-bar-heading">
    <title-bar style="width: 100%;" title="Help" />
  </div>
  <div class="help-container">
    <div class="row ">
      <div class="col-12 text-center text-h5 text-weight-medium gray-color">
        How can we help?
      </div>
    </div>
    <div class="row flex justify-center q-mt-lg">
      <div class="col-md-3 flex justify-end q-pa-md">
        <div
          @mouseover="onMouseOverHelpCenter"
          @mouseleave="onMouseLeaveHelpCenter"
          :class="help_center_hover?'primary-background':'bg-white'"
          @click="()=>$router.push({name:'help-center'})"
          class="container-buttons q-pa-md cursor-pointer">
          <div class="row">
            <div class="col-12 flex justify-center">
              <img :src="help_center_hover?HelpIconHover:HelpIcon" style="width: 40px;height: 40px;" />
            </div>
            <div
              :class="help_center_hover?'text-white':'gray-color'"
              class="col-12 text-center text-h6 text-weight-medium q-mt-md">
              Help Center
            </div>
            <div
              :class="help_center_hover?'text-white':'light-gray-color'"
              class="col-12 text-center q-mt-sm">
              Here you will find frequently asked questions, rules, guidelines etc.
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3 flex justify-start q-pa-md">
        <div
          :class="contact_hover?'primary-background':'bg-white'"
          @mouseover="onMouseOverContact"
          @mouseleave="onMouseLeaveHelpContact"
          @click="onClickContactUs"
          class="container-buttons q-pa-md cursor-pointer">
          <div class="row">
            <div class="col-12 flex justify-center">
              <img :src="contact_hover?ScreenShareHover:ScreenShare" style="width: 38px;height: 38px;" />
            </div>
            <div
              :class="contact_hover?'text-white':'gray-color'"
              class="col-12 text-center text-h6 text-weight-medium q-mt-md">
              Contact
            </div>
            <div
              :class="contact_hover?'text-white':'light-gray-color'"
              class="col-12 text-center q-mt-sm">
              Our support team is ready to assist you with general or technical questions.
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row q-mt-xl">
      <div class="col-12 text-center text-h5 text-weight-medium gray-color">
        Quick Answers
      </div>
    </div>
    <div class="row flex justify-center q-mt-md">
      <div class="col-8 text-black">
        <quick-answer class="q-my-md" v-for="(faq,index) in faqs"
                      :key="index"
                      :faq="faq"></quick-answer>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import HelpIcon from "../assets/img/help.svg";
import HelpIconHover from "../assets/img/help_hover.svg";
import ScreenShare from "../assets/img/screen_share.svg";
import ScreenShareHover from "../assets/img/screen_share_hover.svg";

import { ref, onBeforeUnmount } from "vue";

const help_center_hover = ref<boolean>(false);
const contact_hover = ref<boolean>(false);

const zendeskScriptId = "ze-snippet";
const zendeskScriptSrc = "https://static.zdassets.com/ekr/snippet.js?key=c8538d88-0c02-421a-82d1-bab7be7b5b82";
loadZendeskScript();

const faqs = ref([
  {
    question: "Lorem ipsum dolor sit amet?",
    answer: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor in."
  },
  {
    question: "Lorem ipsum dolor sit amet?",
    answer: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor in."
  },
  {
    question: "Lorem ipsum dolor sit amet?",
    answer: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor in."
  },
  {
    question: "Lorem ipsum dolor sit amet?",
    answer: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor in."
  }
]);

const onMouseOverHelpCenter = () => {
  help_center_hover.value = true;
};
const onMouseLeaveHelpCenter = () => {
  help_center_hover.value = false;

};
const onMouseOverContact = () => {
  contact_hover.value = true;
};
const onMouseLeaveHelpContact = () => {
  contact_hover.value = false;
};


const onClickContactUs = () => {
  if (typeof (window as any).zE !== "undefined") {
    (window as any).zE("messenger", "open");
  }
};

function loadZendeskScript() {
  const existingScript = document.getElementById(zendeskScriptId);
  if (!existingScript) {
    const zendeskScript = document.createElement("script");
    zendeskScript.setAttribute("id", zendeskScriptId);
    zendeskScript.setAttribute("src", zendeskScriptSrc);
    document.head.appendChild(zendeskScript);
  } else {
    console.log("loaded already");
    if (typeof (window as any).zE !== "undefined") {
      (window as any).zE("messenger", "show");
    }
  }
}

function unloadZendeskWidget() {
  if (typeof (window as any).zE !== "undefined") {
    (window as any).zE("messenger", "hide");
  }
}

/*onMounted(() => {
  const recaptchaScript = document.createElement('script')
  recaptchaScript.setAttribute('id', 'ze-snippet')
  recaptchaScript.setAttribute('src', 'https://static.zdassets.com/ekr/snippet.js?key=c8538d88-0c02-421a-82d1-bab7be7b5b82')
  document.head.appendChild(recaptchaScript)
})*/
onBeforeUnmount(() => {
  unloadZendeskWidget();
});
</script>

<style scoped>
.help-container {
  padding: 20px 40px;
}

.container-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 280px;
  height: 215px;
  border-radius: 5px;
  box-shadow: 0px 4px 20px 0px rgba(170, 169, 184, 0.32);

}

.primary-background {
  background: #243E90;
}

.gray-color {
  color: #313131
}

.light-gray-color {
  color: #757575
}
</style>
