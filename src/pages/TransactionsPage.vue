<template>
  <div class="flex justify-between items-center title-bar-heading q-mt-md">
    <div>
      <title-bar
        style="width: 100%"
        title="Transactions"
        subtitle="An overview of all transactions"
      />
    </div>
    <big-search
      placeholder="Search for transaction type"
      @update:modelValue="searchVal"
      v-model="search"
      class="transactions_page-actions-big_search"
    />
  </div>
  <div class="transactions_page q-mt-xs" v-if="!rows.length">
    <shipvagoo-placeholder text="No transactions are available"> </shipvagoo-placeholder>
  </div>
  <div class="transactions_page" v-if="rows.length">
    <shipvagoo-table-v1
      v-model:pagination="pagination"
      :columns="columns"
      :rows="rows"
      :total="pagination.rowsNumber"
      :loading="loadingService.loadings.has('main-loader:get-transactions')"
      @request="getTransactions($event.pagination)"
      no-data-label="No transactions are available"
      class="transactions-page-table"
    >
      <template #body-cell-more="props">
        <q-td :props="props" class="more">
          <q-btn
            dense
            flat
            round
            style="color: #1f2124"
            class="more-action"
            field="edit"
            icon="more_horiz"
          >
            <shipvagoo-menu>
              <shipvagoo-menu-item clickable @click="onClickPrintReceipt(null, props.row)">
                Download
              </shipvagoo-menu-item>
            </shipvagoo-menu>
          </q-btn>
        </q-td>
      </template>
      <template #body-cell-created_at="props">
        <q-td :props="props">
          <span class="text-no-wrap">{{
            dayjs(String(props.row.created_at)).format('DD/MM/YYYY')
          }}</span
          ><br />
          <span class="text-no-wrap">{{
            dayjs(String(props.row.created_at)).format('HH:mm')
          }}</span>
        </q-td>
      </template>
      <template #body-cell-carrier="props">
        <q-td :props="props">
          <div
            class="flex items-center no-wrap"
            v-if="
              CarrierService.getCarrierFromShippingProductCode(
                $store.state.courierStore.carriers,
                $store.state.courierStore.carrierProducts,
                asTransaction(props.row).shipment?.carrier_product_code,
              )?.carrier_id?.toLowerCase()
            "
          >
            <div
              class="shipvagoo-table-carrier-logo"
              :style="{
                backgroundImage:
                  'url(' +
                  CarrierService.getCarrierFromShippingProductCode(
                    $store.state.courierStore.carriers,
                    $store.state.courierStore.carrierProducts,
                    asTransaction(props.row).shipment?.carrier_product_code,
                  )?.icon +
                  ')',
              }"
            />
            <div class="text-no-wrap q-ml-sm shipped-carrier">
              {{
                CarrierService.getCarrierFromShippingProductCode(
                  $store.state.courierStore.carriers,
                  $store.state.courierStore.carrierProducts,
                  asTransaction(props.row).shipment?.carrier_product_code,
                )?.name
              }}
            </div>
          </div>
        </q-td>
      </template>
      <template #body-cell-service="props">
        <q-td :props="props">
          <div
            class="flex items-center no-wrap"
            v-if="
              CarrierService.getCarrierFromShippingProductCode(
                $store.state.courierStore.carriers,
                $store.state.courierStore.carrierProducts,
                asTransaction(props.row).shipment?.carrier_product_code,
              )?.carrier_id?.toLowerCase()
            "
          >
            <div class="text-no-wrap q-ml-sm shipped-carrier">
              {{
                $store.state.courierStore.carrierProducts.find(
                  (item: IShippingProduct) =>
                    item.carrier_product_code ==
                    asTransaction(props.row).shipment?.carrier_product_code,
                )?.name
              }}
            </div>
          </div>
        </q-td>
      </template>

      <!-- <template v-slot:body-cell-amount="props">
         <q-td :props="props" class="text-right">
           {{ (props.row.transaction_type === 'INVOICE' ? '-' : '') +
           PriceService.formatPrice(Number(props.row.amount), 'de-DE')}}
         </q-td>
       </template>
       <template v-slot:body-cell-balance="props">
         <q-td :props="props" class="text-right">
           {{ formatBalance(parseFloat(props.row.balance), props.row.amount, props.row.transaction_type,
           props.row.payment_method?.type)}}
         </q-td>
       </template>-->
    </shipvagoo-table-v1>
  </div>

  <Invoice v-if="invoiceToPrint" :invoiceToPrint="invoiceToPrint"></Invoice>
</template>

<script setup lang="ts">
  import { Ref, ref, onBeforeMount } from 'vue';
  import { QTableProps } from 'quasar';
  import dayjs from 'dayjs';
  import * as _ from 'lodash-es';
  import { useI18n } from 'vue-i18n';
  import { ITransaction } from '../model/transactions.model';
  import {
    loadingService,
    loggingService,
    notificationService,
    transactionService,
  } from '../services/_singletons';
  import { Pagination } from '../model/common.model';
  import { IRequestParam } from '../model/http.model';
  import HelperService from '../services/helper.service';
  import { useStore } from '../store';
  import PriceService from '../services/price.service';
  import { IInvoice } from '../model/invoice.model';
  import { useHelper } from '../composeable/Helper';
  import CarrierService from '../services/carrier.service';
  import { IShippingProduct } from '../model/carrier.model';

  const { showPdf } = useHelper();
  const { t } = useI18n();
  const $store = useStore();
  const searchVal = (value: string) => {
    search.value = value;
    getTransactions(pagination.value);
  };
  const pagination = ref<Pagination>({
    rowsNumber: 0,
    rowsPerPage: 10,
    page: 1,
  });

  const search: Ref<string> = ref<string>('');
  const rows = ref<ITransaction[]>([]);
  const invoiceToPrint = ref<IInvoice | null>(null);
  const asTransaction = (row: ITransaction): ITransaction => row;
  const getTransactions = async (tablePagination: Pagination | null) => {
    try {
      loadingService.startLoading('main-loader:get-transactions');

      const page = tablePagination?.page ?? 1;
      const limit = tablePagination?.rowsPerPage ?? 10;

      const params: IRequestParam[] = [];

      if (search.value && search.value !== '') {
        params.push({ key: 'conditions[description:like]', value: search.value });
      }
      params.push({ key: 'with', value: 'shipment' });
      if (tablePagination?.sortBy) {
        const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
        params.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
      } else {
        // params.push({key: 'sorting[created_at]', value: 'desc'});
        params.push({ key: 'sorting[id]', value: 'desc' });
      }

      const result = await transactionService.getTransactions(page, limit, params);
      rows.value = result.entities;
      pagination.value.rowsNumber = result.total;
      pagination.value.page = result.current_page;
      pagination.value.sortBy = tablePagination?.sortBy;
      pagination.value.descending = tablePagination?.descending;
    } catch (e) {
      loggingService.error('Error while getting transactions', e);
      notificationService.showError('Error while getting transactions');
    } finally {
      loadingService.stopLoading('main-loader:get-transactions');
    }
  };

  const formatBalance = (
    balance: number,
    amount: number,
    type: string,
    payment_method: string | null | undefined,
  ): string | null => {
    // TEMPORARY
    /*if (payment_method == 'card' && type !== 'ADD_FUNDS') {
return ""
}*/
    if (type === 'ADD_FUNDS') {
      return PriceService.formatPrice(balance + amount, 'de-DE');
    }

    if (type === 'INVOICE') {
      return PriceService.formatPrice(balance, 'de-DE');
    }

    if (type === 'REFUND') {
      return PriceService.formatPrice(balance, 'de-DE');
    }

    return PriceService.formatPrice(balance, 'de-DE');
  };

  const columns: QTableProps['columns'] = [
    {
      name: 'created_at',
      field: '',
      required: true,
      label: 'Transaction date',
      align: 'left',
      sortable: false,
    },
    {
      name: 'description',
      field: (row: ITransaction) => {
        const description = row.description;
        if (row.transaction_type === 'INVOICE') {
          if (description.toLocaleLowerCase().includes('return')) {
            /*let arr = description.split("(");
      let returnId = "" as string | null;
      if (arr.length) {
        const str = arr[0];
        const arr1 = str.match(/\d+/g);
        returnId = arr1 ? arr1[0] : "";
      }*/
            return `Shipment ID: ${row.shipment ? row.shipment.shipment_code : ''} - (Return ID: ${
              row.shipment ? row.shipment.id.toString().padStart(6, '0') : ''
            })`;
          } else {
            return description.split('(')[0];
          }
        } else {
          return description;
        }
      },
      required: true,
      label: 'Transaction type',
      align: 'left',
      sortable: false,
    },
    {
      name: 'carrier',
      field: '',
      required: true,
      label: 'Carrier',
      align: 'left',
      sortable: false,
    },
    {
      name: 'service',
      field: '',
      required: true,
      label: 'Service',
      align: 'left',
      sortable: false,
    },
    {
      name: 'payment_method',
      field: (row: ITransaction) => {
        const payment_method = row.payment_method;
        if (payment_method) {
          const type = payment_method.type;
          const details = payment_method.details;

          if (type == 'card') {
            if (details) {
              return `${
                details.brand.charAt(0).toUpperCase() + details.brand.slice(1)
              } (${details.bin.substring(0, 4)} **** **** ${details.last4})`;
            } else {
              return type.charAt(0).toUpperCase() + type.slice(1);
            }
          } else if (Number(row.balance) < 0) {
            return 'Credit';
          } else {
            return type.charAt(0).toUpperCase() + type.slice(1);
          }
        } else {
          return 'Balance';
        }
      },
      required: true,
      label: 'Payment method',
      align: 'left',
      sortable: false,
    },
    {
      name: 'amount',
      field: (row: ITransaction) => formatAmount(row),
      required: true,
      label: t('common.amount'),
      align: 'right',
      sortable: false,
    },
    {
      name: 'balance',
      field: (row: ITransaction) =>
        formatBalance(
          parseFloat(row.balance),
          Number(row.amount),
          row.transaction_type,
          row.payment_method?.type,
        ),
      required: true,
      label: t('common.balance'),
      align: 'right',
      sortable: false,
    },
    { name: 'more', align: 'center', label: 'Action', sortable: false, field: 'more' },
  ];

  const formatAmount = (transaction: ITransaction) => {
    const paymentMethod = Number(transaction.balance) < 0 ? 'credit' : 'balance';
    const amount =
      (transaction.transaction_type === 'INVOICE' ? '-' : '') +
      PriceService.formatPrice(Number(transaction.amount), 'de-DE');
    if (paymentMethod == 'credit') {
      return amount.replace('-', '');
    } else {
      return amount;
    }
  };
  const onClickPrintReceipt = async (event: Event | null, row: ITransaction) => {
    if (event && HelperService.containsIgnoredRowElements(event)) {
      return;
    }
    try {
      loadingService.startLoading('main-loader:print-transaction-receipt');
      if (row.transaction_type == 'INVOICE') {
        /* const invoice: IInvoice = await transactionService.invoice(row.transaction_code);
   if (invoice.id !== undefined) {

     invoiceToPrint.value = invoice;
   }*/
        const response = await transactionService.downloadInvoicePdf(row.transaction_code);
        await showPdf(response.file);
      } else {
        const response = await transactionService.downloadFundsPdf(row.transaction_code);
        await showPdf(response.file);
      }
    } catch (e) {
      loggingService.error('Error while printing receipt', e);
      notificationService.showError('Error while printing receipt');
    } finally {
      loadingService.stopLoading('main-loader:print-transaction-receipt');
    }
  };

  onBeforeMount(() => {
    getTransactions(null);
  });
</script>

<style scoped lang="scss">
  .transactions_page {
    padding: 0 40px 10px 40px;

    @media (max-width: 1024px) {
      padding: 0 20px 10px 20px;
    }

    &-actions {
      display: flex;
      flex-direction: row;

      & > *:not(:first-child) {
        margin-left: 10px;

        @media (max-width: 600px) {
          margin-left: unset;
        }
      }

      .big_search-rows_per_page-group {
        display: flex;
        flex: 1;
        flex-direction: row;

        & > *:not(:first-child) {
          margin-left: 10px;
        }

        .rows_per_page {
          display: none;

          @media (max-width: $tablet) {
            display: block;
          }
        }
      }

      &-big_search {
        width: 300px;
      }

      @media (max-width: 600px) {
        flex-direction: column;

        &-big_search {
          margin-bottom: 10px;
        }
      }
    }
  }
</style>
<style>
  .transactions-page-table th:nth-child(1),
  .transactions-page-table td:nth-child(1) {
    width: 8%;
  }

  .transactions-page-table th:nth-child(2),
  .transactions-page-table td:nth-child(2) {
    width: 25%;
  }

  .transactions-page-table th:nth-child(3),
  .transactions-page-table td:nth-child(3) {
    width: 12%;
  }

  .transactions-page-table th:nth-child(4),
  .transactions-page-table td:nth-child(4) {
    width: 12%;
  }

  .transactions-page-table th:nth-child(5),
  .transactions-page-table td:nth-child(5) {
    width: 20%;
  }

  .transactions-page-table th:nth-child(6),
  .transactions-page-table td:nth-child(6) {
    width: 8%;
  }

  .transactions-page-table th:nth-child(7),
  .transactions-page-table td:nth-child(7) {
    width: 10%;
  }

  .transactions-page-table th:nth-child(8),
  .transactions-page-table td:nth-child(8) {
    width: 5%;
  }
</style>
