<template>
  <Title :title="$t('common.prices')" />
  <div class="prices">
    <info-bar>
      <div>See our shipping rates for the whole world.</div>
      <div>
        If you have your own shipping agreement with a specific carrier, it can be activated on your
        Shipvagoo account. Read more about own shipping agreements
        <shipvagoo-hyperlink label="here" to="#" type="external-link" />.
      </div>
    </info-bar>
    <div class="selects">
      <shipvagoo-select
        v-model="senderCountry"
        :label="$t('common.sender_country')"
        :options="countries"
        option-value="country_code"
        option-label="default_name"
        disable
      >
        <template #prepend>
          <shipvagoo-country-icon v-if="senderCountry" :country="senderCountry" />
        </template>
        <template #option="scope">
          <q-item v-bind="scope.itemProps">
            <q-item-section side>
              <shipvagoo-country-icon v-if="scope.opt" :country="scope.opt" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ scope.opt.default_name }}</q-item-label>
            </q-item-section>
          </q-item>
        </template>
      </shipvagoo-select>
      <shipvagoo-select
        v-model="recieverCountry"
        :label="$t('common.receiver_country')"
        :options="countries"
        option-value="country_code"
        option-label="default_name"
        @update:model-value="changeLocation"
      >
        <template #prepend>
          <shipvagoo-country-icon v-if="recieverCountry" :country="recieverCountry" />
        </template>
        <template #option="scope">
          <q-item v-bind="scope.itemProps">
            <q-item-section side>
              <shipvagoo-country-icon v-if="scope.opt" :country="scope.opt" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ scope.opt.default_name }}</q-item-label>
            </q-item-section>
          </q-item>
        </template>
      </shipvagoo-select>
    </div>

    <div class="carriers">
      <div v-for="carrier in carriers" :key="carrier.carrier_code" class="carriers_carrier">
        <h2 class="carrier_name">{{ carrier.name }}</h2>
        <div
          v-for="product in carrier.products"
          :key="product.carrier_product_code"
          class="carrier_product"
          :style="{ display: product.rates.length > 0 ? 'block' : 'none' }"
        >
          <p class="carrier_product_name">
            {{ product.name }}<template v-if="product.note"> - {{ product.note }}</template>
          </p>
          <table border="1" frame="void" rules="rows">
            <th align="left">Weight class</th>
            <th align="right">Price excl. VAT</th>
            <tbody>
              <tr v-for="rate in product.rates" :key="rate.price_code">
                <td align="left">
                  {{ parseFloat(rate.weight_class.min_weight.toString()) }}-{{
                    parseFloat(rate.weight_class.max_weight.toString())
                  }}
                  {{ rate.weight_class.unit }}
                </td>
                <td align="right">
                  {{ PriceService.formatPrice(Number(rate.price)) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onBeforeMount, ref } from 'vue';
  import * as _ from 'lodash-es';
  import { euMember } from 'is-european';
  import Title from '../components/title-bar.vue';
  import { ICarrier } from '../model/carrier.model';
  import { ICountry } from '../model/country.model';
  import PriceService from '../services/price.service';
  import {
    countryService,
    loadingService,
    loggingService,
    notificationService,
    carrierService,
  } from '../services/_singletons';

  const senderCountry = ref<ICountry | null>(null);
  const recieverCountry = ref<ICountry | null>(null);
  const carriers = ref<ICarrier[]>([]);
  const countries = ref<ICountry[]>([]);

  const getPriceList = async (senderCountryCode: number, receiverCountryCode: number) => {
    try {
      loadingService.startLoading('main-loader:get-price-list');
      const response = await carrierService.getShippingProducts(
        senderCountryCode,
        receiverCountryCode,
      );
      carriers.value = _.get(response, 'carriers', []);
    } catch (e) {
      loggingService.error('Error while getting price list', e);
      notificationService.showError('Error while getting price list');
    } finally {
      loadingService.stopLoading('main-loader:get-price-list');
    }
  };

  const getCountries = async () => {
    try {
      loadingService.startLoading('main-loader:get-countries');
      const allCountries = await countryService.getCountries();
      countries.value = allCountries.filter((c: ICountry) => euMember(c.iso));
      const denmark = countries.value.find((c) => c.iso === 'DK');
      senderCountry.value = denmark || null;
    } catch (e) {
      notificationService.showError('Error getting countries');
      loggingService.error('Error while getting countries', e);
    } finally {
      loadingService.stopLoading('main-loader:get-countries');
    }
  };

  const changeLocation = () => {
    if (!senderCountry.value || !recieverCountry.value) {
      return;
    }

    getPriceList(senderCountry.value.country_code, recieverCountry.value.country_code);
  };

  onBeforeMount(() => {
    getCountries();
  });
</script>
<style scoped lang="scss">
  .prices {
    padding: 20px 40px;

    @media (max-width: 1024px) {
      padding: 20px 10px;
    }

    .selects {
      display: flex;
      flex-direction: row;
      gap: 40px;
      margin-top: 20px;

      & > * {
        flex: 1;
      }

      @media (max-width: 1024px) {
        flex-direction: column;
        gap: 10px;
        margin-top: 10px;
      }
    }

    .carriers {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 40px;
      margin-top: 30px;

      @media (max-width: 1024px) {
        grid-template-columns: 1fr;
        gap: 10px;
      }

      .carriers_carrier {
        border: 1px solid #2929c8;
        border-radius: 5px;
      }

      h2.carrier_name {
        margin: 0;
        margin: 10px 10px 6px 10px;
        font-size: 20px;
        font-variation-settings: 'wght' 500;
        line-height: 25px;
      }

      .carrier_product {
        &:not(:first-of-type) {
          margin-top: 23px;
        }

        p.carrier_product_name {
          margin: 0 10px;
          font-size: 15px;
          line-height: 20px;
        }

        table {
          width: 100%;
          margin-top: 6px;
          border-collapse: collapse;
        }

        table th {
          padding: 12px 10px;
          font-size: 15px;
          font-variation-settings: 'wght' 700;
          line-height: 25px;
          color: #e1e1e1;
        }

        table tbody tr {
          border: solid;
          border-color: #e1e1e1;
          border-width: 1px 0;
        }

        table tbody td {
          padding: 12px 10px;
          font-size: 15px;
          font-variation-settings: 'wght' 500;
          line-height: 25px;
          color: #e1e1e1;
        }
      }
    }
  }
</style>
