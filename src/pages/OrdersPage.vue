<template>
  <div class="flex justify-between items-center title-bar-heading q-mt-md">
    <title-bar
      style="color: #212121"
      :title="$t('common.orders')"
      subtitle="Overview of incoming orders for connected webshops"
    />
    <div class="action-container">
      <shipvagoo-multi-select
        :options="fulfillmentOptions"
        v-model="fulfillmentFilter"
        label="Fulfillment"
        class="q-mr-sm"
        @update:modelValue="onChangeFilter"
      />

      <shipvagoo-multi-select
        :options="shipmentOptions"
        v-model="shipmentFilter"
        label="Shipment"
        class="q-mr-sm"
        @update:modelValue="onChangeFilter"
      />
      <shipvagoo-multi-select
        :options="paymentOptions"
        v-model="paymentFilter"
        label="Payment"
        class="q-mr-sm"
        @update:modelValue="onChangeFilter"
      />

      <shipvagoo-multi-select
        :options="countryOptions"
        v-model="countryFilter"
        label="Country"
        class="q-mr-sm"
        @update:modelValue="onChangeFilter"
      />

      <shipvagoo-multi-select
        :options="integrateOptions"
        v-model="integrationFilter"
        label="Integration"
        class="q-mr-sm"
        @update:modelValue="onChangeFilter"
      />

      <!--<q-select
        dense
        outlined
        v-model="shipmentFilter"
        no-label
        :options="filterOptions"
        class="q-mr-sm"
        style="min-width: 100px"
        emit-value
        option-label="label"
        option-value="value"
        map-options
        multiple
        use-chips
        @update:model-value="onChangeFilter"
      />
       <shipvagoo-select
        v-model="shipmentFilter"
        no-label
        :options="filterOptions"
        class="q-mr-sm"
        style="min-width: 100px"
        emit-value
        option-label="label"
        option-value="value"
        map-options
        @update:model-value="onChangeFilter"
      /> -->
      <big-search
        placeholder="Search for order, recipient, email or notes"
        @update:modelValue="searchVal"
        v-model="search"
        class="orders_page-actions-big_search q-mr-sm"
      />
      <div class="flex">
        <shipvagoo-button-dropdown-v1
          style="flex: 1"
          class="q-mr-sm"
          color="#243E90"
          bg-color="#FFFFFF"
          outline
          no-caps
          dropdown-icon="fas fa-caret-down"
          label="More"
        >
          <shipvagoo-list>
            <shipvagoo-menu-item
              v-close-popup
              clickable
              @click="onClickDeleteOrders"
              class="text-dark"
            >
              Delete
            </shipvagoo-menu-item>
          </shipvagoo-list>
        </shipvagoo-button-dropdown-v1>
        <shipvagoo-button-dropdown-v1
          style="flex: 1"
          dropdown-icon="fas fa-caret-down"
          outline
          no-caps
          label="Create"
        >
          <shipvagoo-list>
            <!--<shipvagoo-menu-item
              v-close-popup
              clickable
              @click="onClickDropdownAction(onClickStartPickPath)"
            >
              Start pick path
            </shipvagoo-menu-item>
            <shipvagoo-separator/>-->
            <shipvagoo-menu-item
              v-close-popup
              clickable
              @click="onClickDropdownAction(onClickCreatePickLists)"
            >
              Create picking lists
            </shipvagoo-menu-item>
            <shipvagoo-menu-item
              v-close-popup
              clickable
              @click="onClickDropdownAction(onClickCreateFulfillmentAndShipments)"
            >
              Create fulfillments and shipments
            </shipvagoo-menu-item>
            <shipvagoo-menu-item
              v-close-popup
              clickable
              @click="onClickDropdownAction(onClickCreateFulfillmentsShipmentsAndPackingSlips)"
            >
              Create fulfillments, shipments and packing lists
            </shipvagoo-menu-item>
            <shipvagoo-menu-item
              v-close-popup
              clickable
              @click="onClickDropdownAction(onClickCreatePickListsFulfillmentsAndShipments)"
            >
              Create picking lists, fulfillments and shipments
            </shipvagoo-menu-item>
            <shipvagoo-menu-item
              v-close-popup
              clickable
              @click="onClickDropdownAction(onClickCapturePayment)"
            >
              Capture payment
            </shipvagoo-menu-item>
            <shipvagoo-menu-item
              v-if="environment == 'LOCAL'"
              v-close-popup
              clickable
              @click="onClickCancelFulfillment"
            >
              Cancel Fufillments
            </shipvagoo-menu-item>
          </shipvagoo-list>
        </shipvagoo-button-dropdown-v1>
      </div>
    </div>
  </div>
  <div
    class="orders_page q-mt-xs"
    v-if="
      $store.state.integrationStore.count <= 0 &&
      loadingService.loadings.size == 0 &&
      !ordersTableLoading
    "
  >
    <shipvagoo-placeholder
      text="No webshop connected. Link your webshop for effortless order management, shipment creation across multiple carriers and more"
    >
      <template #actions>
        <shipvagoo-button min-width="170px" to="order-integration?need-to-connect=1">
          Connect your webshop
        </shipvagoo-button>
      </template>
    </shipvagoo-placeholder>
  </div>
  <div class="orders_page">
    <div class="orders_page-table">
      <shipvagoo-table-v1
        class="orders-page-table"
        v-model:selected="selectedRows"
        v-model:pagination="pagination"
        :columns="columns"
        :rows="rows"
        row-key="order_number"
        selection="multiple"
        :total="pagination.rowsNumber"
        :loading="ordersTableLoading"
        @request="getOrders($event.pagination)"
        no-data-label="No orders are available"
        @row-click="onRowClick"
      >
        <template v-slot:sync-pagination>
          <q-pagination
            v-if="pagination.page"
            v-model="pagination.page"
            :max="maxPages"
            color="primary"
            max-pages="7"
            :input-style="{ width: '50px' }"
            class="q-ml-md"
            @update:model-value="getOrders(pagination)"
          />
          <q-input
            v-model="inputPage"
            type="number"
            dense
            outlined
            placeholder="Go to page"
            :min="1"
            :max="maxPages"
            class="q-ml-sm goToPage"
            @keyup.enter="goToPage(inputPage)"
            label="Go to page"
          />
        </template>

        <template #header-selection="scope">
          <q-td>
            <shipvagoo-checkbox v-model="scope.selected" size="md" />
          </q-td>
        </template>
        <template #body-selection="scope">
          <q-td @click.stop="handleCellClick($event, scope.row)">
            <shipvagoo-checkbox v-model="scope.selected" size="md" @click.stop />
          </q-td>
        </template>

        <template #body-cell-order_number="props">
          <q-td class="order-number" key="order_number" :props="props">
            {{ props.row.name ?? props.row.order_number }}
          </q-td>
        </template>
        <template #body-cell-channel="props">
          <q-td key="channel" :props="props">
            <img
              v-if="props.row && props.row.platform"
              style="width: 21px; height: 24px"
              :src="asOrder(props.row).platform.image_url"
              decoding="async"
            />
          </q-td>
        </template>
        <template #body-cell-receiver="props">
          <q-td key="receiver" :props="props">
            <div class="row" v-if="asOrder(props.row).shipping_address">
              <div class="order-recipient">
                {{ asOrder(props.row).shipping_address?.name || 'No receiver' }}<br />
                <span class="text-no-wrap">{{ asOrder(props.row).shipping_address?.address1 }}</span
                ><br />
                <span class="text-no-wrap"
                  >{{ _.get(props.row, 'shipping_address.country_code', '') }}-{{
                    _.get(props.row, 'shipping_address.zip', '')
                  }}
                  {{ _.get(props.row, 'shipping_address.city', '') }}
                </span>
              </div>
            </div>
          </q-td>
        </template>
        <template #body-cell-items="props">
          <q-td key="items" :props="props">
            <div class="row">
              <div class="order-items">
                <span class="text-no-wrap"> {{ getItemsQuantity(props.row) }} Items<br /></span>
                {{ getItemsWeight(props.row) }}kg
              </div>
            </div>
          </q-td>
        </template>
        <template #body-cell-amount_with_vat="props">
          <q-td key="amount_with_vat" class="order-total" :props="props">
            <shipvagoo-tooltip
              :tooltip-text="
                PriceService.formatInternationalPrice(findStoreMoney(props.row.total_price_set))
              "
            >
              {{ PriceService.formatInternationalPrice(findLocalMoney(props.row.total_price_set)) }}
            </shipvagoo-tooltip>
          </q-td>
        </template>
        <template #body-cell-ordered_at="props">
          <q-td key="ordered_at" class="ordered-date" :props="props">
            {{ dayjs(String(props.row.ordered_at)).format('DD/MM/YYYY') }} <br />
            {{ dayjs(String(props.row.ordered_at)).format('HH:mm') }}
          </q-td>
        </template>
        <template #body-cell-financial_status="props">
          <q-td key="financial_status" :props="props">
            <order-financial-status
              v-if="props.row.authorization_status !== 'expired'"
              :financial-status="props.row.financial_status.toLowerCase()"
            /><br v-if="props.row.authorization_status !== 'expired'" />
            <shipvagoo-status-tag
              v-if="isPaymentDue(props.row)"
              color="#FF5F00"
              bg-color="#FFD4BB"
              text="Due"
              class="q-mt-sm"
            />
            <shipvagoo-status-tag
              v-if="props.row.authorization_status === 'expiring'"
              text="Expiring"
              bg-color="#FFD4BB"
              color="#FF5F00"
            />
            <shipvagoo-status-tag
              v-else-if="props.row.authorization_status === 'expired'"
              text="Expired"
              bg-color="#FFD4E0"
              color="#BE4748"
            />
          </q-td>
        </template>
        <template #body-cell-is_fullfilled="props">
          <q-td key="is_fullfilled" :props="props">
            <shipvagoo-status-tag
              :color="OrderService.getFulfillmentStatus(props.row).color"
              :bg-color="OrderService.getFulfillmentStatus(props.row).bgColor"
              :text="OrderService.getFulfillmentStatus(props.row).text"
            >
            </shipvagoo-status-tag>
          </q-td>
        </template>
        <template #body-cell-is_sihpment_created="props">
          <q-td key="is_sihpment_created" :props="props">
            <shipvagoo-status-tag
              v-if="OrderService.getOrderShipmentStatus(props.row) == OrderShipmentStatus.COMPLETE"
              color="#0C9247"
              bg-color="#BEF3D7"
              text="Yes"
            />
            <shipvagoo-status-tag
              v-else-if="
                OrderService.getOrderShipmentStatus(props.row) == OrderShipmentStatus.NOT_CREATED
              "
              color="#BE4748"
              bg-color="#FFD4E0"
              text="No"
            />
            <shipvagoo-status-tag v-else color="#6158F5" bg-color="#E1DDFF" text="Partial" />
          </q-td>
        </template>
        <template #body-cell-delivery_method="props">
          <q-td key="delivery_method" :props="props">
            <div class="flex items-center no-wrap">
              <div>
                <div
                  v-if="props.row.delivery_template?.carrier_product_code"
                  class="shipvagoo-table-carrier-logo"
                  :style="{
                    backgroundImage:
                      'url(' +
                      CarrierService.getCarrierFromShippingProductCode(
                        $store.state.courierStore.carriers,
                        $store.state.courierStore.carrierProducts,
                        props.row.delivery_template.carrier_product_code,
                      )?.icon +
                      ')',
                  }"
                ></div>
                <div v-else style="width: 24px; height: 24px"></div>
              </div>
              <div
                v-if="props.row.shipping_lines?.[0]?.delivery_method_title"
                class="order-delivery-methods"
                style="margin-left: 3px"
              >
                {{ props.row.shipping_lines?.[0]?.delivery_method_title }}
              </div>
              <div v-else class="order-delivery-methods" style="margin-left: 3px">
                {{ extractDeliveryMethod(props.row.shipping_lines[0]?.title)?.part1 }}<br />
                {{ extractDeliveryMethod(props.row.shipping_lines[0]?.title)?.part2 }}
              </div>
            </div>
          </q-td>
        </template>
        <template #body-cell-shipping_rule="props">
          <q-td key="shipping_rule" :props="props">
            <!--                        <div class="order-shipping-rules"
                                         v-if="asOrder(props.row).delivery_template?.name">
                                        <span class="text-no-wrap">{{
                                            extractShippingRule(asOrder(props.row).delivery_template?.name)?.part1
                                          }}</span><br />
                                      <span class="text-no-wrap">      
                                          {{ extractShippingRule(asOrder(props.row).delivery_template?.name)?.part2 }}
                                          </span>
                                      <q-tooltip>
                                        {{ asOrder(props.row).delivery_template?.name }}
                                      </q-tooltip>
                                    </div>-->
            <div>
              <shipvagoo-tooltip
                class="order-shipping-rules"
                v-if="asOrder(props.row).delivery_template?.name"
                :tooltip-text="asOrder(props.row).delivery_template?.name"
              >
                <span class="text-no-wrap">{{
                  extractShippingRule(asOrder(props.row).delivery_template?.name)?.part1
                }}</span
                ><br />
                <span class="text-no-wrap">
                  {{ extractShippingRule(asOrder(props.row).delivery_template?.name)?.part2 }}
                </span>
              </shipvagoo-tooltip>
            </div>
          </q-td>
        </template>
        <template #body-cell-shipping_rule_edit="props">
          <q-td key="shipping_rule_edit" :props="props">
            <!-- <q-icon
              @click.prevent="onClickEditShippingRule(props.row)"
              style="margin-top: -6px; margin-right: 8px; margin-left: 13px; color: #7d8398"
              name="far fa-edit"
            ></q-icon> -->
            <q-btn
              @click="onClickEditShippingRule(props.row)"
              dense
              flat
              size="xs"
              icon="far fa-edit"
              :disabled="OrderService.isOrderShipmentCreated(props.row)"
              style="margin-top: -6px; margin-right: 8px; margin-left: 13px; color: #7d8398"
            />
          </q-td>
        </template>
      </shipvagoo-table-v1>
    </div>
  </div>

  <q-dialog v-model="isStartPickPathOpen" persistent class="orders_page-modal">
    <start-pick-path :orders="selectedOrdersForStartPickPath" @on-close="onCloseStartPickPath" />
  </q-dialog>

  <q-dialog v-model="orderModal" @before-hide="getOrders(null, currentFilter)">
    <order-detail
      v-if="selectedRow"
      :row="selectedRow"
      @refresh-order="getOrder($event)"
      @refresh-orders="getOrders(null, currentFilter)"
    />
  </q-dialog>

  <q-dialog v-model="orderModalOld" @before-hide="getOrders(null, currentFilter)">
    <order-detail
      v-if="selectedRow && orderModalOld"
      :row="selectedRow"
      @refresh-order="getOrder($event)"
      @refresh-orders="getOrders(null, currentFilter)"
    />
  </q-dialog>

  <q-dialog
    v-model="selectedOrdersCouldNotPickedModal"
    class="orders_page-modal"
    @before-hide="onCloseSelectedOrdersCouldNotPickedModal"
  >
    <shipvagoo-modal title="Pick Path" size="sm">
      <shipvagoo-card-section>
        <div>
          {{ selectedRows.length - fulfillableOrderCount }} out of the selected
          {{ selectedRows.length }} orders could not be picked.
        </div>
        <div v-if="fulfillableOrderCount > 1">
          Do you want to pick the other {{ fulfillableOrderCount }} orders?
        </div>
        <p v-if="fulfillableOrderCount === 1">Do you want to pick one order?</p>
      </shipvagoo-card-section>
      <shipvagoo-separator />
      <shipvagoo-card-actions>
        <shipvagoo-button class="flex-1" @click="onClickYesOnSelectedOrdersCouldNotPickedModal">
          Yes
        </shipvagoo-button>
        <shipvagoo-button v-close-popup class="flex-1" outline>No</shipvagoo-button>
      </shipvagoo-card-actions>
    </shipvagoo-modal>
  </q-dialog>

  <q-dialog v-model="isDeleteOrdersOpen">
    <shipvagoo-modal title="Delete Orders" size="sm">
      <shipvagoo-card-section>
        Are you sure you wish to delete marked orders? ({{ selectedRows.length }})
      </shipvagoo-card-section>
      <shipvagoo-separator />
      <shipvagoo-card-actions>
        <shipvagoo-button class="flex-1" @click="onDeleteOrders">
          {{ $t('common.yes') }}
        </shipvagoo-button>
        <shipvagoo-button v-close-popup class="flex-1" outline>
          {{ $t('common.no') }}
        </shipvagoo-button>
      </shipvagoo-card-actions>
    </shipvagoo-modal>
  </q-dialog>

  <q-dialog
    no-backdrop-dismiss
    v-if="isCreatePickListsModalOpen && selectedRows.length > 0"
    v-model="isCreatePickListsModalOpen"
  >
    <create-pick-lists :orders="selectedRows" @close="onCloseCreatePickLists" />
  </q-dialog>

  <q-dialog
    no-backdrop-dismiss
    v-if="isCreateFulfillmentsAndShipmentsModalOpen && selectedRows.length > 0"
    v-model="isCreateFulfillmentsAndShipmentsModalOpen"
  >
    <create-fulfillments-and-shipments
      :orders="selectedRows"
      @close="onCloseCreateFulfillmentsAndShipmentsModal"
    />
  </q-dialog>

  <q-dialog
    no-backdrop-dismiss
    v-if="isCreateFulfillmentsShipmentsAndPackingSlipsModalOpen && selectedRows.length > 0"
    v-model="isCreateFulfillmentsShipmentsAndPackingSlipsModalOpen"
  >
    <create-fulfillments-shipments-packing-slips
      :orders="selectedRows"
      @close="onCloseCreateFulfillmentsShipmentsAndPackingSlipsModal"
    />
  </q-dialog>

  <q-dialog
    no-backdrop-dismiss
    v-if="isCreatePickListsFulfillmentsAndShipmentsModalOpen && selectedRows.length > 0"
    v-model="isCreatePickListsFulfillmentsAndShipmentsModalOpen"
  >
    <create-pick-lists-fulfillments-and-shipments
      :orders="selectedRows"
      @close="onCloseCreatePickListsFulfillmentsAndShipmentsModal"
    />
  </q-dialog>

  <q-dialog v-model="isEditDeliveryMethodModalOpen" v-if="selectedRow">
    <change-shipment-template
      :order-code="selectedRow.order_code"
      :delivery-template-code="selectedRow.delivery_template_code"
      :return-template-code="selectedRow.return_template_code"
      :shipping-address="selectedRow.shipping_address"
      :shipping-line-code="
        selectedRow.shipping_lines.length
          ? selectedRow.shipping_lines[selectedRow.shipping_lines.length - 1].code
          : null
      "
      @close="onCloseChangeShipmentTemplate"
    />
  </q-dialog>

  <q-dialog
    no-backdrop-dismiss
    v-if="showCapturePaymentMethodModal && selectedRows.length > 0"
    v-model="showCapturePaymentMethodModal"
  >
    <bulk-orders-capture-payment :orders="selectedRows" @close="onCloseCapturePayment" />
  </q-dialog>
</template>

<script setup lang="ts">
  import { onBeforeMount, onBeforeUnmount, ref, Ref, watch, computed } from 'vue';
  import { QTableProps } from 'quasar';
  import dayjs from 'dayjs';
  import * as _ from 'lodash-es';
  import { IOrder, IOrderProduct, OrderShipmentStatus } from '../model/order.model';
  import { IOption } from '../model/common.model';
  import {
    loadingService,
    loggingService,
    notificationService,
    orderService,
  } from '../services/_singletons';
  import OrderService from '../services/order.service';
  import HelperService from '../services/helper.service';
  import { Pagination } from '../model/common.model';
  import StartPickPath from '../components/orders/start-pick-path.vue';
  import { useStore } from '../store';
  import CarrierService from '../services/carrier.service';
  import PriceService from '../services/price.service';
  import { useRoute } from 'vue-router';
  import { IRequestParam } from '../model/http.model';
  import { useHelper } from '../composeable/Helper';
  import { danishNumberFormat } from '../helpers';

  const $store = useStore();
  const $route = useRoute();
  const { findStoreMoney, findLocalMoney } = useHelper();

  const showCapturePaymentMethodModal = ref<boolean>(false);
  const isEditDeliveryMethodModalOpen = ref<boolean>(false);
  const selectedOrdersCouldNotPickedModal = ref<boolean>(false);
  const fulfillableOrderCount = ref<number>(0);
  const primaryActionLabel = ref<string>('Loading...');
  const primaryActionHandler = ref<() => void>(() => {});
  const search: Ref<string> = ref<string>('');
  const selectedOrdersForStartPickPath = ref<IOrder[]>([]);
  const startPickPathItems: Ref<IOrderProduct[]> = ref<IOrderProduct[]>([]);
  const isStartPickPathOpen: Ref<boolean> = ref(false);
  const isDeleteOrdersOpen: Ref<boolean> = ref(false);
  const orderModal: Ref<boolean> = ref(false);
  const orderModalOld: Ref<boolean> = ref(false);
  const selectedRow: Ref<IOrder | undefined> = ref();
  const subscriptions: (() => void)[] = [];
  const currentFilter = ref<'all' | 'open' | 'ongoing' | 'sent'>('all');
  const isCreatePickListsModalOpen = ref<boolean>(false);
  const isCreateFulfillmentsAndShipmentsModalOpen = ref<boolean>(false);
  const isCreateFulfillmentsShipmentsAndPackingSlipsModalOpen = ref<boolean>(false);
  const isCreatePickListsFulfillmentsAndShipmentsModalOpen = ref<boolean>(false);
  const environment = import.meta.env.VITE_ENVIRONMENT;
  const envPerPage = import.meta.env.VITE_PER_PAGE;
  const ordersTableLoading = ref<boolean>(false);

  const shipmentFilter = ref<string[]>([]);
  const fulfillmentFilter = ref<string[]>([]);
  const paymentFilter = ref<string[]>([]);
  const countryFilter = ref<string[]>([]);
  const integrationFilter = ref<string[]>([]);

  const shipmentOptions = ref<IOption<string, string>[]>([
    {
      label: 'Yes',
      value: 'yes',
    },
    {
      label: 'No',
      value: 'no',
    },
    {
      label: 'Partial',
      value: 'partial',
    },
  ]);

  const fulfillmentOptions = ref<IOption<string, string>[]>([
    {
      label: 'Fulfilled',
      value: 'fulfilled',
    },
    {
      label: 'Partially fulfilled',
      value: 'partially_fulfilled',
    },
    {
      label: 'Unfulfilled',
      value: 'unfulfilled',
    },
  ]);

  const paymentOptions = ref<IOption<string, string>[]>([
    {
      label: 'Paid',
      value: 'paid',
    },
    {
      label: 'Authorized',
      value: 'authorized',
    },
    {
      label: 'Partially paid',
      value: 'partially_paid',
    },
    {
      label: 'Refunded',
      value: 'refunded',
    },
    {
      label: 'Partially refunded',
      value: 'partially_refunded',
    },
  ]);
  const countryOptions = ref<IOption<string, string>[]>([]);

  const integrateOptions = computed<IOption<string, string>[]>(() => {
    return $store.state.integrationStore.integrations.map((integration) => {
      return {
        label: integration.name,
        value: integration.integration_code.toString(),
      };
    });
  });

  const pagination: Ref<Pagination> = ref({
    page: 1,
    rowsPerPage: envPerPage ?? 20,
    rowsNumber: 0,
    descending: true,
  });

  const inputPage = ref<number>(1);
  const totalRecords = computed(() => {
    return pagination.value?.rowsNumber ?? 10;
  });
  const maxPages = ref(Math.ceil(totalRecords.value / (pagination.value.rowsPerPage ?? 10)));

  const goToPage = (page: number) => {
    console.log('page ', page);
    if (page >= 1 && page <= maxPages.value) {
      pagination.value.page = page;
      getOrders(pagination.value);
    }
  };

  const columns: QTableProps['columns'] = [
    {
      name: 'order_number',
      label: 'Order',
      field: 'order_number',
      sortable: false,
      align: 'left',
    },
    {
      name: 'ordered_at',
      label: 'Date',
      field: 'ordered_at',
      sortable: false,
      align: 'left',
    },
    {
      name: 'receiver',
      label: 'Recipient',
      field: 'shipping_address',
      sortable: false,
      align: 'left',
    },
    {
      name: 'channel',
      label: 'Channel',
      field: 'channel',
      sortable: false,
      align: 'center',
    },
    {
      name: 'amount_with_vat',
      label: 'Total',
      field: 'amount_with_vat',
      sortable: false,
      align: 'right',
    },
    {
      name: 'items',
      label: 'Items',
      field: 'items',
      sortable: false,
      align: 'left',
    },
    {
      name: 'financial_status',
      label: 'Payment',
      field: 'financial_status',
      sortable: false,
      align: 'left',
    },

    {
      name: 'is_fullfilled',
      label: 'Fulfillment',
      field: 'is_fullfilled',
      align: 'left',
    },
    {
      name: 'is_sihpment_created',
      label: 'Shipment',
      field: 'is_sihpment_created',
      align: 'left',
    },
    {
      name: 'delivery_method',
      label: 'Shipping and delivery',
      field: 'delivery_method',
      sortable: false,
      align: 'left',
      headerClasses: 'text-no-wrap',
    },
    {
      name: 'shipping_rule',
      label: 'Shipping rule',
      field: 'shipping_rule',
      sortable: false,
      align: 'left',
      headerClasses: 'text-no-wrap',
    },
    {
      name: 'shipping_rule_edit',
      label: '',
      field: 'shipping_rule_edit',
      sortable: false,
      align: 'left',
    },
  ];

  const rows = ref<IOrder[]>([]);
  const selectedRows = ref<IOrder[]>([]);

  const onClickEditShippingRule = (order: IOrder) => {
    selectedRow.value = order;
    isEditDeliveryMethodModalOpen.value = true;
  };
  const getItemsQuantity = (row: IOrder) => {
    return row.line_items.reduce(
      (accumulator: number, orderProduct: IOrderProduct) =>
        accumulator + orderProduct.quantity - orderProduct.returned_quantity,
      0,
    );
  };
  const getItemsWeight = (row: IOrder) => {
    /*const weightInGrams = row.line_items.filter((item: IOrderProduct) => item.grams != null).reduce(
      (accumulator: number, orderProduct: IOrderProduct) => accumulator + (orderProduct.product ? (orderProduct.product?.weight as unknown as number) : 0),
      0
    );
    const weightInKiloGrams = row.line_items.filter((item: IOrderProduct) => item.grams == null).reduce(
      (accumulator: number, orderProduct: IOrderProduct) => accumulator + (orderProduct.product ? (orderProduct.product.weight as unknown as number) : 0),
      0
    );
    return weightInKiloGrams + (weightInGrams / 1000)*/
    return danishNumberFormat((row.total_weight ?? 0) / 1000);
  };
  const onCloseChangeShipmentTemplate = () => {
    isEditDeliveryMethodModalOpen.value = false;
    getOrders(pagination.value);
  };
  const searchVal = (value: string) => {
    search.value = value;
    pagination.value.page = 1;
    getOrders(pagination.value);
  };
  const onChangeFilter = () => {
    pagination.value.page = 1;
    getOrders(pagination.value);
  };

  const isPaymentDue = (order: IOrder) => {
    const payment_terms = order.payment_terms;
    if (payment_terms) {
      return (
        payment_terms.payment_schedules[0]?.due_at < new Date().toISOString() &&
        order.financial_status.toLowerCase() !== 'paid' &&
        order.financial_status.toLowerCase() !== 'refunded' &&
        order.financial_status.toLowerCase() !== 'partially_refunded' &&
        order.authorization_status !== 'expired'
      );
    }
    return false;
  };
  const getOrders = async (
    tablePagination: Pagination | null = null,
    orderType: 'all' | 'open' | 'ongoing' | 'sent' = currentFilter.value,
  ) => {
    try {
      /** commented because its functionality not visible **/
      //await $store.dispatch('orderStore/getOpenOrderCount');

      selectedRows.value = [];

      // loadingService.startLoading('main-loader:get-orders');
      ordersTableLoading.value = true;
      if (tablePagination) {
        pagination.value.page = tablePagination.page;
        pagination.value.rowsPerPage = tablePagination.rowsPerPage;
        pagination.value.sortBy = tablePagination.sortBy;
        pagination.value.descending = tablePagination.descending;
      }

      const field: string = _.get(pagination.value, 'sortBy') ?? 'created_at';
      const order: 'desc' | 'asc' = _.get(pagination.value, 'descending', true) ? 'desc' : 'asc';
      const page: number = pagination.value.page ?? 1;

      let mGetOrders;

      if (orderType === 'open') {
        mGetOrders = orderService.getOpenOrders.bind(orderService);
      } else if (orderType === 'ongoing') {
        mGetOrders = orderService.getOngoingOrders.bind(orderService);
      } else if (orderType === 'sent') {
        mGetOrders = orderService.getSentOrders.bind(orderService);
      } else {
        mGetOrders = orderService.getOrders.bind(orderService);
      }
      const params: IRequestParam[] = [];
      if (search.value && search.value !== '') {
        params.push({ key: 'name[filter]', value: String(encodeURIComponent(search.value)) });
      }

      params.push({ key: 'page', value: page });
      params.push({ key: 'per_page', value: tablePagination?.rowsPerPage ?? envPerPage ?? 20 });
      params.push({ key: `sorting[${field}]`, value: order });

      if (fulfillmentFilter.value.length) {
        params.push({ key: 'fulfillment_status', value: fulfillmentFilter.value.toString() });
      }
      if (shipmentFilter.value.length) {
        params.push({ key: 'shipment_status', value: shipmentFilter.value.toString() });
      }
      if (paymentFilter.value.length) {
        params.push({ key: 'payment_status', value: paymentFilter.value.toString() });
      }
      if (countryFilter.value.length) {
        params.push({ key: 'countries', value: countryFilter.value.toString() });
      }
      if (integrationFilter.value.length) {
        params.push({ key: 'integrations', value: integrationFilter.value.toString() });
      }

      console.log('params ', params);
      const result = await mGetOrders(params);
      rows.value = [];
      setTimeout(() => {
        rows.value = result.entities; //?.sort((a, b) => (new Date(b.created_at) as any) - (new Date(a.created_at) as any));
      }, 0);
      pagination.value.page = result.current_page;
      pagination.value.rowsNumber = result.total;
      inputPage.value = result.current_page;
    } catch (e) {
      loggingService.error('Error while getting orders', e);
      notificationService.showError('Error while getting orders');
    } finally {
      //loadingService.stopLoading('main-loader:get-orders');
      ordersTableLoading.value = false;
    }
  };

  const extractShippingRule = (name: any) => {
    const parts = name.split('-', 3);

    if (parts.length >= 3) {
      let part1 = parts.slice(0, 3).join('-').trim();
      let part2 = name
        .replace(part1, '')
        ?.replace(/^\s*-\s*/, '')
        ?.trim();
      if (part2.length > 40) {
        part2 = part2.substring(0, 37) + '...';
      }
      return {
        part1,
        part2,
      };
    } else {
      return { part1: name, part2: '' };
    }
  };
  const onCloseStartPickPath = () => {
    isStartPickPathOpen.value = false;
  };

  const extractDeliveryMethod = (title: string): any => {
    try {
      if (title == null || title == '') {
        return { part1: title, part2: '' };
      }
      const parts = title.split(/[\s-]+/);
      return { part1: parts.slice(0, 2).join(' ').trim(), part2: parts.slice(2).join(' ').trim() };
    } catch (exception) {
      return { part1: title, part2: '' };
    }
  };

  const onClickStartPickPath = async () => {
    if (selectedRows.value.length <= 0) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:start-pick-path');

      const promises = selectedRows.value.map((order: IOrder) =>
        orderService.getOrder(order.order_code),
      );

      const orders: IOrder[] = await Promise.all(promises);

      if (orders.every((order: IOrder) => OrderService.isOrderFulfilled(order))) {
        throw new Error(
          'It is not possible to pick the selected orders. An order cannot be picked if it has already been handled or has an order status that does not allow picking.',
        );
      }

      if (orders.some((order: IOrder) => OrderService.isOrderFulfilled(order))) {
        fulfillableOrderCount.value = orders.filter(
          (order) => !OrderService.isOrderFulfilled(order),
        ).length;

        selectedOrdersCouldNotPickedModal.value = true;
        return;
      }

      startPickPathItems.value = [];

      await $store.dispatch('pickSettingsStore/syncPickSettings');

      orders.forEach((row) => {
        orderService.setOrderProcessing(row.order_code);
      });

      selectedOrdersForStartPickPath.value = orders;
      isStartPickPathOpen.value = true;
    } catch (e) {
      if (e instanceof Error) {
        notificationService.showError(e.message);
      } else {
        loggingService.error('Error while starting pick path', e);
        notificationService.showError('Error while starting pick path');
      }
    } finally {
      loadingService.stopLoading('main-loader:start-pick-path');
    }
  };

  const getOrder = async (orderId: number) => {
    try {
      loadingService.startLoading('main-loader:get-order');
      selectedRow.value = await orderService.getOrder(orderId);
      orderModal.value = true;
    } catch (e) {
      loggingService.error('Error while getting order', e);
      notificationService.showError('Error while getting order');
    } finally {
      loadingService.stopLoading('main-loader:get-order');
    }
  };
  const handleCellClick = (event: Event, row: IOrder) => {
    const index = selectedRows.value.findIndex((selectedRow) => selectedRow === row);
    if (index === -1) {
      selectedRows.value.push(row);
    } else {
      selectedRows.value.splice(index, 1);
    }
    event.stopPropagation();
  };

  /* const onContextMenu = async (event: Event, row: IOrder) => {
      event.preventDefault();
      try {
        loadingService.startLoading('main-loader:get-order');
        selectedRow.value = await orderService.getOrder(row.order_code);
        orderModalOld.value = true;
      } catch (e) {
        loggingService.error('Error while getting order', e);
        notificationService.showError('Error while getting order');
      } finally {
        loadingService.stopLoading('main-loader:get-order');
      }
    }; */
  const onRowClick = (event: Event, row: IOrder) => {
    if (HelperService.containsIgnoredRowElements(event)) {
      return;
    }

    getOrder(row.order_code);
  };

  const onClickCreatePickLists = () => {
    isCreatePickListsModalOpen.value = true;
  };

  const onClickDropdownAction = (cb: (...args: unknown[]) => void) => {
    if (selectedRows.value.length <= 0) {
      notificationService.showError('Please select at least one order');
      return;
    }

    cb();
  };

  const onCloseCreatePickLists = () => {
    isCreatePickListsModalOpen.value = false;
    selectedRows.value = [];
  };

  const setPrimaryAction = () => {
    if ($store.state.pickSettingsStore.primary_action === 'StartPickPath') {
      primaryActionLabel.value = 'Start Pick Path';
      primaryActionHandler.value = onClickStartPickPath;
    }

    if ($store.state.pickSettingsStore.primary_action === 'CreatePickPathList') {
      primaryActionLabel.value = 'Create Picking Lists';
      primaryActionHandler.value = onClickCreatePickLists;
    }
  };

  const onCloseSelectedOrdersCouldNotPickedModal = () => {
    selectedOrdersCouldNotPickedModal.value = false;
    fulfillableOrderCount.value = 0;
  };

  const onClickYesOnSelectedOrdersCouldNotPickedModal = async () => {
    selectedOrdersCouldNotPickedModal.value = false;
    startPickPathItems.value = [];

    loadingService.startLoading('main-loader:start-pick-path');
    await $store.dispatch('pickSettingsStore/syncPickSettings');

    // TODO: This is a temporary solution. We need to refactor this.
    const ordersToBeFulfilled: IOrder[] = selectedRows.value.filter(
      (order) => order.fulfillment_status !== 'Fulfilled',
    );

    ordersToBeFulfilled.forEach((row) => {
      orderService.setOrderProcessing(row.order_code);
    });

    const promises = ordersToBeFulfilled.map((order: IOrder) =>
      orderService.getOrder(order.order_code),
    );

    const result: IOrder[] = await Promise.all(promises);
    selectedOrdersForStartPickPath.value = result;
    loadingService.stopLoading('main-loader:start-pick-path');
    isStartPickPathOpen.value = true;
  };

  const onClickDeleteOrders = () => {
    if (selectedRows.value.length <= 0) {
      notificationService.showError('Please select at least one order');
      return;
    }

    isDeleteOrdersOpen.value = true;
  };

  const onDeleteOrders = async () => {
    if (selectedRows.value.length <= 0) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:delete-orders');
      await orderService.deleteOrders(selectedRows.value.map((order: IOrder) => order.order_code));
      notificationService.showSuccess('Orders deleted successfully');
      selectedRows.value = [];
      isDeleteOrdersOpen.value = false;
      getOrders(null);
    } catch (e) {
      loggingService.error('Error while deleting orders', e);
      notificationService.showError('Error while deleting orders');
    } finally {
      loadingService.stopLoading('main-loader:delete-orders');
    }
  };

  const onClickCreateFulfillmentAndShipments = () => {
    if (selectedRows.value.length <= 0) {
      return;
    }
    $store.dispatch('companyStore/syncCompanyData');
    isCreateFulfillmentsAndShipmentsModalOpen.value = true;
  };

  const onCloseCreateFulfillmentsAndShipmentsModal = async () => {
    isCreateFulfillmentsAndShipmentsModalOpen.value = false;
    await $store.dispatch('companyStore/syncCompanyData');
    await $store.dispatch('orderStore/refreshOrders');
  };

  const onClickCreateFulfillmentsShipmentsAndPackingSlips = () => {
    if (selectedRows.value.length <= 0) {
      return;
    }

    isCreateFulfillmentsShipmentsAndPackingSlipsModalOpen.value = true;
  };

  const onCloseCreateFulfillmentsShipmentsAndPackingSlipsModal = async () => {
    isCreateFulfillmentsAndShipmentsModalOpen.value = false;
    await $store.dispatch('companyStore/syncCompanyData');
    await $store.dispatch('orderStore/refreshOrders');
  };

  const onClickCapturePayment = () => {
    if (selectedRows.value.length <= 0) {
      return;
    }
    showCapturePaymentMethodModal.value = true;
  };
  const onCloseCapturePayment = async () => {
    showCapturePaymentMethodModal.value = false;
    await $store.dispatch('companyStore/syncCompanyData');
    await $store.dispatch('orderStore/refreshOrders');
  };

  const onClickCreatePickListsFulfillmentsAndShipments = () => {
    if (selectedRows.value.length <= 0) {
      return;
    }

    isCreatePickListsFulfillmentsAndShipmentsModalOpen.value = true;
  };

  const onCloseCreatePickListsFulfillmentsAndShipmentsModal = async () => {
    isCreatePickListsFulfillmentsAndShipmentsModalOpen.value = false;
    await $store.dispatch('companyStore/syncCompanyData');
    await $store.dispatch('orderStore/refreshOrders');
  };

  /** this is for multiple fulfillment cancels for devs only **/
  const onClickCancelFulfillment = async () => {
    if (selectedRows.value.length <= 0) {
      return;
    }
    selectedRows.value.forEach((order: IOrder) => {
      const fulfillments = order.fulfillments;
      fulfillments?.forEach(async (fulfillMent) => {
        await orderService.cancelFullfillment(order.order_code, fulfillMent.fulfillment_code);
      });
    });
    await getOrders();
  };

  const asOrder = (row: IOrder): IOrder => row;

  /** watch properties */
  watch(
    () => totalRecords.value,
    () => {
      maxPages.value = Math.ceil(totalRecords.value / (pagination.value.rowsPerPage ?? 10));
    },
  );
  watch(inputPage, (newPage) => {
    if (newPage < 1) {
      inputPage.value = 1;
    } else if (newPage > maxPages.value) {
      inputPage.value = maxPages.value;
    }
  });
  watch(() => $store.state.pickSettingsStore.primary_action, setPrimaryAction);
  /*watch(() => currentFilter.value, (newVal, oldVal) => {
    console.log("Old ", oldVal)
    console.log("New ", newVal)
  })*/

  const getRecipientCountries = async () => {
    const countries = (await orderService.getRecipientCountries()) ?? [];
    countryOptions.value = countries
      .filter((country) => country.country_code != null)
      .map((country) => {
        return {
          label: country.country,
          value: country.country_code,
        };
      });
  };
  onBeforeMount(async () => {
    $store.dispatch('pickSettingsStore/syncPickSettings');
    getRecipientCountries();
    getOrders(null);
    setPrimaryAction();
    if ($route.query.id !== undefined && $route.query.id !== null) {
      getOrder(parseInt($route.query.id.toString()));
    }

    subscriptions.push(
      $store.subscribeAction((action) => {
        if (action.type === 'orderStore/refreshOrders') {
          isCreateFulfillmentsShipmentsAndPackingSlipsModalOpen.value = false;
          isCreatePickListsFulfillmentsAndShipmentsModalOpen.value = false;
          isCreateFulfillmentsAndShipmentsModalOpen.value = false;
          isCreatePickListsModalOpen.value = false;
          getOrders(null);
        }
      }),
    );
  });
  onBeforeUnmount(() => {
    subscriptions.forEach((unsubscribe) => unsubscribe());
  });
</script>
<style lang="scss">
  .linear_progress_labels {
    margin-top: 12px;
  }

  .order_modal-responsive {
    .order_modal-responsive-side_actions {
      width: 321px;
      padding-left: 0;
    }

    .order_modal-responsive-fulfillment_events {
      flex: 1;
      padding-right: 10px;
    }

    @media (max-width: 600px) {
      flex-direction: column;

      .order_modal-responsive-fulfillment_events {
        padding-right: 16px;
      }

      .order_modal-responsive-side_actions {
        width: unset;
        padding-left: 16px;
      }
    }
  }

  .orders_page {
    padding: 0 40px 10px 40px;
    @media (max-width: 1024px) {
      padding: 0 20px 10px 20px;
    }

    .info {
      font-size: 15px;
      color: #e1e1e1;

      &-label {
        display: block;
        margin-bottom: 10px;
        font-weight: bold;
      }
    }

    &-actions {
      display: flex;
      flex-direction: row;
      margin-top: 15px;

      & > *:not(:first-child) {
        margin-left: 10px;

        @media (max-width: 600px) {
          margin-left: unset;
        }
      }

      .big_search-rows_per_page-group {
        display: flex;
        flex: 1;
        flex-direction: row;

        & > *:not(:first-child) {
          margin-left: 10px;
        }

        .rows_per_page {
          display: none;

          @media (max-width: $tablet) {
            display: block;
          }
        }
      }

      &-big_search {
        flex: 1;
        width: 320px;
      }

      @media (max-width: 600px) {
        flex-direction: column;

        &-big_search {
          margin-bottom: 10px;
        }
      }
    }

    &-chips {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 11px;
    }

    &-modal {
      &-header {
        font-size: 18px;
      }

      &-order_modal {
        display: flex;
        flex-direction: column;
        gap: 20px;

        &-fulfillments {
          &-title {
            margin-bottom: 20px;
            font-size: 20px;
            line-height: 24px;
          }

          &-header {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            padding: 20px;

            &-prices {
              display: flex;
              flex-direction: row;
              min-width: max-content;
              height: 90px;

              & > *:not(:first-child) {
                margin-left: 30px;
              }
            }
          }

          &-footer {
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            padding: 20px;

            & > *:not(:first-child) {
              margin-left: 10px;
            }

            @media (max-width: 800px) {
              flex-direction: column;

              & > *:not(:first-child) {
                margin-top: 10px;
                margin-left: unset;
              }
            }
          }
        }

        &-events {
          &-title {
            margin-bottom: 20px;
            font-size: 20px;
            line-height: 24px;
          }

          &-date {
            display: flex;
            flex-direction: column;
            font-size: 12px;
            line-height: 14px;
            color: #e1e1e1;
          }
        }
      }

      .q-card {
        min-width: 310px;
        //  background: #151526;
        background: #fff;

        &.pick_order {
          width: 898px;
          max-width: 898px;
        }

        &.order_modal {
          width: 1038px;
          max-width: 1038px;
        }
      }

      &-footer {
        display: flex;
        justify-content: flex-end;

        &-pick_button {
          width: 220px;
        }
      }
    }

    &-success {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 42px;
      height: 42px;
      border: 1px solid #389b99;
      border-radius: 50%;

      img {
        width: 14px;
        height: 10px;
        filter: invert(55%) sepia(11%) saturate(2247%) hue-rotate(130deg) brightness(95%)
          contrast(76%);
      }

      &-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        margin-top: 44px !important;
        margin-bottom: 44px !important;

        .find_service_point {
          display: flex;
          margin-top: 24px;
        }
      }

      &-info {
        margin-top: 24px;
        font-size: 15px;
        line-height: 17px;
        text-align: center;

        p {
          margin-bottom: 10px;
        }
      }
    }
  }

  .q-timeline--comfortable .q-timeline__subtitle {
    width: unset !important;
  }

  .q-timeline--comfortable--right .q-timeline__subtitle {
    padding-right: 11px !important;
  }

  .q-timeline--comfortable--right .q-timeline__content {
    padding-left: 0 !important;
    margin-left: -2px !important;
  }

  .q-timeline__dot::before,
  .q-timeline__dot::after {
    position: absolute;
    display: block;
    content: '';
    background: #1f2124;
  }

  .border_container {
    border: 1px solid #e1e1e1;
    border-radius: 5px;
  }

  // Temporary Quasar Overrides

  .q-linear-progress__track {
    opacity: 1 !important;
  }

  .q-item {
    align-items: center;
  }

  .column-on-tablet {
    @media (max-width: $tablet) {
      flex-direction: column;
      gap: 26px !important;

      &.full-width-items > * {
        width: 100% !important;
      }
    }
  }

  .text-shipvago_primary {
    color: #ffffff !important;
  }

  .bg-shipvago_primary {
    background-color: $btn-color !important;
  }

  .text-shipvago_primary_three {
    color: #ffffff !important;
  }

  .bg-shipvago_primary_three {
    background-color: $primary-three !important;
  }

  .show-on-tablet {
    $min-desktop-value: $tablet + 1px;

    @media (min-width: $min-desktop-value) {
      display: none;
    }
  }

  .show-on-desktop {
    @media (max-width: 1024px) {
      display: none;
    }
  }

  .orders-country_flag {
    width: 20px;
    height: 20px;
    object-fit: cover;
    border-radius: 50%;
  }

  .action-container {
    display: flex !important;
    min-height: 50px;
  }

  @media (max-width: 600px) {
    .action-container {
      flex-direction: column;
      min-height: 120px;
      margin-bottom: 10px;
    }
  }
</style>

<style>
  .orders-page-table th:nth-child(1),
  .orders-page-table td:nth-child(1),
  .orders-page-table th:nth-child(13),
  .orders-page-table td:nth-child(13) {
    width: 2% !important;
  }

  .orders-page-table th:nth-child(2),
  .orders-page-table td:nth-child(2) {
    width: 4% !important;
  }

  .orders-page-table th:nth-child(3),
  .orders-page-table td:nth-child(3),
  .orders-page-table th:nth-child(7),
  .orders-page-table td:nth-child(7) {
    width: 7% !important;
  }

  .orders-page-table th:nth-child(4),
  .orders-page-table td:nth-child(4) {
    width: 10% !important;
  }

  .orders-page-table th:nth-child(5),
  .orders-page-table td:nth-child(5) {
    width: 3% !important;
  }

  .orders-page-table th:nth-child(6),
  .orders-page-table td:nth-child(6),
  .orders-page-table th:nth-child(10),
  .orders-page-table td:nth-child(10) {
    width: 8% !important;
  }

  .orders-page-table th:nth-child(8),
  .orders-page-table td:nth-child(8) {
    max-width: 10% !important;
  }

  .orders-page-table th:nth-child(9),
  .orders-page-table td:nth-child(9) {
    width: 9% !important;
  }

  .orders-page-table th:nth-child(11),
  .orders-page-table td:nth-child(11),
  .orders-page-table th:nth-child(12),
  .orders-page-table td:nth-child(12) {
    width: 15% !important;
  }
</style>

<style scoped lang="css">
  .two-line-text {
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .order-number,
  .ordered-date,
  .order-recipient,
  .order-total,
  .order-items,
  .order-delivery-methods,
  .order-shipping-rules {
    font-family: Helvetica, sans-serif !important;
    font-size: 13px !important;
    font-style: normal !important;
    font-weight: 400 !important;
    color: #212121 !important;
  }

  .order-number,
  .order-total,
  .order-items {
    line-height: normal !important;
  }

  .ordered-date,
  .order-recipient,
  .order-delivery-methods,
  .order-shipping-rules {
    line-height: 16px !important;
  }

  .goToPage {
    width: 100px;
    margin: 0 10px;
  }
</style>
