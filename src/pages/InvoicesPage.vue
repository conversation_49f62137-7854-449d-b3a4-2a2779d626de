<template>
  <div class="flex justify-between items-center title-bar-heading">
    <title-bar style="width: 100%;" :title="$t('common.invoices')"/>
  </div>
  <div class="invoices_page">
    <shipvagoo-tabfilter-v1 align="left" v-model="currentFilter" :options="tabOptions"/>
    <div v-if="currentFilter=='monthly_invoices'">
      <monthly-invoices-list></monthly-invoices-list>
    </div>
    <div v-if="currentFilter=='invoices'">
      <invoices-list></invoices-list>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {ref} from 'vue';
  import {ITabOption} from "../model/common.model";

  const currentFilter = ref<'monthly_invoices' | 'invoices'>('monthly_invoices');
  const tabOptions: ITabOption[] = [
    {
      label: 'Monthly Invoices',
      value: 'monthly_invoices',
      onClick: () => {
      },
    },
    {
      label: 'Invoices',
      value: 'invoices',
      onClick: () => {

      },
    }
  ];


</script>

<style scoped lang="scss">
  .invoices_page {
    padding-right: 40px;
    padding-left: 40px;
    margin-top: -25px;
  }
</style>
