<template>
  <Title :title="$t('common.staff_accounts')" />
  <div class="staff_accounts_page">
    <div class="staff_accounts_page-actions">
      <div class="big_search-rows_per_page-group">
        <big-search
          v-model="search"
          class="staff_accounts_page-actions-big_search"
          @update:model-value="getCompanyUsers(null)"
        />
      </div>

      <!-- <shipvagoo-button outline label="Download bulk" /> -->
    </div>

    <shipvagoo-table
      v-model:pagination="pagination"
      :columns="columns"
      :rows="rows"
      :total="pagination.rowsNumber"
      @row-click="onClickRow"
      @request="getCompanyUsers($event.pagination)"
    >
      <template #body-cell-more="props">
        <q-td :props="props" class="more">
          <q-btn dense flat round   style="color:#1f2124;" class="more-action" field="edit" icon="more_horiz">
            <shipvagoo-menu>
              <shipvagoo-menu-item clickable @click="onClickDeleteStaffMember(props.row)">
                {{ $t('common.delete') }}
              </shipvagoo-menu-item>
            </shipvagoo-menu>
          </q-btn>
        </q-td>
      </template>
    </shipvagoo-table>
  </div>

  <!-- <shipvagoo-fab icon="add" @click="onClickInviteStaff" /> -->

  <q-dialog v-model="isStaffDetailModalOpen" @before-hide="onCloseInvitationDetailModal">
    <shipvagoo-modal :title="$t('staff_accounts.update_staff')">
      <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
        <div class="input-double input-double-responsive">
          <shipvagoo-input v-model="staffDetailForm.name" :label="$t('common.name')" />
          <shipvagoo-input v-model="staffDetailForm.email" :label="$t('common.email')" />
        </div>
      </shipvagoo-card-section>
      <shipvagoo-separator />
      <shipvagoo-card-section>
        <shipvagoo-checkbox
          v-model="staffDetailForm.staffMemberHasAccessToEverything"
          size="sm"
          style="font-size: 12px"
          @update:model-value="onUpdateStaffMemberHasAccessToEverything"
        >
          {{ $t('staff_accounts.staff_member_has_access_to_everything') }}
        </shipvagoo-checkbox>
      </shipvagoo-card-section>
      <shipvagoo-separator />
      <shipvagoo-card-section style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px">
        <shipvagoo-checkbox
          v-for="_module in modules"
          :key="_module.module_code"
          v-model="_module.isChecked"
          size="sm"
          :disabled="_module.module_key === EModukeKey.DASHBOARD"
          :label="_module.name"
          @update:model-value="onUpdateModuleCheckbox"
        />
      </shipvagoo-card-section>
      <shipvagoo-separator />
      <shipvagoo-card-actions>
        <shipvagoo-button
          :disable="
            isEmpty(staffDetailForm.email) ||
            isEmpty(staffDetailForm.name) ||
            modules.every((m) => !m.isChecked)
          "
          min-width="120px"
          @click="onClickSave"
        >
          {{ $t('common.save') }}
        </shipvagoo-button>
      </shipvagoo-card-actions>
    </shipvagoo-modal>
  </q-dialog>
</template>

<script setup lang="ts">
  import { computed, onBeforeMount, Ref, ref } from 'vue';
  import { QTableProps } from 'quasar';
  import { isEmpty } from 'lodash-es';
  import useVuelidate, { ValidationArgs } from '@vuelidate/core';
  import { email, helpers, required } from '@vuelidate/validators';
  import { useI18n } from 'vue-i18n';
  import Title from '../components/title-bar.vue';
  import {
    companyService,
    loadingService,
    loggingService,
    moduleService,
    notificationService,
  } from '../services/_singletons';
  import { EModukeKey, IModule } from '../model/module.model';
  import { ICheckable, Pagination } from '../model/common.model';
  import { ICompanyUser } from '../model/company.model';
  import HelperService from '../services/helper.service';
  import { IRequestParam } from '../model/http.model';

  const isStaffDetailModalOpen = ref<boolean>();

  const selectedUserId = ref<number | null>(null);
  const { t } = useI18n();

  interface IStaffAccountForm {
    name: string;
    email: string;
    staffMemberHasAccessToEverything: boolean;
  }
  const staffDetailForm = ref<IStaffAccountForm>({
    name: '',
    email: '',
    staffMemberHasAccessToEverything: false,
  });

  const $v = useVuelidate<IStaffAccountForm, ValidationArgs<IStaffAccountForm>>(
    {
      name: {
        required: helpers.withMessage('This field is required.', required),
      },
      email: {
        required: helpers.withMessage('This field is required.', required),
        email: helpers.withMessage('This email not using', email),
      },
      staffMemberHasAccessToEverything: {},
    },
    staffDetailForm,
  );

  const modules: Ref<ICheckable<IModule>[]> = ref<ICheckable<IModule>[]>([]);

  const search: Ref<string> = ref<string>('');
  const columns = computed<QTableProps['columns']>(() => [
    { name: 'name', field: 'name', label: t('common.name'), align: 'left', sortable: true },
    { name: 'email', field: 'email', label: t('common.email'), align: 'left', sortable: true },
    { name: 'more', field: 'more', label: t('common.more'), align: 'right' },
  ]);
  const rows = ref<ICompanyUser[]>([]);
  const pagination: Ref<Pagination> = ref({
    page: 1,
    rowsPerPage: 10,
    rowsNumber: 0,
    descending: true,
  });

  const getCompanyUsers = async (tablePagination: Pagination | null) => {
    try {
      loadingService.startLoading('main-loader:get-company-users');
      const page = tablePagination?.page ?? 1;
      const limit = tablePagination?.rowsPerPage ?? 10;

      const params: IRequestParam[] = [];

      if (search.value && search.value !== '') {
        params.push(
          { key: 'conditions[name:like]', value: search.value },
          { key: 'conditions[email:like]', value: search.value },
        );
      }

      if (tablePagination?.sortBy) {
        const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
        params.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
      } else {
        params.push({ key: 'sorting[created_at]', value: 'desc' });
      }

      const response = await companyService.getCompanyUsers(page, limit, params);
      rows.value = response.entities;
      pagination.value.rowsNumber = response.total;
      pagination.value.page = page;
      pagination.value.rowsPerPage = limit;
      pagination.value.descending = tablePagination?.descending;
      pagination.value.sortBy = tablePagination?.sortBy;
    } catch (e) {
      loggingService.error('Error getting company users', e);
      notificationService.showSuccess('Error getting company users');
    } finally {
      loadingService.stopLoading('main-loader:get-company-users');
    }
  };

  const onClickRow = (event: Event, row: ICompanyUser) => {
    if (HelperService.containsIgnoredRowElements(event)) {
      return;
    }

    selectedUserId.value = row.user_code;
    staffDetailForm.value = {
      name: row.name,
      email: row.email,
      staffMemberHasAccessToEverything: false,
    };

    modules.value = modules.value.map((_m) => {
      const m = { ..._m };
      m.isChecked = row.permitted_modules.includes(m.module_code);
      return m;
    });

    staffDetailForm.value.staffMemberHasAccessToEverything = modules.value.every(
      (m) => m.isChecked,
    );

    isStaffDetailModalOpen.value = true;
  };

  const onClickSave = async () => {
    await $v.value.$validate();

    if ($v.value.$error) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:update-staff-member');
      await companyService.updateCompanyUser(selectedUserId.value ?? 0, {
        name: staffDetailForm.value.name,
        email: staffDetailForm.value.email,
        permitted_modules: modules.value.filter((m) => m.isChecked).map((m) => m.module_code),
      });

      isStaffDetailModalOpen.value = false;

      notificationService.showSuccess('Staff member updated successfully');
      getCompanyUsers(pagination.value);
    } catch (e) {
      loggingService.error('Error updating staff member', e);
      notificationService.showError('Error updating staff member');
    } finally {
      loadingService.stopLoading('main-loader:update-staff-member');
    }
  };

  const onCloseInvitationDetailModal = () => {
    staffDetailForm.value = {
      name: '',
      email: '',
      staffMemberHasAccessToEverything: false,
    };

    modules.value = modules.value.map((m): ICheckable<IModule> => ({ ...m, isChecked: false }));
  };

  const onUpdateStaffMemberHasAccessToEverything = (value: boolean) => {
    modules.value = modules.value.map(
      (_module: IModule): ICheckable<IModule> => ({
        ..._module,
        isChecked: value,
      }),
    );
  };

  const onUpdateModuleCheckbox = () => {
    staffDetailForm.value.staffMemberHasAccessToEverything = modules.value.every(
      (_module: ICheckable<IModule>) => _module.isChecked,
    );
  };

  const syncModules = async () => {
    loadingService.startLoading('main-loader:sync-modules');

    const allModules: IModule[] = await moduleService.getModules();
    modules.value = allModules.map(
      (_module: IModule): ICheckable<IModule> => ({
        ..._module,
        isChecked: false,
      }),
    );

    loadingService.stopLoading('main-loader:sync-modules');
  };

  const onClickDeleteStaffMember = async (user: ICompanyUser) => {
    try {
      loadingService.startLoading('main-loader:delete-staff-member');
      await companyService.deleteCompanyUser(user.user_code);

      const page = pagination.value?.page ?? 1;

      if (page > 1 && rows.value.length === 1) {
        pagination.value.page = page - 1;
      }

      getCompanyUsers(pagination.value);
    } catch (e) {
      loggingService.error('Error deleting staff member', e);
      notificationService.showError('Error deleting staff member');
    } finally {
      loadingService.stopLoading('main-loader:delete-staff-member');
    }
  };

  onBeforeMount(async () => {
    syncModules();
    getCompanyUsers(null);
  });
</script>

<style scoped lang="scss">
  .staff_accounts_page {
    padding: 20px 40px;

    @media (max-width: 1024px) {
      padding: 20px 10px;
    }
  }
</style>
