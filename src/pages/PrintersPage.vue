<template>
  <div class="flex justify-between items-center title-bar-heading q-mt-md">
    <title-bar style="width: 50%;" title="Add Printer" subtitle="Setup your new printer." />
    <shipvagoo-card-actions class="flex justify-end no-padding">
      <shipvagoo-button-dropdown-v1
        color="#FFFFFF"
        bg-color="#243E90"
        min-width="140px"
        dropdown-icon="arrow_forward_ios"
        outline no-caps
        label="Download"
      >
        <shipvagoo-list>
          <shipvagoo-menu-item
            v-close-popup
            clickable
            @click="onClickDropdownAction('windows')"
          >
            For Windows
          </shipvagoo-menu-item>
          <shipvagoo-menu-item
            v-close-popup
            clickable
            @click="onClickDropdownAction('apple-m1')"
          >
            Apple M1
          </shipvagoo-menu-item>
          <shipvagoo-menu-item
            v-close-popup
            clickable
            @click="onClickDropdownAction('apple-intel')"
          >
            Apple Intel
          </shipvagoo-menu-item>
        </shipvagoo-list>
      </shipvagoo-button-dropdown-v1>
      <shipvagoo-button
        min-width="140px"
        @click="store.dispatch('printerStore/openApiAccessModalOpen')">
        Printer API Keys
      </shipvagoo-button>
    </shipvagoo-card-actions>
  </div>

  <div class="printers_page">
    <!-- <div class="printers_page-actions">
       <div class="big_search-rows_per_page-group">
         <big-search v-model="search" placeholder="Search for id, name" class="printers_page-actions-big_search"/>
       </div>
     </div>-->
    <div class="text-gray-brand" style="font-size: 16px;font-weight: 700">
      My Printers
    </div>
    <div class="row q-mt-sm">
      <div class="col-lg-3 col-md-4 col-sm-6" v-for="(printer) in rows">
        <div
          style=" width: 100%;height: 100%;padding:7px;">
          <printer-item
            @update="onClickPrinter(printer)"
            @delete="onClickDeletePrinter(printer)"
            :printer="printer"
          ></printer-item>
        </div>
      </div>
      <div class="col-lg-3 col-md-4 col-sm-6">
        <div style=" width: 100%;height: 100%;padding:7px;">
          <div class="add-card-box" @click="onClickCreate">
            <div class="row cursor-pointer">
              <div class="col-md-12 flex justify-center">
                <q-icon name="fas fa-plus-circle" size="md" color="grey-7"></q-icon>
              </div>
              <div class="col-md-12 flex justify-center q-mt-sm">
                <div class="h5-heading text-grey-7">Add New Printer</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <q-dialog v-if="isPrinterDetailOpen" v-model="isPrinterDetailOpen">
    <printer-detail
      :printer="selectedPrinterToEdit"
      @close="onClosePrinterDetail"
      @create="getPrinters()"
      @update="getPrinters()"
    />
  </q-dialog>
</template>

<script setup lang="ts">
import { onBeforeMount, ref } from "vue";

import { IRequestParam } from "../model/http.model";
import { IPrinter } from "../model/printer.model";
import {
  loadingService,
  loggingService,
  notificationService,
  printerService
} from "../services/_singletons";
import store from "../store";

const primaryActionLabel = ref<string>("Download Printer App (Windows)");
const primaryActionHandler = ref<string>("windows");
const rows = ref<IPrinter[]>([]);
const isPrinterDetailOpen = ref<boolean>(false);
const selectedPrinterToEdit = ref<IPrinter | null>(null);

const getPrinters = async () => {
  try {
    loadingService.startLoading("main-loader:get-printers");
    const page: number = 1;
    const limit: number = 1000;
    const params: IRequestParam[] = [];
    const result = await printerService.getPrinters(page, limit, params);
    rows.value = result.entities;
  } catch (error) {
    loggingService.error("Error getting printers", error);
    notificationService.showError("Error getting printers");
  } finally {
    loadingService.stopLoading("main-loader:get-printers");
  }
};

const onClickPrinter = (printer: IPrinter) => {
  selectedPrinterToEdit.value = printer;
  isPrinterDetailOpen.value = true;
};

const onClickDeletePrinter = async (row: IPrinter) => {
  try {
    loadingService.startLoading("main-loader:delete-printer");
    await printerService.deletePrinter(row.printer_code);
    getPrinters();
    notificationService.showSuccess("Printer deleted successfully");
  } catch (error) {
    loggingService.error("Error deleting printer", error);
    notificationService.showError("Error deleting printer");
  } finally {
    loadingService.stopLoading("main-loader:delete-printer");
  }
};

const onClickCreate = () => {
  selectedPrinterToEdit.value = null;
  isPrinterDetailOpen.value = true;
};

const onClosePrinterDetail = () => {
  isPrinterDetailOpen.value = false;
  selectedPrinterToEdit.value = null;
};


const onClickDropdownAction = (machine: string) => {
  let fileUrl = "";
  let fileName = "";
  switch (machine) {
    case "windows":
      fileUrl = import.meta.env.VITE_s3_app_link_windows;
      fileName = "printer_app.exe";
      break;
    case "apple-m1":
      fileUrl = import.meta.env.VITE_s3_app_link_apple_m1;
      fileName = "printer_app_m1.dmg";
      break;
    case "apple-intel":
      fileUrl = import.meta.env.VITE_s3_app_link_apple_intel;
      fileName = "printer_app_intel.dmg";
      break;
    default:
      fileUrl = import.meta.env.VITE_s3_app_link_windows;
      fileName = "printer_app.exe";
      break;
  }

  // Create a link element
  const link = document.createElement("a");
  link.href = fileUrl;
  link.download = fileName; // Specify the desired filename

  // Append the link to the document body
  document.body.appendChild(link);

  // Trigger the click event to start the download
  link.click();

  // Clean up by removing the link from the document body
  document.body.removeChild(link);
};

onBeforeMount(() => {
  getPrinters();

  console.log(navigator.platform);
  console.log(navigator.userAgent);
  console.log(navigator.hardwareConcurrency);

  if (navigator.platform.toLowerCase().includes("win")) {
    primaryActionLabel.value = "Download Printer App (Windows)";
    primaryActionHandler.value = "windows";
  } else if (navigator.platform.toLowerCase().includes("arm")) {
    primaryActionLabel.value = "Download Printer App (Apple M1)";
    primaryActionHandler.value = "apple-m1";
  } else if (navigator.platform.toLowerCase().includes("intel")) {
    primaryActionLabel.value = "Download Printer App (Apple Intel)";
    primaryActionHandler.value = "apple-intel";
  }
});
</script>

<style scoped lang="scss">
.printers_page {
  padding: 0 40px 10px 40px;

  @media (max-width: 1024px) {
    padding: 0 20px 10px 20px;
  }

  .info {
    font-size: 15px;
    color: #e1e1e1;

    &-label {
      display: block;
      margin-bottom: 10px;

      font-weight: bold;
    }
  }

  &-actions {
    display: flex;
    flex-direction: row;

    & > *:not(:first-child) {
      margin-left: 10px;

      @media (max-width: 600px) {
        margin-left: unset;
      }
    }

    .big_search-rows_per_page-group {
      display: flex;
      flex: 1;
      flex-direction: row;

      & > *:not(:first-child) {
        margin-left: 10px;
      }

      .rows_per_page {
        display: none;

        @media (max-width: $tablet) {
          display: block;
        }
      }
    }

    &-big_search {
      flex: 1;
    }

    @media (max-width: 600px) {
      flex-direction: column;

      &-big_search {
        margin-bottom: 10px;
      }
    }
  }

  &-chips {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 11px;
  }

  &-modal {
    &-header {
      font-size: 18px;
    }

    &-order_modal {
      display: flex;
      flex-direction: column;
      gap: 20px;

      &-fulfillments {
        &-title {
          margin-bottom: 20px;
          font-size: 20px;
          line-height: 24px;
        }

        &-header {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          padding: 20px;
          font-size: 15px;

          &-prices {
            display: flex;
            flex-direction: row;

            & > *:not(:first-child) {
              margin-left: 30px;
            }
          }
        }

        &-footer {
          display: flex;
          flex-direction: row;
          justify-content: flex-end;
          padding: 20px;

          & > *:not(:first-child) {
            margin-left: 10px;
          }

          @media (max-width: 800px) {
            flex-direction: column;

            & > *:not(:first-child) {
              margin-top: 10px;
              margin-left: unset;
            }
          }
        }
      }

      &-events {
        &-title {
          margin-bottom: 20px;
          font-size: 20px;
          line-height: 24px;
        }

        &-date {
          display: flex;
          flex-direction: column;
          font-size: 12px;
          line-height: 14px;
          color: #e1e1e1;
        }
      }
    }

    .q-card {
      min-width: 310px;
      //  background: #151526;
      background: #FFF;

      &.pick_order {
        width: 898px;
        max-width: 898px;
      }

      &.order_modal {
        width: 1038px;
        max-width: 1038px;
      }
    }

    &-footer {
      display: flex;
      justify-content: flex-end;

      &-pick_button {
        width: 220px;
      }
    }
  }

  &-success {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 42px;
    height: 42px;
    border: 1px solid #389b99;
    border-radius: 50%;

    img {
      width: 14px;
      height: 10px;
      filter: invert(55%) sepia(11%) saturate(2247%) hue-rotate(130deg) brightness(95%) contrast(76%);
    }

    &-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      margin-top: 44px !important;
      margin-bottom: 44px !important;

      .find_service_point {
        display: flex;
        margin-top: 24px;
      }
    }

    &-info {
      margin-top: 24px;
      font-size: 15px;
      line-height: 17px;
      text-align: center;

      p {
        margin-bottom: 10px;
      }
    }
  }
}

.add-card-box {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 345px;
  background: #FFFFFF;
  border-radius: 5px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
}
</style>
