<template>
  <div class="flex justify-between items-center title-bar-heading">
    <title-bar style="width: 50%" subtitle="An overview of return portals" title="Return Portal" />
  </div>
  <div class="return_portals_page">
    <div class="row q-mt-sm">
      <div
        class="col-lg-3 col-md-4 col-sm-6 col-12 q-pa-xs"
        v-for="(row, index) in rows"
        :key="`return_portal_${index}`"
      >
        <return-portal-item
          class="full-height"
          :return-portal-item="row"
          @edit="onClickEditReturnPortal(row)"
          @deactivate="onClickActiveDeactivate(row)"
          @activate="onClickActiveDeactivate(row)"
          @delete="onClickDelete(row)"
        />
      </div>
      <div class="col-lg-3 col-md-4 col-sm-6 col-12 q-pa-xs" style="min-height: 211px">
        <add-new-box class="full-height" @click="onClickAdd" label="Add New" />
      </div>
    </div>
  </div>
  <q-dialog v-model="showDeleteConfirm">
    <confirm-popup
      @confirm="onClickDeleteReturnPortal"
      confirm-label="Delete"
      confirmation-message="You are about to delete this return portal. This action 
cannot be undone"
    />
  </q-dialog>
  <q-dialog v-model="showActiveInactiveConfirm">
    <confirm-popup
      @confirm="onClickActiveInactiveReturnPortal"
      :confirmation-message="
        selectedReturnPortal?.is_active
          ? 'You are about to deactive this return portal'
          : 'You are about to activate this return portal'
      "
    />
  </q-dialog>
  <q-dialog v-model="isReturnPortalDetailOpen" @before-hide="onCloseReturnPortalDetail">
    <return-portal-detail :return-portal="returnPortalToBeEdited" @close="onUpdatedReturnPortal" />
  </q-dialog>
</template>

<script setup lang="ts">
  import { onBeforeMount, ref } from 'vue';
  import TitleBar from '../components/title-bar.vue';
  import { Pagination } from '../model/common.model';
  import { IRequestParam } from '../model/http.model';
  import { IReturnPortal } from '../model/return-portal.model';
  import { useRoute } from 'vue-router';
  import {
    returnPortalService,
    loadingService,
    loggingService,
    notificationService,
  } from '../services/_singletons';
  import { useStore } from '../store';

  const $store = useStore();
  const pagination = ref<Pagination>({
    page: 1,
    rowsNumber: 0,
    rowsPerPage: 10000,
    descending: true,
  });

  const $route = useRoute();
  const rows = ref<IReturnPortal[]>([]);
  const isReturnPortalDetailOpen = ref<boolean>(false);
  const returnPortalToBeEdited = ref<IReturnPortal | null>(null);
  const selectedReturnPortal = ref<IReturnPortal | null>(null);
  const showDeleteConfirm = ref<boolean>(false);
  const showActiveInactiveConfirm = ref<boolean>(false);

  const getReturnPortals = async (tablePagination: Pagination | null) => {
    try {
      loadingService.startLoading('main-loader:get-return-portals');
      const page = tablePagination?.page || 1;
      const limit = tablePagination?.rowsPerPage || 10000;

      const params: IRequestParam[] = [];
      const sortingOrder = tablePagination?.descending ? 'desc' : 'asc';
      const sortBy = tablePagination?.sortBy || 'created_at';
      params.push({ key: `sorting[${sortBy}]`, value: sortingOrder });
      params.push({ key: 'with', value: 'country' });

      const response = await returnPortalService.getReturnPortals(page, limit, params);
      pagination.value.page = page;
      pagination.value.rowsPerPage = limit;
      pagination.value.rowsNumber = response.total;
      pagination.value.sortBy = tablePagination?.sortBy;
      pagination.value.descending = tablePagination?.descending;
      rows.value = response.entities;
      $store.commit('returnPortalStore/setCount', rows.value.length);
    } catch (error) {
      loggingService.error('Error getting return portals', error);
      notificationService.showError('Error getting return portals');
    } finally {
      loadingService.stopLoading('main-loader:get-return-portals');
    }
  };

  const onClickAdd = () => {
    isReturnPortalDetailOpen.value = true;
  };

  const onCloseReturnPortalDetail = () => {
    isReturnPortalDetailOpen.value = false;
    returnPortalToBeEdited.value = null;
    getReturnPortals(pagination.value);
  };

  const onClickEditReturnPortal = (row: IReturnPortal) => {
    returnPortalToBeEdited.value = row;
    isReturnPortalDetailOpen.value = true;
  };

  const updateReturnPortal = async (row: IReturnPortal, isActive: boolean) => {
    try {
      loadingService.startLoading('main-loader:update-return-portal');
      await returnPortalService.updateReturnPortal(row.return_portal_code, {
        ...row,
        is_active: isActive,
      });
      getReturnPortals(pagination.value);
    } catch (error) {
      loggingService.error('Error updating return portal', error);
      notificationService.showError('Error updating return portal status');
    } finally {
      loadingService.stopLoading('main-loader:update-return-portal');
    }
  };

  /*const onClickActivate = (row: IReturnPortal) => {
  updateReturnPortal(row, true);
};*/

  const onClickActiveDeactivate = (row: IReturnPortal) => {
    selectedReturnPortal.value = row;
    showActiveInactiveConfirm.value = true;
  };

  const onClickDelete = (row: IReturnPortal) => {
    selectedReturnPortal.value = row;
    showDeleteConfirm.value = true;
  };

  const onClickActiveInactiveReturnPortal = async () => {
    if (!selectedReturnPortal.value) return;
    updateReturnPortal(selectedReturnPortal.value, !selectedReturnPortal.value.is_active);
  };
  const onClickDeleteReturnPortal = async () => {
    if (!selectedReturnPortal.value) return;

    try {
      loadingService.startLoading('main-loader:delete-return-portal');
      await returnPortalService.deleteReturnPortal(selectedReturnPortal.value.return_portal_code);
      getReturnPortals(pagination.value);
    } catch (error) {
      loggingService.error('Error deleting return portal', error);
      notificationService.showError('Error deleting return portal');
    } finally {
      loadingService.stopLoading('main-loader:delete-return-portal');
    }
  };

  const onUpdatedReturnPortal = () => {
    isReturnPortalDetailOpen.value = false;
    getReturnPortals(pagination.value);
  };

  onBeforeMount(() => {
    getReturnPortals(null);
    if ($route.query['add-return-portal']) {
      isReturnPortalDetailOpen.value = true;
    }
  });
</script>

<style scoped lang="scss">
  .return_portals_page {
    padding: 0 40px;

    @media (max-width: 1024px) {
      padding: 0 20px 10px 20px;
    }
  }
</style>
