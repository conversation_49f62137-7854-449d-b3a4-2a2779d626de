<template>
  <div class="reset_password">
    <h1 class="reset_password-heading">Reset password</h1>

    <form @submit.prevent="submit">
      <shipvagoo-input
        v-model="state.form.password"
        type="password"
        label="Password"
        :error="v$.form.password.$error"
        :error-message="v$.form.password.$errors[0]?.$message.toString()"
      >
        <template #append>
          <q-icon name="img:/assets/img/password.svg" size="12px" />
        </template>
      </shipvagoo-input>

      <shipvagoo-input
        v-model="state.form.password_confirmation"
        type="password"
        label="Confirm Password"
        :error="v$.form.password_confirmation.$error"
        :error-message="v$.form.password_confirmation.$errors[0]?.$message.toString()"
      >
        <template #append>
          <q-icon name="img:/assets/img/password.svg" size="12px" />
        </template>
      </shipvagoo-input>

      <shipvagoo-button class="reset_password-login_button" type="submit" label="Reset Password" />
    </form>
  </div>
  <img class="reset_password-background_banner" :src="LoginImage" alt="" decoding="async" />
</template>

<script setup lang="ts">
  import useValidate from '@vuelidate/core';
  import { required, helpers, minLength } from '@vuelidate/validators';
  import { Router, useRoute, useRouter } from 'vue-router';
  import { onBeforeMount, ref } from 'vue';
  import { authService, loadingService, notificationService } from '../services/_singletons';
  import LoginImage from '../assets/img/sign.png';

  const state = ref({
    form: {
      password: '',
      password_confirmation: '',
    },
  });

  const v$ = useValidate(
    {
      form: {
        password: {
          required: helpers.withMessage('This field is required.', required),
          minLength: helpers.withMessage('Password must be at least 8 characters.', minLength(8)),
        },
        password_confirmation: {
          required: helpers.withMessage('This field is required.', required),
          sameAsPassword: helpers.withMessage(
            'Password does not match.',
            (value: string) => value === state.value.form.password,
          ),
        },
      },
    },
    state,
  );

  const $router: Router = useRouter();
  const $route = useRoute();

  const submit = async () => {
    await v$.value.$validate();

    if (v$.value.$error || !$route.params.token || typeof $route.params.token !== 'string') {
      return;
    }

    try {
      loadingService.startLoading('main-loader:reset-password');
      await authService.resetPassword($route.params.token, state.value.form.password,state.value.form.password_confirmation);
      notificationService.showSuccess(
        'Password reset successfully. You can now login with your new password.',
      );
      $router.push('/login');
    } finally {
      loadingService.stopLoading('main-loader:reset-password');
    }
  };

  onBeforeMount(async () => {
    const { token } = $route.query;

    if (!token) {
      await $router.push('/login');
    }
  });
</script>

<style scoped lang="scss">
  .reset_password {
    width: 340px;

    transition: transform 0.3s ease-in-out, width 0.3s ease-in-out;
    transform: translateX(78px) translateY(81px);
    will-change: width, transform;

    @media (max-width: 768px) {
      width: calc(100% - 20px);
      max-width: unset;

      transform: translateX(10px) translateY(241px);
    }

    &-start_for_free {
      font-size: 15px;
      font-weight: 500;
      line-height: 17px;
      color: #9eb2c6;
    }

    &-heading {
      margin: 0;
      font-size: 35px;
      line-height: 42px;
    }

    &-not_a_member {
      font-size: 15px;
      color: #9eb2c6;
    }

    &-login_button {
      min-width: 115px;
      margin-top: 27px;
    }

    &-background_banner {
      position: absolute;
      top: 0;
      right: 0;
      z-index: -1;
      width: calc(100% - 340px - 78px - 23px);
      height: 100%;
      object-fit: cover;
      object-position: center;
      transition: width 300ms ease-in-out, height 300ms ease-in-out;
      will-change: width, height;

      @media (max-width: 768px) {
        width: 100%;
        max-width: unset;
        max-height: 375px;
      }
    }

    & > *:not(:first-child) {
      margin-top: 17px;
    }

    form {
      & > .q-input:not(:first-of-type) {
        margin-top: 17px;
      }
    }
  }
</style>
