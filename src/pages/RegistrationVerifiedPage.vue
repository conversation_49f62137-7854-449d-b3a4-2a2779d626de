<template>
  <div class="rootLogin">
    <p>Redirect to Dashboard...</p>
  </div>
  <loader :status="true"></loader>
</template>
<script>
  import Loader from '../components/loader.vue';

  export default {
    components: {
      Loader,
    },
    created() {
      const token = window.location.hash.split('hash=');
      sessionStorage.setItem('token', token[1]);
      this.$router.push('/');
    },
  };
</script>

<style scoped lang="scss">
  .rootLogin {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;

    p {
      font-size: 30px;
      color: #fff;
    }
  }
</style>
