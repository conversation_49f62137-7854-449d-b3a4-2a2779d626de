<template>
  <div class="flex justify-between items-center title-bar-heading">
    <title-bar style="width: 100%;" :title="$t('common.products')"/>
  </div>
  <div class="products_page">
    <div class="products_page-actions">
      <div class="big_search-rows_per_page-group row">
        <div class="col-12">
          <big-search
            @update:modelValue="searchVal"
            placeholder="Search for SKU or name"
            v-model="search" class="booked_page-actions-big_search"/>
        </div>
      </div>


      <!-- <shipvagoo-button-dropdown outline no-caps split :label="$t('common.download_item_label')">
        <shipvagoo-list>
          <shipvagoo-menu-item v-close-popup clickable>
            {{ $t('common.delete') }}
          </shipvagoo-menu-item>
        </shipvagoo-list>
      </shipvagoo-button-dropdown> -->
    </div>

    <shipvagoo-table
      v-model:pagination="pagination"
      selection="multiple"
      :columns="columns"
      :rows="rows"
      :total="pagination.rowsNumber"
      :loading="loadingService.loadings.has('main-loader:get-products')"
      @request="getProducts($event.pagination)"
      class="products-page-table"
    >

      <template #header-selection="scope">
        <q-td>
          <shipvagoo-checkbox v-model="scope.selected" size="md"/>
        </q-td>
      </template>
      <template #body-selection="scope">
        <q-td @click.stop="(event:Event)=> event.stopPropagation()">
          <shipvagoo-checkbox
            v-model="scope.selected" size="md" @click.stop/>
        </q-td>
      </template>

      <template #body-cell-more="props">
        <q-td :props="props" class="more">
          <q-btn dense flat round style="color:#1f2124;" class="more-action" field="edit" icon="more_horiz">
            <shipvagoo-menu>
              <shipvagoo-menu-item clickable @click="onClickDownloadProductLabel(props.row)">
                {{ $t('common.download_item_label') }}
              </shipvagoo-menu-item>
              <!--              <shipvagoo-menu-item clickable>-->
              <!--                {{ $t('common.delete') }}-->
              <!--              </shipvagoo-menu-item>-->
            </shipvagoo-menu>
          </q-btn>
        </q-td>
      </template>
    </shipvagoo-table>

    <!--<shipvagoo-fab icon="sync" @click="onClickSync"/>-->
  </div>

  <q-dialog
    v-if="selectedProduct"
    v-model="isProductDetailOpen"
    @before-hide="onCloseProductDetail"
  >
    <product-detail
      :product="selectedProduct"
      @close="onCloseProductDetail"
      @refresh="getProducts(pagination)"
    />
  </q-dialog>
</template>

<script setup lang="ts">
import {onBeforeMount, Ref, ref} from 'vue';
import {QTableProps} from 'quasar';
import dayjs from 'dayjs';
import {useI18n} from 'vue-i18n';
import {IProduct} from '../model/product.model';
import {
  loadingService,
  notificationService,
  loggingService,
  productService,
} from '../services/_singletons';
import HelperService from '../services/helper.service';
import {IRequestParam} from '../model/http.model';
import {Pagination} from '../model/common.model';
import WindowService from '../services/window.service';

const search = ref<string>('');
const isProductDetailOpen = ref<boolean>(false);
const selectedProduct = ref<IProduct | null>(null);
const {t} = useI18n();

let windowRef: Window | null;

const columns: QTableProps['columns'] = [
  {
    name: 'integration',
    align: 'left',
    label: t('common.shop'),
    field: (row: IProduct) => row.integration?.name,
  },
  {
    name: 'sku',
    align: 'left',
    label: 'SKU',
    field: (row: IProduct) => (!row.sku || row.sku.length <= 0 ? row.handle : row.sku),
  },
  {name: 'title', align: 'left', label: t('common.name'), field: 'title'},
  /*{
    name: 'variant',
    align: 'left',
    label: t('common.variant'),
    field: 'variant_code',
    sortable: true,
  },
  {name: 'bin', align: 'left', label: 'Bin', field: 'bin', sortable: true},*/
  {
    name: 'updated_at',
    align: 'left',
    label: t('common.updated'),
    field: (row: IProduct) => dayjs(row.updated_at).format('DD/MM/YYYY HH:mm:ss'),
  },
  /*  {name: 'more', align: 'center', label: t('common.more'), field: 'more'},*/
];
const searchVal = (value: string) => {
  search.value = value;
  getProducts(pagination.value);
}
const rows = ref<IProduct[]>([]);
const pagination: Ref<Pagination> = ref({
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
  descending: true,
});
/*
  const onClickProduct = (event: Event, row: IProduct) => {
    if (HelperService.containsIgnoredRowElements(event)) {
      return;
    }

    isProductDetailOpen.value = true;
    selectedProduct.value = row;
  };
*/

const getProducts = async (tablePagination: Pagination | null) => {
  try {
    loadingService.startLoading('main-loader:get-products');
    const page = tablePagination?.page || 1;
    const limit = tablePagination?.rowsPerPage || 10;
    const params: IRequestParam[] = [];

    if (tablePagination?.sortBy) {
      const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
      params.push({key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder});
    } else {
      params.push({key: 'sorting[created_at]', value: 'desc'});
    }
    if (search.value && search.value !== '') {
      params.push(
        {key: 'conditions[handle:like]', value: search.value},
        {key: 'conditions[title:or_like]', value: search.value},
        {key: 'conditions[variant_code:or_like]', value: search.value},
      );
    }
    const result = await productService.getProducts(page, limit, params);
    rows.value = result.entities;
    pagination.value.page = page;
    pagination.value.rowsPerPage = limit;
    pagination.value.rowsNumber = result.total;
  } catch (error) {
    loggingService.error('Error getting products', error);
    notificationService.showError('Error getting products');
  } finally {
    loadingService.stopLoading('main-loader:get-products');
  }
};

const onCloseProductDetail = () => {
  isProductDetailOpen.value = false;
  selectedProduct.value = null;
};

const previewPDF = async (row: IProduct) => {
  try {
    loadingService.startLoading('main-loader:download-pdf');

    if (!windowRef) {
      throw new Error('Window reference is not defined');
    }

    if ((!row.sku && !row.handle) || !row.barcode || !row.title) {
      throw new Error('Missing product data. Verify SKU, Handle, Barcode, Title');
    }

    const response = await productService.generateProductLabel({
      sku: row.sku || row.handle,
      barcode: row.barcode,
      name: row.title,
      bin: row.bin || undefined,
    });

    const blob: Blob = await HelperService.toBlob(
      `data:application/pdf;base64,${encodeURI(response.file)}`,
    );

    windowRef.location = URL.createObjectURL(blob);
  } catch (error: any) {
    loggingService.error('Error while downloading PDF', error);
    notificationService.showError('Error while downloading PDF ' + error.toString());

    if (windowRef) {
      windowRef.close();
    }
  } finally {
    loadingService.stopLoading('main-loader:download-pdf');
  }
};

const onClickDownloadProductLabel = (row: IProduct) => {
  windowRef = WindowService.openWindow('Product Labels Preview');

  previewPDF(row);
};

/*const onClickSync = async () => {
  try {
    loadingService.startLoading('main-loader:sync-products');
    await productService.importProducts();
    await getProducts(null);
  } catch (e) {
    notificationService.showError('Error while syncing products');
    loggingService.error('Error while syncing products', e);
  } finally {
    loadingService.stopLoading('main-loader:sync-products');
  }
};*/

onBeforeMount(async () => {
  getProducts(null);
});
</script>

<style lang="scss">
.products_page {
  padding: 0 40px 10px 40px;

  @media (max-width: 1024px) {
    padding: 0 20px 10px 20px;
  }

  &-actions {
    display: flex;
    flex-direction: row;

    & > *:not(:first-child) {
      margin-left: 10px;

      @media (max-width: 600px) {
        margin-left: unset;
      }
    }

    .big_search-rows_per_page-group {
      display: flex;
      flex: 1;
      flex-direction: row;

      & > *:not(:first-child) {
        margin-left: 10px;
      }

      .rows_per_page {
        display: none;

        @media (max-width: $tablet) {
          display: block;
        }
      }
    }

    &-big_search {
      flex: 1;
    }

    @media (max-width: 600px) {
      flex-direction: column;

      &-big_search {
        margin-bottom: 10px;
      }
    }
  }
}

.q-btn-dropdown--split .q-btn-dropdown__arrow-container {
  width: 40px !important;
  background-color: #2929c8 !important;
}

.q-btn-dropdown--split .q-btn-dropdown__arrow-container.q-btn--outline {
  border-left: unset !important;
}

.q-btn-dropdown--split.flat-split {
  .q-btn--outline:before {
    border: unset !important;
  }

  .q-btn-dropdown--current {
    margin-right: 1px;
    background-color: #2929c8 !important;
  }
}

.q-item {
  align-items: center;
}
</style>

<style>
.products-page-table th:nth-child(1),
.products-page-table td:nth-child(1) {
  width: 3%;
}

.products-page-table th:nth-child(2),
.products-page-table td:nth-child(2) {
  width: 25%;
}

.products-page-table th:nth-child(3),
.products-page-table td:nth-child(3) {
  width: 24%;
}

.products-page-table th:nth-child(4),
.products-page-table td:nth-child(4) {
  width: 24%;
}

.products-page-table th:nth-child(5),
.products-page-table td:nth-child(5) {
  width: 24%;
}

.products-page-table th:nth-child(6),
.products-page-table td:nth-child(6) {
  width: 17%;
}

.products-page-table th:nth-child(7),
.products-page-table td:nth-child(7) {
  width: 17%;
}

.products-page-table th:nth-child(8),
.products-page-table td:nth-child(8) {
  width: 5%;
}
</style>