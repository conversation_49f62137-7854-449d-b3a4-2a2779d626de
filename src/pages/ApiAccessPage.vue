<template>
  <div class="flex justify-between items-center title-bar-heading">
    <title-bar style="width: 100%;" title="Api Access"/>

  </div>
  <div class="pick_settings-page_container">
    <div
      class="pick_settings-inputs_container"
      style="display: flex; flex-direction: column; gap: 20px"
    >
      <shipvagoo-input v-model="form.user" class="key-input" readonly label="User"/>
      <shipvagoo-input
        v-model="form.key"
        class="key-input"
        readonly
        label="Key"
        style="font-variant-numeric: tabular-nums"
      />
      <div class="pick_settings-footer">
        <shipvagoo-button
          class="save_button"
          label="Generate"
          min-width="120px"
          @click="onClickGenerate"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {onBeforeMount, ref} from 'vue';
  import {
    apiAccessService,
    loadingService,
    loggingService,
    notificationService,
  } from '../services/_singletons';

  interface IForm {
    user: string | null;
    key: string | null;
  }

  const form = ref<IForm>({
    user: null,
    key: null,
  });

  const getApiAccess = async () => {
    try {
      loadingService.startLoading('main-loader:get-api-access');
      const response = await apiAccessService.getApiAccess();

      if (response !== '') {
        form.value.user = response.user;
        form.value.key = response.key;
      }
    } catch (e) {
      loggingService.error('Error while getting api access', e);
      notificationService.showError('Error while getting api access');
    } finally {
      loadingService.stopLoading('main-loader:get-api-access');
    }
  };

  const onClickGenerate = async () => {
    try {
      loadingService.startLoading('main-loader:generate-api-access');
      const response = await apiAccessService.generateApiAccess();

      form.value.user = response.user;
      form.value.key = response.key;

      notificationService.showSuccess('Api access generated successfully');
    } catch (e) {
      loggingService.error('Error while generating api access', e);
      notificationService.showError('Error while generating api access');
    } finally {
      loadingService.stopLoading('main-loader:generate-api-access');
    }
  };

  onBeforeMount(() => {
    getApiAccess();
  });
</script>

<style lang="scss" scoped>
  .responsive-double-input {
    display: flex;
    flex-direction: row;
    gap: 20px;
    align-items: center;
    width: 100%;

    & > * {
      flex: 1;
    }

    @media (max-width: $tablet) {
      flex-direction: column;

      & > * {
        flex: unset;
        width: 100%;
      }
    }
  }

  .pick_settings {
    &-page_container {
      padding: 20px 40px;

      @media (max-width: $tablet) {
        padding: 20px 10px;
      }
    }

    &-inputs_container {
      padding: 20px;
      margin-top: 25px;
      border: 1px solid #e1e1e1;
      border-radius: 5px;
    }

    &-pick_path_label {
      padding-bottom: 4px;
      margin-top: 35px;
      font-size: 16px;
      line-height: 19px;
      border-bottom: 1px dashed #e1e1e1;
    }

    &-checkboxes_container {
      display: flex;
      flex-direction: row;
      width: 100%;
      margin-top: 20px;

      & > * {
        flex: 1;
      }

      @media (max-width: $tablet) {
        flex-direction: column;
        gap: 20px;
      }

      &-checkbox_group {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }
    }

    &-voice_options {
      margin-top: 20px;

      &-inputs {
        display: flex;
        flex-direction: column;
        gap: 20px;

        width: calc(50% - 10px);

        @media (max-width: $tablet) {
          width: 100%;
        }
      }
    }

    &-pick_list_options {
      margin-top: 20px;
    }

    &-footer {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      padding: 20px;
      margin-top: 20px;
      margin-right: -20px;
      margin-bottom: -20px;
      margin-left: -20px;
      border-top: 1px solid #e1e1e1;

      @media (max-width: $tablet) {
        & > * {
          width: 100%;
        }
      }
    }
  }

  .key-input input {
    font-variant-numeric: tabular-nums;
  }
</style>
