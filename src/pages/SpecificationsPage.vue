<template>
  <div class="flex justify-between title-bar-heading q-mt-md">
    <div>
      <title-bar
        title="Monthly invoice specifications"
        subtitle="Detailed specification of monthly invoice"
      />
      <div>
        <div>
          <span class="text-dark text-bold text-fifteen"> Invoice No: </span>
          <span class="text-black q-ml-md">
            {{ monthlyInvoiceId }}
          </span>
        </div>
      </div>
      <div class="q-mt-xs">
        <span class="text-dark text-bold text-fifteen"> Month: </span>
        <span class="text-black q-ml-md">
          {{ dayjs(String($route.query.created_at)).subtract(1, 'month').format('MMMM') }}
        </span>
      </div>
      <div class="q-mt-xs">
        <span class="text-dark text-bold text-fifteen"> Total (DKK):</span>
        <span class="text-black q-ml-md">
          {{ PriceService.formatPrice(Number($route.query.total ?? 0), 'de-DE') }}</span
        >
      </div>
    </div>
    <shipvagoo-button @click="onClickExport">Export Excel</shipvagoo-button>
  </div>

  <div class="title-bar-heading">
    <title-bar title="Shipments" style="height: 40px !important" />
  </div>
  <!--   <div class="specifications_page">
    <shipvagoo-placeholder  text="No monthly invoices are available">
    </shipvagoo-placeholder>
  </div> -->
  <div class="specifications_page">
    <div class="specifications_page-table" v-if="shipmentRows.length">
      <shipvagoo-table-v1
        v-model:pagination="pagination"
        hide-pagination
        hide-bottom
        :rows="shipmentRows"
        :columns="shipmentsColumns"
        :total="pagination.rowsNumber"
        :loading="loadingService.loadings.has('main-loader:loading-invoices')"
        class="monthly-specifications-page-table"
      >
        <template #body="props">
          <q-tr :props="props">
            <q-td colspan="4">
              <span class="text-bold">({{ props.row.title }})</span>
            </q-td>
          </q-tr>

          <q-tr :props="props" v-for="row in props.row.data">
            <q-td key="date">
              <span class="text-no-wrap">
                {{ dayjs(String(row.date)).format('DD/MM/YYYY') }} <br />
                {{ dayjs(String(row.date)).format('HH:mm') }}
              </span>
            </q-td>
            <q-td key="description ">
              <span class="text-no-wrap">{{ row.description }}</span>
            </q-td>
            <q-td key="reference">
              <span class="text-no-wrap">{{ row.reference }}</span>
            </q-td>

            <q-td key="amount">
              <span class="text-no-wrap text-center">{{ row.amount }}</span>
            </q-td>
          </q-tr>
        </template>
      </shipvagoo-table-v1>
    </div>
  </div>

  <div class="title-bar-heading">
    <title-bar style="height: 50px !important" title="Additionals" />
  </div>
  <div class="specifications_page">
    <div class="specifications_page-table q-pb-lg">
      <shipvagoo-table-v1
        v-model:pagination="pagination"
        :rows="additionalRows"
        :columns="shipmentsColumns"
        :total="pagination.rowsNumber"
        :loading="loadingService.loadings.has('main-loader:loading-invoices')"
        @request="getMonthlyInvoiceSpecifications($event.pagination)"
        class="monthly-specifications-page-table"
      >
        <template #body-cell-date="props">
          <q-td :props="props">
            <span class="text-no-wrap">
              {{ dayjs(String(props.row.date)).format('DD/MM/YYYY') }} <br />
              {{ dayjs(String(props.row.date)).format('HH:mm') }}
            </span>
          </q-td>
        </template>
      </shipvagoo-table-v1>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, onBeforeMount, Ref, ref } from 'vue';
  import { QTableProps } from 'quasar';
  import dayjs from 'dayjs';
  import * as _ from 'lodash-es';
  import {
    invoiceService,
    loadingService,
    loggingService,
    notificationService,
  } from '../services/_singletons';
  import { Pagination } from '../model/common.model';
  import { IInvoice } from '../model/invoice.model';
  import { IRequestParam } from '../model/http.model';

  import { useRoute } from 'vue-router';
  import { danishNumberFormat } from '../helpers';
  import PriceService from '../services/price.service';

  interface IShipmentData {
    reference: string;
    description: string;
    date: string;
    amount: string;
  }
  interface IShipmentType {
    title: string;
    data: IShipmentData[];
  }

  const route = useRoute();

  const monthlyInvoiceId = ref<string>(String(route.params.monthlyInvoiceCode));

  const search: Ref<string> = ref('');
  const shipmentsColumns = computed<QTableProps['columns']>(() => [
    {
      name: 'date',
      align: 'left',
      label: 'Date',
      field: 'date',
      sortable: false,
    },
    {
      name: 'description',
      align: 'left',
      label: 'Description',
      field: 'description',
      sortable: false,
    },
    {
      name: 'reference',
      align: 'left',
      label: 'Reference',
      field: 'reference',
      sortable: false,
    },

    {
      name: 'amount',
      align: 'left',
      label: 'Amount (excl. VAT)',
      field: 'amount',
      sortable: false,
    },
  ]);
  const shipmentRows = ref<IShipmentType[]>([]);
  const additionalRows = ref<IShipmentData[]>([]);

  const pagination: Ref<Pagination> = ref({
    page: 1,
    rowsPerPage: 10,
    rowsNumber: 0,
    descending: true,
  });

  const onClickExport = async () => {
    window.location.href = `${import.meta.env.VITE_baseUrl}/export-data/${monthlyInvoiceId.value}`;
    /*  const response = await invoiceService.exportSpecficationsToExcel(monthlyInvoiceId.value);

    console.log('Response ', response);
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const blobUrl = window.URL.createObjectURL(blob);

    //  const blob: Blob = await HelperService.toBlob(
    //   `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,${encodeURI(
    //     response,
    //   )}`,
    // );

    //const blobUrl = URL.createObjectURL(blob);
    WindowService.downloadFromBlobUrl(blobUrl, 'specifications.xlsx'); */
  };

  const getMonthlyInvoiceSpecifications = async (tablePagination: Pagination | null) => {
    try {
      loadingService.startLoading('main-loader:loading-invoices');
      const page = tablePagination?.page ?? 1;
      const limit = tablePagination?.rowsPerPage ?? 100;

      const params: IRequestParam[] = [];

      if (search.value && search.value !== '') {
        params.push({ key: 'conditions[invoice_code:like]', value: search.value });
      }

      if (tablePagination?.sortBy) {
        const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
        params.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
      } else {
        params.push({ key: 'sorting[created_at]', value: 'desc' });
      }

      const invoices = await invoiceService.getMonthlyInvoiceSpecifications(
        page,
        limit,
        params,
        monthlyInvoiceId.value,
      );

      hydrateTheShipmentsData(invoices.data);

      pagination.value.rowsNumber = invoices.total;
      pagination.value.page = page;
      pagination.value.rowsPerPage = limit;
      pagination.value.descending = tablePagination?.descending;
      pagination.value.sortBy = tablePagination?.sortBy;
    } catch (e) {
      loggingService.error('Error loading invoices', e);
      notificationService.showError('Error loading invoices');
    } finally {
      loadingService.stopLoading('main-loader:loading-invoices');
    }
  };

  const hydrateTheShipmentsData = (invoices: IInvoice[]) => {
    shipmentRows.value = Object.values(
      invoices.reduce((acc, invoice) => {
        const title = invoice.description ?? 'No description';

        if (!acc[title]) {
          acc[title] = { title, data: [] };
        }
        const shipment = invoice.invoice_items.find((item) => item.type == 'Shipments')?.items[0];
        let amount = '0,00';
        let weight_class = '';
        if (shipment) {
          amount = danishNumberFormat(Number(shipment.unit_price).toFixed(2));
          weight_class = shipment.weight_class;
        }
        acc[title].data.push({
          reference: `Shipment ${invoice.shipment_code}`,
          description: weight_class,
          date: invoice.created_at,
          amount: amount,
        });
        return acc;
      }, {} as Record<string, { title: string; data: IShipmentData[] }>),
    );

    additionalRows.value = invoices.flatMap((invoice) => {
      const additionalServices = invoice.invoice_items
        .filter((item) => item.type !== 'Shipments')
        .flatMap((item) => item.items || []); // Collect all 'items' arrays and flatten them

      return additionalServices.map((ad_item) => ({
        reference: `Shipment ${invoice.shipment_code}`,
        description: ad_item.name,
        date: invoice.created_at,
        amount: danishNumberFormat(Number(ad_item.unit_price).toFixed(2)),
      }));
    });

    console.log('Additional rows', additionalRows.value);
  };
  onBeforeMount(() => {
    getMonthlyInvoiceSpecifications(null);
  });
</script>
<style scoped lang="scss">
  .specifications_page {
    padding: 0px 40px;

    @media (max-width: 1024px) {
      padding: 0px 10px;
    }

    &-actions {
      display: flex;
      flex-direction: row;

      & > *:not(:first-child) {
        margin-left: 10px;

        @media (max-width: 600px) {
          margin-left: unset;
        }
      }

      .big_search-rows_per_page-group {
        display: flex;
        flex: 1;
        flex-direction: row;

        & > *:not(:first-child) {
          margin-left: 10px;
        }

        .rows_per_page {
          display: none;

          @media (max-width: $tablet) {
            display: block;
          }
        }
      }

      &-big_search {
        width: 320px;
      }

      @media (max-width: 600px) {
        flex-direction: column;

        &-big_search {
          margin-bottom: 10px;
        }
      }
    }
  }
</style>

<style lang="scss">
  .monthly-specifications-page-table th:nth-child(1),
  .monthly-specifications-page-table td:nth-child(1) {
    width: 25% !important;
  }
  .monthly-specifications-page-table th:nth-child(2),
  .monthly-specifications-page-table td:nth-child(2) {
    width: 30% !important;
  }

  .monthly-specifications-page-table th:nth-child(3),
  .monthly-specifications-page-table td:nth-child(3) {
    width: 25% !important;
  }
  .monthly-specifications-page-table th:nth-child(4),
  .monthly-specifications-page-table td:nth-child(4) {
    width: 20% !important;
  }

  .monthly-specifications-page-table {
    /* height or max-height is important */
    height: 525px;

    .q-table__top,
    .q-table__bottom,
    thead tr:first-child th {
      background-color: #f7f7f7;
      position: sticky;
      z-index: 1;
    }

    /* this will be the loading indicator */
    thead tr:last-child th {
      /* height of all previous header rows */
      top: 48px;
    }

    thead tr:first-child th {
      top: 0;
    }

    /* prevent scrolling behind sticky top row on focus */
    tbody {
      /* height of all previous header rows */
      scroll-margin-top: 48px;
    }
  }
</style>
