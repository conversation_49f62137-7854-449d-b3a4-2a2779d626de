<template>
  <div class="flex justify-between items-center title-bar-heading q-mt-md">
    <title-bar style="color: #212121" title="Balance" subtitle="An overview of added balance" />
    <div class="action-container">
      <big-search
        placeholder="Search"
        @update:modelValue="searchVal"
        v-model="search"
        class="add-funds-page-actions-big_search q-mr-md"
      />
      <shipvagoo-button clickable @click="onClickAddFunds">Add balance</shipvagoo-button>
    </div>
  </div>
  <div class="add-funds-page q-mt-xs" v-if="!rows.length">
    <shipvagoo-placeholder text="No balance has been added to this account">
    </shipvagoo-placeholder>
  </div>
  <div class="add-funds-page" v-if="rows.length">
    <shipvagoo-table-v1
      v-model:pagination="pagination"
      :columns="columns"
      :rows="rows"
      :total="pagination.rowsNumber"
      :loading="loadingService.loadings.has('main-loader:get-transactions-add-funds')"
      @row-click="onClickPrintReceipt"
      @request="getFunds($event.pagination)"
    >
      <template #body-cell-created_at="props">
        <q-td key="ordered_at" class="created_at" :props="props">
          {{ dayjs(String(props.row.created_at)).format('DD/MM/YYYY') }} <br />
          {{ dayjs(String(props.row.created_at)).format('HH:mm') }}
        </q-td>
      </template>
      <template #body-cell-more="props">
        <q-td key="more" class="more" :props="props">
          <q-btn
            dense
            flat
            round
            style="color: #1f2124"
            class="more-action"
            field="edit"
            icon="more_horiz"
          >
            <shipvagoo-menu style="width: 160px">
              <shipvagoo-menu-item
                v-if="!props.row.is_cancelled"
                clickable
                @click="onClickPrintReceipt(null, props.row)"
              >
                {{ $t('common.download') }}
              </shipvagoo-menu-item>
            </shipvagoo-menu>
          </q-btn>
        </q-td>
      </template>
    </shipvagoo-table-v1>
  </div>
  <q-dialog persistent no-backdrop-dismiss no-esc-dismiss v-model="isAddFundsModalOpen">
    <add-balance @close="onCloseAddFunds" @refresh="onRefreshAddFunds" />
  </q-dialog>
</template>

<script setup lang="ts">
  import { computed, onBeforeMount, Ref, ref } from 'vue';
  import { QTableProps } from 'quasar';
  import dayjs from 'dayjs';
  import {
    loadingService,
    loggingService,
    notificationService,
    transactionService,
  } from '../services/_singletons';
  import { ITransaction } from '../model/transactions.model';
  import { Pagination } from '../model/common.model';
  import { IRequestParam } from '../model/http.model';
  import HelperService from '../services/helper.service';
  import PriceService from '../services/price.service';
  import { useHelper } from '../composeable/Helper';

  const { showPdf } = useHelper();
  const isAddFundsModalOpen: Ref<boolean> = ref<boolean>(false);
  const search: Ref<string> = ref<string>('');
  const rows = ref<ITransaction[]>([]);
  const searchVal = (value: string) => {
    search.value = value;
    getFunds(pagination.value);
  };
  const pagination = ref<Pagination>({
    rowsNumber: 0,
    rowsPerPage: 10,
    page: 1,
  });

  const onClickAddFunds = () => {
    isAddFundsModalOpen.value = true;
  };

  const onCloseAddFunds = () => {
    isAddFundsModalOpen.value = false;
  };

  const columns = computed<QTableProps['columns']>(() => [
    {
      name: 'created_at',
      field: '',
      required: true,
      label: 'Transaction date',
      align: 'left',
      sortable: false,
    },
    {
      name: 'description',
      field: 'description',
      required: true,
      label: 'Transaction type',
      align: 'left',
      sortable: false,
    },
    {
      name: 'id',
      field: (row: ITransaction) => HelperService.formatId(row.id),
      required: true,
      label: 'Payment confirmation',
      align: 'left',
      sortable: false,
    },
    {
      name: 'payment_id',
      field: (row: ITransaction) => row.payment_id ?? '',
      required: true,
      label: 'Payment ID',
      align: 'left',
      sortable: false,
    },
    {
      name: 'payment_method',
      field: (row: ITransaction) => {
        const payment_method = row.payment_method;
        if (payment_method) {
          const type = payment_method.type;
          const details = payment_method.details;
          if (type == 'card') {
            if (details) {
              return `${
                details.brand.charAt(0).toUpperCase() + details.brand.slice(1)
              } (${details.bin.substring(0, 4)} **** **** ${details.last4})`;
            } else {
              return type.charAt(0).toUpperCase() + type.slice(1);
            }
          } else {
            return type.charAt(0).toUpperCase() + type.slice(1);
          }
        } else {
          return 'Balance';
        }
      },
      required: true,
      label: 'Payment method',
      align: 'left',
      sortable: false,
    },
    {
      name: 'amount',
      field: (row: ITransaction) => PriceService.formatPrice(Number(row.amount)),
      required: true,
      label: 'Total amount',
      align: 'right',
      sortable: false,
    },
    {
      name: 'more',
      align: 'center',
      label: 'Action',
      field: 'more',
      sortable: false,
    },
  ]);

  const getFunds = async (tablePagination: Pagination | null) => {
    try {
      loadingService.startLoading('main-loader:get-transactions-add-funds');

      const page = tablePagination?.page ?? 1;
      const limit = tablePagination?.rowsPerPage ?? 10;

      const params: IRequestParam[] = [
        { key: 'conditions[transaction_status:equal]', value: 'COMPLETED' },
      ];

      if (search.value && search.value !== '') {
        params.push({ key: 'conditions[created_at:like]', value: search.value });
        params.push({ key: 'conditions[description:or_like]', value: search.value });
        params.push({ key: 'conditions[id:or_like]', value: search.value });
      }

      if (tablePagination?.sortBy) {
        const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
        params.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
      } else {
        params.push({ key: 'sorting[created_at]', value: 'desc' });
      }

      const result = await transactionService.getFundsTransactions(page, limit, params);
      rows.value = result.entities;
      pagination.value.rowsNumber = result.total;
      pagination.value.page = result.current_page;
      pagination.value.sortBy = tablePagination?.sortBy;
      pagination.value.descending = tablePagination?.descending;
    } catch (e) {
      loggingService.error('Error while getting transactions for add funds', e);
      notificationService.showError('Error while getting transactions for add funds');
    } finally {
      loadingService.stopLoading('main-loader:get-transactions-add-funds');
    }
  };

  const onClickPrintReceipt = async (event: Event | null, row: ITransaction) => {
    if (event && HelperService.containsIgnoredRowElements(event)) {
      return;
    }

    if (row.transaction_status !== 'COMPLETED') {
      notificationService.showError("Only completed transactions' receipts can be downloaded");
      return;
    }
    try {
      loadingService.startLoading('main-loader:print-transaction-receipt');
      const response = await transactionService.downloadFundsPdf(row.transaction_code);
      await showPdf(response.file);
    } finally {
      loadingService.stopLoading('main-loader:print-transaction-receipt');
    }
  };

  const onRefreshAddFunds = () => {
    getFunds(null);
  };

  onBeforeMount(() => {
    getFunds(null);
  });
</script>

<style scoped lang="scss">
  .action-container {
    display: flex !important;
    height: 50px;
  }

  @media (max-width: 600px) {
    .action-container {
      flex-direction: column;
      height: 120px;
      margin-bottom: 10px;
    }
  }

  .add-funds-page {
    padding: 0 40px 10px 40px;

    @media (max-width: 1024px) {
      padding: 0 20px 10px 20px;
    }

    &-actions {
      display: flex;
      flex-direction: row;

      &-big_search {
        flex: 1;
        width: 300px;
      }

      @media (max-width: 600px) {
        flex-direction: column;

        &-big_search {
          margin-bottom: 10px;
        }
      }
    }
  }
</style>
