<template>
  <div class="flex justify-between items-center title-bar-heading">
    <title-bar
      style="width: 100%"
      title="Integration"
      subtitle="An overview of all integrations."
    />
  </div>
  <div class="order_integration_page">
    <div>
      <div v-if="rows.length">
        <div class="title dark-text text-sixteen text-weight-7">My integrations</div>
      </div>
      <div class="row q-mt-sm gap-15" v-if="rows.length">
        <div
          class="col-lg-3 col-md-4 col-sm-6 col-xs-12 col-12"
          v-for="(row, index) in rows"
          :key="index"
        >
          <order-integration-item
            @click-store="onClickStore(row)"
            @click-checkout-rule="onClickCheckoutRule(row)"
            @click-shipping-rule="onClickShippingRule(row)"
            :order-integration="row"
            @refresh="updateIntegration(row.api_url)"
            @activate-integration="activateIntegration(row.company_code, row.integration_code)"
            @disconnect="deactivateIntegration(row.company_code, row.integration_code)"
          ></order-integration-item>
        </div>
      </div>
      <div>
        <div class="q-mt-lg">
          <div class="title dark-text text-sixteen text-weight-7">E-Commerce integrations</div>
        </div>
        <div class="row gap-15 q-mt-sm">
          <div
            class="col-lg-3 col-md-4 col-sm-6 col-12"
            v-for="(row, index) in ecommerceIntegrations"
            :key="`${index}_ecommerce`"
          >
            <ecommerce-integration-item
              @clickConnect="onClickInstallAppOpenModel"
              :ecommerce-order-integration="row"
            ></ecommerce-integration-item>
          </div>
        </div>
      </div>
      <div>
        <div class="q-mt-lg">
          <div class="title dark-text text-sixteen text-weight-7">ERP Integrations</div>
        </div>
        <div class="row gap-15 q-mt-sm">
          <div
            class="col-lg-3 col-md-4 col-sm-6 col-12"
            v-for="(row, index) in erpIntegrations"
            :key="`${index}_erp`"
          >
            <ecommerce-integration-item
              :ecommerce-order-integration="row"
            ></ecommerce-integration-item>
          </div>
        </div>
      </div>
    </div>
  </div>

  <q-dialog v-model="isInstallAppFormOpen" @before-hide="onCloseInstallAppModal">
    <shopify-install
      v-model:url="shop_url_to_install_app"
      @install="onClickInstallApp"
    ></shopify-install>
  </q-dialog>

  <q-dialog v-model="isCreateTemplateOpen">
    <template-modal @close="onCloseCreateTemplate" />
  </q-dialog>
  <q-dialog v-model="isShowDisconnectPopup">
    <disconnect-popup @confirm="onConfirmDelete"></disconnect-popup>
  </q-dialog>
  <q-dialog v-model="isStoreModalOpen" @hide="getOrderIntegrations" v-if="selectedRow">
    <store-modal
      :store="selectedRow"
      @close="
        () => {
          isStoreModalOpen = false;
        }
      "
    />
  </q-dialog>
  <q-dialog v-model="shippingRuleModalOpen" @hide="getOrderIntegrations" v-if="selectedRow">
    <shipping-rule-listing-modal
      :store="selectedRow"
      @close="
        () => {
          shippingRuleModalOpen = false;
        }
      "
    />
  </q-dialog>
  <q-dialog v-model="checkoutRuleModalOpen" @hide="getOrderIntegrations">
    <shipvagoo-modal :header-separator="false" size="x-lg" title="Checkout rule" class="q-mt-lg">
      <shipvagoo-separator style="margin: 0 20px" />
      <div class="q-pa-md">
        <checkout-rule-list v-if="selectedRow" :integration="selectedRow" />
      </div>
    </shipvagoo-modal>
  </q-dialog>
</template>

<script setup lang="ts">
  import { onBeforeMount, Ref, ref } from 'vue';
  import { useStore } from 'vuex';
  import Woocommerce from '../assets/img/woocommerce.svg';
  import Economic from '../assets/img/economic.svg';
  import Dinero from '../assets/img/dinero.svg';
  import Billy from '../assets/img/billy.svg';

  import {
    loadingService,
    loggingService,
    notificationService,
    orderIntegrationService,
    templateService,
  } from '../services/_singletons';
  import {
    IOrderIntegration,
    IOrderIntegrationPlatform,
    IOrderEcommerceIntegration,
  } from '../model/order-integration.model';
  import { IRootState } from '../model/store.model';
  import { IRequestParam } from '../model/http.model';
  import { ITemplate } from '../model/template.model';
  import { IPackaging } from '../model/packaging.model';
  import { useRoute } from 'vue-router';

  const $store = useStore<IRootState>();
  const $route = useRoute();
  const ecommerceIntegrations = ref<IOrderEcommerceIntegration[]>([
    {
      name: 'Shopify',
      url: 'https://shipvagoo.com/wp-content/uploads/Shipvagoo-Shopify-integration-App-via-plalform-DK-EN.pdf',
      icon: 'fab fa-shopify',
      is_available: true,
      size: 'lg',
    },
    {
      name: 'WooCommerce',
      url: '',
      icon: `img:${Woocommerce}`,
      is_available: false,
      size: 'lg',
    },
  ]);

  const erpIntegrations = ref<IOrderEcommerceIntegration[]>([
    {
      name: 'Dinero',
      url: '',
      icon: `img:${Dinero}`,
      is_available: false,
      size: 'xl',
    },
    {
      name: 'E-conomics',
      url: '',
      icon: `img:${Economic}`,
      is_available: false,
      size: 'xl',
    },
    {
      name: 'Billy',
      url: '',
      icon: `img:${Billy}`,
      is_available: false,
      size: 'xl',
    },
  ]);

  const checkoutRuleModalOpen = ref<boolean>(false);
  const isStoreModalOpen = ref<boolean>(false);
  const selectedCompanyCode = ref<any>(null);
  const selectedIntegrationCode = ref<any>(null);
  const isShowDisconnectPopup = ref<boolean>(false);
  const isOrderIntegrationFormOpen = ref<boolean>(false);
  const shop_url_to_install_app = ref<string>('');
  const isInstallAppFormOpen = ref<boolean>(false);
  const orderIntegrationFormType = ref<'create' | 'update'>('create');
  const platforms = ref<IOrderIntegrationPlatform[]>([]);

  const selectedRowId = ref<number>(0);
  const selectedRow = ref<IOrderIntegration | null>(null);
  const orderIntegrationDetailModalTitle = ref<string | null>(null);
  const templates = ref<ITemplate[]>([]);
  const isCreateTemplateOpen = ref<boolean>(false);
  const createTemplateType = ref<'delivery' | 'return' | null>(null);

  const deliveryMethodForm = {
    isTransferOrderChecked: ref<boolean>(false),
    deliveryTemplate: ref<ITemplate | null>(null),
    returnTemplate: ref<ITemplate | null>(null),
    packaging: ref<IPackaging | null>(null),
    isDeliveredByThirdParty: ref<boolean>(false),
    isSkipUsingShipmentTemplate: ref<boolean>(false),
  };

  const search: Ref<string> = ref<string>('');

  const rows = ref<IOrderIntegration[]>([]);

  const currentIntegrationDetailTab = ref<'general' | 'delivery-methods' | 'shipping-methods'>(
    'general',
  );

  const shippingRuleModalOpen = ref<boolean>(false);
  const onClickStore = (row: IOrderIntegration) => {
    selectedRow.value = row;
    isStoreModalOpen.value = true;
  };
  const onClickRow = (row: IOrderIntegration) => {
    orderIntegrationFormType.value = 'update';
    orderIntegrationDetailModalTitle.value = `Edit Order Integration - ${row.name}`;
    isOrderIntegrationFormOpen.value = true;
    selectedRowId.value = row.integration_code;
    selectedRow.value = row;
  };

  const onCloseInstallAppModal = () => {
    isInstallAppFormOpen.value = false;
  };

  const onClickInstallAppOpenModel = () => {
    isInstallAppFormOpen.value = true;
  };
  const onClickInstallApp = () => {
    loadingService.startLoading('main-loader:get-order-integrations');
    var href = shop_url_to_install_app.value;
    let parser = document.createElement('a');
    parser.href = href;
    if (import.meta.env.VITE_appUrl.includes(parser.host)) {
      notificationService.showError('Please enter a valid shop url');
      loadingService.stopLoading('main-loader:get-order-integrations');
      return;
    } else {
      isInstallAppFormOpen.value = false;
    }
    let url =
      'https://' +
      parser.host +
      '/admin/oauth/authorize?client_id=' +
      import.meta.env.VITE_clientId +
      '&scope=write_customers,read_customers,write_discounts,read_discounts,read_gdpr_data_request,write_gift_cards,read_gift_cards,write_inventory,read_inventory,write_orders,read_orders,write_payment_terms,read_payment_terms,write_price_rules,read_price_rules,write_product_listings,read_product_listings,write_products,read_products,write_reports,read_reports,write_shipping,read_shipping,write_assigned_fulfillment_orders,read_assigned_fulfillment_orders,write_fulfillments,read_fulfillments,read_locations,write_third_party_fulfillment_orders,read_order_edits,read_third_party_fulfillment_orders,write_merchant_managed_fulfillment_orders,read_merchant_managed_fulfillment_orders&redirect_uri=' +
      import.meta.env.VITE_shopify_callback_url +
      '&state=' +
      $store.state.companyStore.company_code;
    window.location.href = url;
  };

  const updateIntegration = (api_url: any) => {
    loadingService.startLoading('main-loader:get-order-integrations');
    var href = api_url;
    let parser = document.createElement('a');
    parser.href = href;
    if (import.meta.env.VITE_appUrl.includes(parser.host)) {
      notificationService.showError('Please enter a valid shop url');
      loadingService.stopLoading('main-loader:get-order-integrations');
      return;
    }
    let url =
      'https://' +
      parser.host +
      '/admin/oauth/authorize?client_id=' +
      import.meta.env.VITE_clientId +
      '&scope=write_customers,read_customers,write_discounts,read_discounts,read_gdpr_data_request,write_gift_cards,read_gift_cards,write_inventory,read_inventory,write_orders,read_orders,write_payment_terms,read_payment_terms,write_price_rules,read_price_rules,write_product_listings,read_product_listings,write_products,read_products,write_reports,read_reports,write_shipping,read_shipping,write_assigned_fulfillment_orders,read_assigned_fulfillment_orders,write_fulfillments,read_fulfillments,read_locations,write_third_party_fulfillment_orders,read_order_edits,read_third_party_fulfillment_orders,write_merchant_managed_fulfillment_orders,read_merchant_managed_fulfillment_orders&redirect_uri=' +
      import.meta.env.VITE_shopify_callback_url +
      '&state=' +
      $store.state.companyStore.company_code;
    window.location.href = url;
  };

  const activateIntegration = async (companyCode: any, integrationCode: any) => {
    try {
      loadingService.startLoading('main-loader:create-order-integrations');
      await orderIntegrationService.activateOrderIntegration({
        company_code: companyCode,
        integration_code: integrationCode,
      });

      // notificationService.showSuccess('Order integration ha');
      getOrderIntegrations();
    } catch (error) {
      loggingService.error('updating order integration', error);
      notificationService.showError('Error updating integration');
    } finally {
      loadingService.stopLoading('main-loader:create-order-integrations');
    }
  };
  const onConfirmDelete = async () => {
    try {
      loadingService.startLoading('main-loader:create-order-integrations');
      await orderIntegrationService.deactivateOrderIntegration({
        company_code: selectedCompanyCode.value,
        integration_code: selectedIntegrationCode.value,
      });
      isShowDisconnectPopup.value = false;
      // notificationService.showSuccess('Order integration ha');
      await getOrderIntegrations();
    } catch (error) {
      loggingService.error('updating order integration', error);
      notificationService.showError('Error updating integration');
    } finally {
      loadingService.stopLoading('main-loader:create-order-integrations');
    }
  };
  const deactivateIntegration = async (companyCode: any, integrationCode: any) => {
    selectedCompanyCode.value = companyCode;
    selectedIntegrationCode.value = integrationCode;
    isShowDisconnectPopup.value = true;
  };

  const getOrderIntegrations = async () => {
    try {
      loadingService.startLoading('main-loader:get-order-integrations');
      const page = 1;
      const limit = 1000;

      const params: IRequestParam[] = [];

      if (search.value && search.value !== '') {
        params.push(
          { key: 'conditions[name:like]', value: search.value },
          { key: 'conditions[api_url:or_like]', value: search.value },
        );
      }
      const response = await orderIntegrationService.getOrderIntegrations(page, limit, params);
      $store.commit('integrationStore/setIntegrationsCount', response.entities.length);
      $store.commit('integrationStore/setIntegrations', response.entities);
      rows.value = response.entities;
      shouldOpenOrderIntegration();
    } catch (e) {
      loggingService.error('Loading order integrations', e);
      notificationService.showError('Error loading order integrations');
    } finally {
      loadingService.stopLoading('main-loader:get-order-integrations');
    }
  };

  const getIntegrationPlatforms = async () => {
    const response = await orderIntegrationService.getOrderIntegrationPlatforms();
    platforms.value = response;
  };
  const onClickShippingRule = (row: IOrderIntegration) => {
    selectedRow.value = row;
    shippingRuleModalOpen.value = true;
  };
  const onClickCheckoutRule = (row: IOrderIntegration) => {
    selectedRow.value = row;
    checkoutRuleModalOpen.value = true;
  };

  const getTemplates = async () => {
    try {
      loadingService.startLoading('main-loader:get-templates');
      const params: IRequestParam[] = [{ key: 'sorting[created_at]', value: 'desc' }];
      const response = await templateService.getTemplates(1, 1000, params);
      templates.value = response.entities;
    } catch (error) {
      loggingService.error('Error loading templates', error);
      notificationService.showError('Error loading templates');
    } finally {
      loadingService.stopLoading('main-loader:get-templates');
    }
  };
  const onCloseCreateTemplate = async (is?: 'create') => {
    isCreateTemplateOpen.value = false;

    if (is !== 'create') {
      return;
    }

    await getTemplates();

    const [template] = templates.value;

    if (createTemplateType.value === 'delivery') {
      deliveryMethodForm.deliveryTemplate.value = template;
    }

    if (createTemplateType.value === 'return') {
      deliveryMethodForm.returnTemplate.value = template;
    }
  };

  const shouldOpenOrderIntegration = () => {
    const integration_code: number = Number($route.query.integration_code);
    if (integration_code) {
      const orderIntegration = rows.value.find(
        (item: IOrderIntegration) => item.integration_code == integration_code,
      );
      console.log('Integration Code ', integration_code);
      if (orderIntegration) {
        onClickRow(orderIntegration);
        currentIntegrationDetailTab.value = 'shipping-methods';
      }
    }
  };

  onBeforeMount(async () => {
    if ($route.query.error_message)
      notificationService.showError($route.query.error_message.toString());
    await getOrderIntegrations();
    await getIntegrationPlatforms();
    if ($route.query['need-to-connect']) {
      isInstallAppFormOpen.value = true;
    }
  });
</script>

<style scoped lang="scss">
  .border-top {
    border-top: 1px solid $border-color;
  }
</style>

<style scoped lang="scss">
  .order_integration_page {
    padding: 20px 40px;

    @media (max-width: 1024px) {
      padding: 20px 10px;
    }
  }
</style>
