<template>
  <div class="flex justify-between items-center title-bar-heading">
    <title-bar title="Tracking Messages" subtitle="An overview of all your tracking messages." />
  </div>
  <!-- <div class="personalized_message_page q-mt-xs" v-if="!rows.length">
    <shipvagoo-placeholder
      text="No tracking messages are available">
    </shipvagoo-placeholder>
  </div> -->
  <div class="personalized_message_page">
    <div class="row q-mt-sm">
      <div class="col-lg-3 col-md-4 col-sm-6 col-12 q-pa-xs" v-for="(row, index) in rows"
        :key="`return_portal_${index}`">
        <tracking-message-item class="full-height" :tracking-message="row" @edit="onClickEdit"
          @delete="onClickDelete(row.message_code)" />
      </div>
      <div class="col-lg-3 col-md-4 col-sm-6 col-12 q-pa-xs card-min-height">
        <add-new-box class="full-height" @click="onClickAdd" label="Add New" />
      </div>
    </div>
  </div>

  <q-dialog v-model="isPersonalizedMessageModalOpen" @before-hide="onCloseFormModal">
    <tracking-message-modal :tracking-message="selectedPersonalizedMessage" @close="onCloseModal" />
  </q-dialog>
  <q-dialog v-model="showDeleteConfirm">
    <confirm-popup @confirm="onClickDeletePersonalizedMessage" confirm-label="Delete" confirmation-message="You are about to delete this tracking message. This action 
can not be undone" />
  </q-dialog>
</template>

<script setup lang="ts">
import { onBeforeMount, Ref, ref } from "vue";
import { Pagination } from "../../model/common.model";
import {
  loadingService,
  notificationService,
  loggingService,
  personalizedMessageService
} from "../../services/_singletons";
import {
  IPersonalizedMessage

} from "../../model/personalized-message.model";

import { IRequestParam } from "../../model/http.model";


const showDeleteConfirm = ref<boolean>(false);


const rows = ref<IPersonalizedMessage[]>([]);
const isPersonalizedMessageModalOpen: Ref<boolean> = ref<boolean>(false);
const selectedPersonalizedMessage = ref<IPersonalizedMessage | null>(null);
const selectedPersonalizedMessageCode = ref<number | null>(null);


const pagination = ref<Pagination>({
  rowsNumber: 0,
  rowsPerPage: 10,
  page: 1
});

const onCloseModal = () => {
  selectedPersonalizedMessage.value = null;
  isPersonalizedMessageModalOpen.value = false;
  getPersonalizedMessages(null);
};
const onClickDelete = (message_code: number) => {
  selectedPersonalizedMessageCode.value = message_code;
  showDeleteConfirm.value = true;
};

const onCloseFormModal = () => {
  isPersonalizedMessageModalOpen.value = false;
  selectedPersonalizedMessageCode.value = null;
  selectedPersonalizedMessage.value = null;
};

const onClickAdd = () => {
  selectedPersonalizedMessage.value = null;
  isPersonalizedMessageModalOpen.value = true;
};


const getPersonalizedMessages = async (tablePagination: Pagination | null) => {
  try {
    loadingService.startLoading("main-loader:personalized-messages");

    const page: number = tablePagination?.page || 1;
    const limit: number = tablePagination?.rowsPerPage || 10;

    const params: IRequestParam[] = [];

    if (tablePagination?.sortBy) {
      const sortingOrder = tablePagination.descending ? "desc" : "asc";
      params.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
    } else {
      params.push({ key: "sorting[created_at]", value: "desc" });
    }

    const response = await personalizedMessageService.getPersonalizedMessages(
      page,
      limit,
      params
    );

    pagination.value.page = page;
    pagination.value.rowsPerPage = limit;
    pagination.value.rowsNumber = response.total;
    pagination.value.sortBy = tablePagination?.sortBy;
    pagination.value.descending = tablePagination?.descending;
    rows.value = response.entities;
  } catch (e) {
    loggingService.error("Error while loading personalized messages", e);
    notificationService.showError("Error while loading personalized messages");
  } finally {
    loadingService.stopLoading("main-loader:personalized-messages");
  }
};

const onClickEdit = (row: IPersonalizedMessage) => {
  console.log("edit ", row);
  selectedPersonalizedMessage.value = row;
  isPersonalizedMessageModalOpen.value = true;
};

const onClickDeletePersonalizedMessage = async () => {
  if (!selectedPersonalizedMessageCode.value) {
    return;
  }
  try {
    loadingService.startLoading("main-loader:delete-personalized-message");
    await personalizedMessageService.deletePersonalizedMessage(selectedPersonalizedMessageCode.value);
    const page = pagination.value.page ?? 1;

    if (page > 1 && rows.value.length === 1) {
      pagination.value.page = page - 1;
    }

    getPersonalizedMessages(pagination.value);
  } catch (e) {
    notificationService.showError("Error while deleting personalized message");
    loggingService.error("Error while deleting personalized message", e);
  } finally {
    loadingService.stopLoading("main-loader:delete-personalized-message");
  }
};

onBeforeMount(() => {
  getPersonalizedMessages(null);
});
</script>

<style scoped lang="scss">
.personalized_message_page {
  padding: 0 40px 10px 40px;

  @media (max-width: 1024px) {
    padding: 0 20px 10px 20px;
  }

  .card-min-height {
    min-height: 200px;
  }
}
</style>