<template>
  <div class="q-pa-md">
    <div class="row login-page">
      <div class="col box-left flex items-center justify-center">
        <div style="width: 50%;">
          <img style="width: 100%;" class="image-logo" :src="LoginLogo" alt="logo-left"/>
          <div class="text-center">
            <p class="text-white" style="font-size: 40px;">
              Simplify Shipping
            </p>
          </div>
        </div>
      </div>
      <div class="col flex items-center justify-center box-right">
        <div class="wrapper" style="width: 65%;">
          <h4 class="text-black text-bold" style="margin: 0;">Forgot your password?</h4>
          <div class="text-grey">
            Please fill in e-mail to receive a new password or
            <shipvagoo-hyperlink label="sign in." to="/login" type="router-link"/>
          </div>
          <form @submit.prevent="submit" class="q-mt-lg">
            <q-input
              autocomplete="off"
              label="Email"
              label-color="gray"
              color="gray"
              type="email"
              :lazy-rules="true"
              :rules="[ (val, rules) => rules.email(val) || 'Please enter a valid email address' ]"
              v-model="state.form.email">
            </q-input>

            <shipvagoo-button
              class="login_page-login_button q-mt-lg"
              type="submit"
              label="Submit Request"
            ></shipvagoo-button>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {reactive} from 'vue';
  import useValidate from '@vuelidate/core';
  import {required, helpers, email} from '@vuelidate/validators';
  import {Router, useRouter} from 'vue-router';
  import {authService, loadingService, notificationService} from '../../services/_singletons';
  import LoginLogo from '../../assets/img/logo.svg';
  import {AxiosError} from "axios";
  import {IForgotPasswordResponse} from "../../model/auth.model";

  const state = reactive({
    form: {
      email: '',
    },
  });

  const v$ = useValidate(
    {
      form: {
        email: {
          required: helpers.withMessage('This field is required.', required),
          email: helpers.withMessage('Please enter a valid e-mail.', email),
        },
      },
    },
    state,
  );

  const $router: Router = useRouter();

  const submit = async () => {
    await v$.value.$validate();

    if (v$.value.$error) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:login');
      await authService.forgotPassword(state.form.email).then(async (response: IForgotPasswordResponse) => {
        if (response[0]) {
          notificationService.showSuccess(
            'If the email address you entered is associated with an account, you will receive an email with a link to reset your password.',
          );
          await $router.push('/login');
        }
      }).catch((error: AxiosError) => {
        if (error.response?.status == 422) {
          notificationService.showError('Invalid email is entered.')
        }
      });
    } finally {
      loadingService.stopLoading('main-loader:login');
    }
  };
</script>

<style scoped lang="scss">
  .label-inputs {
    color: #757575;
  }

  .invalid-feedback {
    // display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 80%;
    color: #dc3545;
  }

  .d-none {
    display: none;
  }


  .login-page {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 0 !important;

    .wrapper {
      box-sizing: border-box;
      width: 100%;
      padding: 10px;
    }

    .box-left {
      background-image: url("../../assets/img/login2.png");
      background-position: 50% 50%;
      background-size: cover;
    }

    .box-right {
      background: white;
    }
  }

  @media (max-width: 1200px) {
    .login-page {
      .box-left {
        .wrapper {
          .h-big {
            font-size: 50px;
          }
        }
      }
    }
  }

  @media (max-width: 900px) {
    .login-page {
      .box-left {
        .wrapper {
          .h-big {
            font-size: 30px;
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    .login-page {
      display: block;
      overflow: auto;

      .side-box {
        display: block;
      }
    }
  }

  .text-white {
    color: white
  }
</style>
