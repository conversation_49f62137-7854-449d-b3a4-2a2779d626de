<template>
  <div class="q-pa-md">
    <div class="row login-page">
      <div class="col box-left flex items-center justify-center">
        <div style="width: 50%;">
          <img style="width: 100%;" class="image-logo" :src="LoginLogo" alt="logo-left"/>
          <div class="text-center">
            <p class="text-white" style="font-size: 40px;">
              Simplify Shipping
            </p>
          </div>
        </div>
      </div>
      <div class="col flex items-center justify-center box-right">
        <div class="wrapper" style="width: 65%;">
          <h4 class="text-black text-bold" style="margin: 0;">Reset your password</h4>
          <form @submit.prevent="submit" class="q-mt-lg">
            <q-input
              autocomplete="off"
              class="q-mt-lg"
              label="Password"
              type="password"
              label-color="gray"
              color="gray"
              :rules="[ val => val.length >=8 || 'Password must be at least 8 characters in length.' ]"
              v-model="state.form.password">
            </q-input>
            <q-input
              autocomplete="off"
              class="q-mt-sm"
              label="Confirm Password"
              type="password"
              label-color="gray"
              color="gray"
              :rules="[ val => val ===state.form.password || 'Confirm password does not match.' ]"
              v-model="state.form.password_confirmation">
            </q-input>
            <shipvagoo-button
              class="login_page-login_button q-mt-lg"
              type="submit"
              label="Reset Password"
            ></shipvagoo-button>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import useValidate from '@vuelidate/core';
  import {required, helpers, minLength} from '@vuelidate/validators';
  import {Router, useRoute, useRouter} from 'vue-router';
  import {onBeforeMount, ref} from 'vue';
  import {authService, loadingService, notificationService} from '../../services/_singletons';
  import LoginLogo from '../../assets/img/logo.svg';

  const token = ref<string | null>(null);
  const state = ref({
    form: {
      password: '',
      password_confirmation: '',
    },
  });

  const v$ = useValidate(
    {
      form: {
        password: {
          required: helpers.withMessage('This field is required.', required),
          minLength: helpers.withMessage('Password must be at least 8 characters.', minLength(8)),
        },
        password_confirmation: {
          required: helpers.withMessage('This field is required.', required),
          sameAsPassword: helpers.withMessage(
            'Password does not match.',
            (value: string) => value === state.value.form.password,
          ),
        },
      },
    },
    state,
  );

  const $router: Router = useRouter();
  const $route = useRoute();

  const submit = async () => {
    await v$.value.$validate();

    if (v$.value.$error || !token.value || typeof token.value !== 'string') {
      console.log(v$.value.$error)
      console.log(token.value)
      console.log(typeof $route.params.token)
      return;
    }
    try {
      loadingService.startLoading('main-loader:reset-password');
      await authService.resetPassword(token.value, state.value.form.password,state.value.form.password_confirmation);
      notificationService.showSuccess(
        'Password reset successfully. You can now login with your new password.',
      );
      $router.push('/login');
    } finally {
      loadingService.stopLoading('main-loader:reset-password');
    }
  };

  onBeforeMount(async () => {
    token.value = $route.query.token as string;
    if (!token.value) {
      await $router.push('/login');
    }
  });
</script>

<style scoped lang="scss">
  .label-inputs {
    color: #757575;
  }

  .invalid-feedback {
    // display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 80%;
    color: #dc3545;
  }

  .d-none {
    display: none;
  }


  .login-page {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 0 !important;

    .wrapper {
      box-sizing: border-box;
      width: 100%;
      padding: 10px;
    }

    .box-left {
      background-image: url("../../assets/img/login2.png");
      background-position: 50% 50%;
      background-size: cover;
    }

    .box-right {
      background: white;
    }
  }

  @media (max-width: 1200px) {
    .login-page {
      .box-left {
        .wrapper {
          .h-big {
            font-size: 50px;
          }
        }
      }
    }
  }

  @media (max-width: 900px) {
    .login-page {
      .box-left {
        .wrapper {
          .h-big {
            font-size: 30px;
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    .login-page {
      display: block;
      overflow: auto;

      .side-box {
        display: block;
      }
    }
  }

  .text-white {
    color: white
  }
</style>
