<template>
  <q-layout>
    <q-page-container>
      <!--      <q-page>
              <q-img
                :src="dashboard_image"
                style="max-width: 100%; height: 100%"
                mode="cover"
              >
              </q-img>
            </q-page>-->
      <q-page class="q-page-container">
        <div class="row">
          <div class="col-12 dashboard-name-box">
            Welcome {{ $store.state.companyStore.company_name }}
          </div>
        </div>
        <div class="row">
          <div class="col-md-6 col-12 q-pr-md no-padding-md">
            <div class="row">
              <div class="col-12">
                <div class="q-orders-container">
                  <div class="row">
                    <div class="col-12 container-header custom-padding flex items-center">
                      <div class="row full-width">
                        <div class="col-3 flex items-center">
                          <div class="text-gray-dark h3-heading">Order</div>
                        </div>
                        <div class="col-9 flex justify-end">
                          <div class="row">
                            <div class="flex items-center q-mr-lg">
                              <span class="text-gray-dark h3-heading"
                                >Filter By Year
                                <span class="h5-heading"
                                  >({{ new Date().getFullYear() }})</span
                                ></span
                              >
                            </div>
                            <!--                            <q-select outlined
                                                                  popup-content-style="color: gray;"
                                                                  style="width: 130px;"
                                                                  dense
                                                                  v-model="order_filter"
                                                                  :options="order_filter_options">
                            
                                                        </q-select>-->
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-12">
                      <q-separator />
                    </div>
                    <div class="col-12">
                      <div class="row">
                        <div class="col-12 custom-padding">
                          <div class="row">
                            <div class="col-sm-6 col-12 q-pr-sm no-padding-sm">
                              <DashboardTile
                                :icon="'img:' + NewOrdersIcon"
                                type="order"
                                label="New Orders"
                                :count="orders_stats.total_orders"
                              ></DashboardTile>
                            </div>
                            <div class="col-sm-6 col-12 q-pl-sm no-padding-sm">
                              <DashboardTile
                                :icon="'img:' + OngoingOrdersIcon"
                                type="order"
                                label="Ongoing Orders"
                                :count="orders_stats.ongoing_orders"
                              ></DashboardTile>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-12 container-header custom-padding flex items-center">
                      <div class="row full-width">
                        <div class="col-6 flex items-center">
                          <div class="text-gray-dark h3-heading q-pa-md">Return</div>
                        </div>
                      </div>
                    </div>
                    <div class="col-12">
                      <q-separator style="margin-top: 14px" />
                    </div>
                    <div class="col-12">
                      <div class="row">
                        <div class="col-12 custom-padding">
                          <div class="row">
                            <div class="col-sm-6 col-12 q-pr-sm no-padding-sm">
                              <DashboardTile
                                :icon="'img:' + UnfilledReturn"
                                type="return"
                                label="Unfilled Return"
                                :count="orders_stats.unfilled_return"
                              ></DashboardTile>
                            </div>
                            <div class="col-sm-6 col-12 q-pl-sm no-padding-sm">
                              <DashboardTile
                                :icon="'img:' + TotalReturn"
                                type="return"
                                label="Total Return"
                                :count="orders_stats.total_return"
                              ></DashboardTile>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-12">
                <div class="q-orders-container">
                  <div class="row">
                    <div class="col-12 container-header custom-padding flex items-center">
                      <div class="row full-width">
                        <div class="col-6 flex items-center">
                          <div class="text-gray-dark h3-heading">Open Tasks</div>
                        </div>
                        <div class="col-6 flex justify-end">
                          <div class="flex items-center justify-end">
                            <!-- <q-btn
                               dense
                               flat
                               round
                               style="color:#BBBBBB;padding: 0"
                               class="more-action"
                               field="edit"
                               icon="more_vert"
                             >
                               <shipvagoo-menu style="width: 160px">
                                 <shipvagoo-menu-item
                                   clickable
                                 >
 
                                 </shipvagoo-menu-item>
 
                               </shipvagoo-menu>
                             </q-btn>-->
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-12">
                      <q-separator />
                    </div>
                    <div class="col-12 custom-padding q-open-tasks-container">
                      <ContentLoader
                        v-if="task_stats_loading"
                        v-for="n in 5"
                        :key="n"
                        ry="10"
                        viewBox="0 0 600 110"
                      >
                        <rect width="100%" height="90" />
                      </ContentLoader>
                      <DashboardTaskAlert
                        v-else
                        v-for="task in open_tasks"
                        :type="task.type"
                        :label="task.description"
                        class="q-my-sm"
                        @resolve-now="resolveNow"
                        @accept-now="acceptAllShipmentTemplate(task.label)"
                        @deny-now="denyALLShipmentTemplate(task.label)"
                      ></DashboardTaskAlert>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6 col-12 q-pl-md no-padding-md">
            <div class="row">
              <div class="col-12">
                <div class="q-orders-container">
                  <div class="row">
                    <div class="col-12 container-header custom-padding flex items-center">
                      <div class="row full-width">
                        <div class="col-3 flex items-center">
                          <div class="text-gray-dark h3-heading">Sales Statistics</div>
                        </div>
                        <div class="col-9 flex justify-end">
                          <div class="row">
                            <div class="flex items-center q-mr-lg">
                              <span class="text-gray-dark h3-heading">Filter By</span>
                            </div>
                            <q-select
                              outlined
                              popup-content-style="color: gray;"
                              style="width: 150px"
                              dense
                              @update:model-value="onChangeSaleFilter"
                              v-model="order_filter"
                              :options="order_filter_options"
                            >
                            </q-select>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-12">
                      <q-separator />
                    </div>
                    <div class="col-12">
                      <div class="row">
                        <div class="col-12 custom-padding"></div>
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-12">
                      <div class="row">
                        <div class="col-12 custom-padding">
                          <div class="row">
                            <div class="col-sm-6 col-12 q-pr-sm no-padding-sm">
                              <DashboardTile
                                icon="fas fa-money-bill-wave"
                                type="shipment"
                                :label="`Shipment Cost <span style=font-size:11px;>(ex. VAT)</span>`"
                                :count="`DKK ${
                                  sales_stats.shipment_cost
                                    ? danishNumberFormat(
                                        Number(sales_stats.shipment_cost)?.toFixed(2),
                                      )
                                    : '0'
                                }`"
                              ></DashboardTile>
                            </div>
                            <div class="col-sm-6 col-12 q-pl-sm no-padding-sm">
                              <DashboardTile
                                icon="fas fa-shipping-fast"
                                type="shipment"
                                label="Total Shipments"
                                :count="
                                  sales_stats.total_shipments
                                    ? danishNumberFormat(sales_stats.total_shipments)
                                    : 0
                                "
                              ></DashboardTile>
                            </div>
                          </div>
                        </div>
                        <!-- <div class="col-12 custom-padding">
                          <div class="flex items-center q-mt-sm">
                            <div class="text-gray-dark h3-heading  " style="width: 60px;">Carrier</div>
                            <div>
                              <q-select
                                hide-bottom-space
                                multiple
                                class="dashboard-q-select"
                                dense
                                style="min-width: 1px;"
                                label-color="black"
                                v-model="carrier"
                                :options="carriersFilterOptions">
                                <template v-slot:selected-item="scope">
                                  <q-chip
                                    dense
                                    @remove="scope.removeAtIndex(scope.index)"
                                    :tabindex="scope.tabindex"
                                    color="white"
                                    text-color="blue"
                                    class="q-ma-xs"
                                  >
                                    <q-img height="20px" width="20px" class="q-ma-sm" :src="scope.opt.img"/>
                                    {{ scope.opt.label }}
                                    <div
                                      @click="scope.removeAtIndex(scope.index)"
                                      class="clearable-option">
                                      <q-icon name="clear" color="white"/>
                                    </div>
                                  </q-chip>
                                </template>
                                <template v-slot:option="{ itemProps, opt, selected ,toggleOption}">
                                  <q-item
                                    :active="selected"
                                    active-class="bg-blue"
                                    v-bind="itemProps"
                                    style="padding-left: 0">
                                    <q-item-section side>
                                      <q-checkbox
                                        size="xs"
                                        :model-value="selected"
                                        @update:model-value="toggleOption(opt)"></q-checkbox>
                                    </q-item-section>
                                    <q-item-section>
                                      <div class="row">
                                        <q-img :src="opt.img" height="18px" width="18px"></q-img>
                                        <q-item-label class="q-ml-sm" style="color: black;"
                                                      v-html="opt.label"></q-item-label>
                                      </div>
                                    </q-item-section>
                                  </q-item>
                                </template>
                              </q-select>
                            </div>
                          </div>
                          <q-separator class="q-mt-md"/>
                        </div> 
                        <div class="col-12 q-mt-md">
                          <div class="row">
                            <div class="col custom-padding q-pr-sm no-padding-sm">
                              <ListLoader v-if="sales_loading"
                                          viewBox="0 0 100 90">
                              </ListLoader>
                              <StatisValues
                                v-else
                                :values="sales_stats.shipping_products"
                                class="q-mt-md"
                                heading="Shipping Product"></StatisValues>
                            </div>
                            <q-separator vertical></q-separator>
                            <div class="col custom-padding q-pr-sm no-padding-sm">
                              <ListLoader
                                v-if="sales_loading"
                                viewBox="0 0 100 90">
                              </ListLoader>
                              <StatisValues
                                v-else
                                :values="sales_stats.weight_classes"
                                class="q-mt-md" heading="Weight Class"></StatisValues>
                            </div>
                          </div>
                        </div>
                        <div class="col-12">
                          <q-separator class="q-mt-md"/>
                        </div>
                        <div class="col-12 custom-padding">
                          <div class="row">
                            <div class="col-sm-6 col-12 q-pr-sm no-padding-sm">
                              <DashboardTile :icon="'img:'+SalesInvoice" type="sales"
                                             :label="`Sales Invoice <span style=font-size:11px;>(ex. VAT)</span>`"
                                             :count="`DKK ${sales_stats.sales_invoice?danishNumberFormat(sales_stats.sales_invoice):'0'}`"></DashboardTile>
                            </div>
                            <div class="col-sm-6 col-12 q-pl-sm no-padding-sm">
                              <DashboardTile :icon="'img:'+SalesInvoice" type="sales"
                                             :label="`Avg. Sales Invoice <span style=font-size:11px;>(ex. VAT)</span>`"
                                             :count="`${sales_stats.avg_sale_price?danishNumberFormat(sales_stats.avg_sale_price):'0'}`"></DashboardTile>
                            </div>
                          </div>
                          <div class="row">
                            <div class="col-12">
                              <div class="flex items-center">
                                <div class="text-gray-dark h3-heading  " style="min-width: 40px;">Shop
                                </div>
                                <div>
                                  <q-select
                                    class="dashboard-q-select"
                                    dense
                                    style="min-width: 1px;"
                                    v-model="shop"
                                    :options="shopCarriersOptions"
                                  >
                                    <template v-slot:selected-item="scope">
                                      <q-chip
                                        dense
                                        @remove="scope.removeAtIndex(scope.index)"
                                        :tabindex="scope.tabindex"
                                        color="white"
                                        text-color="blue"
                                        class="q-ma-xs"
                                      >
                                        {{ scope.opt.label }}
                                      </q-chip>
                                    </template>
                                    <template v-slot:option="{ itemProps, opt, selected ,toggleOption}">
                                      <q-item
                                        :active="selected"
                                        active-class="bg-blue"
                                        v-bind="itemProps">
                                        <q-item-section side>
                                          <q-checkbox
                                            size="xs"
                                            :model-value="selected"
                                            @update:model-value="toggleOption(opt)"></q-checkbox>
                                        </q-item-section>
                                        <q-item-section>
                                          <q-item-label class="q-ml-sm" style="color: black;"
                                                        v-html="opt.label"></q-item-label>
                                        </q-item-section>
                                      </q-item>
                                    </template>
                                  </q-select>
                                </div>
                              </div>
                            </div>
                            <div class="col-12">
                              <q-separator class="q-my-md"/>
                            </div>
                            <div class="col-12">
                              <ListLoader v-if="sales_loading"
                                          viewBox="0 0 300 90">
                              </ListLoader>
                              <StatisValues
                                v-else
                                :values="sales_stats.top_selling_products"
                                class="q-mt-md" heading="Top 5 Selling Products"></StatisValues>
                            </div>
                          </div>
                        </div> -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<chat-bot />

<script setup lang="ts">
  //import dashboard_image from '../../assets/img/dashboard.png';

  // import {ContentLoader, ListLoader} from 'vue-content-loader'
  import { ContentLoader } from 'vue-content-loader';
  import {
    IDashboardSalesStats,
    IDashboardOrderStats,
    IDashboardCarriers,
  } from '../../model/v1/dashboard.model';
  import { onMounted, reactive, ref, watch } from 'vue';
  import { danishNumberFormat } from '../../helpers';

  import NewOrdersIcon from '../../assets/img/dashboard/new_orders.svg';
  import OngoingOrdersIcon from '../../assets/img/dashboard/ongoing_orders.svg';
  import UnfilledReturn from '../../assets/img/dashboard/unfilled_return.svg';
  import TotalReturn from '../../assets/img/dashboard/total_return.svg';
  // import SalesInvoice from '../../assets/img/dashboard/sales_invoice.svg'
  import {
    dashboardServiceV1,
    loadingService,
    loggingService,
    notificationService,
    orderService,
  } from '../../services/_singletons';
  import { useStore } from '../../store';
  import { useRoute } from 'vue-router';

  interface IAlert {
    type: string;
    description: string;
    label: string;
  }

  interface IFilter {
    label: string;
    value: string;
  }

  interface ICarriers {
    label?: string;
    value?: string;
    img?: string;
  }

  const resolveNow = () => {
    window.location.href = '/orders';
  };

  const acceptAllShipmentTemplate = async (value: any) => {
    try {
      loadingService.startLoading('main-loader:accept-all-shipment-template');
      await orderService.acceptAllShipmentTemplate({
        type: value,
      });
      //  getDashboardTasks();
    } catch (e: any) {
      loggingService.error('Error accepting or ', e);
      notificationService.showError(
        e.response.data.error ? e.response.data.error : 'Error accepting templates',
      );
    } finally {
      loadingService.stopLoading('main-loader:accept-all-shipment-template');
    }
  };

  const denyALLShipmentTemplate = async (value: any) => {
    try {
      loadingService.startLoading('main-loader:deny-all-shipment-template');
      await orderService.denyAllShipmentTemplate({
        type: value,
      });
      //getDashboardTasks();
    } catch (e: any) {
      loggingService.error('Error accepting or ', e);
      notificationService.showError(
        e.response.data.error ? e.response.data.error : 'Error accepting templates',
      );
    } finally {
      loadingService.stopLoading('main-loader:deny-all-shipment-template');
    }
  };

  // const denyShipmentTemplate = async () => {
  //   try{
  //     loadingService.startLoading('main-loader:deny-shipment-template');
  //
  //     await orderService.denyShipmentTemplate(props.row.order_code, {
  //       type: "deny"
  //     });
  //     emit('refresh-order', props.row.order_code);
  //   } catch (e: any) {
  //     loggingService.error('Error accepting or ', e);
  //     notificationService.showError(e.response.data.error ? e.response.data.error : 'Error denying template');
  //   } finally {
  //     loadingService.stopLoading('main-loader:deny-shipment-template');
  //   }
  // }

  const $store = useStore();
  const $route = useRoute();
  let orders_stats = reactive<IDashboardOrderStats>({
    total_orders: 0,
    ongoing_orders: 0,
    unfilled_return: 0,
    total_return: 0,
  });
  let sales_stats = ref<IDashboardSalesStats>({});
  const task_stats_loading = ref<boolean>(false);
  const sales_loading = ref<boolean>(false);
  const order_filter = ref<IFilter>({
    label: 'Today',
    value: 'TODAY',
  });
  const order_filter_options = ref<Array<IFilter>>([
    {
      label: 'Today',
      value: 'TODAY',
    },
    {
      label: 'Week to Date',
      value: 'WEEK',
    },
    {
      label: 'Month to Date',
      value: 'MONTH',
    },
    {
      label: 'Year to Date',
      value: 'YEAR',
    },
  ]);
  let open_tasks = reactive<IAlert[]>([]);

  const prefix = '../../assets/img/';
  const stringOptions = [
    { label: 'GLS', value: `GLS Denmark`, img: `${prefix}gls_dk.png` },
    { label: 'DAO', value: 'DAO', img: `${prefix}dao.png` },
    /* {label: 'Postnord', value: 'postnord', img: `${prefix}Postnord.png`},
   {label: 'DHL', value: 'dhl', img: `${prefix}dhl_express.png`},
   {label: 'Budbee', value: 'budbee', img: `${prefix}gls_dk.png`,},*/
  ];
  const carriersFilterOptions = ref<ICarriers[]>(stringOptions);
  const carrier = ref<ICarriers[]>([]);
  const shopCarriersOptions = ref<ICarriers[]>(stringOptions);
  const shop = ref<ICarriers>({});

  watch(
    () => carrier.value,
    (newVal: ICarriers[], oldValue) => {
      const carriers = newVal.map((item: ICarriers) => item.value);
      getDashboardSalesStats({
        filter: order_filter.value.value,
        carriers: carriers,
        shop: shop.value.value,
      });
    },
  );
  watch(
    () => shop.value,
    (newVal: ICarriers, oldValue) => {
      const carriers = carrier.value.map((item: ICarriers) => item.value);
      getDashboardSalesStats({
        filter: order_filter.value.value,
        shop: newVal.value,
        carriers: carriers,
      });
    },
  );
  watch(
    () => $store.state.userStore.user_code,
    (newVal: number | string, oldValue) => {
      if (newVal != 0) {
        getCarriers(newVal);
      }
    },
  );
  /*const filterFn = (val, update) => {
  update(() => {
    if (val === '') {
      carriersFilterOptions.value = stringOptions
    } else {
      const needle = val.toLowerCase()
      carriersFilterOptions.value = stringOptions.filter(
        v => v.toLowerCase().indexOf(needle) > -1
      )
    }
  })
}*/

  const onChangeSaleFilter = (selected: IFilter) => {
    const carriers = carrier.value.map((item: ICarriers) => item.value);
    getDashboardSalesStats({ filter: selected.value, shop: shop.value.value, carriers: carriers });
  };
  const getDashboardOrdersStats = () => {
    dashboardServiceV1.getDashboardOrderStats(2023).then((response) => {
      orders_stats = response;
    });
  };
  const getDashboardSalesStats = (params: any) => {
    sales_loading.value = true;
    dashboardServiceV1
      .getDashboardSalesStats(params)
      .then((response) => {
        sales_stats.value = response;
      })
      .finally(() => {
        sales_loading.value = false;
      });
  };
  /* const getDashboardTasks = () => {
  task_stats_loading.value = true;
  dashboardServiceV1.getDashboardTasks().then(response => {
    open_tasks = response;
  }).finally(() => {
    setTimeout(() => {
      task_stats_loading.value = false;
    }, 500);
  });
}; */

  interface IResponse {
    [key: string]: any;
  }

  const getCarriers = (code: number | string) => {
    dashboardServiceV1.getUserCarriers(code).then((response: IResponse) => {
      const carriers = response.carriers;
      const shops = response.shops;
      carriersFilterOptions.value = carriers.map((item: IDashboardCarriers) => {
        return {
          label: item.name,
          value: item.name,
          img: item.url,
        } as ICarriers;
      });
      shopCarriersOptions.value = shops.map((item: IResponse) => {
        return {
          label: item.name,
          value: item.integration_code,
        } as ICarriers;
      });
      if (shopCarriersOptions.value.length) {
        shop.value = shopCarriersOptions.value[0];
      }
    });
  };
  onMounted(() => {
    const user_code: number = $store.state.userStore.user_code;
    if (user_code) {
      getCarriers(user_code);
    }
    getDashboardOrdersStats();
    getDashboardSalesStats({
      filter: order_filter.value.value,
      carriers: [],
      ship: '',
    });
    //getDashboardTasks();

    if ($route.query.error_message)
      notificationService.showError($route.query.error_message.toString());
  });
</script>

<style scoped>
  .q-page-container {
    padding: 20px;
    background-color: #ffffff;
  }

  .dashboard-name-box {
    height: 97px;
    padding-left: 30px;
    font-size: 24px;
    font-style: normal;
    font-weight: 800;
    line-height: 97px;
    color: #1467cc;
    background: #e9f3ff;
    border-radius: 5px;
  }

  .q-orders-container {
    margin-top: 25px;
    background: #ffffff;
    border-radius: 4.91561px;
    box-shadow: 0px 3.93249px 19.6624px rgba(170, 169, 184, 0.25);
  }

  .q-open-tasks-container {
    height: 450px;
    overflow-y: auto;
  }

  .container-header {
    height: 50px;
  }

  @media (min-width: 1665px) {
    .dashboard-name-box {
      height: 120px;
      padding-left: 50px;
      line-height: 120px;
    }
  }

  .custom-padding {
    padding: 0 20px 0 20px;
  }

  .text-gray-dark {
    font-weight: 700;
    color: #313131;
  }

  .h6-heading {
    font-size: 10px;
  }

  .h5-heading {
    font-size: 13px;
  }

  .h4-heading {
    font-size: 15px;
  }

  .h3-heading {
    font-size: 18px;
  }

  .h2-heading {
    font-size: 21px;
  }

  .h1-heading {
    font-size: 24px;
  }

  @media (max-width: 978px) {
    .no-padding-md {
      padding: 0 !important;
    }
  }

  @media (max-width: 598px) {
    .no-padding-sm {
      padding: 0 !important;
    }
  }

  .bg-blue {
    color: white;
    background: #1f2124;
  }

  .q-field__label {
    font-size: 30px !important;
    font-weight: bold !important;
  }
</style>
<style>
  .q-field--outlined .q-field__control {
    border-radius: 8px;
  }

  .dashboard-q-select .q-field__control:before,
  .q-field__control:after {
    display: none;
  }

  .clearable-option {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 17px;
    height: 17px;
    margin-left: 8px;
    background-color: #a8a8a8;
    border-radius: 50%;
  }
</style>
