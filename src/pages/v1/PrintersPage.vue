<template>
  <div class="flex justify-between items-center title-bar-heading q-mt-md">
    <title-bar style="width: 50%;" title="Add Printer" subtitle="Setup your new printer." />
    <shipvagoo-card-actions class="flex justify-end no-padding">
      <shipvagoo-button-dropdown-v1
        color="#FFFFFF"
        bg-color="#243E90"
        min-width="140px"
        dropdown-icon="arrow_forward_ios"
        outline no-caps
        label="Download"
      >
        <shipvagoo-list>
          <shipvagoo-menu-item
            v-close-popup
            clickable
            @click="onClickDropdownAction('windows')"
          >
            For Windows
          </shipvagoo-menu-item>
          <shipvagoo-menu-item
            v-close-popup
            clickable
            @click="onClickDropdownAction('apple-m1')"
          >
            Apple M1
          </shipvagoo-menu-item>
          <shipvagoo-menu-item
            v-close-popup
            clickable
            @click="onClickDropdownAction('apple-intel')"
          >
            Apple Intel
          </shipvagoo-menu-item>
        </shipvagoo-list>
      </shipvagoo-button-dropdown-v1>
      <shipvagoo-button
        min-width="140px"
        @click="store.dispatch('printerStore/openApiAccessModalOpen')">
        Printer API Keys
      </shipvagoo-button>
    </shipvagoo-card-actions>
  </div>

  <div class="printers_page">
    <div class="row">
      <div class="col-md-6">
        <shipvagoo-card-section
          class="flex column gap-20 rounded-borders"
          style="border: 1px solid #7d839830;">
          <shipvagoo-input-group responsive>
            <shipvagoo-input-v1 v-model="form.name" placeholder="Enter printer name" label="Printer name"
                                :error="$v.name.$error"
                                :error-message="String($v.name.$errors?.[0]?.$message ?? null)" />

            <shipvagoo-select-v1 v-model="form.computer" :options="printerApps" placeholder="Select device "
                                 label="Device" option-label="hostname" option-value="printer_app_code"
                                 @update:model-value="onUpdateComputer" :error="$v.computer.$error"
                                 :error-message="String($v.computer.$errors?.[0]?.$message ?? null)" />

          </shipvagoo-input-group>

          <shipvagoo-input-group responsive>
            <shipvagoo-select-v1 v-model="form.printer" placeholder="Select printer" label="Printer"
                                 :error="$v.printer.$error"
                                 :error-message="String($v.printer.$errors?.[0]?.$message ?? null)"
                                 :options="form.computer?.printers || []" />

            <shipvagoo-select-v1 v-model="form.format" placeholder="Select print format"
                                 :error="$v.format.$error"
                                 :error-message="String($v.format.$errors?.[0]?.$message ?? null)"
                                 label="Format" :options="formats" />

          </shipvagoo-input-group>

          <shipvagoo-card-actions>
            <shipvagoo-button :label="primaryActionLabel" min-width="80px"
                              @click="isFormChanged ? onEditPrinterSubmit() : onCreatePrinterSubmit()" />
          </shipvagoo-card-actions>

        </shipvagoo-card-section>
      </div>
      <div class="col-md-6 q-pl-md">
        <div style="border: 1px solid #7d839830;" class="q-pa-md rounded-borders" v-if="rows">
          <q-list>

            <template v-for="printer in rows">
              <q-expansion-item default-opened class="bg-grey-2 text-bold q-mt-sm rounded-borders"
                                style="border: 1px solid #7d839830;"
                                :header-style="{ color: 'black' }">
                <template v-slot:header>
                  <div class="flex al-rt">
                    <!-- Label with printer name -->
                    <div class="q-pa-sm"> {{ form.computer?.hostname }}, ({{ printer.name }})</div>
                    <div class="dl-rd-btn q-mr-md" style="text-align: end;">
                      <!-- Edit button -->
                      <q-btn flat style=" width: 20%;background-color: white; " :icon="'img:' + editicon"
                             class="q-ml-md" @click.stop="onClickUpdatePrinter(printer)" />
                      <!-- Delete button -->
                      <q-btn flat style=" width: 20%;background-color: white; " :icon="'img:' + Delicon"
                             class="q-ml-md" @click.stop="onClickDeletePrinter(printer)" />
                    </div>
                  </div>
                </template>

                <q-card class="">
                  <q-card-section>
                    <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
                      <div class="printer-item-container">
                        <div class="printer-item-container">
                          <div class="printer-header" style="display: flex">
                            <div class="text-gray-brand printer-header-name">
                              <subtitle>Default printing documents:</subtitle>
                            </div>
                          </div>
                          <div class="printer-content flx-rw text-weight-regular">
                            <div class="q-mt-md">

                              <shipvagoo-checkbox-v1 v-model="printer.is_default_label">Printing
                                labels
                              </shipvagoo-checkbox-v1>
                            </div>
                            <div class="q-mt-md">
                              <shipvagoo-checkbox-v1 v-model="printer.is_default_pick_documents">
                                Picking list
                              </shipvagoo-checkbox-v1>
                            </div>
                            <div class="q-mt-md">
                              <shipvagoo-checkbox-v1 v-model="printer.is_default_other">
                                Other documents
                              </shipvagoo-checkbox-v1>
                            </div>
                            <div class="q-mt-md">
                              <shipvagoo-checkbox-v1 v-model="printer.is_auto_print_pick_list">
                                Automatic printing
                              </shipvagoo-checkbox-v1>
                            </div>
                          </div>
                        </div>
                      </div>
                    </shipvagoo-card-section>
                  </q-card-section>
                </q-card>
              </q-expansion-item>
            </template>
          </q-list>
        </div>
      </div>
    </div>
  </div>
</template>


<script setup lang="ts">
import { onBeforeMount, ref } from "vue";
import { IRequestParam } from "../../model/http.model";
import { IPrinter, IPrinterApp } from "../../model/printer.model";
import {
  loadingService,
  loggingService,
  notificationService,
  printerService
} from "../../services/_singletons";

// Printer-defaultOptionListAppearanceProvider.vue code below


import { useStore } from "../../store";
// import { defaultOptionListAppearanceProvider } from 'pdf-lib';

import editicon from "../../assets/img/edt-icon.svg";
import Delicon from "../../assets/img/del-icn.svg";

import useVuelidate, { ValidationArgs } from "@vuelidate/core";
import { helpers, required } from "@vuelidate/validators";

const store = useStore();
const primaryActionLabel = ref<string>("Download Printer App (Windows)");
const primaryActionHandler = ref<string>("windows");
const rows = ref<IPrinter[]>([]);
// const isPrinterDetailOpen = ref<boolean>(false);
const selectedPrinterToEdit = ref<IPrinter | null>(null);
const getPrinters = async () => {
  try {
    loadingService.startLoading("main-loader:get-printers");
    const page: number = 1;
    const limit: number = 1000;
    const params: IRequestParam[] = [];
    const result = await printerService.getPrinters(page, limit, params);
    rows.value = result.entities;
  } catch (error) {
    loggingService.error("Error getting printers", error);
    notificationService.showError("Error getting printers");
  } finally {
    loadingService.stopLoading("main-loader:get-printers");
  }
};
const onClickDropdownAction = (machine: string) => {
  let fileUrl = "";
  let fileName = "";
  switch (machine) {
    case "windows":
      fileUrl = import.meta.env.VITE_s3_app_link_windows;
      fileName = "printer_app.exe";
      break;
    case "apple-m1":
      fileUrl = import.meta.env.VITE_s3_app_link_apple_m1;
      fileName = "printer_app_m1.dmg";
      break;
    case "apple-intel":
      fileUrl = import.meta.env.VITE_s3_app_link_apple_intel;
      fileName = "printer_app_intel.dmg";
      break;
    default:
      fileUrl = import.meta.env.VITE_s3_app_link_windows;
      fileName = "printer_app.exe";
      break;
  }

  // Create a link element
  const link = document.createElement("a");
  link.href = fileUrl;
  link.download = fileName; // Specify the desired filename

  // Append the link to the document body
  document.body.appendChild(link);

  // Trigger the click event to start the download
  link.click();

  // Clean up by removing the link from the document body
  document.body.removeChild(link);
};
// const onClickPrinter = (printer: IPrinter) => {
//     selectedPrinterToEdit.value = printer;
//     isPrinterDetailOpen.value = true;
// };

// const onClickCreate = () => {
//     selectedPrinterToEdit.value = null;
//     isPrinterDetailOpen.value = true;
// };

// const onClosePrinterDetail = () => {
//     isPrinterDetailOpen.value = false;
//     selectedPrinterToEdit.value = null;
// };


// const onClickDropdownAction = (machine: string) => {
//     let fileUrl = ''
//     let fileName = ''
//     switch (machine) {
//         case 'windows':
//             fileUrl = import.meta.env.VITE_s3_app_link_windows;
//             fileName = "printer_app.exe";
//             break;
//         case 'apple-m1':
//             fileUrl = import.meta.env.VITE_s3_app_link_apple_m1;
//             fileName = "printer_app_m1.dmg";
//             break;
//         case 'apple-intel':
//             fileUrl = import.meta.env.VITE_s3_app_link_apple_intel;
//             fileName = "printer_app_intel.dmg";
//             break;
//         default:
//             fileUrl = import.meta.env.VITE_s3_app_link_windows;
//             fileName = "printer_app.exe";
//             break;
//     }

// Create a link element
//     const link = document.createElement('a');
//     link.href = fileUrl;
//     link.download = fileName; // Specify the desired filename

//     // Append the link to the document body
//     document.body.appendChild(link);

//     // Trigger the click event to start the download
//     link.click();

//     // Clean up by removing the link from the document body
//     document.body.removeChild(link);
// };

onBeforeMount(() => {
  getPrinters();

  console.log(navigator.platform);
  console.log(navigator.userAgent);
  console.log(navigator.hardwareConcurrency);

  if (navigator.platform.toLowerCase().includes("win")) {
    primaryActionLabel.value = "Download Printer App (Windows)";
    primaryActionHandler.value = "windows";
  } else if (navigator.platform.toLowerCase().includes("arm")) {
    primaryActionLabel.value = "Download Printer App (Apple M1)";
    primaryActionHandler.value = "apple-m1";
  } else if (navigator.platform.toLowerCase().includes("intel")) {
    primaryActionLabel.value = "Download Printer App (Apple Intel)";
    primaryActionHandler.value = "apple-intel";
  }
});


interface Props {
  printer: IPrinter | null;
}

const $store = useStore();
const props = withDefaults(defineProps<Props>(), { printer: null });
const emit = defineEmits(["close", "create", "update"]);
const printerApps = ref<IPrinterApp[]>([]);

interface IForm {
  name: string | null;
  computer: IPrinterApp | null;
  printer: string | null;
  format: "A4" | "10x19" | "ZPL" | null;
  isDefaultPrinterForLabels: boolean;
  isDefaultPrinterForPickDocuments: boolean;
  isDefaultPrinterForOther: boolean;
  isAutoPrintPickList: boolean;
}

const form = $ref<IForm>({
  name: null,
  computer: null,
  printer: null,
  format: null,
  isDefaultPrinterForLabels: false,
  isDefaultPrinterForPickDocuments: false,
  isDefaultPrinterForOther: false,
  isAutoPrintPickList: false
});


// Temporarily disabled ZPL format
// const formats = ['A4', '10x19', 'ZPL'];
const formats = ["A4", "10x19"];

// const isCreate = $computed(() => props.printer === null);
// const isEdit = $computed(() => props.printer !== null);

// const printerDetailTitle = $computed(() => {
//     if (props.printer) {
//         return props.printer.name;
//     }

//     return 'New printer';
// });


const $v = useVuelidate<IForm, ValidationArgs<IForm>>(
  {
    name: {
      required: helpers.withMessage("Name field is required.", required)
    },
    computer: {
      required: helpers.withMessage("Device field is required.", required)
    },
    format: {
      required: helpers.withMessage("Format field is required.", required)
    },
    printer: {
      required: helpers.withMessage("Printer field is required.", required)
    },
    isDefaultPrinterForLabels: {},
    isAutoPrintPickList: {},
    isDefaultPrinterForOther: {},
    isDefaultPrinterForPickDocuments: {}
  },
  form
);

const onClickDeletePrinter = async (row: IPrinter) => {
  try {
    loadingService.startLoading("main-loader:delete-printer");
    await printerService.deletePrinter(row.printer_code);
    getPrinters();
    notificationService.showSuccess("Printer deleted successfully");
    resetForm();

  } catch (error) {
    loggingService.error("Error deleting printer", error);
    notificationService.showError("Error deleting printer");
  } finally {
    loadingService.stopLoading("main-loader:delete-printer");
  }
};


const onCreatePrinterSubmit = async () => {
  await $v.value.$validate();
  if ($v.value.$error) {
    return;
  }
  try {
    if (!form.name || !form.computer || !form.printer || !form.format) {
      return;
    }

    loadingService.startLoading("main-loader:create-printer");
    const createdPrinter = await printerService.createPrinter({
      name: form.name,
      printer_app_code: form.computer?.printer_app_code,
      linked_printer: form.printer,
      format: form.format,
      is_default_label: form.isDefaultPrinterForLabels,
      is_default_other: form.isDefaultPrinterForOther,
      is_default_pick_documents: form.isDefaultPrinterForPickDocuments,
      is_auto_print_pick_list: form.isAutoPrintPickList
    });

    // Clear the form fields after creating the printer
    form.name = null;
    form.printer = null;
    form.format = null;
    form.computer = null;
    form.isDefaultPrinterForLabels = false;
    form.isDefaultPrinterForOther = false;
    form.isDefaultPrinterForPickDocuments = false;
    form.isAutoPrintPickList = false;

    // Fetch the printer's details again and assign them to form.computer
    await getPrinterApps();

    // Update the form.computer object with the newly created printer's details
    form.computer = printerApps.value.find((printerApp) => printerApp.printer_app_code === createdPrinter.printer_app_code) ?? null;

    // After successful creation, directly add the new printer to the list
    rows.value.push(createdPrinter);

    $store.dispatch("printerStore/syncPrinters");
    emit("create");
    emit("close");
  } catch (error) {
    loggingService.error("Error while creating printer", error);
    notificationService.showError("Error creating printer");
  } finally {
    loadingService.stopLoading("main-loader:create-printer");
  }
};

const isFormChanged = ref<boolean>(false); // Variable to track form changes

const onClickUpdatePrinter = async (printer: IPrinter) => {
  try {
    selectedPrinterToEdit.value = printer;
    // Populate the form fields with printer details
    form.name = printer.name;
    form.computer = printerApps.value.find(
      (printerApp) => printerApp.printer_app_code === printer.printer_app_code
    ) || null;
    form.printer = printer.linked_printer;
    form.format = printer.format;
    form.isDefaultPrinterForLabels = printer.is_default_label;
    form.isDefaultPrinterForPickDocuments = printer.is_default_pick_documents;
    form.isDefaultPrinterForOther = printer.is_default_other;
    form.isAutoPrintPickList = printer.is_auto_print_pick_list;

    // Set a flag indicating that the form is being edited
    isFormChanged.value = true;

    // Update the button label to 'Update'
    primaryActionLabel.value = "Update ";
  } catch (error) {
    loggingService.error("Error updating printer", error);
    notificationService.showError("Error updating printer");
  }


};


// Logic to handle form submission
const onEditPrinterSubmit = async () => {
  try {
    if (!selectedPrinterToEdit.value) {
      return;
    }
    if (!form.name || !form.computer || !form.printer || !form.format) {
      return;
    }
    loadingService.startLoading("main-loader:edit-printer");
    await printerService.updatePrinter(selectedPrinterToEdit.value.printer_code, {
      name: form.name,
      printer_app_code: form.computer?.printer_app_code,
      linked_printer: form.printer,
      format: form.format,
      is_default_label: form.isDefaultPrinterForLabels,
      is_default_other: form.isDefaultPrinterForOther,
      is_default_pick_documents: form.isDefaultPrinterForPickDocuments,
      is_auto_print_pick_list: form.isAutoPrintPickList
    });
    resetForm();

    // Change the button label back to 'Create'
    primaryActionLabel.value = "Create";
  } catch (error) {
    // Existing error handling code...
  } finally {
    loadingService.stopLoading("main-loader:edit-printer");
  }

};

// Logic to reset the form
const resetForm = () => {
  form.name = null;
  form.computer = null;
  form.printer = null;
  form.format = null;
  form.isDefaultPrinterForLabels = false;
  form.isDefaultPrinterForOther = false;
  form.isDefaultPrinterForPickDocuments = false;
  form.isAutoPrintPickList = false;
  isFormChanged.value = false; // Reset form change flag
  primaryActionLabel.value = "Create ";
};

onBeforeMount(() => {
  getPrinters();

  // Set the primary action label directly to 'Create'
  primaryActionLabel.value = "Create";

  console.log(navigator.platform);
  console.log(navigator.userAgent);
  console.log(navigator.hardwareConcurrency);

  // The rest of the logic detecting the platform and setting the label accordingly can be removed
});


const getPrinterApps = async () => {
  try {
    loadingService.startLoading("main-loader:get-printer-apps");
    const result = await printerService.getPrinterApps();
    printerApps.value = result.map((p) => ({
      ...p,
      hostname: `${p.hostname}${p.instance <= 1 ? "" : `-${p.instance}`}`
    }));
  } catch (error) {
    loggingService.error("Error getting printer apps", error);
    notificationService.showError("Error getting printer apps");
  } finally {
    loadingService.stopLoading("main-loader:get-printer-apps");
  }
};

const onUpdateComputer = () => {
  form.printer = null;
};

const hydrate = async () => {
  await getPrinterApps();

  if (props.printer) {
    form.name = props.printer.name;
    form.computer = printerApps.value.find(
      (printerApp) => printerApp.printer_app_code === props.printer?.printer_app_code
    ) as IPrinterApp;
    form.printer = props.printer.linked_printer;
    form.format = props.printer.format;
    form.isDefaultPrinterForLabels = props.printer.is_default_label;
    form.isDefaultPrinterForPickDocuments = props.printer.is_default_pick_documents;
    form.isDefaultPrinterForOther = props.printer.is_default_other;
    form.isAutoPrintPickList = props.printer.is_auto_print_pick_list;
  }
};

onBeforeMount(() => {
  hydrate();
});


// Pervious code
// const onClickUpdatePrinter = async (printer: IPrinter) => {
//     try {
//         // Check if there are any changes in the Shipvago card input fields
//         const isFormChanged = (
//             form.name !== printer.name ||
//             form.computer?.printer_app_code !== printer.printer_app_code ||
//             form.printer !== printer.linked_printer ||
//             form.format !== printer.format ||
//             form.isDefaultPrinterForLabels !== printer.is_default_label ||
//             form.isDefaultPrinterForPickDocuments !== printer.is_default_pick_documents ||
//             form.isDefaultPrinterForOther !== printer.is_default_other ||
//             form.isAutoPrintPickList !== printer.is_auto_print_pick_list
//         );

//         // If there are changes, populate the form fields and change the button to an update button
//         if (isFormChanged) {
//             form.name = printer.name;
//             form.computer = printerApps.value.find(
//                 (printerApp) => printerApp.printer_app_code === printer.printer_app_code,
//             ) || null;
//             form.printer = printer.linked_printer;
//             form.format = printer.format;
//             form.isDefaultPrinterForLabels = printer.is_default_label;
//             form.isDefaultPrinterForPickDocuments = printer.is_default_pick_documents;
//             form.isDefaultPrinterForOther = printer.is_default_other;
//             form.isAutoPrintPickList = printer.is_auto_print_pick_list;

//             // Change the button to an update button
//             primaryActionLabel.value = 'Update Printer';
//             primaryActionHandler.value = 'update';
//         }

//         // You can add logic here to handle the update action if necessary
//     } catch (error) {
//         loggingService.error('Error updating printer', error);
//         notificationService.showError('Error updating printer');
//     }
// };


// const onEditPrinterSubmit = async () => {
//     try {
//         if (
//             !props.printer?.printer_code ||
//             !form.name ||
//             !form.computer ||
//             !form.printer ||
//             !form.format
//         ) {
//             return;
//         }

//         loadingService.startLoading('main-loader:edit-printer');
//         await printerService.updatePrinter(props.printer.printer_code, {
//             name: form.name,
//             printer_app_code: form.computer.printer_app_code,
//             linked_printer: form.printer,
//             format: form.format,
//             is_default_label: form.isDefaultPrinterForLabels,
//             is_default_other: form.isDefaultPrinterForOther,
//             is_default_pick_documents: form.isDefaultPrinterForPickDocuments,
//             is_auto_print_pick_list: form.isAutoPrintPickList,
//         });
//         $store.dispatch('printerStore/syncPrinters');
//         emit('update');
//         emit('close');
//     } catch (error) {
//         loggingService.error('Error while updating printer', error);
//         notificationService.showError('Error updating printer');
//     } finally {
//         loadingService.stopLoading('main-loader:edit-printer');
//     }
// };


// const onCreatePrinterSubmit = async () => {
//     try {
//         if (!form.name || !form.computer || !form.printer || !form.format) {
//             return;
//         }

//         loadingService.startLoading('main-loader:create-printer');
//         await printerService.createPrinter({
//             name: form.name,
//             printer_app_code: form.computer?.printer_app_code,
//             linked_printer: form.printer,
//             format: form.format,
//             is_default_label: form.isDefaultPrinterForLabels,
//             is_default_other: form.isDefaultPrinterForOther,
//             is_default_pick_documents: form.isDefaultPrinterForPickDocuments,
//             is_auto_print_pick_list: form.isAutoPrintPickList,
//         });
//         $store.dispatch('printerStore/syncPrinters');
//         emit('create');
//         emit('close');
//     } catch (error) {
//         loggingService.error('Error while creating printer', error);
//         notificationService.showError('Error creating printer');
//     } finally {
//         loadingService.stopLoading('main-loader:create-printer');
//     }
// };
</script>


<style scoped lang="scss">
.printers_page {
  padding: 0 40px 10px 40px;

  @media (max-width: 1024px) {
    padding: 0 20px 10px 20px;
  }

  .info {
    font-size: 15px;
    color: #e1e1e1;

    &-label {
      display: block;
      margin-bottom: 10px;

      font-weight: bold;
    }
  }

  &-actions {
    display: flex;
    flex-direction: row;

    & > *:not(:first-child) {
      margin-left: 10px;

      @media (max-width: 600px) {
        margin-left: unset;
      }
    }

    .big_search-rows_per_page-group {
      display: flex;
      flex: 1;
      flex-direction: row;

      & > *:not(:first-child) {
        margin-left: 10px;
      }

      .rows_per_page {
        display: none;

        @media (max-width: $tablet) {
          display: block;
        }
      }
    }

    &-big_search {
      flex: 1;
    }

    @media (max-width: 600px) {
      flex-direction: column;

      &-big_search {
        margin-bottom: 10px;
      }
    }
  }

  &-chips {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 11px;
  }

  &-modal {
    &-header {
      font-size: 18px;
    }

    &-order_modal {
      display: flex;
      flex-direction: column;
      gap: 20px;

      &-fulfillments {
        &-title {
          margin-bottom: 20px;
          font-size: 20px;
          line-height: 24px;
        }

        &-header {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          padding: 20px;
          font-size: 15px;

          &-prices {
            display: flex;
            flex-direction: row;

            & > *:not(:first-child) {
              margin-left: 30px;
            }
          }
        }

        &-footer {
          display: flex;
          flex-direction: row;
          justify-content: flex-end;
          padding: 20px;

          & > *:not(:first-child) {
            margin-left: 10px;
          }

          @media (max-width: 800px) {
            flex-direction: column;

            & > *:not(:first-child) {
              margin-top: 10px;
              margin-left: unset;
            }
          }
        }
      }

      &-events {
        &-title {
          margin-bottom: 20px;
          font-size: 20px;
          line-height: 24px;
        }

        &-date {
          display: flex;
          flex-direction: column;
          font-size: 12px;
          line-height: 14px;
          color: #e1e1e1;
        }
      }
    }

    .q-card {
      min-width: 310px;
      //  background: #151526;
      background: #FFF;

      &.pick_order {
        width: 898px;
        max-width: 898px;
      }

      &.order_modal {
        width: 1038px;
        max-width: 1038px;
      }
    }

    &-footer {
      display: flex;
      justify-content: flex-end;

      &-pick_button {
        width: 220px;
      }
    }
  }

  &-success {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 42px;
    height: 42px;
    border: 1px solid #389b99;
    border-radius: 50%;

    img {
      width: 14px;
      height: 10px;
      filter: invert(55%) sepia(11%) saturate(2247%) hue-rotate(130deg) brightness(95%) contrast(76%);
    }

    &-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      margin-top: 44px !important;
      margin-bottom: 44px !important;

      .find_service_point {
        display: flex;
        margin-top: 24px;
      }
    }

    &-info {
      margin-top: 24px;
      font-size: 15px;
      line-height: 17px;
      text-align: center;

      p {
        margin-bottom: 10px;
      }
    }
  }
}

.add-card-box {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 345px;
  background: #FFFFFF;
  border-radius: 5px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
}

.dropdown-icon {
  fill: black;
}
</style>


<style scoped>


.flx-rw {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  justify-items: start;
}

.header-bg {
  background-color: #243E90;
}

.btn-style {
  font-size: 15px;
  color: white;
  background: #243E90
}

.return-portal-modal {
  width: 650px;
  max-width: 650px !important;
  overflow: hidden;
  border-radius: 15px;
}
</style>


<style>
.q-item__section--side > .q-icon {
  font-size: 14px;
  color: black;

}

.q-item__section.column.q-item__section--side.justify-center.q-focusable.relative-position.cursor-pointer {
  padding: 7px 10px;
  background: white;
  border-radius: 4px;

}

.return-portal-modal[data-v-cd143fe9] {
  margin-right: 2%;
}

.al-rt {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.edt .del {
  background: white;

}

.q-card__actions.justify-start.q-card__actions--horiz.row.ui_kit-shipvagoo_card_actions.ui_kit-shipvagoo_card_actions--right {
  padding: 0 !important;
}
</style>
  