<template>
  <div class="flex justify-between items-center title-bar-heading">
    <title-bar style="width: 50%;" :title="$t('common.funds')"/>
    <div style="height: 50px;" class="flex items-end">
      <shipvagoo-button min-width="140px" @click="onClickAddFunds">
        Add Funds
      </shipvagoo-button>
    </div>
  </div>
  <div class="add_funds_page">
    <div class="add_funds_page-actions">
      <div class="big_search-rows_per_page-group row">
        <div class="col-12">
          <big-search
            @update:modelValue="searchVal"
            placeholder="Search for created at, comment, order no."
            v-model="search" class="booked_page-actions-big_search"/>
        </div>
      </div>
      <!-- <shipvagoo-button outline label="Download bulk" /> -->
    </div>

    <shipvagoo-table
      v-model:pagination="pagination"
      :columns="columns"
      :rows="rows"
      :total="pagination.rowsNumber"
      :loading="loadingService.loadings.has('main-loader:get-transactions-add-funds')"
      @row-click="onClickPrintReceipt"
      @request="getFunds($event.pagination)"
    />
  </div>

  <!--<shipvagoo-fab :icon="`img:${CardIcon}`" @click="onClickAddFunds"/>-->

  <!--<div class="pdf-assets" aria-hidden>
    <img class="pdf-logo" height="106" width="662.88" :src="LogoDark"/>\
  </div>
  <PaymentReceipt :transactionToPrint="transactionToPrint"
                  v-if="transactionToPrint"></PaymentReceipt>-->
  <q-dialog v-model="isAddFundsModalOpen">
    <add-fund @close="onCloseAddFunds" @refresh="onRefreshAddFunds"/>
  </q-dialog>
</template>

<script setup lang="ts">
  import {computed, onBeforeMount, Ref, ref} from 'vue';
  import {QTableProps} from 'quasar';
  import dayjs from 'dayjs';
  import {useI18n} from 'vue-i18n';
  import {
    loadingService,
    loggingService,
    notificationService,
    transactionService,
  } from '../services/_singletons';
  import {ITransaction} from '../model/transactions.model';
  import {Pagination} from '../model/common.model';
  import {IRequestParam} from '../model/http.model';
  import HelperService from '../services/helper.service';
  import PriceService from '../services/price.service';
  import {useHelper} from "../composeable/Helper";

  const {showPdf} = useHelper();
  const {t} = useI18n();
  const isAddFundsModalOpen: Ref<boolean> = ref<boolean>(false);
  const search: Ref<string> = ref<string>('');
  const rows = ref<ITransaction[]>([]);
  const searchVal = (value: string) => {
    search.value = value;
    getFunds(pagination.value);
  }
  const pagination = ref<Pagination>({
    rowsNumber: 0,
    rowsPerPage: 10,
    page: 1,
  });

  const onClickAddFunds = () => {
    isAddFundsModalOpen.value = true;
  };

  const onCloseAddFunds = () => {
    isAddFundsModalOpen.value = false;
  };

  const columns = computed<QTableProps['columns']>(() => [
    {
      name: 'created_at',
      field: (row: ITransaction) => dayjs(row.created_at).format('DD/MM/YYYY HH:mm:ss'),
      required: true,
      label: t('common.created_at'),
      align: 'left',
      sortable: true,
    },
    {
      name: 'description',
      field: 'description',
      required: true,
      label: t('common.comment'),
      align: 'left',
      sortable: true,
    },
    {
      name: 'id',
      field: (row: ITransaction) => HelperService.formatId(row.id),
      required: true,
      label: t('common.reference_no'),
      align: 'left',
      sortable: true,
    },
    {
      name: 'amount',
      field: (row: ITransaction) => PriceService.formatPrice(Number(row.amount)),
      required: true,
      label: t('common.amount'),
      align: 'right',
      sortable: true,
    },
  ]);

  const getFunds = async (tablePagination: Pagination | null) => {
    try {
      loadingService.startLoading('main-loader:get-transactions-add-funds');

      const page = tablePagination?.page ?? 1;
      const limit = tablePagination?.rowsPerPage ?? 10;

      const params: IRequestParam[] = [
        {key: 'conditions[transaction_status:equal]', value: 'COMPLETED'},
      ];

      if (search.value && search.value !== '') {
        params.push({key: 'conditions[created_at:like]', value: search.value});
        params.push({key: 'conditions[description:or_like]', value: search.value});
        params.push({key: 'conditions[id:or_like]', value: search.value});
      }

      if (tablePagination?.sortBy) {
        const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
        params.push({key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder});
      } else {
        params.push({key: 'sorting[created_at]', value: 'desc'});
      }

      const result = await transactionService.getFundsTransactions(page, limit, params);
      rows.value = result.entities;
      pagination.value.rowsNumber = result.total;
      pagination.value.page = result.current_page;
      pagination.value.sortBy = tablePagination?.sortBy;
      pagination.value.descending = tablePagination?.descending;
    } catch (e) {
      loggingService.error('Error while getting transactions for add funds', e);
      notificationService.showError('Error while getting transactions for add funds');
    } finally {
      loadingService.stopLoading('main-loader:get-transactions-add-funds');
    }
  };

  const onClickPrintReceipt = async (event: Event | null, row: ITransaction) => {
    if (event && HelperService.containsIgnoredRowElements(event)) {
      return;
    }

    if (row.transaction_status !== 'COMPLETED') {
      notificationService.showError("Only completed transactions' receipts can be downloaded");
      return;
    }
    try {
      loadingService.startLoading('main-loader:print-transaction-receipt');
      const response = await transactionService.downloadFundsPdf(row.transaction_code);
      await showPdf(response.file);
    } finally {
      loadingService.stopLoading('main-loader:print-transaction-receipt');
    }
  };


  const onRefreshAddFunds = () => {
    getFunds(null);
  };

  onBeforeMount(() => {
    getFunds(null);
  });
</script>

<style scoped lang="scss">
  .pdf-assets {
    height: 0;
    overflow: hidden;
  }

  .add_funds_page {
    padding: 0 40px 10px 40px;

    @media (max-width: 1024px) {
      padding: 0 20px 10px 20px;
    }

    &-actions {
      display: flex;
      flex-direction: row;

      & > *:not(:first-child) {
        margin-left: 10px;

        @media (max-width: 600px) {
          margin-left: unset;
        }
      }

      .big_search-rows_per_page-group {
        display: flex;
        flex: 1;
        flex-direction: row;

        & > *:not(:first-child) {
          margin-left: 10px;
        }

        .rows_per_page {
          display: none;

          @media (max-width: $tablet) {
            display: block;
          }
        }
      }

      &-big_search {
        flex: 1;
      }

      @media (max-width: 600px) {
        flex-direction: column;

        &-big_search {
          margin-bottom: 10px;
        }
      }
    }

    &-iframe {
      width: 100%;
      height: 100%;
      min-height: 500px;
      border: none;
    }
  }
</style>
