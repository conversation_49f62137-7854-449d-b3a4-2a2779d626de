<template>
  <div class="flex items-center title-bar-heading q-mt-md" style="width: 100%">
    <div style="flex: 1">
      <title-bar style="color: #212121" title="Billing" :subtitle="subTitle" />
    </div>
    <div class="billing-shipvagoo-tabs flex justify-center">
      <div>
        <shipvagoo-tabs :options="options" v-model="tabValue" />
      </div>
    </div>
    <div style="flex: 1" class="flex justify-end">
      <big-search
        placeholder="Search"
        v-model="search"
        @update:modelValue="(value) => (search = value)"
        class="billing-page-actions-big_search"
      />
    </div>
  </div>
  <div class="billing-page">
    <billing-invoices-list
      v-if="tabValue == 'billing_invoices'"
      :search-param="search"
    ></billing-invoices-list>
    <monthly-invoices-list
      v-if="tabValue == 'monthly_invoices'"
      :search-param="search"
    ></monthly-invoices-list>
    <invoices-list v-if="tabValue == 'invoices'" :search-param="search"></invoices-list>
    <credit-notes-list v-if="tabValue == 'credit_notes'" :search-param="search"></credit-notes-list>
    <monthly-credit-notes-list
      v-if="tabValue == 'monthly_credit_notes'"
      :search-param="search"
    ></monthly-credit-notes-list>
  </div>
</template>
<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { ITabOption } from '../model/common.model';

  const search = ref<string>('');
  const options = ref<ITabOption[]>([
    {
      label: 'Billing invoices',
      value: 'billing_invoices',
      subtitle: 'Shipments purchased with credit balance',
    },
    /*  {
      label: 'Invoices',
      value: 'invoices',
      subtitle: 'Shipments purchased with saved credit card',
    }, */
    {
      label: 'Monthly invoices',
      value: 'monthly_invoices',
      subtitle: 'Monthly invoice of shipments paid with balance payment',
    },
    { label: 'Credit notes', value: 'credit_notes', subtitle: 'Credit notes' },
    // {label: 'Monthly credit notes', value: 'monthly_credit_notes', subtitle: 'Monthly credit notes '},
  ]);
  const tabValue = ref<string>('billing_invoices');

  const subTitle = computed(() => {
    return options.value.find((item) => item.value == tabValue.value)?.subtitle ?? '';
  });
</script>

<style scoped lang="scss">
  .billing-page {
    padding-right: 40px;
    padding-left: 40px;
    &-actions-big_search {
      width: 300px !important;
    }
  }

  .billing-shipvagoo-tabs {
    flex: 0 0 30%;
  }
  @media (max-width: 1366px) {
    .billing-shipvagoo-tabs {
      flex: 0 0 40%;
    }
  }
</style>
