<template>
  <title-bar :title="$t('common.pdf_settings')"/>
  <div class="pick_settings-page_container">
    <div class="pick_settings-inputs_container">
      <shipvagoo-input-group responsive>
        <shipvagoo-select
          v-model="shippingLabelFormat"
          label="Shipping Label Format"
          :options="formats"
        />
        <shipvagoo-select v-model="pickListFormat" label="Picking List Format" :options="formats" />
      </shipvagoo-input-group>
      <div class="pick_settings-footer">
        <shipvagoo-button
          class="save_button"
          label="Save"
          min-width="120px"
          @click="updatePdfSettings"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onBeforeMount } from 'vue';
  import {
    loadingService,
    notificationService,
    loggingService,
    pdfSettingsService,
  } from '../services/_singletons';

  let shippingLabelFormat = $ref<'A4' | '10x19'>('A4');
  let pickListFormat = $ref<'A4' | '10x19'>('A4');
  const formats = ['A4', '10x19'];

  const getPdfSettings = async () => {
    try {
      loadingService.startLoading('main-loader:pdf-settings');
      const response = await pdfSettingsService.getPdfSettings();
      shippingLabelFormat = response.shipping_label_format || 'A4';
      pickListFormat = response.pick_list_format || 'A4';
    } catch (e) {
      notificationService.showError('Error while loading pdf settings');
      loggingService.error('Error while loading pdf settings', e);
    } finally {
      loadingService.stopLoading('main-loader:pdf-settings');
    }
  };

  const updatePdfSettings = async () => {
    try {
      loadingService.startLoading('main-loader:update-pdf-settings');
      await pdfSettingsService.updatePdfSettings({
        shipping_label_format: shippingLabelFormat,
        pick_list_format: pickListFormat,
      });
      getPdfSettings();
    } catch (e) {
      notificationService.showError('Error while updating pdf settings');
      loggingService.error('Error while updating pdf settings', e);
    } finally {
      loadingService.stopLoading('main-loader:update-pdf-settings');
    }
  };

  onBeforeMount(() => {
    getPdfSettings();
  });
</script>

<style lang="scss" scoped>
  .pick_settings {
    &-page_container {
      padding: 20px 40px;

      @media (max-width: $tablet) {
        padding: 20px 10px;
      }
    }

    &-inputs_container {
      padding: 20px;
      margin-top: 25px;
      border: 1px solid #e1e1e1;
      border-radius: 5px;
    }

    &-pick_path_label {
      padding-bottom: 4px;
      margin-top: 35px;
      font-size: 16px;
      line-height: 19px;
      border-bottom: 1px dashed #e1e1e1;
    }

    &-checkboxes_container {
      display: flex;
      flex-direction: row;
      width: 100%;
      margin-top: 20px;

      & > * {
        flex: 1;
      }

      @media (max-width: $tablet) {
        flex-direction: column;
        gap: 20px;
      }

      &-checkbox_group {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }
    }

    &-voice_options {
      margin-top: 20px;

      &-inputs {
        display: flex;
        flex-direction: column;
        gap: 20px;

        width: calc(50% - 10px);

        @media (max-width: $tablet) {
          width: 100%;
        }
      }
    }

    &-pick_list_options {
      margin-top: 20px;
    }

    &-footer {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      padding: 20px;
      margin-top: 20px;
      margin-right: -20px;
      margin-bottom: -20px;
      margin-left: -20px;
      border-top: 1px solid #e1e1e1;

      @media (max-width: $tablet) {
        & > * {
          width: 100%;
        }
      }
    }
  }
</style>
