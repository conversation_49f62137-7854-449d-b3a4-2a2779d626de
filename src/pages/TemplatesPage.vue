<template>
  <div class="flex justify-between items-center title-bar-heading">
    <title-bar title="Shipping Rules" subtitle="An overview of all your shipping rules." />
    <div class="flex">
      <big-search
        placeholder="Search for ID or shipping rule"
        style="width: 300px"
        @update:modelValue="searchVal"
        v-model="search"
        class="address_book_page-actions-big_search q-mr-md"
      />
      <shipvagoo-button clickable @click="onClickCreateTemplate"
        >New shipping rule</shipvagoo-button
      >
    </div>
  </div>
  <div class="templates_page q-mt-xs" v-if="!rows.length">
    <shipvagoo-placeholder text="Shipping rules are being generated"> </shipvagoo-placeholder>
  </div>
  <div class="templates_page" v-if="rows.length">
    <div class="templates_page-table">
      <shipvagoo-table-v1
        v-model:pagination="pagination"
        :columns="columns"
        :rows="rows"
        :total="pagination.rowsNumber"
        :loading="loadingService.loadings.has('main-loader:get-templates')"
        @row-click="onClickEditTemplate"
        @request="getTemplates($event.pagination)"
      >
        <template #body-cell-parcel_weight="props">
          <q-td :props="props" class="parcel_weight">
            <span class="text-no-wrap" v-if="props.row.parcels.length">
              {{
                `${props.row.parcels[0]?.min_weight ?? 0}-${
                  props.row.parcels[0]?.max_weight ?? 0
                } ${props.row.parcels[0]?.unit ?? ''}`
              }}
            </span>
          </q-td>
        </template>
        <template #body-cell-updated_at="props">
          <q-td :props="props" class="updated_at">
            <span class="text-no-wrap">
              {{ dayjs(String(props.row.updated_at)).format('DD/MM/YYYY') }}</span
            ><br />
            <span class="text-no-wrap">
              {{ dayjs(String(props.row.updated_at)).format('HH:mm') }}</span
            >
          </q-td>
        </template>
        <template #body-cell-name="props">
          <q-td :props="props" class="name">
            <span class="text-no-wrap">
              {{ getCountry(props.row.sender_country_code).default_name }} -
              {{ getCountry(props.row.receiver_country_code)?.default_name ?? '' }}</span
            ><br />
            <span class="text-no-wrap">
              {{
                CarrierService.getCarrierFromShippingProductCode(
                  $store.state.courierStore.carriers,
                  $store.state.courierStore.carrierProducts,
                  props.row.carrier_product_code,
                )?.name
              }} </span
            ><br />
            <span class="text-no-wrap">
              {{
                $store.state.courierStore.carrierProducts.find(
                  (item: IShippingProduct) =>
                    item.carrier_product_code == props.row?.carrier_product_code,
                )?.name
              }}
              <span class="text-no-wrap" v-if="props.row.parcels.length">
                {{
                  `${props.row.parcels[0]?.min_weight ?? 0}-${
                    props.row.parcels[0]?.max_weight ?? 0
                  } ${props.row.parcels[0]?.unit ?? ''}`
                }}
              </span>
            </span>
            <br />
          </q-td>
        </template>
        <template #body-cell-more="props">
          <q-td :props="props" class="more">
            <q-btn
              dense
              flat
              round
              style="color: #1f2124"
              class="more-action"
              field="edit"
              icon="more_horiz"
            >
              <shipvagoo-menu>
                <!--                <shipvagoo-menu-item clickable @click="onClickCreateShipment(props.row)">-->
                <!--                  {{ $t('common.create_shipment') }}-->
                <!--                </shipvagoo-menu-item>-->
                <!--                <shipvagoo-separator/>-->
                <shipvagoo-menu-item clickable @click="onClickDeleteTemplate(props.row)">
                  {{ $t('common.delete') }}
                </shipvagoo-menu-item>
              </shipvagoo-menu>
            </q-btn>
          </q-td>
        </template>
        <template #body-cell-origin_country="props">
          <q-td key="origin_country" :props="props">
            <div
              class="flex items-center no-wrap"
              v-if="props.row && getCountry(props.row.sender_country_code)"
            >
              <img
                class="orders-country_flag q-mr-sm"
                :src="
                  '/assets/img/' +
                  getCountry(props.row.sender_country_code)?.iso.toLowerCase() +
                  '.svg'
                "
                decoding="async"
              />
              <div>
                {{ getCountry(props.row.sender_country_code).default_name }}
              </div>
            </div>
          </q-td>
        </template>
        <template #body-cell-destination_country="props">
          <q-td key="destination_country" :props="props">
            <div
              class="flex items-center no-wrap"
              v-if="props.row && getCountry(props.row.receiver_country_code)"
            >
              <img
                class="orders-country_flag q-mr-sm"
                :src="
                  '/assets/img/' +
                  getCountry(props.row.receiver_country_code)?.iso.toLowerCase() +
                  '.svg'
                "
                decoding="async"
              />
              <div>
                {{ getCountry(props.row.receiver_country_code).default_name }}
              </div>
            </div>
          </q-td>
        </template>
        <template #body-cell-via="props">
          <q-td :props="props">
            <div
              v-if="
                CarrierService.getCarrierFromShippingProductCode(
                  $store.state.courierStore.carriers,
                  $store.state.courierStore.carrierProducts,
                  props.row.carrier_product_code,
                )?.carrier_id?.toLowerCase()
              "
              class="flex items-center no-wrap"
            >
              <div
                class="shipvagoo-table-carrier-logo"
                :style="{
                  backgroundImage:
                    'url(' +
                    CarrierService.getCarrierFromShippingProductCode(
                      $store.state.courierStore.carriers,
                      $store.state.courierStore.carrierProducts,
                      props.row.carrier_product_code,
                    )?.icon +
                    ')',
                }"
              />
              <div class="q-ml-sm">
                {{
                  CarrierService.getCarrierFromShippingProductCode(
                    $store.state.courierStore.carriers,
                    $store.state.courierStore.carrierProducts,
                    props.row.carrier_product_code,
                  )?.name
                }}
              </div>
            </div>
          </q-td>
        </template>
        <template #body-cell-service="props">
          <q-td :props="props">
            <div
              class="flex items-center no-wrap"
              v-if="
                CarrierService.getCarrierFromShippingProductCode(
                  $store.state.courierStore.carriers,
                  $store.state.courierStore.carrierProducts,
                  props.row?.carrier_product_code,
                )?.carrier_id?.toLowerCase()
              "
            >
              <div class="text-no-wrap q-ml-sm shipped-carrier">
                {{
                  $store.state.courierStore.carrierProducts.find(
                    (item: IShippingProduct) =>
                      item.carrier_product_code == props.row?.carrier_product_code,
                  )?.name
                }}
              </div>
            </div>
          </q-td>
        </template>
      </shipvagoo-table-v1>
    </div>
  </div>
  <!--<shipvagoo-fab icon="add" size="24px" @click="onClickCreateTemplate"/>-->

  <q-dialog v-model="isTemplateDetailOpen" @before-hide="onCloseTemplateDetail()">
    <template-modal :template="selectedTemplate" @close="onCloseTemplateDetail" />
  </q-dialog>
</template>

<script setup lang="ts">
  import dayjs from 'dayjs';
  import { QTableProps } from 'quasar';
  import { onBeforeMount, ref } from 'vue';
  import { useStore } from 'vuex';
  import * as _ from 'lodash-es';
  import { useI18n } from 'vue-i18n';
  import { ITemplate } from '../model/template.model';
  import {
    loadingService,
    loggingService,
    notificationService,
    templateService,
  } from '../services/_singletons';

  import { ICountry } from '../model/country.model';
  import TitleBar from '../components/title-bar.vue';
  import { IRootState } from '../model/store.model';
  import { Pagination } from '../model/common.model';
  import { IRequestParam } from '../model/http.model';
  import HelperService from '../services/helper.service';
  import CarrierService from '../services/carrier.service';
  import { IShippingProduct } from '../model/carrier.model';

  const $store = useStore<IRootState>();
  const { t } = useI18n();

  // Templates
  const search = ref<string>('');
  // const carriers = ref<ICarrier[]>([]);
  // const productWithClientIds = ref<string>("{}");
  const countries = ref<ICountry[]>([]);
  const columns: QTableProps['columns'] = [
    {
      name: 'id',
      align: 'left',
      label: 'ID',
      field: (row: ITemplate) => String(row.id).padStart(6, '0'),
      sortable: false,
    },
    {
      name: 'updated_at',
      align: 'left',
      label: 'Updated',
      field: '',
      sortable: false,
    },
    {
      name: 'name',
      align: 'left',
      label: 'Shipping rule',
      field: 'name',
      sortable: false,
    },
    {
      name: 'origin_country',
      align: 'left',
      label: 'Origin country',
      field: '',
      sortable: false,
    },
    {
      name: 'destination_country',
      align: 'left',
      label: 'Destination country',
      field: '',
      sortable: false,
    },
    {
      name: 'via',
      align: 'left',
      label: t('common.carrier'),
      field: 'via',
    },
    {
      name: 'service',
      align: 'left',
      label: 'Service',
      field: 'service',
    },
    {
      name: 'parcel_weight',
      align: 'left',
      label: 'Parcel weight',
      field: '',
    },
    { name: 'more', align: 'right', label: t('common.more'), field: 'more' },
  ];
  const rows = ref<ITemplate[]>([]);

  const searchVal = (value: string) => {
    search.value = value;
    getTemplates(pagination.value);
  };
  const pagination = ref<Pagination>({
    page: 1,
    rowsPerPage: 10,
    rowsNumber: 0,
  });

  const getCountry = (country_code: number): ICountry => {
    return $store.state.countryStore.countriesIndexedByCountryCode[country_code];
  };
  // Delivery Variables
  const isTemplateDetailOpen = ref<boolean>(false);
  const selectedTemplate = ref<ITemplate | null>(null);

  const onClickEditTemplate = (event: Event, row: ITemplate) => {
    if (HelperService.containsIgnoredRowElements(event)) {
      return;
    }

    selectedTemplate.value = row;
    isTemplateDetailOpen.value = true;
  };

  const getCountries = () => {
    if (countries.value.length > 0) {
      return;
    }

    countries.value = $store.state.countryStore.countries;
    // countries.value = $store.state.countryStore.countries.filter(
    //   (country) => country.iso === 'DK' || country.iso === 'SE' || country.iso === 'DE',
    // );
  };

  // const getCarriers = async (senderCountry: number, receiverCountry: number) => {
  //   try {
  //     loadingService.startLoading('main-loader:carriers');
  //     const response = await carrierService.getShippingProducts(senderCountry, receiverCountry);
  //     carriers.value = _.get(response, 'carriers', []);
  //     productWithClientIds.value = _.get(response, 'productWithClientIds', []) as any;
  //   } catch (e) {
  //     notificationService.showError('Error getting carriers');
  //     loggingService.error('Error getting carriers', e);
  //   } finally {
  //     loadingService.stopLoading('main-loader:carriers');
  //   }
  // };

  // const getAdditionalServices = async (
  //   shippingProduct: IShippingProduct,
  //   senderCountry: ICountry,
  //   receiverCountry: ICountry,
  // ) => {
  //   return shipmentService.getAdditionalServiceCharges(
  //     senderCountry.country_code,
  //     receiverCountry.country_code,
  //     shippingProduct.carrier_product_code,
  //   );
  // };

  // const onClickCreateShipment = async (row: ITemplate) => {
  //   try {
  //     loadingService.startLoading('main-loader:create-shipment-from-template');
  //
  //     await getCarriers(row.sender_country_code, row.receiver_country_code);
  //     const senderCountry = $store.state.countryStore.countries.find(
  //       (c: ICountry) => c.country_code === row.sender_country_code,
  //     );
  //     const receiverCountry = $store.state.countryStore.countries.find(
  //       (c: ICountry) => c.country_code === row.receiver_country_code,
  //     );
  //     const carrier: ICarrier | null = CarrierService.getCarrierFromShippingProductCode(
  //       $store.state.courierStore.carriers,
  //       $store.state.courierStore.carrierProducts,
  //       row.carrier_product_code,
  //     );
  //     const shippingProduct: IShippingProduct | undefined =
  //       $store.state.courierStore.carrierProducts.find(
  //         (p) => p.carrier_product_code === row.carrier_product_code,
  //       );
  //     // const weightClasses: IWeightClass[] = await shipmentService.getWeightClasses(
  //     //   row.carrier_product_code,
  //     //   row.sender_country_code,
  //     //   row.receiver_country_code,
  //     // );
  //
  //     if (!carrier || !shippingProduct || !senderCountry || !receiverCountry) {
  //       notificationService.showError('Carrier not found');
  //       return;
  //     }
  //
  //     const additionalServices = await getAdditionalServices(
  //       shippingProduct,
  //       senderCountry,
  //       receiverCountry,
  //     );
  //
  //     const additionalServicesForCreateShipmentData: { [key: number]: boolean } = {};
  //
  //     if (row.additional_services) {
  //       row.additional_services.forEach((asChargeCode) => {
  //         const additionalService = additionalServices.find(
  //           (as) => as.as_charge_code === asChargeCode,
  //         );
  //
  //         if (!additionalService) return;
  //
  //         additionalServicesForCreateShipmentData[additionalService.as_charge_code] = true;
  //       });
  //     }
  //
  //     await $store.dispatch('shipmentStore/setCreateShipmentData', {
  //       one: {
  //         senderCountry,
  //         receiverCountry,
  //         carrier,
  //         shippingProduct,
  //         shippingAgreement: 'Shipvagoo',
  //         additionalServices: additionalServicesForCreateShipmentData,
  //         parcels: ShipmentService.parcelToFormParcel(row.parcels, []), // use weight classes variable
  //       },
  //       two: {
  //         name: null,
  //         email: null,
  //         phone: null,
  //         zipCode: null,
  //         address1: null,
  //         address2: null,
  //         city: null,
  //       },
  //       three: {
  //         name: null,
  //         email: null,
  //         phone: null,
  //         zipCode: null,
  //         address1: null,
  //         address2: null,
  //         city: null,
  //       },
  //       four: {isTermsAndConditionsChecked: false, payWith: 0},
  //     } as ICreateShipmentForm);
  //
  //     await $store.dispatch('shipmentStore/openCreateShipmentModal');
  //   } catch (e) {
  //     notificationService.showError('Error creating shipment');
  //     loggingService.error('Error creating shipment', e);
  //   } finally {
  //     loadingService.stopLoading('main-loader:create-shipment-from-template');
  //   }
  // };

  const onClickCreateTemplate = () => {
    isTemplateDetailOpen.value = true;
  };

  const getTemplates = async (tablePagination: Pagination | null) => {
    try {
      loadingService.startLoading('main-loader:get-templates');
      const page: number = tablePagination?.page || 1;
      const limit: number = tablePagination?.rowsPerPage || 10;

      const params: IRequestParam[] = [];

      if (search.value && search.value !== '') {
        params.push({ key: 'conditions[id:or_like]', value: search.value });
        params.push({ key: 'conditions[name:or_like]', value: search.value });
      }
      params.push({ key: 'with', value: 'senderCountry' });
      if (tablePagination?.sortBy) {
        const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
        params.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
      } else {
        params.push({ key: 'sorting[created_at]', value: 'desc' });
        params.push({ key: 'sorting[id]', value: 'desc' });
      }

      const result = await templateService.getTemplates(page, limit, params);
      rows.value = result.entities;
      pagination.value.rowsNumber = result.total;
      pagination.value.page = page;
      pagination.value.rowsPerPage = limit;
      pagination.value.sortBy = tablePagination?.sortBy;
      pagination.value.descending = tablePagination?.descending;
    } catch (e) {
      loggingService.error('Error getting templates', e);
      notificationService.showError('Error getting templates');
    } finally {
      loadingService.stopLoading('main-loader:get-templates');
    }
  };

  const onCloseTemplateDetail = (is?: 'create' | 'edit') => {
    isTemplateDetailOpen.value = false;
    selectedTemplate.value = null;

    if (is === 'create') {
      getTemplates(null);
    }

    if (is === 'edit') {
      getTemplates(pagination.value);
    }
  };

  const onClickDeleteTemplate = async (row: ITemplate) => {
    try {
      loadingService.startLoading('main-loader:delete-template');
      await templateService.deleteTemplate(row.template_code);
      notificationService.showSuccess(`Template ${row.name} deleted`);
      await getTemplates(pagination.value);
    } catch (e) {
      loggingService.error('Error deleting template', e);
      notificationService.showError('Error deleting template');
    } finally {
      loadingService.stopLoading('main-loader:delete-template');
    }
  };

  onBeforeMount(() => {
    getTemplates(null);
    getCountries();
  });
</script>

<style scoped lang="scss">
  .templates_page {
    padding: 0 40px 10px 40px;

    @media (max-width: 1024px) {
      padding: 0 20px 10px 20px;
    }
  }
</style>
