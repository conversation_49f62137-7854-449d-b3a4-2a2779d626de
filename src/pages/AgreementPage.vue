<template>
  <div class="flex justify-between items-center title-bar-heading">
    <title-bar style="width: 100%;" :title="$t('common.agreement')"/>
  </div>
  <div class="prices">
    <div class="customer-agreement-dialog position-relative">
      <section id="customer-invoice" ref="DownloadComp">
        <!--<div class="logo-wrapper logo" style="text-align: right">
          <img width="190" :src="Logo" class="logo" alt="logo"/>
        </div>-->
        <div class="user-info">
          <h3 class="customer-name m-0 text-black" style="margin: 10px 10px">{{ company_name }}</h3>
          <!-- <p class="m-0 text-black" style="margin: 5px 0">{{ address }}</p>
           <p class="text-right date m-0 text-black" style="text-align: right">
             <strong>Date:</strong> <span v-if="merchant_agreement">{{ merchant_agreement.date }}</span>
           </p>-->
        </div>
        <!-- <div class="application">
           <p class="text-black">
             Dear, <strong v-if="agreement">{{ agreement.name }}</strong>
           </p>
           <div v-if="merchant_agreement" class="application-description m-0 text-black" style="margin-left: 8px;">
             <div v-html="merchant_agreement.summary"></div>
           </div>
           <p></p>
           <p></p>
           <p></p>
         </div>-->
        <div class="table mt-10">
          <table style="width: 100%; border-collapse: collapse">
            <thead style="border-bottom: 2px solid #8b8b8b">
            <tr>
              <td class="text-black" style="padding-bottom: 5px">
                <strong style="padding-left: 10px"> Description </strong>
              </td>
              <td class="text-black" style=" width: 160px; padding-bottom: 5px;text-align: right">
                <strong style="padding-right: 10px"> Price excl. VAT </strong>
              </td>
            </tr>
            </thead>
            <tbody style="border-bottom: 2px solid #8b8b8b">
            <tr v-for="(data, index) in printData" :key="index">
              <td style="
                                                    padding-left: 10px;   margin: 0;
                                                    margin-top: 4px;
                                                    margin-bottom: 4px;
                                                    color: #000;white-space: nowrap;"
                  :style="`${index === 0 ? 'margin-top: 20px' : ''}`"
              >
                {{ data.desc }}
              </td>
              <td
                :style="`${index === 0 ? 'margin-top: 10px' : ''}`"
                style=" padding-right: 10px;
                margin: 0; margin-top: 4px;
                 margin-bottom: 4px; color: #000;text-align: right;white-space: nowrap">
                {{ danishNumberFormat(data.price) }} DKK
              </td>
            </tr>
            </tbody>
          </table>
        </div>
        <div class="invoice-footer">
          <div class="footer-info">
            <div v-if="merchant_agreement" style="text-align: center">
              <div v-html="merchant_agreement.footer" style="color: #444444;">
              </div>
            </div>
          </div>
        </div>
      </section>
      <div class="text-center">
        <q-btn color="primary" @click="printDownload" label="Download PDF"></q-btn>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onBeforeMount, ref, watch} from 'vue';
import {
  agreementService, countryService,
  loadingService,
  loggingService,
  notificationService,
} from '../services/_singletons';
import {useStore} from '../store';
import {ICountry} from "~/model/country.model";
import {euMember} from "is-european";
import {danishNumberFormat} from "../helpers";
//import Logo from '../assets/img/log_dark_text.png';

const $store = useStore();
const agreement = ref<any>([]);
const merchant_agreement = ref<any>();
const printData = ref<any[]>([]);
const countries = ref<ICountry[]>([]);
const company_name = ref<string>('');
const address = ref<string>('');
const DownloadComp = ref<HTMLElement>();


const printDownload = () => {
  const data = DownloadComp.value;
  let w = window.open()
  w!.document.write("<html><head><style>")
  w!.document.write('@import url("https://fonts.googleapis.com/css2?family=Nunito+Sans&display=swap");')
  w!.document.write(
    '@font-face { font-family: "MyCustomFont"; src: url(../../../../../assets/fonts/NunitoSans-ExtraLightItalic.woff2) format("woff2"); }'
  )
  w!.document.write('body { font-family: "Nunito Sans", sans-serif !important; }')
  w!.document.write("</style></head><body>")
  const el: any = data?.innerHTML;
  w!.document.write(el)
  w!.document.write("</body></html>")
  w!.document.close()
  w!.setTimeout(function () {
    w!.print()
  }, 200)
}
const getAgreementData = async () => {
  try {
    loadingService.startLoading('main-loader:get-price-list');
    agreement.value = await agreementService.getAgreement(($store.state.userStore.user_code).toString());
    await getInvoiceData()
  } catch (e) {
    loggingService.error('Error while getting agreement', e);
    notificationService.showError('Error while getting agreement');
  } finally {
    loadingService.stopLoading('main-loader:get-price-list');
  }
};
const getCountryByIso = (country: any) => {
  let found_country = countries.value.find(el => el.name.toLowerCase() == country.toLowerCase())
  if (found_country) {
    return found_country.iso
  } else {
    return false
  }
}
const getInvoiceData = async () => {
  printData.value = []
  merchant_agreement.value = agreement.value?.merchant_agreement[0];
  let pricing = [...merchant_agreement.value.pricing]
  pricing.forEach((res, idx) => {
    let toCountry = getCountryByIso(res.shipvagoo_pricing.ubsend_price.to_country)
    if (toCountry) {
      const price = Number(res.shipvagoo_pricing.ship_standard_list_price - res.discount).toFixed(2);
      const obj = {
        desc: `DK >${toCountry} - ${res.price_group.carrier} - ${res.shipvagoo_pricing.ubsend_price.product}  ${res.shipvagoo_pricing.ubsend_price.weight_class_from} - ${res.shipvagoo_pricing.ubsend_price.weight_class_to} kg`,
        price: price
      };
      printData.value.push(obj as any)
    }
  })
}
const getCountries = async () => {
  try {
    loadingService.startLoading('main-loader:get-countries');
    const allCountries = await countryService.getCountries();
    countries.value = allCountries.filter((c: ICountry) => euMember(c.iso));
  } catch (e) {
    notificationService.showError('Error getting countries');
    loggingService.error('Error while getting countries', e);
  } finally {
    loadingService.stopLoading('main-loader:get-countries');
  }
};
watch(() => $store.state.companyStore.company_name, (newVal: string, oldVal) => {
  company_name.value = newVal
})
onBeforeMount(async () => {
  company_name.value = $store.state.companyStore.company_name
  address.value = $store.state.companyStore.address
  await getCountries();
  await getAgreementData();
});
</script>
<style scoped lang="scss">
.prices {
  padding: 20px 40px;

  @media (max-width: 1024px) {
    padding: 20px 10px;
  }

  .selects {
    display: flex;
    flex-direction: row;
    gap: 40px;
    margin-top: 20px;

    & > * {
      flex: 1;
    }

    @media (max-width: 1024px) {
      flex-direction: column;
      gap: 10px;
      margin-top: 10px;
    }
  }

  .carriers {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-top: 30px;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 10px;
    }

    .carriers_carrier {
      border: 1px solid #2929c8;
      border-radius: 5px;
    }

    h2.carrier_name {
      margin: 0;
      margin: 10px 10px 6px 10px;
      font-size: 20px;
      font-variation-settings: 'wght' 500;
      line-height: 25px;
    }

    .carrier_product {
      &:not(:first-of-type) {
        margin-top: 23px;
      }

      p.carrier_product_name {
        margin: 0 10px;
        font-size: 15px;
        line-height: 20px;
      }

      table {
        width: 100%;
        margin-top: 6px;
        border-collapse: collapse;
      }

      table th {
        padding: 12px 10px;
        font-size: 15px;
        font-variation-settings: 'wght' 700;
        line-height: 25px;
        color: #e1e1e1;
      }

      table tbody tr {
        border: solid;
        border-color: #e1e1e1;
        border-width: 1px 0;
      }

      table tbody td {
        padding: 12px 10px;
        font-size: 15px;
        font-variation-settings: 'wght' 500;
        line-height: 25px;
        color: #e1e1e1;
      }
    }
  }
}
</style>
