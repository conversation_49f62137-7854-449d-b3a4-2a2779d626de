<template>
  <div class="flex justify-between items-center title-bar-heading q-mt-md">
    <title-bar
      style="color: #212121"
      title="Order Invoice"
      subtitle="Sales invoices and credit notes for connected webshop orders"
    />
    <div class="flex" v-if="rows.length">
      <big-search
        placeholder="Search"
        v-model="search"
        class="sales_invoices_page-actions-big_search q-mr-sm"
      />
      <div>
        <shipvagoo-button no-caps split label="Download" @click="onClickExportBulk">
        </shipvagoo-button>
      </div>
    </div>
  </div>
  <div class="sales_invoices_page q-mt-xs" v-if="!rows.length">
    <shipvagoo-placeholder
      text="No orders invoices or credit notes are available"
    ></shipvagoo-placeholder>
  </div>
  <div class="sales_invoices_page" v-if="rows.length">
    <div class="sales_invoices_page-table">
      <shipvagoo-table-v1
        v-model:selected="selectedRows"
        v-model:pagination="pagination"
        selection="multiple"
        :rows="rows"
        :columns="columns"
        :loading="loadingService.loadings.has('main-loader:get-sales-invoices')"
        :total="pagination.rowsNumber"
        @request="getSalesInvoices($event.pagination)"
        @row-click="onClickDownloadSalesInvoice"
        class="sales-invoice-page"
      >
        <template #header-selection="scope">
          <q-td>
            <shipvagoo-checkbox v-model="scope.selected" size="md" />
          </q-td>
        </template>
        <template #body-selection="scope">
          <q-td @click.stop="handleCellClick($event, scope.row)">
            <shipvagoo-checkbox v-model="scope.selected" size="md" @click.stop />
          </q-td>
        </template>

        <template #body-cell-updated_at="props">
          <q-td key="updated_at" :props="props">
            {{ dayjs(props.row.updated_at || props.row.created_at).format('DD/MM/YYYY') }}<br />
            {{ dayjs(props.row.updated_at || props.row.created_at).format('HH:mm') }}
          </q-td>
        </template>
        <template #body-cell-name="props">
          <q-td key="name" :props="props">
            <div v-if="props.row.customer_info">
              {{ props.row.customer_info?.first_name + ' ' + props.row.customer_info?.last_name
              }}<br />
              <span class="text-no-wrap">{{ props.row.customer_info?.address1 }}</span
              ><br />
              <span class="text-no-wrap"
                >{{ _.get(props.row.customer_info, 'country_code', '') }}-{{
                  _.get(props.row.customer_info, 'zip', '')
                }}
                {{ _.get(props.row.customer_info, 'city', '') }}
              </span>
            </div>
          </q-td>
        </template>

        <!-- <template #body-cell-amount_incl_vat="props">
          <q-td key="amount_incl_vat" class="order-total" :props="props">
            <div>
              {{ showTotalAmountInclVat(props.row, 'storeMoney') }}
            </div>
            <shipvagoo-tooltip :offset="[10, -50]">
              {{ showTotalAmountInclVat(props.row, 'localMoney') }}
            </shipvagoo-tooltip>
          </q-td>
        </template> -->

        <template #body-cell-more="props">
          <q-td :props="props" class="more">
            <q-btn
              dense
              flat
              round
              style="color: #1f2124"
              class="more-action"
              field="edit"
              icon="more_horiz"
            >
              <shipvagoo-menu>
                <shipvagoo-menu-item
                  clickable
                  @click="onClickDownloadSalesInvoice(null, props.row)"
                >
                  {{ $t('common.download') }}
                </shipvagoo-menu-item>
              </shipvagoo-menu>
            </q-btn>
          </q-td>
        </template>
      </shipvagoo-table-v1>
    </div>
    <!-- <div>Testing</div> -->
  </div>
</template>

<script setup lang="ts">
  import { onBeforeMount, Ref, ref } from 'vue';
  import { QTableProps } from 'quasar';
  import dayjs from 'dayjs';
  import * as _ from 'lodash-es';
  import { useI18n } from 'vue-i18n';
  import JSZip from 'jszip';
  import {
    loadingService,
    loggingService,
    notificationService,
    salesInvoiceService,
  } from '../services/_singletons';
  import HelperService from '../services/helper.service';
  import { ISalesInvoice } from '../model/sales-invoice.model';
  import { Pagination } from '../model/common.model';
  import { IRequestParam } from '../model/http.model';
  import PriceService from '../services/price.service';
  import { useHelper } from '../composeable/Helper';

  const { showPdf } = useHelper();
  const { t } = useI18n();
  const selectedRows = ref<ISalesInvoice[]>([]);
  const getTypeColumnText = (type: 'INVOICE' | 'CREDIT_NOTE') => {
    switch (type) {
      case 'INVOICE':
        return t('common.invoice');
      case 'CREDIT_NOTE':
        return t('common.credit_note');
      default:
        return '';
    }
  };

  const columns: QTableProps['columns'] = [
    {
      name: 'id',
      label: t('common.number'),
      field: (row: ISalesInvoice) => HelperService.formatId(row.id),
      align: 'left',
      sortable: false,
    },
    {
      name: 'updated_at',
      align: 'left',
      label: 'Date',
      field: '',
    },
    {
      name: 'invoice_type',
      label: t('common.type'),
      field: (row: ISalesInvoice) => getTypeColumnText(row.invoice_type),
      align: 'left',
      sortable: false,
    },
    {
      name: 'order_number',
      label: 'Order',
      field: (row: ISalesInvoice) => row?.order?.name ?? row?.order?.order_number ?? '',
      align: 'left',
      sortable: false,
    },

    {
      name: 'name',
      label: 'Customer name',
      field: '',
      align: 'left',
      sortable: false,
    },
    {
      name: 'amount_incl_vat',
      label: 'Amount',
      /* field: (row: ISalesInvoice) =>
      PriceService.formatPrice(
        _.sumBy(row.invoice_items, (item) => parseFloat(item.amount) * item.count) + (parseFloat(row?.order?.tax_lines[0]?.price_set?.shop_money?.amount ?? 0.00)) + (parseFloat((row?.order?.shipping_lines[0]?.price_set?.shop_money.amount ?? row?.order?.shipping_lines[0]?.price_set?.presentment_money.amount) ?? 0.00)),
      ),
      */
      field: (row: ISalesInvoice) => {
        if (row.total_amount != null && row.total_amount != undefined) {
          let currency = row.order.total_price_set.presentment_money.currency_code;
          return PriceService.formatInternationalPrice({
            currency: currency,
            amount: row.total_amount,
          });
        } else {
          let totalFulfillments = 0;
          let itemsCount = 0;
          row.order.line_items.forEach((item) => {
            totalFulfillments += item.quantity;
          });

          row.invoice_items.forEach((item) => {
            itemsCount += item.count;
          });

          const taxable = row.order.taxes_included;
          // const amountTotal = (_.sumBy(row.invoice_items, (item) => parseFloat(item.amount) * item.count));
          let amountTotal = 0;
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          let amountWithTax = 0;

          row.invoice_items.forEach((item) => {
            let itemAmount = 0;

            if (item.price_set) {
              if (item?.price_set?.shop_money.currency_code == 'DKK') {
                itemAmount = parseFloat(item?.price_set?.shop_money.amount);
              } else if (item?.price_set?.presentment_money.currency_code == 'DKK') {
                itemAmount = parseFloat(item?.price_set?.presentment_money.amount);
              } else {
                itemAmount = parseFloat(item?.price_set?.shop_money.amount);
              }
            } else {
              itemAmount = parseFloat(item?.amount);
            }

            if (item.vat_rate !== 0) {
              amountWithTax += item.count * itemAmount;
            }
            amountTotal += item.count * itemAmount;
          });

          let taxAmount = 0;
          let totalTaxAmount = 0;
          // let amount_incl_vat = 0;

          const deliveryAmount = parseFloat(
            row?.order?.shipping_lines[0]?.price_set?.shop_money.amount ??
              row?.order?.shipping_lines[0]?.price_set?.presentment_money.amount ??
              0.0,
          );

          let delivery = (deliveryAmount / totalFulfillments) * itemsCount;

          if (row.invoice_type === 'CREDIT_NOTE') {
            //
            if (row.shipping_lines) {
              if (row.shipping_lines.price) {
                delivery = parseFloat(row.shipping_lines.price);
              }
            }
          }

          if (!taxable) {
            taxAmount = amountWithTax * ((row.tax * 100 + 100) / 100);
            let deliveryTax = delivery * ((row.tax * 100 + 100) / 100);
            deliveryTax -= delivery;
            taxAmount -= amountWithTax;
            totalTaxAmount = deliveryTax + taxAmount;
          }

          return PriceService.formatPrice(amountTotal + delivery + totalTaxAmount);
        }
      },
      align: 'right',
      sortable: false,
    },

    { name: 'more', label: 'Action', field: 'more', sortable: false },
  ];
  const search: Ref<string> = ref<string>('');
  const rows = ref<ISalesInvoice[]>([]);
  const pagination: Ref<Pagination> = ref({
    page: 1,
    rowsPerPage: 10,
    rowsNumber: 0,
    descending: true,
  });

  const handleCellClick = (event: Event, row: ISalesInvoice) => {
    const index = selectedRows.value.findIndex((selectedRow) => selectedRow === row);
    if (index === -1) {
      selectedRows.value.push(row);
    } else {
      selectedRows.value.splice(index, 1);
    }
    event.stopPropagation();
  };

  // const showTotalAmountInclVat = (data: any, type: any) => {
  //   if (type == 'localMoney' && data.local_total_amount) {
  //     return PriceService.formatInternationalPrice({
  //       currency: data.order.total_price_set.presentment_money.currency_code,
  //       amount: data.local_total_amount
  //     })
  //   }

  //   return PriceService.formatInternationalPrice({
  //     currency: data.order.total_price_set.shop_money.currency_code,
  //     amount: data.total_amount
  //   })
  // }

  const getSalesInvoices = async (tablePagination: Pagination | null) => {
    try {
      loadingService.startLoading('main-loader:get-sales-invoices');

      const page = tablePagination?.page ?? 1;
      const limit = tablePagination?.rowsPerPage ?? 10;

      const params: IRequestParam[] = [];

      /* if (search.value && search.value !== '') {
      params.push({key: 'conditions[invoice_code:like]', value: search.value});
    } */

      if (tablePagination?.sortBy) {
        const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
        params.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
      } else {
        params.push({ key: 'sorting[created_at]', value: 'desc' });
      }

      const salesInvoices = await salesInvoiceService.getSalesInvoices(page, limit, params);

      rows.value = salesInvoices.entities;
      pagination.value.rowsNumber = salesInvoices.total;
      pagination.value.page = page;
      pagination.value.rowsPerPage = limit;
      pagination.value.descending = tablePagination?.descending;
      pagination.value.sortBy = tablePagination?.sortBy;
    } catch (e) {
      loggingService.error('Error loading sales invoices', e);
      notificationService.showError('Error loading sales invoices');
    } finally {
      loadingService.stopLoading('main-loader:get-sales-invoices');
    }
  };

  const onClickExportBulk = async () => {
    if (!selectedRows.value.length) {
      notificationService.showError('No row is selected.');
    }
    try {
      loadingService.startLoading('main-loader:print-sales-invoice-receipt');
      const invoices: ISalesInvoice[] = selectedRows.value;
      const zip = new JSZip();
      for (const invoice of invoices) {
        const response = await salesInvoiceService.downloadSalesInvoicePdf(
          invoice.sales_invoice_code,
          invoice.invoice_type,
        );
        const blob: Blob = await HelperService.toBlob(
          `data:application/pdf;base64,${encodeURI(response.file)}`,
        );
        zip.file(`${invoice.sales_invoice_code}.pdf`, blob);
      }
      if (zip.file(/pdf/).length > 0) {
        zip.generateAsync({ type: 'blob' }).then((content) => {
          const link = document.createElement('a');
          link.href = URL.createObjectURL(content);
          link.download = 'order-invoices.zip';
          link.click();
        });
      }
    } catch (error) {
      loggingService.error('Error while exporting sales invoices', error);
      notificationService.showError('Error while exporting sales invoices');
    } finally {
      loadingService.stopLoading('main-loader:print-sales-invoice-receipt');
    }
  };
  const onClickDownloadSalesInvoice = async (event: Event | null, row: ISalesInvoice) => {
    if (event && HelperService.containsIgnoredRowElements(event)) {
      return;
    }
    const salesInvoiceCode: number = row.sales_invoice_code;
    if (!salesInvoiceCode) {
      return;
    }
    console.log('Sales invoice ', row);
    try {
      loadingService.startLoading('main-loader:print-sales-invoice-receipt');
      const response = await salesInvoiceService.downloadSalesInvoicePdf(
        salesInvoiceCode,
        row.invoice_type,
      );
      await showPdf(response.file);
    } catch (error) {
      loggingService.error('Error while downloading PDF', error);
      notificationService.showError('Error while downloading PDF');
    } finally {
      loadingService.stopLoading('main-loader:print-sales-invoice-receipt');
    }
  };

  onBeforeMount(() => {
    getSalesInvoices(null);
  });
</script>
<style scoped lang="scss">
  .sales_invoices_page {
    padding: 0 40px 10px 40px;

    @media (max-width: 1024px) {
      padding: 0 20px 10px 20px;
    }

    &-actions {
      display: flex;
      flex-direction: row;

      & > *:not(:first-child) {
        margin-left: 10px;

        @media (max-width: 600px) {
          margin-left: unset;
        }
      }

      .big_search-rows_per_page-group {
        display: flex;
        flex: 1;
        flex-direction: row;

        & > *:not(:first-child) {
          margin-left: 10px;
        }

        .rows_per_page {
          display: none;

          @media (max-width: $tablet) {
            display: block;
          }
        }
      }

      &-big_search {
        flex: 1;
        width: 300px;
      }

      @media (max-width: 600px) {
        flex-direction: column;

        &-big_search {
          margin-bottom: 10px;
        }
      }
    }
  }
</style>
<style>
  .sales-invoice-page th:nth-child(1),
  .sales-invoice-page td:nth-child(1) {
    width: 3%;
  }

  .sales-invoice-page th:nth-child(2),
  .sales-invoice-page td:nth-child(2) {
    width: 13%;
  }

  .sales-invoice-page th:nth-child(3),
  .sales-invoice-page td:nth-child(3) {
    width: 13%;
  }

  .sales-invoice-page th:nth-child(4),
  .sales-invoice-page td:nth-child(4) {
    width: 13%;
  }

  .sales-invoice-page th:nth-child(5),
  .sales-invoice-page td:nth-child(5) {
    width: 13%;
  }

  .sales-invoice-page th:nth-child(6),
  .sales-invoice-page td:nth-child(6) {
    width: 15%;
  }

  .sales-invoice-page th:nth-child(7),
  .sales-invoice-page td:nth-child(7) {
    width: 15%;
  }

  .sales-invoice-page th:nth-child(8),
  .sales-invoice-page td:nth-child(8) {
    width: 15%;
    padding-right: 25px;
  }
</style>
