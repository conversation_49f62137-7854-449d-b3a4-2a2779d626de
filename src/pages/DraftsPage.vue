<template>
  <div class="flex justify-between items-center title-bar-heading q-mt-md">
    <div>
      <title-bar
        style="width: 100%"
        title="Draft"
        subtitle="An overview of all incompleted shipments"
      />
    </div>
    <big-search
      placeholder="Search"
      @update:modelValue="searchVal"
      v-model="search"
      class="big_search"
    />
  </div>
  <div class="drafts_page q-mt-xs" v-if="!rows.length && !draftsTableLoading">
    <shipvagoo-placeholder text="No drafts are available"> </shipvagoo-placeholder>
  </div>
  <div class="drafts_page" v-if="rows.length || draftsTableLoading">
    <!--    <info-bar class="info" dense>
          Need help with integration setup? Click
          <shipvagoo-hyperlink to="#" type="external-link" label="here" />
        </info-bar>

    <div class="drafts_page-actions">
       <shipvagoo-button-dropdown
        outline
        no-caps
        split
        label="Create Shipment"
        @click="openCreateShipment"
      >
        <shipvagoo-list>
          <shipvagoo-menu-item v-close-popup clickable @click="onClickDeleteDrafts">
            Delete drafts
          </shipvagoo-menu-item>
        </shipvagoo-list>
      </shipvagoo-button-dropdown> 
    </div>-->

    <shipvagoo-table-v1
      v-model:selected="selectedRows"
      v-model:pagination="pagination"
      :columns="columns"
      :rows="rows"
      :total="pagination.rowsNumber"
      row-key="shipment_code"
      :loading="draftsTableLoading"
      @row-click="openDraftDetail"
      @request="getDrafts($event.pagination)"
      class="drafts-page-table"
    >
      <template #body-cell-country="props">
        <q-td :props="props">
          <div v-if="props" class="drafts_page-country_img">
            <!--            <shipvagoo-country-icon
                          v-if="props.row.recipient.country"
                          :country="recipientCountry(props.row.recipient.country)"
                        />-->
            <!--            <img
                         v-if="props.row.recipient.country"
                         decoding="async"
                         style="width: 20px; height: 20px; border-radius: 50%; object-fit: cover"
                         :src="'/assets/img/dk.svg'"
                       />-->
            <img
              v-if="props.row.recipient.country"
              decoding="async"
              :src="`/assets/img/${props.row.recipient?.country?.toLowerCase?.() ?? ''}.svg`"
              style="width: 20px; height: 20px; border-radius: 50%; object-fit: cover"
            />

            {{ props.row.recipient.city }}
          </div>
        </q-td>
      </template>
      <template #body-cell-updated_at="props">
        <q-td :props="props">
          {{ dayjs(String(props.row.updated_at)).format('DD/MM/YYYY') }}<br />
          {{ dayjs(String(props.row.updated_at)).format('HH:mm') }}
        </q-td>
      </template>
      <template #body-cell-source="props">
        <q-td :props="props">
          <span class="text-no-wrap"
            >{{ props.row.sender.firstName }} {{ props.row.sender.lastName }}</span
          ><br />
          <span class="text-no-wrap">{{ props.row.sender.addressLine1 }}</span
          ><br />
          <span class="text-no-wrap"
            >{{ props.row.sender.country }}-{{ props.row.sender.postCode }}
            {{ props.row.sender.city }}</span
          >
        </q-td>
      </template>
      <template #body-cell-recipient="props">
        <q-td :props="props">
          <span class="text-no-wrap"
            >{{ props.row.recipient.firstName }} {{ props.row.recipient.lastName }}</span
          ><br />
          <span class="text-no-wrap">{{ props.row.recipient.addressLine1 }}</span
          ><br />
          <span class="text-no-wrap"
            >{{ props.row.recipient.country }}-{{ props.row.recipient.postCode }}
            {{ props.row.recipient.city }}</span
          >
        </q-td>
      </template>

      <template #body-cell-via="props">
        <q-td :props="props">
          <div v-if="props">
            <div
              class="flex items-center no-wrap"
              v-if="
                CarrierService.getCarrierFromShippingProductCode(
                  $store.state.courierStore.carriers,
                  $store.state.courierStore.carrierProducts,
                  props.row.carrier_product_code,
                )?.carrier_id?.toLowerCase()
              "
            >
              <div
                class="shipvagoo-table-carrier-logo"
                :style="{
                  backgroundImage:
                    'url(' +
                    CarrierService.getCarrierFromShippingProductCode(
                      $store.state.courierStore.carriers,
                      $store.state.courierStore.carrierProducts,
                      props.row.carrier_product_code,
                    )?.icon +
                    ')',
                }"
              />
              <div class="text-no-wrap q-ml-sm shipped-carrier">
                {{
                  CarrierService.getCarrierFromShippingProductCode(
                    $store.state.courierStore.carriers,
                    $store.state.courierStore.carrierProducts,
                    props.row.carrier_product_code,
                  )?.name
                }}
              </div>
            </div>
          </div>
        </q-td>
      </template>
      <template #body-cell-service="props">
        <q-td :props="props">
          <div
            v-if="
              CarrierService.getCarrierFromShippingProductCode(
                $store.state.courierStore.carriers,
                $store.state.courierStore.carrierProducts,
                props.row.carrier_product_code,
              )?.carrier_id?.toLowerCase()
            "
            class="flex items-center no-wrap"
          >
            <div class="text-no-wrap q-ml-sm">
              {{
                $store.state.courierStore.carrierProducts.find(
                  (item: IShippingProduct) =>
                    item.carrier_product_code == props.row.carrier_product_code,
                )?.name
              }}
            </div>
          </div>
        </q-td>
      </template>
      <template #body-cell-status="props">
        <q-td :props="props">
          <shipvagoo-status-tag
            bg-color="#F2E1FF"
            color="#702FA0"
            text="Draft"
          ></shipvagoo-status-tag>
        </q-td>
      </template>
      <!--<template #body-cell-label="props">
        <q-td :props="props">
          <q-btn
            dense
            flat
            round
            style="color:#1f2124;"
            class="more-action"
            field="edit"
            icon="add"
            @click="onClickGenerateShipment(props.row.shipment_code)"
          />
        </q-td>
      </template>-->
      <template #body-cell-more="props">
        <q-td :props="props" class="more">
          <q-btn
            dense
            flat
            round
            style="color: #1f2124"
            class="more-action"
            field="edit"
            icon="more_horiz"
          >
            <shipvagoo-menu style="width: 200px">
              <shipvagoo-menu-item clickable @click="onClickDeleteShipment(props.row)">
                Delete draft
              </shipvagoo-menu-item>
              <shipvagoo-separator />
              <shipvagoo-menu-item clickable @click="onClickContinueFromDraft(props.row)">
                {{ $t('drafts_page.continue_from_draft') }}
              </shipvagoo-menu-item>
            </shipvagoo-menu>
          </q-btn>
        </q-td>
      </template>
    </shipvagoo-table-v1>
  </div>

  <!--  <q-dialog v-if="clickedRow" v-model="isDraftDetailOpen">
      <shipment-detail :shipment="clickedRow" is-draft/>
    </q-dialog>-->
</template>

<script setup lang="ts">
  import { onBeforeMount, onBeforeUnmount, Ref, ref } from 'vue';
  import dayjs from 'dayjs';
  import { QTableProps } from 'quasar';
  import * as _ from 'lodash-es';
  import { useI18n } from 'vue-i18n';
  import { useHelper } from '../composeable/Helper';
  import {
    carrierService,
    loadingService,
    loggingService,
    notificationService,
    shipmentService,
    templateService,
  } from '../services/_singletons';
  import { useStore } from '../store';
  import { ICreateNewShipmentForm, IParcelShop, IShipment } from '../model/shipment.model';
  import { Pagination } from '../model/common.model';
  import HelperService from '../services/helper.service';
  import { IRequestParam } from '../model/http.model';
  //import WindowService from '../services/window.service';
  import { ICountry } from '../model/country.model';
  import { ICarrier, IShippingProduct, IWeightClass } from '../model/carrier.model';
  import CarrierService from '../services/carrier.service';

  /*const isDraftDetailOpen = ref<boolean>(false);
const clickedRow = ref<IShipment | null>(null);*/
  const $store = useStore();
  const search = ref<string>('');
  const selectedRows = ref<IShipment[]>([]);
  const subscriptions: (() => void)[] = [];
  // let windowRef: Window | null = null;
  const { t } = useI18n();
  const { extractMinMaxWeight } = useHelper();
  // get services

  const draftsTableLoading = ref<boolean>(false);
  const columns: QTableProps['columns'] = [
    {
      name: 'shipment_code',
      align: 'left',
      label: 'Shipment ID',
      field: 'shipment_code',
      sortable: false,
    },
    {
      name: 'updated_at',
      align: 'left',
      label: 'Date',
      field: '',
      sortable: false,
    },
    {
      name: 'source',
      align: 'left',
      label: t('common.sender'),
      field: '',
      sortable: false,
    },
    {
      name: 'recipient',
      required: true,
      label: 'Recipient',
      align: 'left',
      field: '',
      sortable: false,
    },
    {
      name: 'via',
      align: 'left',
      label: t('common.carrier'),
      field: 'via',
      sortable: false,
    },
    {
      name: 'service',
      align: 'left',
      label: 'Service',
      field: 'service',
      sortable: false,
    },
    {
      name: 'status',
      align: 'left',
      label: 'Status',
      field: 'status',
      sortable: false,
    },
    /*{
    name: 'country',
    align: 'left',
    label: `${t('common.country')} / ${t('common.city')}`,
    field: (row: IShipment) => `${row.recipient.country} / ${row.recipient.city}`,
    sortable: false,
  },*/
    //  {name: 'label', align: 'center', label: t('common.label'), field: 'label'},
    {
      name: 'more',
      align: 'center',
      label: 'Action',
      field: 'more',
    },
  ];

  const rows = ref<IShipment[]>([]);
  const carriers = ref<ICarrier[]>([]);
  const productWithClientIds = ref<string>('{}');
  const pagination: Ref<Pagination> = ref({
    page: 1,
    rowsPerPage: 10,
    rowsNumber: 0,
    descending: true,
  });

  /*const recipientCountry = (countryISO: string) => {
  return $store.state.countryStore.countries.find((country) => country.iso == countryISO);
}*/
  const openDraftDetail = (event: Event, row: IShipment) => {
    if (HelperService.containsIgnoredRowElements(event)) {
      return;
    }
    onClickContinueFromDraft(row);
    /* clickedRow.value = row;
   isDraftDetailOpen.value = true;*/
  };
  const searchVal = (value: string) => {
    search.value = value;
    getDrafts(pagination.value);
  };

  const getDrafts = async (tablePagination: Pagination | null) => {
    try {
      // loadingService.startLoading('main-loader:drafts');
      draftsTableLoading.value = true;
      const page: number = tablePagination?.page || 1;
      const limit: number = tablePagination?.rowsPerPage || 10;

      const params: IRequestParam[] = [];

      if (search.value && search.value !== '') {
        params.push({ key: 'name[filter]', value: search.value });
        /* params.push(
         {key: 'conditions[name:like]', value: search.value},
         {key: 'conditions[email:or_like]', value: search.value},
         {key: 'conditions[address:or_like]', value: search.value},
         {key: 'conditions[address2:or_like]', value: search.value},
         {key: 'conditions[zipcode:or_like]', value: search.value},
         {key: 'conditions[city:or_like]', value: search.value},
         {key: 'conditions[mobile:or_like]', value: search.value},
       );*/
      }

      if (tablePagination?.sortBy) {
        const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
        params.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
      } else {
        params.push({ key: 'sorting[created_at]', value: 'desc' });
      }

      const response = await shipmentService.getShipmentDrafts(page, limit, params);
      rows.value = response.entities;
      pagination.value.rowsNumber = response.total;
      pagination.value.page = page;
      pagination.value.rowsPerPage = limit;
      pagination.value.sortBy = tablePagination?.sortBy;
      pagination.value.descending = tablePagination?.descending;
    } catch (e) {
      notificationService.showError('Error getting drafts');
      loggingService.error('Error getting drafts', e);
    } finally {
      // loadingService.stopLoading('main-loader:drafts');
      draftsTableLoading.value = false;
    }
  };

  const onClickDeleteShipment = async (row: IShipment) => {
    try {
      loadingService.startLoading('main-loader:deleting-drafts');
      await shipmentService.deleteShipment(row.shipment_code);

      const page = pagination.value.page ?? 1;
      if (rows.value.length === 1 && page > 1) {
        pagination.value.page = page - 1;
      }

      getDrafts(pagination.value);
    } catch (e) {
      notificationService.showError('Error deleting drafts');
      loggingService.error('Error deleting drafts', e);
    } finally {
      loadingService.stopLoading('main-loader:deleting-drafts');
    }
  };

  const getCarriers = async (senderCountry: number, receiverCountry: number) => {
    try {
      loadingService.startLoading('main-loader:carriers');
      const response = await carrierService.getShippingProducts(senderCountry, receiverCountry);
      carriers.value = _.get(response, 'carriers', []);
      productWithClientIds.value = _.get(response, 'productWithClientIds', []) as any;
    } catch (e) {
      notificationService.showError('Error getting carriers');
      loggingService.error('Error getting carriers', e);
    } finally {
      loadingService.stopLoading('main-loader:carriers');
    }
  };

  const getAdditionalServices = async (
    shippingProduct: IShippingProduct,
    senderCountry: ICountry,
    receiverCountry: ICountry,
  ) => {
    return shipmentService.getAdditionalServiceCharges(
      senderCountry.country_code,
      receiverCountry.country_code,
      shippingProduct.carrier_product_code,
    );
  };

  const onClickContinueFromDraft = async (row: IShipment) => {
    try {
      loadingService.startLoading('main-loader:continue-from-draft');
      await getCarriers(row.sender_country_code, row.receiver_country_code);
      const senderCountry = $store.state.countryStore.countries.find(
        (c: ICountry) => c.country_code === row.sender_country_code,
      );
      const receiverCountry = $store.state.countryStore.countries.find(
        (c: ICountry) => c.country_code === row.receiver_country_code,
      );
      const carrier: ICarrier | null = CarrierService.getCarrierFromShippingProductCode(
        $store.state.courierStore.carriers,
        $store.state.courierStore.carrierProducts,
        row.carrier_product_code,
      );
      const shippingProduct: IShippingProduct | undefined =
        $store.state.courierStore.carrierProducts.find(
          (p) => p.carrier_product_code === row.carrier_product_code,
        );

      let ubsend_client_id = null;
      if (row.delivery_template_code) {
        const deliverTemplate = await templateService.getTemplate(row.delivery_template_code);
        ubsend_client_id = deliverTemplate.ubsend_client_id;
        console.log('ShippingProduct ', shippingProduct);
        console.log('ubSendClientID: ', ubsend_client_id);
      } else {
        const product_with_client_ids = JSON.parse(JSON.stringify(productWithClientIds.value));
        console.log('Client IDs: ', product_with_client_ids);
        ubsend_client_id =
          product_with_client_ids[shippingProduct!.product_id] !== undefined
            ? product_with_client_ids[shippingProduct!.product_id]
            : '';
        console.log('Client iD: ', ubsend_client_id);
      }
      /*   if (!ubsend_client_id) {
        notificationService.showError('Ubsend client id not found');
        return;
      } */

      let weightClasses: IWeightClass[] = [];
      if (ubsend_client_id) {
        /*** this code was commented before ***/
        weightClasses = await shipmentService.getWeightClasses(
          row.carrier_product_code,
          row.sender_country_code,
          row.receiver_country_code,
          ubsend_client_id,
        );
        /*** end here ***/
      }

      if (!carrier || !shippingProduct || !senderCountry || !receiverCountry) {
        notificationService.showError('Carrier not found');
        return;
      }

      let servicePoint: IParcelShop | null = null;

      if (row.recipient.parcelShopId) {
        servicePoint = await shipmentService.getParcelShop(
          row.recipient.parcelShopId,
          carrier.carrier_id,
        );
      }

      const additionalServices = await getAdditionalServices(
        shippingProduct,
        senderCountry,
        receiverCountry,
      );

      const additionalServicesForCreateShipmentData: { [key: number]: boolean } = {};

      if (row.additional_services) {
        row.additional_services.forEach((asChargeCode) => {
          const additionalService = additionalServices.find(
            (as) => as.as_charge_code === asChargeCode,
          );

          if (!additionalService) return;

          additionalServicesForCreateShipmentData[additionalService.as_charge_code] = true;
        });
      }
      const parcel = row.parcels[0];
      let weights = extractMinMaxWeight(parcel?.description);
      const weightClass = weightClasses.find((weightClass: IWeightClass) => {
        return weightClass.min_weight == weights[0] && weightClass.max_weight == weights[1];
      });
      await $store.dispatch('shipmentStore/setCreateShipmentData', {
        one: {
          sender: {
            senderCountry: senderCountry,
            name: `${row.sender.firstName} ${row.sender.lastName}`,
            email: row.sender.contactInfo.email,
            phone: row.sender.contactInfo.telephone,
            zipCode: row.sender.postCode,
            address1: row.sender.addressLine1,
            address2: row.sender.addressLine2 || null,
            city: row.sender.city,
          },
          recipient: {
            receiverCountry: receiverCountry,
            name: `${row.recipient.firstName} ${row.recipient.lastName}`,
            email: row.recipient.contactInfo.email,
            phone: row.recipient.contactInfo.telephone,
            zipCode: row.recipient.postCode,
            address1: row.recipient.addressLine1,
            address2: row.recipient.addressLine2 || null,
            city: row.recipient.city,
          },
        },
        two: {
          carrier: carrier,
          shippingProduct: shippingProduct,
          weightClass: weightClass ?? null,
          isChooseClosestServicePointChecked: false,
          servicePoint: servicePoint,
        },
        three: {
          additionalServices: additionalServicesForCreateShipmentData,
        },
        four: { isTermsAndConditionsChecked: false, payWith: 0 },
      } as ICreateNewShipmentForm);
      await $store.dispatch('shipmentStore/setShipmentCode', row.shipment_code);
      await $store.dispatch('shipmentStore/openCreateShipmentModal');
    } catch (e) {
      notificationService.showError('Error continuing from draft');
      loggingService.error('Error continuing from draft', e);
    } finally {
      loadingService.stopLoading('main-loader:continue-from-draft');
    }
  };

  /*const generateShipmentLabel = async (shipmentCode: number) => {
  try {
    loadingService.startLoading('main-loader:generating-label');
    const response = await shipmentService.generateLabel(shipmentCode);

    if (!response.label) {
      throw new Error('Label not found');
    }

    const blob: Blob = await HelperService.toBlob(
      `data:application/pdf;base64,${encodeURI(response.label)}`,
    );

    if (windowRef) {
      windowRef.location = URL.createObjectURL(blob);
    }

    getDrafts(pagination.value);
  } catch (e) {
    notificationService.showError('Error generating label');
    loggingService.error('Error generating label', e);

    if (windowRef) {
      windowRef.close();
    }
  } finally {
    loadingService.stopLoading('main-loader:generating-label');
  }
};

const onClickGenerateShipment = (shipmentCode: number) => {
  if (!shipmentCode) {
    return;
  }

  windowRef = WindowService.openWindow('Shipment Label Preview');

  generateShipmentLabel(shipmentCode);
};*/

  onBeforeMount(() => {
    getDrafts(pagination.value);

    subscriptions.push(
      $store.subscribeAction((action) => {
        if (action.type === 'shipmentStore/refreshDrafts') {
          getDrafts(null);
        }
      }),
    );
  });

  onBeforeUnmount(() => {
    subscriptions.forEach((unsubscribe) => unsubscribe());
  });
</script>

<style scoped lang="scss">
  .drafts_page {
    padding: 0 40px 10px 40px;
    @media (max-width: 1024px) {
      padding: 0 20px 10px 20px;
    }

    &-modal_content {
      padding: 20px;

      p {
        margin: 0;
      }

      &-address {
        display: flex;

        & > * {
          flex: 1;
        }

        &-title {
          margin-bottom: 10px;
          font-size: 15px;
          font-weight: bold;
        }
      }

      &-section {
        padding-bottom: 8px;
        font-size: 15px;
        font-weight: bold;
        border-bottom: 1px dashed #e6e6e6;
      }
    }

    &-country_img {
      display: flex;
      flex-direction: row;
      align-items: center;

      img {
        margin-right: 7px;
        border-radius: 50%;
      }
    }

    &-actions {
      display: flex;
      flex-direction: row;

      & > *:not(:first-child) {
        margin-left: 10px;

        @media (max-width: 600px) {
          margin-left: unset;
        }
      }

      .big_search-rows_per_page-group {
        display: flex;
        flex: 1;
        flex-direction: row;

        & > *:not(:first-child) {
          margin-left: 10px;
        }

        .rows_per_page {
          display: none;

          @media (max-width: $tablet) {
            display: block;
          }
        }
      }

      &-big_search {
        flex: 1;
      }

      @media (max-width: 600px) {
        flex-direction: column;

        &-big_search {
          margin-bottom: 10px;
        }
      }
    }
  }
</style>
<style>
  .drafts-page-table th:nth-child(1),
  .drafts-page-table td:nth-child(1) {
    width: 12%;
  }

  .drafts-page-table th:nth-child(2),
  .drafts-page-table td:nth-child(2) {
    width: 12%;
  }

  .drafts-page-table th:nth-child(3),
  .drafts-page-table td:nth-child(3) {
    width: 15%;
  }

  .drafts-page-table th:nth-child(4),
  .drafts-page-table td:nth-child(4) {
    width: 15%;
  }

  .drafts-page-table th:nth-child(5),
  .drafts-page-table td:nth-child(5) {
    width: 15%;
  }

  .drafts-page-table th:nth-child(6),
  .drafts-page-table td:nth-child(6) {
    width: 15%;
  }

  .drafts-page-table th:nth-child(7),
  .drafts-page-table td:nth-child(7) {
    width: 10%;
  }

  .drafts-page-table th:nth-child(8),
  .drafts-page-table td:nth-child(8) {
    width: 6%;
  }
</style>
