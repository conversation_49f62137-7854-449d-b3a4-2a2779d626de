<template>
  <Title :title="$t('common.product_labels')" />
  <div class="product_labels-page_container">
    <div class="product_labels-inputs_container">
      <div class="product_labels-inputs">
        <shipvagoo-input
          v-model="sku"
          :label="$t('common.sku')"
          :error="v$.sku.$error"
          :error-message="v$.sku.$errors[0]?.$message.toString()"
        />
        <shipvagoo-input
          v-model="name"
          :label="$t('common.name')"
          :error="v$.name.$error"
          :error-message="v$.name.$errors[0]?.$message.toString()"
        />
        <shipvagoo-input
          v-model="bin"
          :label="$t('common.bin')"
          :error="v$.bin.$error"
          :error-message="v$.bin.$errors[0]?.$message.toString()"
        />
        <shipvagoo-input
          v-model="barcode"
          :label="$t('common.barcode')"
          :error="v$.barcode.$error"
          :error-message="v$.barcode.$errors[0]?.$message.toString()"
        />
      </div>

      <div class="product_labels-footer">
        <shipvagoo-button
          class="download_button"
          :label="$t('common.preview')"
          outline
          @click="onClickPreviewButton"
        />
        <shipvagoo-button
          class="download_button"
          :label="$t('common.download')"
          @click="onClickDownloadButton"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import useValidate from '@vuelidate/core';
  import { required, helpers, numeric } from '@vuelidate/validators';
  import Title from '../components/title-bar.vue';
  import {
    fileService,
    loadingService,
    loggingService,
    notificationService,
    productService,
  } from '../services/_singletons';
  import HelperService from '../services/helper.service';
  import { IBase64File } from '../model/common.model';
  import WindowService from '../services/window.service';

  const sku = ref<string>();
  const bin = ref<string>();
  const name = ref<string>();
  const barcode = ref<string>();
  let windowRef: Window | null;

  const v$ = useValidate(
    {
      sku: { required: helpers.withMessage('This field is required.', required) },
      bin: {},
      barcode: {
        required: helpers.withMessage('This field is required.', required),
        numeric: helpers.withMessage('This field must be numeric.', numeric),
      },
      name: { required: helpers.withMessage('This field is required.', required) },
    },
    { sku, bin, barcode, name },
  );

  const previewPDF = async () => {
    await v$.value.$validate();

    if (v$.value.$error || !sku.value || !barcode.value || !name.value || !windowRef) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:download-pdf');

      const response = await productService.generateProductLabel({
        sku: sku.value as string,
        barcode: barcode.value as string,
        name: name.value as string,
        bin: bin.value as string,
      });

      const blob: Blob = await HelperService.toBlob(
        `data:application/pdf;base64,${encodeURI(response.file)}`,
      );

      windowRef.location = URL.createObjectURL(blob);
    } catch (error) {
      windowRef.close();
      loggingService.error('Error while downloading PDF', error);
      notificationService.showError('Error while downloading PDF');
    } finally {
      loadingService.stopLoading('main-loader:download-pdf');
    }
  };

  const onClickPreviewButton = async () => {
    await v$.value.$validate();

    if (v$.value.$error) {
      return;
    }

    windowRef = WindowService.openWindow('Product Labels Preview');

    previewPDF();
  };

  const onClickDownloadButton = async () => {
    await v$.value.$validate();

    if (v$.value.$error || !sku.value || !barcode.value || !name.value) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:download-product-labels-pdf');
      const response: IBase64File = await productService.generateProductLabel({
        sku: sku.value,
        barcode: barcode.value,
        name: name.value,
        bin: bin.value,
      });
      const blob: Blob = await HelperService.toBlob(
        `data:application/pdf;base64,${encodeURI(response.file)}`,
      );
      const date: string = new Intl.DateTimeFormat().format(new Date());
      fileService.saveFile(blob, `product_labels-${date}.pdf`);
    } catch (e) {
      loggingService.error('Error while downloading PDF', e);
      notificationService.showError('Error while downloading PDF');
    } finally {
      loadingService.stopLoading('main-loader:download-product-labels-pdf');
    }
  };
</script>

<style scoped lang="scss">
  .product_labels {
    &-page_container {
      padding: 20px 40px;

      @media (max-width: 1024px) {
        padding: 20px 10px;
      }
    }

    &-inputs {
      display: grid;
      grid-template-columns: 1fr 1fr;
      row-gap: 20px;
      column-gap: 28px;

      @media (max-width: $tablet) {
        grid-template-columns: 1fr;
        column-gap: 0;
      }
    }

    &-inputs_container {
      padding: 20px;
      border: 1px solid #2929c8;
      border-radius: 5px;
    }

    &-footer {
      display: flex;
      flex-direction: row;
      gap: 10px;
      justify-content: flex-end;
      padding: 20px;
      margin-top: 20px;
      margin-right: -20px;
      margin-bottom: -20px;
      margin-left: -20px;
      border-top: 1px solid #2929c8;

      @media (max-width: $tablet) {
        flex-direction: column;

        & > * {
          width: 100%;
        }
      }
    }
  }
</style>
