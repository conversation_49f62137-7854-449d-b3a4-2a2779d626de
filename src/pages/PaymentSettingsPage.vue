<template>
  <div class="flex justify-between items-center title-bar-heading">
    <title-bar class="full-width"
               subtitle="Select your preferred payment method"
               title="Payment Information" />
  </div>
  <q-layout>
    <q-page-container>
      <q-page class="q-page-container">
        <div class="row">
          <div class="col-md-10 flex items-center">
            <div class="text-gray-dark h3-heading"><span>
                Payment methods<!-- <span class="h4-heading"
                                      style="font-weight: 400;">(Choose one of the option below)</span>-->
              </span></div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-3 col-md-4 col-sm-6 col-12 q-mt-sm q-pr-md">
            <payment-method type="pay_with_balance"
                            selected></payment-method>
            <!--            <payment-method type="pay_with_balance" v-else @click="updatePaymentMethod('BALANCE')"></payment-method>-->
          </div>
          <!--          <div class="col-lg-3 col-md-4 col-sm-6 col-12 q-mt-sm q-pr-md">
                      <payment-method type="pay_and_save_card"
                                      v-if="$store.state.paymentSettingsStore.payment_method === 'SAVED_CARD'"
                                      selected></payment-method>
                      <payment-method type="pay_and_save_card" class="q-ml-md" v-else
                                      @click="updatePaymentMethod('SAVED_CARD')"></payment-method>
                    </div>-->
        </div>

        <div class="row q-mt-lg">
          <div class="col-10 flex items-center">
            <div class="text-gray-dark h3-heading">My saved cards</div>
          </div>
        </div>
        <div class="row q-pb-md">
          <div class="col-lg-3 col-md-4 col-sm-6 col-12 " v-for="card in myCards">
            <payment-card
              @statusChange="onCardStatusChange(card)"
              @delete="showDeleteConfirmDialog(card)" class="q-mr-sm q-mt-md"
              :card="card"></payment-card>
          </div>
          <div class="col-lg-3 col-md-4 col-sm-6 col-12">
            <div class="row q-mr-sm q-mt-md">
              <div class="col-12">
                <add-new-box
                  label="Add New Card"
                  @click="onClickAddNewCard" />
              </div>
            </div>
          </div>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
  <!--<div class="pay-settings">
    <div class="pay-settings__settings">
      <shipvagoo-card-section>
        <shipvagoo-checkbox v-model="form.settleTransactionsInCaseOfPositiveBalance"
          label="Automatic settlement of outstanding" size="sm" disabled />
        <div class="pay-settings__settings&#45;&#45;checkbox-subtitle">
          Settles outstanding transactions automatically in case of positive balance.
        </div>
      </shipvagoo-card-section>
      <shipvagoo-separator color="#2929C8" />
      <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
        <div>
          <shipvagoo-checkbox v-model="form.autoRefill" label="Activate auto-refill" size="sm" />
          <div class="pay-settings__settings&#45;&#45;checkbox-subtitle">
            Auto-refill only possible through saved credit card.
          </div>
        </div>

        <shipvagoo-input v-model="form.autoRefillTriggerAmount" type="number" label="Auto-fill when under DKK"
          placeholder="Auto-refill amount must be a whole number..." />

        <shipvagoo-input v-model="form.autoRefillAmount" type="number" label="Auto-fill amount DKK"
          placeholder="Auto-refill amount must be a whole number..." />
      </shipvagoo-card-section>
      <shipvagoo-separator color="#2929C8" />
      <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
        <shipvagoo-checkbox size="sm" label="Receive an e-mail notification when balance is under a specific amount" />
        <shipvagoo-input v-model="form.balanceForNotification" label="Balance in DKK for e-mail notification" />
      </shipvagoo-card-section>
      <shipvagoo-separator color="#2929C8" />
      <shipvagoo-card-actions>
        <shipvagoo-button label="Save" min-width="120px" />
      </shipvagoo-card-actions>
    </div>
    <div />
  </div>-->

  <q-dialog maximized
            persistent
            @hide="onHideMakePaymentDialog"
            :model-value="openMakePaymentModal">
    <make-payment :link="link" title="Add card" ></make-payment>
  </q-dialog>
  <q-dialog v-model="showDeleteConfirm">
    <confirm-popup @confirm="onCardDelete"
                   confirm-label="Delete"
                   confirmation-message="You are about to delete this card. This action cannot be undone." />
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import TitleBar from "../components/title-bar.vue";
import { IAddCardLinkResposne, ICardResponse } from "../model/payment.model";
import { useStore } from "../store";
import {
  cardService,
  loadingService,
  loggingService,
  notificationService
} from "../services/_singletons";

const $store = useStore();
const link = ref<string>("");
const openMakePaymentModal = ref<boolean>(false);
const showDeleteConfirm = ref<boolean>(false);
const selectedCard = ref<ICardResponse | null>(null);

onMounted(() => {
  if ($store.state.paymentSettingsStore.payment_method === "SAVED_CARD") {
    updatePaymentMethod("balance");
  }
});
const showDeleteConfirmDialog = (card: ICardResponse) => {
  selectedCard.value = card;
  showDeleteConfirm.value = true;
};
const onClickAddNewCard = async () => {
  try {
    loadingService.startLoading("main-loader:get-transactions-add-funds");

    const addCardLink: IAddCardLinkResposne = await cardService.getAddCardLink().catch(error => {
      throw new Error(error.response.data.error?.amount[0]);
    });
    link.value = addCardLink.link;
    openMakePaymentModal.value = true;
    loadingService.stopLoading("main-loader:get-transactions-add-funds");
  } catch (e) {
    if (typeof e === "string") {
      notificationService.showError(e);
    } else if (e instanceof Error) {
      notificationService.showError(e.message);
    } else {
      notificationService.showError("Error while adding card");
    }
    loggingService.error("Error while adding card", e);
    loadingService.stopLoading("main-loader:get-transactions-add-funds");
  }
};

const onHideMakePaymentDialog = () => {
  onCloseAddFunds();
  $store.dispatch("paymentSettingsStore/syncPaymentSettings");
};

const onCloseAddFunds = () => {
  console.log("Model Closed");
  openMakePaymentModal.value = false;
};
let myCards = ref<ICardResponse[] | null>($store.state.paymentSettingsStore.cards);
/*let myCards = computed((): ICardResponse[] | null => {
  return $store.state.paymentSettingsStore.cards
})*/
// if ($store.state.paymentSettingsStore.cards) {
watch(() => $store.state.paymentSettingsStore.cards as ICardResponse[], (newVal: ICardResponse[]) => {
  myCards.value = newVal;
});
//}

const updatePaymentMethod = async (payment_method: string) => {
  try {
    loadingService.startLoading("main-loader:get-transactions-add-funds");
    if (payment_method === "SAVED_CARD") {
      const activeCard = $store.state.paymentSettingsStore.cards?.filter(c => c.is_default);
      if (activeCard === undefined || activeCard.length === 0) {
        notificationService.showError("Make sure you have at least one active card");
        loadingService.stopLoading("main-loader:get-transactions-add-funds");
        return;
      }
    }
    await cardService.update_payment_settings().catch(error => {
      throw new Error(error.response.data.error?.amount[0]);
    });
    $store.dispatch("paymentSettingsStore/syncPaymentSettings");
    loadingService.stopLoading("main-loader:get-transactions-add-funds");
  } catch (e) {
    if (typeof e === "string") {
      notificationService.showError(e);
    } else if (e instanceof Error) {
      notificationService.showError(e.message);
    } else {
      notificationService.showError("Error while updating payment settings");
    }
    loggingService.error("Error while updating payment settings", e);
    loadingService.stopLoading("main-loader:get-transactions-add-funds");
  }
};

const onCardStatusChange = async (card: ICardResponse) => {
  try {
    loadingService.startLoading("main-loader:get-transactions-add-funds");

    await cardService.changeCardStatus(card.card_code).catch(error => {
      throw new Error(error.response.data.error?.amount[0]);
    });
    await $store.dispatch("paymentSettingsStore/syncPaymentSettings");
    loadingService.stopLoading("main-loader:get-transactions-add-funds");
  } catch (e) {
    if (typeof e === "string") {
      notificationService.showError(e);
    } else if (e instanceof Error) {
      notificationService.showError(e.message);
    } else {
      notificationService.showError("Error while changing card status");
    }
    loggingService.error("Error while changing card status", e);
    loadingService.stopLoading("main-loader:get-transactions-add-funds");
  }
};

const onCardDelete = async () => {
  if (!selectedCard.value) {
    return;
  }
  try {
    loadingService.startLoading("main-loader:get-transactions-add-funds");
    await cardService.deleteCard(selectedCard.value.card_code).catch(error => {
      throw new Error(error.response.data.error?.amount[0]);
    });
    $store.dispatch("paymentSettingsStore/syncPaymentSettings");
    loadingService.stopLoading("main-loader:get-transactions-add-funds");
    selectedCard.value = null;
  } catch (e) {
    if (typeof e === "string") {
      notificationService.showError(e);
    } else if (e instanceof Error) {
      notificationService.showError(e.message);
    } else {
      notificationService.showError("Error while deleting card");
    }
    loggingService.error("Error while deleting card", e);
    loadingService.stopLoading("main-loader:get-transactions-add-funds");
  }
};


</script>

<style lang="scss">
.text-gray-dark {
  font-weight: 700;
  color: #313131;
}

.h6-heading {
  font-family: Helvetica, sans-serif;
  font-size: 10px;
}

.h5-heading {
  font-family: Helvetica, sans-serif;
  font-size: 13px;
}

.h4-heading {
  font-family: Helvetica, sans-serif;
  font-size: 15px;
}

.h3-heading {
  font-family: Helvetica, sans-serif;
  font-size: 18px;
}

.h2-heading {
  font-family: Helvetica, sans-serif;
  font-size: 21px;
}

.h1-heading {
  font-family: Helvetica, sans-serif;
  font-size: 24px;
}

.q-page-container {
  padding: 0 20px;
}

.add-card-box {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 230px;
  background: #FFFFFF;
  border-radius: 5px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
}

@media (max-width: 1366px) {
  .add-card-box {
    height: 185px;
  }
}

</style>
