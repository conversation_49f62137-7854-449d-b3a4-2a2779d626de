<template>
  <div class="forgot_password">
    <h1 class="forgot_password-heading">Forgot your password?</h1>
    <div class="forgot_password-not_a_member">
      Please fill in e-mail to receive a new password or
      <shipvagoo-hyperlink label="sign in." to="/login" type="router-link" />
    </div>

    <form @submit.prevent="submit">
      <shipvagoo-input
        v-model="state.form.email"
        label="Email"
        type="email"
        :error="v$.form.email.$error"
        :error-message="v$.form.email.$errors[0]?.$message.toString()"
      >
        <template #append>
          <q-icon name="img:/assets/img/email.svg" size="12px" />
        </template>
      </shipvagoo-input>

      <shipvagoo-button
        class="forgot_password-login_button"
        type="submit"
        label="Submit Request"
      ></shipvagoo-button>
    </form>
  </div>
  <img class="forgot_password-background_banner" :src="LoginImage" alt="" decoding="async" />
</template>

<script setup lang="ts">
  import { reactive } from 'vue';
  import useValidate from '@vuelidate/core';
  import { required, helpers, email } from '@vuelidate/validators';
  import { Router, useRouter } from 'vue-router';
  import { authService, loadingService, notificationService } from '../services/_singletons';
  import LoginImage from '../assets/img/sign.png';

  const state = reactive({
    form: {
      email: '',
    },
  });

  const v$ = useValidate(
    {
      form: {
        email: {
          required: helpers.withMessage('This field is required.', required),
          email: helpers.withMessage('Please enter a valid e-mail.', email),
        },
      },
    },
    state,
  );

  const $router: Router = useRouter();

  const submit = async () => {
    await v$.value.$validate();

    if (v$.value.$error) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:login');
      await authService.forgotPassword(state.form.email);
    } finally {
      loadingService.stopLoading('main-loader:login');
      notificationService.showSuccess(
        'If the email address you entered is associated with an account, you will receive an email with a link to reset your password.',
      );
      await $router.push('/login');
    }
  };
</script>

<style scoped lang="scss">
  .forgot_password {
    width: 340px;

    transition: transform 0.3s ease-in-out, width 0.3s ease-in-out;
    transform: translateX(78px) translateY(81px);
    will-change: width, transform;

    @media (max-width: 768px) {
      width: calc(100% - 20px);
      max-width: unset;

      transform: translateX(10px) translateY(241px);
    }

    &-start_for_free {
      font-size: 15px;
      font-weight: 500;
      line-height: 17px;
      color: #9eb2c6;
    }

    &-heading {
      margin: 0;
      font-size: 35px;
      line-height: 42px;
    }

    &-not_a_member {
      font-size: 15px;
      color: #9eb2c6;
    }

    &-forgot_password,
    &-remember_me {
      margin-top: 27px;
    }

    &-login_button {
      min-width: 115px;
      margin-top: 27px;
    }

    &-background_banner {
      position: absolute;
      top: 0;
      right: 0;
      z-index: -1;
      width: calc(100% - 340px - 78px - 23px);
      height: 100%;
      object-fit: cover;
      object-position: center;
      transition: width 300ms ease-in-out, height 300ms ease-in-out;
      will-change: width, height;

      @media (max-width: 768px) {
        width: 100%;
        max-width: unset;
        max-height: 375px;
      }
    }

    & > *:not(:first-child) {
      margin-top: 17px;
    }

    form {
      & > .q-input:not(:first-of-type) {
        margin-top: 17px;
      }
    }
  }
</style>
