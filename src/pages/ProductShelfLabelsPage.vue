<template>
  <Title :title="$t('common.product_shelves')" />
  <div class="shelf_labels-page_container">
    <shipvagoo-card-section class="shelf_labels-inputs_container">
      <shipvagoo-input-group responsive>
        <shipvagoo-select
          v-model="sectionFrom"
          :label="$t('common.section_from')"
          :error="v$.sectionFrom.$error"
          :error-message="v$.sectionFrom.$errors[0]?.$message.toString()"
          :options="alphabet"
        />
        <shipvagoo-select
          v-model="sectionTo"
          :label="$t('common.section_to')"
          :error="v$.sectionTo.$error"
          :error-message="v$.sectionTo.$errors[0]?.$message.toString()"
          :options="alphabet"
        />
      </shipvagoo-input-group>

      <shipvagoo-input-group responsive>
        <shipvagoo-select
          v-model="numberFrom"
          :label="$t('common.number_from')"
          :error="v$.numberFrom.$error"
          :error-message="v$.numberFrom.$errors[0]?.$message.toString()"
          :options="numbers"
        />
        <shipvagoo-select
          v-model="numberTo"
          :label="$t('common.number_to')"
          :error="v$.numberTo.$error"
          :error-message="v$.numberTo.$errors[0]?.$message.toString()"
          :options="numbers"
        />
      </shipvagoo-input-group>

      <shipvagoo-input-group responsive>
        <shipvagoo-select
          v-model="levels"
          :label="$t('common.levels')"
          :error="v$.levels.$error"
          :error-message="v$.levels.$errors[0]?.$message.toString()"
          :options="numbers"
        />
        <shipvagoo-select
          v-model="placements"
          :label="$t('common.placements')"
          :error="v$.placements.$error"
          :error-message="v$.placements.$errors[0]?.$message.toString()"
          :options="numbers"
        />
      </shipvagoo-input-group>

      <div class="shelf_labels-footer">
        <shipvagoo-button
          class="download_button"
          outline
          :label="$t('common.preview')"
          @click="onClickPreviewButton"
        />
        <shipvagoo-button
          class="download_button"
          :label="$t('common.download')"
          @click="onClickDownloadButton"
        />
      </div>
    </shipvagoo-card-section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import useValidate from '@vuelidate/core';
  import { required, helpers } from '@vuelidate/validators';
  import Title from '../components/title-bar.vue';
  import {
    fileService,
    loadingService,
    loggingService,
    notificationService,
    productService,
  } from '../services/_singletons';
  import HelperService from '../services/helper.service';
  import { IBase64File, TAlphabet } from '../model/common.model';
  import WindowService from '../services/window.service';

  const numbers = Array.from({ length: 20 }, (_, i) => i + 1);
  const alphabet: TAlphabet[] = Array.from({ length: 26 }, (_, i) =>
    String.fromCharCode(65 + i),
  ) as TAlphabet[];

  const sectionFrom = ref<TAlphabet>();
  const sectionTo = ref<TAlphabet>();
  const numberFrom = ref<number>();
  const numberTo = ref<number>();
  const levels = ref<number>();
  const placements = ref<number>();

  let windowRef: Window | null;

  const v$ = useValidate(
    {
      sectionFrom: { required: helpers.withMessage('This field is required.', required) },
      sectionTo: { required: helpers.withMessage('This field is required.', required) },
      numberFrom: { required: helpers.withMessage('This field is required.', required) },
      numberTo: { required: helpers.withMessage('This field is required.', required) },
      levels: { required: helpers.withMessage('This field is required.', required) },
      placements: { required: helpers.withMessage('This field is required.', required) },
    },
    { sectionFrom, sectionTo, numberFrom, numberTo, levels, placements },
  );

  const previewPDF = async () => {
    if (
      !sectionFrom.value ||
      !sectionTo.value ||
      !numberFrom.value ||
      !numberTo.value ||
      !levels.value ||
      !placements.value ||
      !windowRef
    ) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:download-pdf');

      const response = await productService.generateShelfLabels({
        sectionFrom: sectionFrom.value,
        sectionTo: sectionTo.value,
        numberFrom: numberFrom.value,
        numberTo: numberTo.value,
        levels: levels.value,
        placements: placements.value,
      });

      const blob: Blob = await HelperService.toBlob(
        `data:application/pdf;base64,${encodeURI(response.file)}`,
      );
      windowRef.location = URL.createObjectURL(blob);
    } catch (error) {
      windowRef.close();
      loggingService.error('Error while download PDF', error);
      notificationService.showError('Error while download PDF');
    } finally {
      loadingService.stopLoading('main-loader:download-pdf');
    }
  };

  const onClickPreviewButton = async () => {
    await v$.value.$validate();

    if (v$.value.$error) {
      return;
    }

    windowRef = WindowService.openWindow('Shelf Labels Preview');

    previewPDF();
  };

  const onClickDownloadButton = async () => {
    await v$.value.$validate();

    if (
      v$.value.$error ||
      !sectionFrom.value ||
      !sectionTo.value ||
      !numberFrom.value ||
      !numberTo.value ||
      !levels.value ||
      !placements.value
    ) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:download-product-labels-pdf');
      const response: IBase64File = await productService.generateShelfLabels({
        sectionFrom: sectionFrom.value,
        sectionTo: sectionTo.value,
        numberFrom: numberFrom.value,
        numberTo: numberTo.value,
        levels: levels.value,
        placements: placements.value,
      });
      const blob: Blob = await HelperService.toBlob(
        `data:application/pdf;base64,${encodeURI(response.file)}`,
      );
      const date: string = new Intl.DateTimeFormat().format(new Date());
      fileService.saveFile(blob, `product_labels-${date}.pdf`);
    } catch (e) {
      loggingService.error('Error while downloading PDF', e);
      notificationService.showError('Error while downloading PDF');
    } finally {
      loadingService.stopLoading('main-loader:download-product-labels-pdf');
    }
  };
</script>

<style scoped lang="scss">
  .shelf_labels {
    &-page_container {
      padding: 20px 40px;

      @media (max-width: 1024px) {
        padding: 20px 10px;
      }
    }

    &-inputs_container {
      display: flex;
      flex-direction: column;
      gap: 20px;
      padding: 20px;
      border: 1px solid #2929c8;
      border-radius: 5px;
    }

    &-footer {
      display: flex;
      flex-direction: row;
      gap: 10px;
      justify-content: flex-end;
      padding: 20px;
      margin-top: 20px;
      margin-right: -20px;
      margin-bottom: -20px;
      margin-left: -20px;
      border-top: 1px solid #2929c8;

      @media (max-width: $tablet) {
        flex-direction: column;

        & > * {
          width: 100%;
        }
      }
    }
  }
</style>
