<template>
  <div class="flex justify-between items-center title-bar-heading q-mt-md">
    <div>
      <title-bar
        style="width: 100%"
        title="Shipped"
        subtitle="An overview of all your created shipments."
      />
    </div>
    <big-search
      placeholder="Search for shipment ID, recipient or email"
      @update:modelValue="searchVal"
      v-model="search"
      class="booked_page-actions-big_search"
    />
  </div>
  <div class="booked_page q-mt-xs" v-if="!rows.length && !shippedTableLoading">
    <shipvagoo-placeholder text="No shipments have been created"> </shipvagoo-placeholder>
  </div>
  <div class="booked_page" v-if="rows.length || shippedTableLoading">
    <div class="booked_page-table">
      <shipvagoo-table-v1
        class="booked-page-table"
        v-model:pagination="pagination"
        :rows="rows"
        :total="pagination.rowsNumber"
        :columns="columns"
        :loading="shippedTableLoading"
        @row-click="onClickRow"
        @request="getShipments($event.pagination)"
      >
        <template v-slot:sync-pagination>
          <q-pagination
            v-if="pagination.page"
            v-model="pagination.page"
            :max="maxPages"
            color="primary"
            max-pages="7"
            :input-style="{ width: '50px' }"
            class="q-ml-md"
            @update:model-value="getShipments(pagination)"
          />
          <q-input
            v-model="inputPage"
            type="number"
            dense
            outlined
            placeholder="Go to page"
            :min="1"
            :max="maxPages"
            class="q-ml-sm goToPage"
            @keyup.enter="goToPage(inputPage)"
            label="Go to page"
          />
        </template>
        <template #body="props">
          <q-tr
            :props="props"
            :class="{ 'booked_page-table-row--is_cancelled': props.row.is_cancelled }"
            style="cursor: pointer"
            @click="onClickRow($event, props.row)"
          >
            <q-td key="id">
              <div class="shipped-shipment-id">{{ props.row.shipment_code }}</div>
            </q-td>
            <q-td key="created_at" class="shipped-date">
              <span class="text-no-wrap">{{
                dayjs(String(props.row.created_at)).format('DD/MM/YYYY')
              }}</span
              ><br />
              <span class="text-no-wrap">{{
                dayjs(String(props.row.created_at)).format('HH:mm')
              }}</span>
            </q-td>
            <q-td key="name" class="shipped-recipient">
              <span class="text-no-wrap"
                >{{ props.row.recipient.firstName }} {{ props.row.recipient.lastName }}</span
              ><br />
              <span class="text-no-wrap">{{ props.row.recipient.addressLine1 }}</span
              ><br />
              <span class="text-no-wrap"
                >{{ props.row.recipient.country }}-{{ props.row.recipient.postCode }}
                {{ props.row.recipient.city }}</span
              >
            </q-td>
            <q-td key="order">
              <shipvagoo-status-tag
                v-if="getType(props.row)"
                :color="getType(props.row).color"
                :bg-color="getType(props.row).bgColor"
                :text="getType(props.row).label"
              ></shipvagoo-status-tag>
            </q-td>
            <q-td key="tracking_id" class="shipped-tracking-id" @click.stop>
              <div
                v-for="(parcel, index) in props.row.parcels"
                :key="`parcel_${index}`"
                class="text-no-wrap"
              >
                <template v-if="index < 3">
                  <shipvagoo-hyperlink
                    v-if="
                      CarrierService.getCarrierFromShippingProductCode(
                        $store.state.courierStore.carriers,
                        $store.state.courierStore.carrierProducts,
                        props.row.carrier_product_code,
                      )?.carrier_id?.toLowerCase()
                    "
                    type="external-link"
                    text-decoration="none"
                    :to="
                      CarrierService.getCarrierFromShippingProductCode(
                        $store.state.courierStore.carriers,
                        $store.state.courierStore.carrierProducts,
                        props.row.carrier_product_code,
                      )?.tracking_url + parcel.carrierReference
                    "
                    :label="parcel.carrierReference"
                    blank
                    noopener
                    noreferer
                  />
                  <div v-else>
                    {{ parcel.carrierReference }}
                  </div>
                </template>
              </div>
              <template v-if="props.row.parcels.length > 3">
                <q-tooltip
                  v-html=" props.row.parcels.map((parcel:IParcel) => parcel.carrierReference).join('<br/>')"
                >
                </q-tooltip>
              </template>
            </q-td>
            <q-td key="via" :props="props">
              <div v-if="props">
                <div
                  class="flex items-center no-wrap"
                  v-if="
                    CarrierService.getCarrierFromShippingProductCode(
                      $store.state.courierStore.carriers,
                      $store.state.courierStore.carrierProducts,
                      props.row.carrier_product_code,
                    )?.carrier_id?.toLowerCase()
                  "
                >
                  <div
                    class="shipvagoo-table-carrier-logo"
                    :style="{
                      backgroundImage:
                        'url(' +
                        CarrierService.getCarrierFromShippingProductCode(
                          $store.state.courierStore.carriers,
                          $store.state.courierStore.carrierProducts,
                          props.row.carrier_product_code,
                        )?.icon +
                        ')',
                    }"
                  />
                  <div class="text-no-wrap q-ml-sm shipped-carrier">
                    {{
                      CarrierService.getCarrierFromShippingProductCode(
                        $store.state.courierStore.carriers,
                        $store.state.courierStore.carrierProducts,
                        props.row.carrier_product_code,
                      )?.name
                    }}
                  </div>
                </div>
              </div>
            </q-td>
            <q-td key="status" :props="props">
              <shipvagoo-booking-status
                :shipment="props.row"
                :is-stop-shipping-monitoring="Boolean(props.row.stop_monitoring)"
                :is-marked-as-delivered="Boolean(props.row.marked_as_delivered)"
              />
            </q-td>
            <q-td key="more" :props="props" class="more">
              <q-btn
                dense
                flat
                round
                style="color: #1f2124"
                class="more-action"
                field="edit"
                icon="more_horiz"
              >
                <shipvagoo-menu style="width: 160px">
                  <shipvagoo-menu-item
                    v-if="!props.row.is_cancelled"
                    clickable
                    @click="onClickDownloadLabel(props.row)"
                  >
                    {{ $t('common.download') }}
                  </shipvagoo-menu-item>
                  <shipvagoo-menu-item v-if="!props.row.is_cancelled" clickable has-caret>
                    {{ $t('common.print_at') }}

                    <shipvagoo-menu anchor="top end" style="width: 160px">
                      <shipvagoo-menu-item
                        v-if="$store.state.printerStore.printers.length <= 0"
                        v-close-popup
                      >
                        {{ $t('common.no_printer_is_set') }}
                      </shipvagoo-menu-item>
                      <shipvagoo-menu-item
                        v-for="printer in $store.state.printerStore.printers"
                        :key="`printer-${printer.printer_code}`"
                        clickable
                        @click="onClickPrintLabel(props.row, printer)"
                        v-close-popup
                      >
                        {{ printer.name }}
                      </shipvagoo-menu-item>
                    </shipvagoo-menu>
                  </shipvagoo-menu-item>
                  <!-- <shipvagoo-menu-item v-if="!props.row.is_cancelled" clickable has-caret>
                     {{ $t('common.email_to') }}
        
                     <shipvagoo-menu anchor="top end" style="width: 160px">
                       <shipvagoo-menu-item clickable @click="onClickEmailToSenderMail(props.row)">
                         {{ $t('booked_page.sender_email') }}
                       </shipvagoo-menu-item>
                       <shipvagoo-menu-item clickable @click="onClickEmailToOptionalMail">
                         {{ $t('booked_page.optional_email') }}
                       </shipvagoo-menu-item>
                     </shipvagoo-menu>
                   </shipvagoo-menu-item>-->
                  <shipvagoo-separator v-if="!props.row.is_cancelled" />
                  <!-- <shipvagoo-menu-item
                     v-if="!props.row.is_cancelled && !Boolean(props.row.marked_as_delivered)"
                     clickable
                     @click="onClickMarkAsDelivered(props.row)"
                   >
                     {{ $t('booked_page.mark_as_delivered') }}
                   </shipvagoo-menu-item>-->
                  <!--                  <shipvagoo-menu-item
                                      v-if="!Boolean(props.row.is_cancelled) && !Boolean(props.row.stop_monitoring)"
                                      clickable
                                      @click="onClickStopShippingMonitoring(props.row)"
                                    >
                                      {{ $t('booked_page.stop_shipment_monitor') }}
                                    </shipvagoo-menu-item>-->
                  <!-- <shipvagoo-menu-item clickable @click="onClickRepurchaseShipment(props.row)">
                     {{ $t('booked_page.repurchase') }}
                   </shipvagoo-menu-item>-->
                  <!--                  <shipvagoo-menu-item
                                      v-if="props.row.source_type!='MANUAL'"
                                      clickable @click="onClickCreateReturn(props.row)">
                                      {{ $t('booked_page.create_return') }}
                                    </shipvagoo-menu-item>-->
                  <shipvagoo-separator v-if="!props.row.is_cancelled" />
                  <!--                  <shipvagoo-menu-item-->
                  <!--                    v-if="!props.row.is_cancelled"-->
                  <!--                    clickable-->
                  <!--                    disable-->
                  <!--                    @click="onClickCancelShipment(props.row)"-->
                  <!--                  >-->
                  <!--                    {{ $t('booked_page.cancel_shipment') }}-->
                  <!--                  </shipvagoo-menu-item>-->
                </shipvagoo-menu>
              </q-btn>
            </q-td>
          </q-tr>
        </template>
      </shipvagoo-table-v1>
    </div>
  </div>

  <q-dialog v-if="selectedBooking" v-model="isBookedDetailOpen">
    <shipment-detail
      :shipment="selectedBooking"
      @downloadLabel="onClickDownloadLabel"
      @printLabel="onPrintLabel"
    />
  </q-dialog>

  <q-dialog v-if="selectedBooking" v-model="isCancelShipmentOpen">
    <cancel-shipment
      :shipment="selectedBooking"
      @refresh="getShipments(pagination)"
      @close="onCloseCancelShipment"
    />
  </q-dialog>

  <q-dialog v-model="isEmailLabelOpen" @before-hide="onCloseEmailShippingLabel">
    <shipvagoo-modal title="E-mail shipping label" size="md">
      <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
        <shipvagoo-input v-model="emailShippingLabelForm.name" label="Name" />
        <shipvagoo-input v-model="emailShippingLabelForm.email" label="Email" />
        <shipvagoo-select
          v-model="emailShippingLabelForm.pageSize"
          label="Page size"
          :options="printSelectOptions"
        />
      </shipvagoo-card-section>
      <shipvagoo-separator />
      <shipvagoo-card-actions style="display: flex; justify-content: space-between">
        <shipvagoo-button v-close-popup label="Cancel" min-width="120px" outline />
        <shipvagoo-button
          min-width="120px"
          :disable="!emailShippingLabelForm.name || !emailShippingLabelForm.email"
          label="Send"
          @click="() => {}"
        />
      </shipvagoo-card-actions>
    </shipvagoo-modal>
  </q-dialog>

  <q-dialog v-if="selectedBooking" v-model="isRepurchaseConfirmationOpen">
    <confirm-repurchase
      :shipment="selectedBooking"
      @confirm="onConfirmRepurchaseShipment"
      @close="onCloseRepurchaseConfirm"
    />
  </q-dialog>

  <!-- <q-dialog v-if="selectedBooking" v-model="isCreateReturnConfirmationOpen">
      <confirm-return
        :shipment="selectedBooking"
        @confirm="onConfirmCreateReturn"
        @close="onCloseCreateReturnConfirm"
      />
    </q-dialog>-->
</template>

<script setup lang="ts">
  import { computed, onBeforeMount, onBeforeUnmount, Ref, ref } from 'vue';
  import { QTableProps } from 'quasar';
  import dayjs from 'dayjs';
  import { useI18n } from 'vue-i18n';
  import {
    loadingService,
    loggingService,
    notificationService,
    orderService,
    printJobService,
    shipmentService,
  } from '../services/_singletons';
  import { IParcel, IShipment } from '../model/shipment.model';
  import WindowService from '../services/window.service';
  import HelperService from '../services/helper.service';
  import { IOption, Pagination } from '../model/common.model';
  import { IRequestParam } from '../model/http.model';
  import CarrierService from '../services/carrier.service';
  import { useStore } from '../store';
  import PriceService from '../services/price.service';
  import { IPrinter } from '../model/printer.model';

  const shippedTableLoading = ref<boolean>(false);
  const search: Ref<string> = ref('');
  const isBookedDetailOpen = ref<boolean>(false);
  const isCancelShipmentOpen = ref<boolean>(false);
  const isRepurchaseConfirmationOpen = ref<boolean>(false);
  //const isCreateReturnConfirmationOpen = ref<boolean>(false);
  const isEmailLabelOpen = ref<boolean>(false);
  const selectedBooking: Ref<IShipment | null> = ref(null);
  const printSelectOptions = [
    { label: 'A4', value: 'A4' },
    { label: '10x19cm', value: '10x19cm' },
  ];
  let windowRef: Window | null;
  const $store = useStore();
  const { t } = useI18n();
  const searchVal = (value: string) => {
    search.value = value;
    pagination.value.page = 1;
    getShipments(pagination.value);
  };

  const getType = (row: IShipment): any => {
    let response = null;
    if (row.source_type === 'MANUAL' || !row.source_id) {
      response = {
        color: '#735C00',
        bgColor: '#FEECA1',
        label: `Shipment`,
      };
    }

    if (row.source_type === 'FULFILLMENT') {
      response = {
        color: '#0C9247',
        bgColor: '#BEF3D7',
        label: `Fulfilled (${row.source_id})`,
      };
    }

    /* if (row.source_type === 'REPURCHASE') {
     return t('booked_page.row_repurchase', {
       shipment_code: HelperService.formatId(row.source_id),
     });
   }
 
   if (row.source_type === 'RETURN') {
     return t('booked_page.row_return', {shipment_code: HelperService.formatId(row.source_id)});
   }*/

    return response;
  };

  const columns = computed((): QTableProps['columns'] => [
    {
      name: 'shipment_code',
      label: 'Shipment ID',
      field: (row: IShipment) => row.shipment_code,
      align: 'left',
      sortable: false,
    },
    {
      name: 'created_at',
      align: 'left',
      label: 'Date',
      sortable: false,
      field: 'created_at',
    },
    {
      name: 'name',
      label: 'Recipient',
      align: 'left',
      field: 'name',
      sortable: false,
    },
    {
      name: 'order',
      label: 'Order',
      align: 'left',
      field: 'order',
      sortable: false,
    },
    {
      name: 'tracking_id',
      label: 'Tracking ID',
      align: 'left',
      sortable: false,
      field: 'tracking_id',
    },

    {
      name: 'via',
      align: 'left',
      label: 'Carrier',
      field: 'via',
      sortable: false,
    },
    {
      name: 'status',
      align: 'left',
      label: t('common.status'),
      field: () => '',
      sortable: false,
    },
    {
      name: 'more',
      align: 'center',
      label: 'Action',
      field: 'more',
      sortable: false,
    },
  ]);

  const rows = ref<IShipment[]>([]);
  const subscriptions: (() => void)[] = [];
  const pagination: Ref<Pagination> = ref({
    page: 1,
    rowsPerPage: 20,
    rowsNumber: 0,
    descending: true,
  });

  interface IEmailShippingLabelForm {
    name: string | null;
    email: string | null;
    pageSize: IOption<string, string>;
  }

  const emailShippingLabelForm = ref<IEmailShippingLabelForm>({
    name: null,
    email: null,
    pageSize: { label: 'A4', value: 'A4' },
  });

  const inputPage = ref<number>(1);
  const totalRecords = computed(() => {
    return pagination.value?.rowsNumber ?? 10;
  });
  const maxPages = computed(() => {
    return Math.ceil(totalRecords.value / (pagination.value.rowsPerPage ?? 10));
  });

  const goToPage = (page: number) => {
    if (page >= 1 && page <= maxPages.value) {
      pagination.value.page = page;
      getShipments(pagination.value);
    }
  };

  /*const onClickEmailToSenderMail = (row: IShipment) => {
  emailShippingLabelForm.value.name = `${row.sender.firstName} ${row.sender.lastName}`;
  emailShippingLabelForm.value.email = row.sender.contactInfo.email;
  isEmailLabelOpen.value = true;
};

const onClickEmailToOptionalMail = () => {
  isEmailLabelOpen.value = true;
};*/

  const onCloseEmailShippingLabel = () => {
    emailShippingLabelForm.value.name = null;
    emailShippingLabelForm.value.email = null;
    emailShippingLabelForm.value.pageSize = { label: 'A4', value: 'A4' };
  };

  // const onClickCancelShipment = (row: IShipment) => {
  //   selectedBooking.value = row;
  //   isCancelShipmentOpen.value = true;
  // };

  const getShipments = async (tablePagination: Pagination | null) => {
    try {
      // loadingService.startLoading('main-loader:loading-shipments');
      shippedTableLoading.value = true;
      const page = tablePagination?.page || 1;
      const limit = tablePagination?.rowsPerPage || 20;

      const params: IRequestParam[] = [{ key: 'with', value: 'shipmentEvents,fulfillments' }];
      if (search.value && search.value !== '') {
        params.push({ key: 'name[filter]', value: search.value });
        // params.push({key: 'conditions[recipient:json_contain]', value: search.value});
      }
      if (tablePagination?.sortBy) {
        const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
        params.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
      } else {
        params.push({ key: 'sorting[created_at]', value: 'desc' });
      }

      const shipments = await shipmentService.getBookedShipments(page, limit, params);

      pagination.value.page = page;
      pagination.value.rowsPerPage = limit;
      pagination.value.rowsNumber = shipments.total;
      pagination.value.sortBy = tablePagination?.sortBy;
      pagination.value.descending = tablePagination?.descending;

      rows.value = shipments.entities;
    } catch (e) {
      loggingService.error('Error loading shipments', e);
      notificationService.showError('Error loading shipments');
    } finally {
      //loadingService.stopLoading('main-loader:loading-shipments');
      shippedTableLoading.value = false;
    }
  };

  /*const onClickMarkAsDelivered = async (row: IShipment) => {
  try {
    loadingService.startLoading('main-loader:mark-as-delivered');
    await shipmentService.markAsDelivered(row.shipment_code);
    notificationService.showSuccess('Shipment marked as delivered');
    getShipments(pagination.value);
  } catch (e) {
    loggingService.error('Error while marking as delivered', e);
    notificationService.showError('Error while marking as delivered');
  } finally {
    loadingService.stopLoading('main-loader:mark-as-delivered');
  }
};*/

  /*const onClickRepurchaseShipment = (row: IShipment) => {
  selectedBooking.value = row;
  isRepurchaseConfirmationOpen.value = true;
};*/

  const onCloseRepurchaseConfirm = () => {
    isRepurchaseConfirmationOpen.value = false;
    selectedBooking.value = null;
  };

  /*const onClickCreateReturn = (row: IShipment) => {
  selectedBooking.value = row;
  isCreateReturnConfirmationOpen.value = true;
};

const onCloseCreateReturnConfirm = () => {
  isCreateReturnConfirmationOpen.value = false;
  selectedBooking.value = null;
};*/

  const onConfirmRepurchaseShipment = async (row: IShipment) => {
    try {
      loadingService.startLoading('main-loader:repurchase-shipment');
      const calculatedCharge = await shipmentService.calculateShipmentCharge(row.shipment_code);
      await shipmentService.repurchaseShipment(row.shipment_code);
      const id = HelperService.formatId(row.id);
      notificationService.showSuccess(
        `Shipment ${id} repurchased successfully for ${PriceService.formatPrice(
          calculatedCharge.totals.after_vat.amount,
        )}`,
      );
      $store.dispatch('companyStore/syncCompanyData');
      getShipments(null);
    } catch (e) {
      loggingService.error('Error while repurchasing shipment', e);
      notificationService.showError('Error while repurchasing shipment');
    } finally {
      loadingService.stopLoading('main-loader:repurchase-shipment');
    }
  };

  /*const onConfirmCreateReturn = async (row: IShipment) => {
  try {
    loadingService.startLoading('main-loader:create-return-shipment');
    const shipment = await shipmentService.createReturnShipment(row.shipment_code);
    const calculatedCharge = await shipmentService.calculateShipmentCharge(
      shipment.shipment_code,
    );
    const id = HelperService.formatId(row.id);
    notificationService.showSuccess(
      `Created return for shipment ${id} successfully for ${PriceService.formatPrice(
        calculatedCharge.totals.after_vat.amount,
      )}`,
    );
    $store.dispatch('companyStore/syncCompanyData');
    getShipments(null);
  } catch (e) {
    loggingService.error('Error while creating return shipment', e);
    notificationService.showError('Error while creating return shipment');
  } finally {
    loadingService.stopLoading('main-loader:create-return-shipment');
  }
};*/

  /* const onClickStopShippingMonitoring = async (row: IShipment) => {
   try {
     loadingService.startLoading('main-loader:stop-monitoring');
     await shipmentService.stopMonitoring(row.shipment_code);
     notificationService.showSuccess('Shipment monitoring stopped');
     getShipments(pagination.value);
   } catch (e) {
     loggingService.error('Error while stopping monitoring', e);
     notificationService.showError('Error while stopping monitoring');
   } finally {
     loadingService.stopLoading('main-loader:stop-monitoring');
   }
 };*/

  const onClickRow = (event: Event, row: IShipment) => {
    if (HelperService.containsIgnoredRowElements(event)) {
      return;
    }

    selectedBooking.value = row;
    isBookedDetailOpen.value = true;
  };

  const downloadLabel = async (row: IShipment) => {
    if (!windowRef) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:download-pdf');

      let generatedLabel: string | null = null;

      if (!row.label && row.source_type === 'MANUAL') {
        const shipment = await shipmentService.generateLabel(row.shipment_code);
        generatedLabel = shipment.label;
      }

      if (!row.label && row.source_type === 'FULFILLMENT' && row.fulfillment_code) {
        await orderService.generateLabelForFulfillmentsShipment(row.fulfillment_code);
        const shipment = await shipmentService.getShipment(row.shipment_code);
        generatedLabel = shipment.label;
        $store.dispatch('companyStore/syncCompanyData');
      }

      if (!row.label && !generatedLabel) {
        return;
      }

      if (generatedLabel) {
        getShipments(pagination.value);
      }

      const blob: Blob = await HelperService.toBlob(
        `data:application/pdf;base64,${encodeURI(row.label ?? generatedLabel ?? '')}`,
      );

      windowRef.location = URL.createObjectURL(blob);
    } catch (error) {
      windowRef.close();
      loggingService.error('Error while downloading PDF', error);
      notificationService.showError('Error while downloading PDF');
    } finally {
      loadingService.stopLoading('main-loader:download-pdf');
    }
  };
  const onPrintLabel = ({ printer, shipment }: any) => {
    onClickPrintLabel(shipment, printer);
  };
  const onClickPrintLabel = async (shipment: IShipment, printer: IPrinter) => {
    console.log('Shipment ', shipment);
    if (!shipment || !shipment.label) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:print-label');

      /*const senderCountryIso =
      $store.state.countryStore.countriesIndexedByCountryCode[shipment.sender_country_code]?.iso;
    const receiverCountryIso =
      $store.state.countryStore.countriesIndexedByCountryCode[shipment.receiver_country_code]
        ?.iso;*/

      let label: string | null = null;

      if (printer.format === 'A4') {
        label = shipment.label;
      }

      if (printer.format === '10x19') {
        label = shipment.label_a6;
      }

      if (printer.format === 'ZPL') {
        label = shipment.label_zpl;
      }

      if (!label) {
        throw new Error('No label found');
      }

      await printJobService.queuePrintJob({
        company_code: $store.state.companyStore.company_code,
        document_base64: label,
        format: printer.format,
        reference: `Shipment #${shipment.shipment_code}`,
        printer: printer.linked_printer,
        printer_app_code: printer.printer_app_code,
      });
      notificationService.showSuccess('Print job queued successfully');
    } catch (e) {
      loggingService.error('Error printing label', e);
      notificationService.showError('Error printing label');
    } finally {
      loadingService.stopLoading('main-loader:print-label');
    }
  };

  const onClickDownloadLabel = async (value: IShipment) => {
    windowRef = WindowService.openWindow('Shipment Label Preview');

    downloadLabel(value);
  };

  const onCloseCancelShipment = () => {
    isCancelShipmentOpen.value = false;
  };

  onBeforeMount(() => {
    getShipments(null);

    subscriptions.push(
      $store.subscribeAction((action) => {
        if (action.type === 'shipmentStore/refreshBookings') {
          getShipments(null);
        }
      }),
    );
  });

  onBeforeUnmount(() => {
    subscriptions.forEach((unsubscribe) => unsubscribe());
  });
</script>

<style scoped lang="scss">
  .booked_page {
    padding: 0px 40px;

    @media (max-width: 1024px) {
      padding: 0px 10px;
    }

    &-table {
      margin-top: 10px;
    }

    &-actions {
      display: flex;
      flex-direction: row;

      & > *:not(:first-child) {
        margin-left: 10px;

        @media (max-width: 600px) {
          margin-left: unset;
        }
      }

      .big_search-rows_per_page-group {
        display: flex;
        flex: 1;
        flex-direction: row;

        & > *:not(:first-child) {
          margin-left: 10px;
        }

        .rows_per_page {
          display: none;

          @media (max-width: $tablet) {
            display: block;
          }
        }
      }

      &-big_search {
        width: 320px;
      }

      @media (max-width: 600px) {
        flex-direction: column;

        &-big_search {
          margin-bottom: 10px;
        }
      }
    }
  }

  .shipped-carrier,
  .shipped-date,
  .shipped-recipient,
  .shipped-shipment-id,
  .shipped-tracking-id {
    line-height: 16px;
  }

  .shipped-shipment-id,
  .shipped-tracking-id,
  .shipped-carrier {
    line-height: normal;
  }
</style>

<style lang="scss">
  .booked_page-table-row--is_cancelled {
    opacity: 0.7;

    td:not(:last-child) {
      text-decoration: line-through;
    }
  }
</style>

<style>
  .booked-page-table th:nth-child(1),
  .booked-page-table td:nth-child(1) {
    width: 12%;
  }

  .booked-page-table th:nth-child(2),
  .booked-page-table td:nth-child(2) {
    width: 10%;
  }

  .booked-page-table th:nth-child(3),
  .booked-page-table td:nth-child(3) {
    width: 16%;
  }

  .booked-page-table th:nth-child(4),
  .booked-page-table td:nth-child(4) {
    width: 15%;
  }

  .booked-page-table th:nth-child(5),
  .booked-page-table td:nth-child(5) {
    width: 13%;
  }

  .booked-page-table th:nth-child(6),
  .booked-page-table td:nth-child(6) {
    width: 15%;
  }

  .booked-page-table th:nth-child(7),
  .booked-page-table td:nth-child(7) {
    width: 13%;
  }

  .booked-page-table th:nth-child(8),
  .booked-page-table td:nth-child(8) {
    width: 6%;
  }
  .goToPage {
    width: 100px;
    margin: 0 10px;
  }
</style>
