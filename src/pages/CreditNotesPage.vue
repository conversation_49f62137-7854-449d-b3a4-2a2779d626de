<template>
  <div class="flex justify-between items-center title-bar-heading">
    <title-bar style="width: 100%;" :title="$t('common.credit_notes')"/>
  </div>
  <div class="invoices_page">
    <shipvagoo-tabfilter-v1 align="left" v-model="currentFilter" :options="tabOptions"/>
    <div v-if="currentFilter=='monthly_credit_notes'">
      <monthly-credit-notes></monthly-credit-notes>
    </div>
    <div v-if="currentFilter=='credit_notes'">
      <credit-notes></credit-notes>
    </div>
  </div>
</template>
<script setup lang="ts">
  import {ref} from 'vue';
  import {ITabOption} from "../model/common.model";

  const currentFilter = ref<'monthly_credit_notes' | 'credit_notes'>('monthly_credit_notes');
  const tabOptions: ITabOption[] = [
    {
      label: 'Monthly Credit Notes',
      value: 'monthly_credit_notes',
      onClick: () => {
      },
    },
    {
      label: 'Credit Notes',
      value: 'credit_notes',
      onClick: () => {

      },
    }
  ];


</script>

<style scoped lang="scss">
  .invoices_page {
    padding-right: 40px;
    padding-left: 40px;
    margin-top: -25px;
  }
</style>

