<template>
  <div class="shipvagoo-dashboard-banner-container">
    <picture>
      <source :srcset="dashboardImageMobile" media="(max-width: 450px)" />
      <img class="shipvagoo-dashboard-banner" :src="dashboardImage" alt="" decoding="async" />
    </picture>
    <div class="shipvagoo-dashboard-banner__gradient-overlay" />
    <div class="shipvagoo-dashboard-banner__overlay">
      <div class="shipvagoo-dashboard-banner__overlay-greeting">
        {{ $t('common.hello') }} {{ $store.state.userStore.name ?? '...' }}
      </div>
      <div class="shipvagoo-dashboard-banner__overlay-shortcut_indicator">
        {{ $t('dashboard_page.shortcuts_subtext') }}
      </div>
      <div class="shipvagoo-dashboard-banner__overlay-shortcuts">
        <button
          class="shipvagoo-dashboard-banner__overlay-shortcuts-shortcut"
          @click="$router.push('/orders')"
        >
          <div class="shipvagoo-dashboard-banner__overlay-shortcuts-shortcut--prefix">
            <q-icon class="rotate-270" name="ion-ios-arrow-down" size="10px" />
          </div>
          <span>{{ $t('common.orders') }}</span>
        </button>
        <button
          class="shipvagoo-dashboard-banner__overlay-shortcuts-shortcut"
          @click="$router.push('/booked')"
        >
          <div class="shipvagoo-dashboard-banner__overlay-shortcuts-shortcut--prefix">
            <q-icon class="rotate-270" name="ion-ios-arrow-down" size="10px" />
          </div>
          <span>{{ $t('common.booked') }}</span>
        </button>
        <button class="shipvagoo-dashboard-banner__overlay-shortcuts-shortcut">
          <div class="shipvagoo-dashboard-banner__overlay-shortcuts-shortcut--prefix">
            <q-icon class="rotate-270" name="ion-ios-arrow-down" size="10px" />
          </div>
          <span>{{ $t('common.returns') }}</span>
        </button>
        <button class="shipvagoo-dashboard-banner__overlay-shortcuts-shortcut" 
                @click="$router.push('/help')">
          <div class="shipvagoo-dashboard-banner__overlay-shortcuts-shortcut--prefix">
            <q-icon class="rotate-270" name="ion-ios-arrow-down" size="10px" />
          </div>
          <span>{{ $t('common.help') }}</span>
        </button>
      </div>
    </div>
  </div>
  <div class="shipvagoo-dashboard-statistics-container">
    <div class="shipvagoo-dashboard-statistics">
      <div class="shipvagoo-dashboard-statistics-item">
        <strong>{{ $t('dashboard_page.this_week') }}</strong>
      </div>
      <div class="shipvagoo-dashboard-statistics-separator" />
      <div class="shipvagoo-dashboard-statistics-item">
        {{
          $t('dashboard_page.shipment_count', {
            count: $store.state.dashboardStore.thisWeekTotalShipmentCount,
          })
        }}
      </div>
    </div>
  </div>
  <div class="shipvagoo-dashboard-content">
    <div
      v-if="inactiveIntegrationsCount > 0 || deliveryMethodsNeedSetupCount > 0"
      class="shipvagoo-dashboard-integration_issues"
    >
      <h2 class="shipvagoo-dashboard-subtitle">Integration Issues</h2>
      <p class="shipvagoo-dashboard-paragraph">
        Address the issue, complete your setup and automate your workflow
      </p>
      <info-bar
        v-if="inactiveIntegrationsCount > 0"
        color="#FFBC01"
        dense
        class="shipvagoo-dashboard-integration_issues--info"
      >
        {{ inactiveIntegrationsCount }} inactive integrations, Please check your
        <shipvagoo-hyperlink
          type="router-link"
          to="/order-integration"
          label="order integrations"
        />.
      </info-bar>
      <info-bar
        v-if="deliveryMethodsNeedSetupCount > 0"
        color="#FFBC01"
        dense
        class="shipvagoo-dashboard-integration_issues--info"
      >
        <i18n-t keypath="dashboard_page.delivery_methods_missing_mapping">
          <template #count>
            {{ deliveryMethodsNeedSetupCount }}
          </template>
          <template #link>
            <shipvagoo-hyperlink
              type="router-link"
              to="/order-integration"
              :label="$t('common.order_integrations').toLowerCase()"
            />
          </template>
        </i18n-t>
      </info-bar>
    </div>
    <div v-if="shipments.length > 0" class="shipvagoo-dashboard-shipment_monitor">
      <h2 class="shipvagoo-dashboard-subtitle">Shipment Monitor</h2>
      <p class="shipvagoo-dashboard-paragraph">
        Help parcels on their way and be proactive in your customer service.
      </p>
      <div class="shipvagoo-dashboard-shipment_monitor--grid">
        <shipment-card
          v-for="shipment in shipments"
          :key="shipment.shipment_code"
          :shipment="shipment"
        />
      </div>
    </div>
    <div class="shipvagoo-dashboard-overview">
      <h2 class="shipvagoo-dashboard-subtitle">
        {{ $t('dashboard_page.overview') }}
      </h2>
      <bar-chart-overview />
    </div>
  </div>
</template>

<chat-bot />

<script setup lang="ts">
  import { useStore } from 'vuex';
  import { Router, useRouter } from 'vue-router';
  import { onBeforeMount, ref } from 'vue';
  import dashboardImage from '../assets/img/dashboard-image.png';
  import dashboardImageMobile from '../assets/img/dashboard-image-mobile.png';
  import { IRootState } from '../model/store.model';
  import {
    dashboardService,
    loadingService,
    loggingService,
    notificationService,
    shipmentService,
  } from '../services/_singletons';
  import { IShipment } from '../model/shipment.model';

  const $store = useStore<IRootState>();
  const $router: Router = useRouter();

  const inactiveIntegrationsCount = ref<number>(0);
  const deliveryMethodsNeedSetupCount = ref<number>(0);
  const shipments = ref<IShipment[]>([]);

  const getShipmentMonitor = async () => {
    try {
      loadingService.startLoading('main-loader:shipment-errors');
      const response = await shipmentService.getShipmentErrors(1, 4, [
        { key: 'sorting[created_at]', value: 'desc' },
      ]);
      shipments.value = response.entities;
    } catch (e) {
      notificationService.showError('Error loading shipment errors');
      loggingService.error('Error loading shipment errors', e);
    } finally {
      loadingService.stopLoading('main-loader:shipment-errors');
    }
  };

  const getInactiveIntegrations = async () => {
    try {
      loadingService.startLoading('main-loader:inactive-integrations');
      const response = await dashboardService.getIntegrationIssues();
      inactiveIntegrationsCount.value = response.inactive_integrations;
      deliveryMethodsNeedSetupCount.value = response.delivery_methods_need_setup;
    } catch (e) {
      notificationService.showError('Error loading inactive integrations');
      loggingService.error('Error loading inactive integrations', e);
    } finally {
      loadingService.stopLoading('main-loader:inactive-integrations');
    }
  };

  onBeforeMount(() => {
    getInactiveIntegrations();
    getShipmentMonitor();

    $store.dispatch('dashboardStore/getDashboardOverview');
  });
</script>

<style scoped lang="scss">
  .shipvagoo-dashboard {
    &-shipment_monitor {
      margin-bottom: 64px;

      &--grid {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr;
        gap: 20px;
        width: 100%;
        margin-top: 20px;

        @media (max-width: 1024px) {
          grid-template-columns: 1fr 1fr;
        }

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
        }

        // & > * {
        //   flex: 1;

        //   &:not(:last-child) {
        //     margin-right: 28px;
        //   }
        // }
      }
    }

    &-integration_issues {
      &--info {
        margin-top: 22px;
      }
    }

    &-content {
      display: flex;
      flex-direction: column;
      gap: 82px;
      max-width: 1200px;
      padding: 0 40px;
      margin: 0 auto;
      margin-top: 65px;
      margin-bottom: 55px;
    }

    &-subtitle {
      margin: 0;
      font-size: 30px;
      font-variation-settings: 'wght' 500;
      font-weight: 500;
      line-height: 36px;
      text-align: center;
    }

    &-paragraph {
      margin: 0;
      margin-top: 12px;
      font-size: 15px;
      line-height: 18px;
      text-align: center;
    }

    &-statistics {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      max-width: 200px;
      margin: 22px auto;

      @media (max-width: 600px) {
        flex-direction: column;
        margin: 16px auto;

        & > *:not(:last-child) {
          margin-bottom: 6px;
        }
      }

      &-container {
        border-bottom: 1px solid #2929c8;
      }

      &-item {
        font-size: 15px;
      }

      &-separator {
        width: 4px;
        height: 4px;
        background-color: #7bc9ee;
        border-radius: 50%;
        transition: transform 0.3s ease-in-out;

        @media (max-width: 600px) {
          transform: scale(0.5);
        }
      }
    }

    &-banner {
      width: 100%;
      aspect-ratio: 1280 / 221;

      @media (max-width: 1024px) {
        height: 375px;
        aspect-ratio: unset;
        object-fit: cover;
      }

      @media (max-width: $tablet) {
        aspect-ratio: 375 / 262;
      }

      &-container {
        position: relative;
        width: 100%;
      }

      &__overlay {
        position: absolute;
        top: 0;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        aspect-ratio: 1280 / 221;

        @media (max-width: 1024px) {
          height: 375px;
          aspect-ratio: unset;
        }

        @media (max-width: $tablet) {
          align-items: flex-start;
          aspect-ratio: 375 / 262;
          margin-left: 40px;
        }

        &-greeting {
          font-size: 30px;
          line-height: 36px;
        }

        &-shortcut_indicator {
          margin-top: 16px;
          font-size: 15px;
          line-height: 17px;
        }

        &-shortcuts {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          width: 100%;
          max-width: 490px;
          margin-top: 23px;

          @media (max-width: 1024px) {
            display: grid;
            flex-direction: unset;
            grid-template-columns: 1fr 1fr;
            row-gap: 20px;
            column-gap: 36px;
            justify-content: unset;
            width: unset;
          }

          &-shortcut {
            display: flex;
            flex-direction: row;
            align-items: center;
            color: #ffffff;
            cursor: pointer;
            background-color: transparent;
            border: none;
            transition: transform 0.1s ease-in-out;
            transform: scale(1) translateZ(0);
            backface-visibility: hidden;

            @media (hover: hover) {
              &:hover {
                transform: scale(1.0625) translateZ(0);
              }
            }

            &--prefix {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 24px;
              height: 24px;
              margin-right: 8px;
              border: 1px solid #ffffff;
              border-radius: 50%;
            }
          }
        }
      }

      &__gradient-overlay {
        position: absolute;
        top: 0;
        z-index: 1;
        width: 100%;
        aspect-ratio: 1280 / 221;

        background: transparent linear-gradient(101deg, #389b9970 0%, #2929c817 100%) 0% 0%
          no-repeat padding-box;

        transition: background 0.3s ease-in-out, opacity 0.3s ease-in-out;

        @media (max-width: 1024px) {
          height: 375px;
          aspect-ratio: unset;
        }

        @media (max-width: $tablet) {
          aspect-ratio: 375 / 262;
          background: #131335;
          opacity: 0.5;
        }
      }
    }
  }
</style>
