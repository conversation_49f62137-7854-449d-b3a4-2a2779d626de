<template>
  <Title title="Pick Settings" />
  <div class="pick_settings-page_container">
<!--    <info-bar dense>
      Read more about the different pick options
      <shipvagoo-hyperlink type="external-link" to="#" label="here." />
    </info-bar>-->
    <div class="pick_settings-inputs_container">
      <div class="responsive-double-input">
        <shipvagoo-select
          v-model="selectedPrimaryAction"
          :options="primaryActionsOptions"
          label="Primary action"
        ></shipvagoo-select>
        <shipvagoo-select
          v-model="selectedPickSort"
          :options="pickSortOptions"
          label="Pick Sorting"
        ></shipvagoo-select>
      </div>
      <div class="pick_settings-pick_path_label">Pick path</div>
      <div class="pick_settings-checkboxes_container">
        <div class="pick_settings-checkboxes_container-checkbox_group">
          <shipvagoo-checkbox v-model="startAtFirstItem">
            Start automatically at first item
          </shipvagoo-checkbox>
          <shipvagoo-checkbox v-model="confirmPickAmountObtained">
            Automatically confirm when pick amount is obtained
          </shipvagoo-checkbox>
          <shipvagoo-checkbox v-model="finishWhenItemsPicked">
            Finish automatically when all items are picked
          </shipvagoo-checkbox>
          <shipvagoo-checkbox v-if="false" v-model="playSoundWhenScanning">
            Play sound when scanning
          </shipvagoo-checkbox>
          <shipvagoo-checkbox v-if="false" v-model="vibrateWhenScanning">
            Vibrate when scanning (Android only)
          </shipvagoo-checkbox>
          <shipvagoo-checkbox v-model="choosePackaging"> Choose packaging </shipvagoo-checkbox>
        </div>
        <div class="pick_settings-checkboxes_container-checkbox_group">
          <shipvagoo-checkbox v-model="warnInCaseOfPartialPick">
            Warn in case of partial pick
          </shipvagoo-checkbox>
          <shipvagoo-checkbox v-model="automaticallyGoToNextItem">
            Automatically go to the next item
          </shipvagoo-checkbox>
          <shipvagoo-checkbox v-model="showItemImageOnList">
            Show item image on list
          </shipvagoo-checkbox>
          <shipvagoo-checkbox v-model="useCameraOnMobileDevices">
            Use camera on mobile devices
          </shipvagoo-checkbox>
          <shipvagoo-checkbox v-if="false" v-model="skipSummaryIfPossible">
            Skip summary if possible
          </shipvagoo-checkbox>
        </div>
      </div>
      <div v-if="false" class="pick_settings-voice_options">
        <div class="pick_settings-voice_options-inputs">
          <shipvagoo-select
            v-model="selectedVoiceCommand"
            :options="voiceCommandOptions"
            label="Use voice commands"
          />
          <shipvagoo-checkbox v-model="sayTheItemName">Say the item name</shipvagoo-checkbox>
        </div>
        <div></div>
      </div>
      <div class="pick_settings-pick_path_label">Picking list</div>
      <div class="pick_settings-pick_list_options">
        <shipvagoo-checkbox v-model="useCameraForScanningPickLists">
          Use camera for scanning picking lists
        </shipvagoo-checkbox>
      </div>
      <div class="pick_settings-pick_path_label">Completing picks</div>
      <div class="pick_settings-checkboxes_container">
        <div class="pick_settings-checkboxes_container-checkbox_group">
          <shipvagoo-checkbox v-model="createFulfillment">Create fulfillment</shipvagoo-checkbox>
          <shipvagoo-checkbox v-model="printShippingLabel">Print shipping label</shipvagoo-checkbox>
          <shipvagoo-checkbox v-model="printPackingSlip">Print packing slip</shipvagoo-checkbox>
        </div>
        <div class="pick_settings-checkboxes_container-checkbox_group">
          <shipvagoo-checkbox v-model="createShipment">Create Shipment</shipvagoo-checkbox>
          <shipvagoo-checkbox v-model="printProformaInvoice">
            Print proforma invoice
          </shipvagoo-checkbox>
          <shipvagoo-checkbox v-model="capturePayment">Capture payment</shipvagoo-checkbox>
        </div>
      </div>
      <div class="pick_settings-footer">
        <shipvagoo-button class="save_button" label="Save" min-width="120px" @click="updatePickSettings" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onBeforeMount, Ref, ref } from 'vue';
  import { useStore } from 'vuex';
  import { find } from 'lodash-es';
  import Title from '../components/title-bar.vue';
  import {
    loadingService,
    loggingService,
    notificationService,
    pickSettingsService,
  } from '../services/_singletons';
  import { IOption } from '../model/common.model';
  import { IRootState } from '../model/store.model';

  const $store = useStore<IRootState>();

  const primaryActionsOptions: IOption<string, 'StartPickPath' | 'CreatePickPathList'>[] = [
    {
      label: 'Create pick path list',
      value: 'CreatePickPathList',
    },
    {
      label: 'Start Pick Path',
      value: 'StartPickPath',
    },
  ];

  const selectedPrimaryAction: Ref<IOption<string, 'StartPickPath' | 'CreatePickPathList'>> = ref(
    primaryActionsOptions[0],
  );

  const pickSortOptions: IOption<string, 'Bin' | null>[] = [
    { label: 'Bin', value: 'Bin' },
    { label: 'None', value: null },
  ];

  const selectedPickSort: Ref<IOption<string, 'Bin' | null>> = ref(pickSortOptions[0]);

  const voiceCommandOptions: IOption<string, number | null>[] = [
    {
      label: 'None',
      value: null,
    },
    {
      label: 'English',
      value: 2693479080,
    },
    {
      label: 'Danish',
      value: 1427052798,
    },
  ];

  const startAtFirstItem: Ref<boolean> = ref<boolean>(false);
  const confirmPickAmountObtained: Ref<boolean> = ref<boolean>(false);
  const finishWhenItemsPicked: Ref<boolean> = ref<boolean>(false);
  const playSoundWhenScanning: Ref<boolean> = ref<boolean>(false);
  const vibrateWhenScanning: Ref<boolean> = ref<boolean>(false);
  const choosePackaging: Ref<boolean> = ref<boolean>(false);
  const warnInCaseOfPartialPick: Ref<boolean> = ref<boolean>(false);
  const automaticallyGoToNextItem: Ref<boolean> = ref<boolean>(false);
  const showItemImageOnList: Ref<boolean> = ref<boolean>(false);
  const useCameraOnMobileDevices: Ref<boolean> = ref<boolean>(false);
  const skipSummaryIfPossible: Ref<boolean> = ref<boolean>(false);
  const sayTheItemName: Ref<boolean> = ref<boolean>(false);
  const useCameraForScanningPickLists: Ref<boolean> = ref<boolean>(false);
  const createFulfillment: Ref<boolean> = ref<boolean>(false);
  const printShippingLabel: Ref<boolean> = ref<boolean>(false);
  const printPackingSlip: Ref<boolean> = ref<boolean>(false);
  const createShipment: Ref<boolean> = ref<boolean>(false);
  const printProformaInvoice: Ref<boolean> = ref<boolean>(false);
  const capturePayment: Ref<boolean> = ref<boolean>(false);

  const selectedVoiceCommand = ref(voiceCommandOptions[0]);

  const syncSettings = async () => {
    loadingService.startLoading('main-loader:sync-settings');
    await $store.dispatch('pickSettingsStore/syncPickSettings');

    startAtFirstItem.value = $store.state.pickSettingsStore.start_at_first_item;
    confirmPickAmountObtained.value = $store.state.pickSettingsStore.confirm_pick_amount_obtained;
    finishWhenItemsPicked.value = $store.state.pickSettingsStore.finish_when_items_picked;
    playSoundWhenScanning.value = $store.state.pickSettingsStore.play_sound;
    vibrateWhenScanning.value = $store.state.pickSettingsStore.vibrate_when_scanning;
    choosePackaging.value = $store.state.pickSettingsStore.choose_packaging;
    warnInCaseOfPartialPick.value = $store.state.pickSettingsStore.warn_partial_pick;
    automaticallyGoToNextItem.value = $store.state.pickSettingsStore.go_to_next_item;
    showItemImageOnList.value = $store.state.pickSettingsStore.show_item_image;
    useCameraOnMobileDevices.value = $store.state.pickSettingsStore.use_camera_on_mobile_devices;
    skipSummaryIfPossible.value = $store.state.pickSettingsStore.skip_summary;
    sayTheItemName.value = $store.state.pickSettingsStore.say_item_name;
    useCameraForScanningPickLists.value =
      $store.state.pickSettingsStore.user_camera_on_mobile_devices;
    createFulfillment.value = $store.state.pickSettingsStore.create_fulfillment;
    printShippingLabel.value = $store.state.pickSettingsStore.print_shipping_label;
    printPackingSlip.value = $store.state.pickSettingsStore.print_packing_slip;
    createShipment.value = $store.state.pickSettingsStore.create_shipment;
    printProformaInvoice.value = $store.state.pickSettingsStore.print_proforma_invoice;
    capturePayment.value = $store.state.pickSettingsStore.capture_payment;
    selectedPrimaryAction.value = find(primaryActionsOptions, {
      value: $store.state.pickSettingsStore.primary_action,
    }) as IOption<string, 'StartPickPath' | 'CreatePickPathList'>;
    selectedPickSort.value = find(pickSortOptions, {
      value: $store.state.pickSettingsStore.pick_sorting,
    }) as IOption<string, 'Bin' | null>;
    selectedVoiceCommand.value = find(voiceCommandOptions, {
      value: $store.state.pickSettingsStore.language_code,
    }) as IOption<string, number>;

    loadingService.stopLoading('main-loader:sync-settings');
  };

  const updatePickSettings = async () => {
    try {
      loadingService.startLoading('main-loader:update-pick-settings');
      await pickSettingsService.savePickSettings({
        start_at_first_item: startAtFirstItem.value,
        confirm_pick_amount_obtained: confirmPickAmountObtained.value,
        finish_when_items_picked: finishWhenItemsPicked.value,
        play_sound: playSoundWhenScanning.value,
        vibrate_when_scanning: vibrateWhenScanning.value,
        choose_packaging: choosePackaging.value,
        warn_partial_pick: warnInCaseOfPartialPick.value,
        go_to_next_item: automaticallyGoToNextItem.value,
        show_item_image: showItemImageOnList.value,
        use_camera_on_mobile_devices: useCameraOnMobileDevices.value,
        skip_summary: skipSummaryIfPossible.value,
        say_item_name: sayTheItemName.value,
        user_camera_on_mobile_devices: useCameraForScanningPickLists.value,
        create_fulfillment: createFulfillment.value,
        print_shipping_label: printShippingLabel.value,
        print_packing_slip: printPackingSlip.value,
        create_shipment: createShipment.value,
        print_proforma_invoice: printProformaInvoice.value,
        capture_payment: capturePayment.value,
        primary_action: selectedPrimaryAction.value.value,
        pick_sorting: selectedPickSort.value.value,
        language_code: selectedVoiceCommand.value.value,
      });
      syncSettings();

      notificationService.showSuccess('Pick settings updated successfully');
    } catch (e) {
      loggingService.error('Error updating pick settings', e);
      notificationService.showError('Error updating pick settings');
    } finally {
      loadingService.stopLoading('main-loader:update-pick-settings');
    }
  };

  onBeforeMount(() => {
    syncSettings();
  });
</script>

<style lang="scss" scoped>
  .responsive-double-input {
    display: flex;
    flex-direction: row;
    gap: 20px;
    align-items: center;
    width: 100%;

    & > * {
      flex: 1;
    }

    @media (max-width: $tablet) {
      flex-direction: column;

      & > * {
        flex: unset;
        width: 100%;
      }
    }
  }
  .pick_settings {
    &-page_container {
      padding: 20px 40px;

      @media (max-width: $tablet) {
        padding: 20px 10px;
      }
    }

    &-inputs_container {
      padding: 20px;
      margin-top: 25px;
      border: 1px solid #2929c8;
      border-radius: 5px;
    }

    &-pick_path_label {
      padding-bottom: 4px;
      margin-top: 35px;
      font-size: 16px;
      line-height: 19px;
      border-bottom: 1px dashed #e1e1e1;
    }

    &-checkboxes_container {
      display: flex;
      flex-direction: row;
      width: 100%;
      margin-top: 20px;

      & > * {
        flex: 1;
      }

      @media (max-width: $tablet) {
        flex-direction: column;
        gap: 20px;
      }

      &-checkbox_group {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }
    }

    &-voice_options {
      margin-top: 20px;

      &-inputs {
        display: flex;
        flex-direction: column;
        gap: 20px;

        width: calc(50% - 10px);

        @media (max-width: $tablet) {
          width: 100%;
        }
      }
    }

    &-pick_list_options {
      margin-top: 20px;
    }

    &-footer {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      padding: 20px;
      margin-top: 20px;
      margin-right: -20px;
      margin-bottom: -20px;
      margin-left: -20px;
      border-top: 1px solid #2929c8;

      @media (max-width: $tablet) {
        & > * {
          width: 100%;
        }
      }
    }
  }
</style>