<template>
  <div class="q-pa-md">
    <div class="row login-page">
      <div class="col box-left flex items-center justify-center">
        <div style="width: 100%;" class="flex justify-center">
          <img style="width: 50%;" class="image-logo" :src="LoginLogo" alt="logo-left" />
          <div class="text-center full-width">
            <p class="text-white" style="font-size: 40px;text-align: center; white-space: nowrap">
              Gør shipping nemt og enkelt
            </p>
          </div>
        </div>
      </div>
      <div class="col flex items-center justify-center box-right">
        <div class="wrapper" style="width: 65%;">
          <h4 class="text-black text-bold" style="margin: 0;">Login to your account</h4>
          <form @submit.prevent="submit" class="q-mt-lg">
            <q-input
              class="Username"
              autocomplete="off"
              label="Username"
              label-color="gray"
              color="gray"
              :rules="[ (val, rules) => rules.email(val) || 'Please enter a valid email address' ]"
              v-model="state.form.email">
            </q-input>
            <q-input
              autocomplete="off"
              class="q-mt-lg password"
              label="Password"
              type="password"
              label-color="gray"
              color="gray"
              :rules="[ val => val.length >=8 || 'Password must be at least 8 characters in length.' ]"
              v-model="state.form.password">
            </q-input>
            <shipvagoo-hyperlink
              class="q-mt-lg"
              to="/forgot-password"
              type="router-link"
              label="Forgot Password?"
            />

            <shipvagoo-checkbox
              v-model="state.form.isRememberMe"
              class="login_page-remember_me q-mt-lg"
              label="Remember Me"
            ></shipvagoo-checkbox>
            <shipvagoo-button
              class="login_page-login_button q-mt-lg"
              type="submit"
              label="Login"
            ></shipvagoo-button>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onBeforeMount, reactive } from "vue";
import useValidate from "@vuelidate/core";
import { required, helpers, email } from "@vuelidate/validators";
import { RouteLocationNormalizedLoaded, Router, useRoute, useRouter } from "vue-router";
import { AxiosError } from "axios";
import {
  authService,
  loadingService,
  loggingService,
  notificationService
} from "../services/_singletons";
import LoginLogo from "../assets/img/logo_high_res.svg";

const state = reactive({
  form: {
    email: "",
    password: "",
    isRememberMe: false
  }
});
const v$ = useValidate(
  {
    form: {
      email: {
        required: helpers.withMessage("This field is required.", required),
        email: helpers.withMessage("This email not using", email)
      },
      password: { required: helpers.withMessage("This field is required.", required) },
      isRememberMe: {}
    }
  },
  state
);

const $router: Router = useRouter();
const $route: RouteLocationNormalizedLoaded = useRoute();
const submit = async () => {
  await v$.value.$validate();
  if (v$.value.$error) {
    return;
  }

  try {
    loadingService.startLoading("main-loader:login");
    await authService.login(state.form.email, state.form.password, state.form.isRememberMe);
    await $router.push($route.query.redirect_to !== undefined ? `${$route.query.redirect_to}` : "/");
  } catch (e) {
    if (e instanceof AxiosError && e.response?.status === 422) {
      notificationService.showError("Invalid email or password.");
      return;
    }


    loggingService.error("Error during login", e);
    notificationService.showError("Error during login");
  } finally {
    loadingService.stopLoading("main-loader:login");
  }
};


onBeforeMount(() => {
  if ($route.query.error_message) notificationService.showError($route.query.error_message.toString());
});
</script>
<style lang="scss" scoped>
.label-inputs {
  color: #757575;
}

.invalid-feedback {
  // display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #dc3545;
}

.d-none {
  display: none;
}


.login-page {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 0 !important;

  .wrapper {
    box-sizing: border-box;
    width: 100%;
    padding: 10px;
  }

  .box-left {
    background-image: url("../assets/img/login2.png");
    background-position: 50% 50%;
    background-size: cover;
  }

  .box-right {
    background: white;
  }
}

@media (max-width: 1200px) {
  .login-page {
    .box-left {
      .wrapper {
        .h-big {
          font-size: 50px;
        }
      }
    }
  }
}

@media (max-width: 900px) {
  .login-page {
    .box-left {
      display: none;

      .wrapper {
        .h-big {
          font-size: 30px;
        }
      }
    }
  }
}


.text-white {
  color: white
}
</style>
