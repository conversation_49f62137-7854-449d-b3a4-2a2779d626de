<template>
  <div class="flex justify-between items-center title-bar-heading q-mt-md">
    <div>
      <title-bar
        style="width: 100%"
        title="Returns"
        subtitle="An overview of all returns created through return portal."
      />
    </div>
    <big-search
      placeholder="Search for shipment ID or recipient"
      @update:modelValue="searchVal"
      v-model="search"
      class="big_search"
    />
  </div>
  <div
    class="return_portal_shipments_page q-mt-xs"
    v-if="$store.state.returnPortalStore.count <= 0 && loadingService.loadings.size == 0"
  >
    <shipvagoo-placeholder
      text="No return portal is setup. Ensure smooth returns and enhance customer satisfaction by setting up a return portal"
    >
      <template #actions>
        <shipvagoo-button min-width="170px" to="return-portals?add-return-portal=1">
          Setup return portal
        </shipvagoo-button>
      </template>
    </shipvagoo-placeholder>
  </div>
  <div class="return_portal_shipments_page">
    <div class="return_portal_shipments_page-table">
      <shipvagoo-table-v1
        class="return-portal-shipments-page-table"
        v-model:pagination="pagination"
        :rows="rows"
        :total="pagination.rowsNumber"
        :columns="columns"
        :loading="loadingService.loadings.has('main-loader:loading-shipments')"
        no-data-label="No returns are available"
        @row-click="onClickRow"
        @request="getReturnShipments($event.pagination)"
      >
        <template #body="props">
          <q-tr
            :props="props"
            :class="{
              'return_portal_shipments_page-table-row--is_cancelled': props.row.is_cancelled,
            }"
            style="cursor: pointer"
            @click="onClickRow($event, props.row)"
          >
            <q-td key="shipment_code" class="returns-shipment-id">
              {{ props.row.shipment_code }}
            </q-td>
            <q-td key="created_at">
              {{ dayjs(String(props.row.created_at)).format('DD/MM/YYYY') }}<br />
              {{ dayjs(String(props.row.created_at)).format('HH:mm') }}
            </q-td>
            <q-td key="id">
              {{ props.row.id.toString().padStart(6, '0') }}
            </q-td>
            <q-td key="sender">
              <span class="text-no-wrap"
                >{{ props.row.sender.firstName }} {{ props.row.sender.lastName }}</span
              ><br />
              <span class="text-no-wrap">{{ props.row.sender.addressLine1 }}</span
              ><br />
              <span class="text-no-wrap"
                >{{ props.row.sender.country }}-{{ props.row.sender.postCode }}
                {{ props.row.sender.city }}</span
              >
            </q-td>
            <q-td key="recipient">
              <span class="text-no-wrap"
                >{{ props.row.recipient.firstName }} {{ props.row.recipient.lastName }}</span
              ><br />
              <span class="text-no-wrap">{{ props.row.recipient.addressLine1 }}</span
              ><br />
              <span class="text-no-wrap"
                >{{ props.row.recipient.country }}-{{ props.row.recipient.postCode }}
                {{ props.row.recipient.city }}</span
              >
            </q-td>

            <q-td key="tracking_id" class="returns-tracking-id" @click.stop>
              <div
                v-for="(parcel, index) in props.row.parcels"
                :key="`parcel_${index}`"
                class="text-no-wrap"
              >
                <template v-if="index < 3">
                  <shipvagoo-hyperlink
                    v-if="
                      CarrierService.getCarrierFromShippingProductCode(
                        $store.state.courierStore.carriers,
                        $store.state.courierStore.carrierProducts,
                        props.row.carrier_product_code,
                      )?.carrier_id?.toLowerCase()
                    "
                    type="external-link"
                    text-decoration="none"
                    :to="
                      CarrierService.getCarrierFromShippingProductCode(
                        $store.state.courierStore.carriers,
                        $store.state.courierStore.carrierProducts,
                        props.row.carrier_product_code,
                      )?.tracking_url + parcel.carrierReference
                    "
                    :label="parcel.carrierReference"
                    blank
                    noopener
                    noreferer
                  />
                  <div v-else>
                    {{ parcel.carrierReference }}
                  </div>
                </template>
              </div>
              <template v-if="props.row.parcels.length > 3">
                <q-tooltip
                  v-html=" props.row.parcels.map((parcel:IParcel) => parcel.carrierReference).join('<br/>')"
                >
                </q-tooltip>
              </template>
            </q-td>
            <!--            <q-td key="country" :props="props">
                          <div v-if="props" style="display: flex; gap: 10px; justify-content: flex-start">
                            <img
                              decoding="async"
                              :src="`/assets/img/${props.row.sender?.country?.toLowerCase?.() ?? ''}.svg`"
                              style="width: 20px; height: 20px; border-radius: 50%; object-fit: cover"
                            />
                            {{ props.row.sender.city }}
                          </div>
                        </q-td>-->

            <q-td key="via" :props="props">
              <div v-if="props">
                <div
                  class="flex items-center no-wrap"
                  v-if="
                    CarrierService.getCarrierFromShippingProductCode(
                      $store.state.courierStore.carriers,
                      $store.state.courierStore.carrierProducts,
                      props.row.carrier_product_code,
                    )?.carrier_id?.toLowerCase()
                  "
                >
                  <div
                    class="shipvagoo-table-carrier-logo"
                    :style="{
                      backgroundImage:
                        'url(' +
                        CarrierService.getCarrierFromShippingProductCode(
                          $store.state.courierStore.carriers,
                          $store.state.courierStore.carrierProducts,
                          props.row.carrier_product_code,
                        )?.icon +
                        ')',
                    }"
                  />
                  <div class="text-no-wrap q-ml-sm shipped-carrier">
                    {{
                      CarrierService.getCarrierFromShippingProductCode(
                        $store.state.courierStore.carriers,
                        $store.state.courierStore.carrierProducts,
                        props.row.carrier_product_code,
                      )?.name
                    }}
                  </div>
                </div>
              </div>
            </q-td>
            <q-td key="status" :props="props">
              <shipvagoo-booking-status
                :shipment="props.row"
                :is-stop-shipping-monitoring="Boolean(props.row.stop_monitoring)"
                :is-marked-as-delivered="Boolean(props.row.marked_as_delivered)"
              />
            </q-td>
            <!--            <q-td key="refunded" :props="props">-->
            <!--              <div style="display: flex; flex-direction: row; gap: 8px; align-items: center">-->
            <!--                <div-->
            <!--                  style="width: 12px; height: 12px; border-radius: 50%"-->
            <!--                  :style="{ backgroundColor: isRefunded(props.row.refunded) ? 'green' : 'red' }"-->
            <!--                />-->
            <!--&lt;!&ndash;                <div>{{ !isRefunded(props.row.refunded) ? $t('common.no') : $t('common.yes') }}</div>&ndash;&gt;-->
            <!--              </div>-->
            <!--              <div></div>-->

            <!--            </q-td>-->
            <q-td key="more" :props="props" class="more">
              <q-btn
                dense
                flat
                round
                style="color: #1f2124"
                class="more-action"
                field="edit"
                icon="more_horiz"
              >
                <shipvagoo-menu style="width: 160px">
                  <shipvagoo-menu-item
                    v-if="!props.row.is_cancelled"
                    clickable
                    @click="onClickDownloadLabel(props.row)"
                  >
                    {{ $t('common.download') }}
                  </shipvagoo-menu-item>

                  <shipvagoo-menu-item v-if="!props.row.is_cancelled" clickable has-caret>
                    {{ $t('common.print_at') }}

                    <shipvagoo-menu anchor="top end" style="width: 160px">
                      <shipvagoo-menu-item v-if="$store.state.printerStore.printers.length <= 0">
                        {{ $t('common.no_printer_is_set') }}
                      </shipvagoo-menu-item>
                      <shipvagoo-menu-item
                        v-for="printer in $store.state.printerStore.printers"
                        :key="`printer-${printer.printer_code}`"
                        clickable
                        @click="onClickPrintLabel(props.row, printer)"
                      >
                        {{ printer.name }}
                      </shipvagoo-menu-item>
                    </shipvagoo-menu>
                  </shipvagoo-menu-item>
                  <!--                  <shipvagoo-menu-item v-if="!props.row.is_cancelled" clickable>-->
                  <!--                    {{ $t('common.print') }}-->
                  <!--                  </shipvagoo-menu-item>-->
                  <!--                  <shipvagoo-menu-item v-if="!props.row.is_cancelled" clickable has-caret>-->
                  <!--                    {{ $t('common.email_to') }}-->

                  <!--                    <shipvagoo-menu anchor="top end" style="width: 160px">-->
                  <!--                      <shipvagoo-menu-item clickable @click="onClickEmailToSenderMail(props.row)">-->
                  <!--                        {{ $t('return_portal_shipments_page.sender_email') }}-->
                  <!--                      </shipvagoo-menu-item>-->
                  <!--                      <shipvagoo-menu-item clickable @click="onClickEmailToOptionalMail">-->
                  <!--                        {{ $t('return_portal_shipments_page.optional_email') }}-->
                  <!--                      </shipvagoo-menu-item>-->
                  <!--                    </shipvagoo-menu>-->
                  <!--                  </shipvagoo-menu-item>-->
                  <!--      <shipvagoo-separator v-if="!props.row.is_cancelled"/>
                    <shipvagoo-menu-item
                        v-if="!props.row.is_cancelled && !Boolean(props.row.marked_as_delivered)"
                        clickable
                        @click="onClickMarkAsDelivered(props.row)"
                      >
                        {{ $t('booked_page.mark_as_delivered') }}
                      </shipvagoo-menu-item>-->
                  <!--                  <shipvagoo-menu-item-->
                  <!--                    v-if="!props.row.is_cancelled || Boolean(props.row.marked_as_delivered)"-->
                  <!--                    clickable-->
                  <!--                    @click="onClickStopShippingMonitoring(props.row)"-->
                  <!--                  >-->
                  <!--                    {{ $t('booked_page.stop_shipment_monitor') }}-->
                  <!--                  </shipvagoo-menu-item>-->
                  <!--                  <shipvagoo-separator v-if="!props.row.is_cancelled"/>-->
                  <!--                  <shipvagoo-menu-item clickable @click="onClickCreateReturn(props.row)">-->
                  <!--                    {{ $t('booked_page.create_return') }}-->
                  <!--                  </shipvagoo-menu-item>-->
                  <shipvagoo-separator />
                  <!--                  <shipvagoo-menu-item v-if="!props.row.refunded" clickable @click="onClickCreateRefund(props.row)">-->
                  <!--                    {{ $t('booked_page.refund') }}-->
                  <!--                  </shipvagoo-menu-item>-->
                  <shipvagoo-menu-item
                    v-if="props.row.return_order?.is_return_fulfilled != 1"
                    clickable
                    @click="onClickOpenOrderDetails(props.row, true)"
                  >
                    {{ $t('booked_page.create_return_refund') }}
                  </shipvagoo-menu-item>
                  <shipvagoo-menu-item clickable @click="onClickOpenOrderDetails(props.row)">
                    {{ $t('booked_page.order_details') }}
                  </shipvagoo-menu-item>
                </shipvagoo-menu>
              </q-btn>
            </q-td>
          </q-tr>
        </template>
      </shipvagoo-table-v1>
    </div>
  </div>

  <q-dialog v-if="selectedBooking" v-model="isBookedDetailOpen">
    <shipment-detail
      @downloadLabel="onClickDownloadLabel"
      @printLabel="onPrintLabel"
      :shipment="selectedBooking"
      no-invoice
    />
  </q-dialog>

  <q-dialog v-model="isEmailLabelOpen" @before-hide="onCloseEmailShippingLabel">
    <shipvagoo-modal title="E-mail shipping label" size="md">
      <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
        <shipvagoo-input v-model="emailShippingLabelForm.name" label="Name" />
        <shipvagoo-input v-model="emailShippingLabelForm.email" label="Email" />
        <shipvagoo-select
          v-model="emailShippingLabelForm.pageSize"
          label="Page size"
          :options="printSelectOptions"
        />
      </shipvagoo-card-section>
      <shipvagoo-separator />
      <shipvagoo-card-actions style="display: flex; justify-content: space-between">
        <shipvagoo-button v-close-popup label="Cancel" min-width="120px" outline />
        <shipvagoo-button
          min-width="120px"
          :disable="!emailShippingLabelForm.name || !emailShippingLabelForm.email"
          label="Send"
          @click="() => {}"
        />
      </shipvagoo-card-actions>
    </shipvagoo-modal>
  </q-dialog>
  <!--<q-dialog
    v-model="orderModal"
    class="orders_page-modal"
  >
    <order-detail
      v-if="selectedOrder"
      :row="selectedOrder"
    />
  </q-dialog>-->
</template>

<script setup lang="ts">
  import { computed, onBeforeMount, Ref, ref } from 'vue';
  import { QTableProps } from 'quasar';
  import dayjs from 'dayjs';
  import { useI18n } from 'vue-i18n';
  import {
    loadingService,
    loggingService,
    notificationService,
    orderService,
    printJobService,
    shipmentService,
  } from '../services/_singletons';
  import { IParcel, IShipment } from '../model/shipment.model';
  import WindowService from '../services/window.service';
  import HelperService from '../services/helper.service';
  import { IOption, Pagination } from '../model/common.model';
  import { IRequestParam } from '../model/http.model';
  import CarrierService from '../services/carrier.service';
  import { useStore } from '../store';
  import { IPrinter } from '../model/printer.model';
  //import {IOrder} from "../model/order.model";
  import { useRouter } from 'vue-router';

  const search: Ref<string> = ref('');
  const isBookedDetailOpen = ref<boolean>(false);
  const isEmailLabelOpen = ref<boolean>(false);
  const selectedBooking: Ref<IShipment | null> = ref(null);
  const printSelectOptions = [
    { label: 'A4', value: 'A4' },
    { label: '10x19cm', value: '10x19cm' },
  ];
  let windowRef: Window | null;

  const $store = useStore();
  const { t } = useI18n();
  const router = useRouter();
  // const isRefunded = (refunded: number) => {
  //   if(refunded === 1){
  //     return true;
  //   }
  //   return false;
  //
  // };
  //const selectedOrder: Ref<IOrder | undefined> = ref();
  //const orderModal = ref<boolean>(false);
  const columns = computed((): QTableProps['columns'] => [
    {
      name: 'shipment_code',
      label: 'Shipment ID',
      field: (row: IShipment) => row.shipment_code,
      align: 'left',
      sortable: false,
    },
    {
      name: 'created_at',
      align: 'left',
      sortable: false,
      label: 'Date',
      field: (row: IShipment) => dayjs(String(row.created_at)).format('DD/MM/YYYY HH:mm:ss'),
    },
    {
      name: 'id',
      label: 'Return ID',
      field: (row: IShipment) => row.id,
      align: 'left',
      sortable: false,
    },
    {
      name: 'sender',
      label: 'Sender',
      align: 'left',
      field: 'sender',
      sortable: false,
    },
    {
      name: 'recipient',
      label: 'Recipient',
      align: 'left',
      field: 'recipient',
      sortable: false,
    },
    {
      name: 'tracking_id',
      label: 'Tracking ID',
      align: 'left',
      field: 'tracking_id',
      sortable: false,
    },
    /* {
     name: 'country',
     label: `${t('common.country')} / ${t('common.city')}`,
     align: 'left',
     sortable: false,
     field: (row: IShipment) => `${row.sender.country} / ${row.sender.city}`,
   },*/

    {
      name: 'via',
      align: 'left',
      label: t('common.carrier'),
      field: () => '',
      sortable: false,
    },
    {
      name: 'status',
      align: 'left',
      label: t('common.status'),
      field: () => '',
      sortable: false,
    },
    // {
    //   name: 'refunded',
    //   align: 'left',
    //   align: 'left',
    //   label: t('common.refunded'),
    //   field: () => '',
    //   sortable: false,
    // },
    {
      name: 'more',
      align: 'center',
      label: 'Action',
      field: 'more',
      sortable: false,
    },
  ]);

  const rows = ref<IShipment[]>([]);
  const pagination: Ref<Pagination> = ref({
    page: 1,
    rowsPerPage: 10,
    rowsNumber: 0,
    descending: true,
  });

  interface IEmailShippingLabelForm {
    name: string | null;
    email: string | null;
    pageSize: IOption<string, string>;
  }

  /** this is to show order details inside this page **/
  /*const getOrder = async (orderId: number) => {
  try {
    loadingService.startLoading('main-loader:get-order');
    selectedOrder.value = await orderService.getOrder(orderId);
    orderModal.value = true;
  } catch (e) {
    loggingService.error('Error while getting order', e);
    notificationService.showError('Error while getting order');
  } finally {
    loadingService.stopLoading('main-loader:get-order');
  }
};*/
  const emailShippingLabelForm = ref<IEmailShippingLabelForm>({
    name: null,
    email: null,
    pageSize: { label: 'A4', value: 'A4' },
  });

  // const onClickEmailToSenderMail = (row: IShipment) => {
  //   emailShippingLabelForm.value.name = `${row.sender.firstName} ${row.sender.lastName}`;
  //   emailShippingLabelForm.value.email = row.sender.contactInfo.email;
  //   isEmailLabelOpen.value = true;
  // };

  // const onClickEmailToOptionalMail = () => {
  //   isEmailLabelOpen.value = true;
  // };

  const onCloseEmailShippingLabel = () => {
    emailShippingLabelForm.value.name = null;
    emailShippingLabelForm.value.email = null;
    emailShippingLabelForm.value.pageSize = { label: 'A4', value: 'A4' };
  };
  const searchVal = (value: string) => {
    search.value = value;
    getReturnShipments(pagination.value);
  };
  const getReturnShipments = async (tablePagination: Pagination | null) => {
    try {
      loadingService.startLoading('main-loader:loading-shipments');
      const page = tablePagination?.page || 1;
      const limit = tablePagination?.rowsPerPage || 10;

      const params: IRequestParam[] = [
        {
          key: 'with',
          value: 'shipmentEvents,fulfillments,returnOrder,returnOrder.resolution',
        },
      ];

      if (search.value && search.value !== '') {
        params.push({ key: 'name[filter]', value: search.value });
      }

      if (tablePagination?.sortBy) {
        const sortingOrder = tablePagination.descending ? 'desc' : 'asc';
        params.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
      } else {
        params.push({ key: 'sorting[created_at]', value: 'desc' });
      }

      const shipments = await shipmentService.getReturnPortalShipments(page, limit, params);

      pagination.value.page = page;
      pagination.value.rowsPerPage = limit;
      pagination.value.rowsNumber = shipments.total;
      pagination.value.sortBy = tablePagination?.sortBy;
      pagination.value.descending = tablePagination?.descending;

      rows.value = shipments.entities;
    } catch (e) {
      loggingService.error('Error loading shipments', e);
      notificationService.showError('Error loading shipments');
    } finally {
      loadingService.stopLoading('main-loader:loading-shipments');
    }
  };

  /*const onClickMarkAsDelivered = async (row: IShipment) => {
  try {
    loadingService.startLoading('main-loader:mark-as-delivered');
    await shipmentService.markAsDelivered(row.shipment_code);
    notificationService.showSuccess('Shipment marked as delivered');
    getReturnShipments(pagination.value);
  } catch (e) {
    loggingService.error('Error while marking as delivered', e);
    notificationService.showError('Error while marking as delivered');
  } finally {
    loadingService.stopLoading('main-loader:mark-as-delivered');
  }
};*/

  // const onClickCreateReturn = async (row: IShipment) => {
  //   try {
  //     loadingService.startLoading('main-loader:create-return-shipment');
  //     const shipment = await shipmentService.createReturnShipment(row.shipment_code);
  //     const calculatedCharge = await shipmentService.calculateShipmentCharge(
  //       shipment.shipment_code,
  //     );
  //     notificationService.showSuccess(
  //       `Created return for shipment ${
  //         row.shipment_code
  //       } successfully for ${PriceService.formatPrice(calculatedCharge.totals.after_vat.amount)}`,
  //     );
  //     $store.dispatch('companyStore/syncCompanyData');
  //     getReturnShipments(null);
  //   } catch (e) {
  //     loggingService.error('Error while creating return shipment', e);
  //     notificationService.showError('Error while creating return shipment');
  //   } finally {
  //     loadingService.stopLoading('main-loader:create-return-shipment');
  //   }
  // };

  const onClickOpenOrderDetails = (row: IShipment, createReturn: boolean = false) => {
    //window.location.href = "/orders?id=" + row.return_order?.order_code + "&return_order_code=" + row.return_order_code;
    const query = {
      id: row.return_order?.order_code,
      ...(createReturn && { return_order_code: row.return_order_code }),
    };

    router.push({
      path: `/orders`,
      query: query,
    });
  };

  // const onClickStopShippingMonitoring = async (row: IShipment) => {
  //   try {
  //     loadingService.startLoading('main-loader:stop-monitoring');
  //     await shipmentService.stopMonitoring(row.shipment_code);
  //     notificationService.showSuccess('Shipment monitoring stopped');
  //     getReturnShipments(pagination.value);
  //   } catch (e) {
  //     loggingService.error('Error while stopping monitoring', e);
  //     notificationService.showError('Error while stopping monitoring');
  //   } finally {
  //     loadingService.stopLoading('main-loader:stop-monitoring');
  //   }
  // };

  const onClickRow = (event: Event, row: IShipment) => {
    if (HelperService.containsIgnoredRowElements(event)) {
      return;
    }

    selectedBooking.value = row;
    isBookedDetailOpen.value = true;
  };
  const onPrintLabel = ({ printer, shipment }: any) => {
    onClickPrintLabel(shipment, printer);
  };

  const onClickPrintLabel = async (shipment: IShipment, printer: IPrinter) => {
    if (!shipment || !shipment.label) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:print-label');

      /*const senderCountryIso =
      $store.state.countryStore.countriesIndexedByCountryCode[shipment.sender_country_code]?.iso;
    const receiverCountryIso =
      $store.state.countryStore.countriesIndexedByCountryCode[shipment.receiver_country_code]
        ?.iso;*/

      let label: string | null = null;

      if (printer.format === 'A4') {
        label = shipment.label;
      }

      if (printer.format === '10x19') {
        label = shipment.label_a6;
      }

      if (printer.format === 'ZPL') {
        label = shipment.label_zpl;
      }

      if (!label) {
        throw new Error('No label found');
      }

      await printJobService.queuePrintJob({
        company_code: $store.state.companyStore.company_code,
        document_base64: label,
        format: printer.format,
        reference: `Shipment #${shipment.shipment_code}`,
        printer: printer.linked_printer,
        printer_app_code: printer.printer_app_code,
      });
      notificationService.showSuccess('Print job queued successfully');
    } catch (e) {
      loggingService.error('Error printing label', e);
      notificationService.showError('Error printing label');
    } finally {
      loadingService.stopLoading('main-loader:print-label');
    }
  };

  const downloadLabel = async (row: IShipment) => {
    if (!windowRef) {
      return;
    }

    try {
      loadingService.startLoading('main-loader:download-pdf');

      let generatedLabel: string | null = null;

      if (!row.label && (row.source_type === 'MANUAL' || row.source_type === 'RETURN')) {
        const shipment = await shipmentService.generateLabel(row.shipment_code);
        generatedLabel = shipment.label;
      }

      if (!row.label && row.source_type === 'FULFILLMENT' && row.fulfillment_code) {
        await orderService.generateLabelForFulfillmentsShipment(row.fulfillment_code);
        const shipment = await shipmentService.getShipment(row.shipment_code);
        generatedLabel = shipment.label;
        $store.dispatch('companyStore/syncCompanyData');
      }

      if (!row.label && !generatedLabel) {
        return;
      }

      if (generatedLabel) {
        getReturnShipments(pagination.value);
      }

      const blob: Blob = await HelperService.toBlob(
        `data:application/pdf;base64,${encodeURI(row.label ?? generatedLabel ?? '')}`,
      );

      windowRef.location = URL.createObjectURL(blob);
    } catch (error) {
      windowRef.close();
      loggingService.error('Error while downloading PDF', error);
      notificationService.showError('Error while downloading PDF');
    } finally {
      loadingService.stopLoading('main-loader:download-pdf');
    }
  };

  const onClickDownloadLabel = async (row: IShipment) => {
    windowRef = WindowService.openWindow('Shipment Label Preview');

    downloadLabel(row);
  };

  onBeforeMount(() => {
    getReturnShipments(null);
  });
</script>

<style scoped lang="scss">
  .return_portal_shipments_page {
    padding: 0 40px 10px 40px;

    @media (max-width: 1024px) {
      padding: 0 20px 10px 20px;
    }

    &-actions {
      display: flex;
      flex-direction: row;

      & > *:not(:first-child) {
        margin-left: 10px;

        @media (max-width: 600px) {
          margin-left: unset;
        }
      }

      .big_search-rows_per_page-group {
        display: flex;
        flex: 1;
        flex-direction: row;

        & > *:not(:first-child) {
          margin-left: 10px;
        }

        .rows_per_page {
          display: none;

          @media (max-width: $tablet) {
            display: block;
          }
        }
      }

      &-big_search {
        flex: 1;
      }

      @media (max-width: 600px) {
        flex-direction: column;

        &-big_search {
          margin-bottom: 10px;
        }
      }
    }
  }

  .returns-carrier,
  .returns-date,
  .returns-recipient,
  .returns-sender {
    line-height: 16px !important;
  }

  .returns-shipment-id,
  .returns-tracking-id,
  .shipped-carrier {
    line-height: normal !important;
  }
</style>

<style lang="scss">
  .return_portal_shipments_page-table-row--is_cancelled {
    opacity: 0.7;

    td:not(:last-child) {
      text-decoration: line-through;
    }
  }
</style>
<style>
  .return-portal-shipments-page-table th:nth-child(1),
  .return-portal-shipments-page-table td:nth-child(1) {
    width: 12%;
  }

  .return-portal-shipments-page-table th:nth-child(2),
  .return-portal-shipments-page-table td:nth-child(2) {
    width: 10%;
  }

  .return-portal-shipments-page-table th:nth-child(3),
  .return-portal-shipments-page-table td:nth-child(3) {
    width: 10%;
  }

  .return-portal-shipments-page-table th:nth-child(4),
  .return-portal-shipments-page-table td:nth-child(4) {
    width: 15%;
  }

  .return-portal-shipments-page-table th:nth-child(5),
  .return-portal-shipments-page-table td:nth-child(5) {
    width: 15%;
  }

  .return-portal-shipments-page-table th:nth-child(6),
  .return-portal-shipments-page-table td:nth-child(6) {
    width: 11%;
  }

  .return-portal-shipments-page-table th:nth-child(7),
  .return-portal-shipments-page-table td:nth-child(7) {
    width: 10%;
  }

  .return-portal-shipments-page-table th:nth-child(8),
  .return-portal-shipments-page-table td:nth-child(8) {
    width: 10%;
  }

  .return-portal-shipments-page-table th:nth-child(9),
  .return-portal-shipments-page-table td:nth-child(9) {
    width: 5%;
  }
</style>
