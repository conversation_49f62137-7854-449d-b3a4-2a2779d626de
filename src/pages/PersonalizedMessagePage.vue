<template>
  <div class="flex justify-between items-center title-bar-heading">
    <title-bar style="width: 50%;" title="Tracking Messages" />
    <div style="height: 50px;" class="flex items-end">
      <shipvagoo-button min-width="160px" @click="onClickAdd('EMAIL')">
        Add Tracking Messages
      </shipvagoo-button>
    </div>
  </div>
  <div class="personalized_message_page q-mt-xs" v-if="!rows.length">
    <shipvagoo-placeholder
      text="No tracking messages are available">
    </shipvagoo-placeholder>
  </div>
  <div class="personalized_message_page" v-if="rows.length">
    <!-- <div class="personalized_message_page-actions">
      <div class="big_search-rows_per_page-group">
        <big-search
          v-model="search"
          class="personalized_message_page-actions-big_search"
          @update:model-value="getPersonalizedMessages(null)"
        />
      </div>

      <shipvagoo-button outline label="Download bulk" />
    </div> -->

    <shipvagoo-table
      v-model:pagination="pagination"
      :columns="columns"
      :rows="rows"
      :total="pagination.rowsNumber"
      @row-click="onClickRow"
      @request="getPersonalizedMessages($event.pagination)"
    >
      <template #body-cell-country="props">
        <q-td :props="props">
          <div
            v-if="props?.row?.receiver_country?.iso"
            style="display: flex; gap: 10px; justify-content: flex-start"
          >
            <img
              decoding="async"
              :src="`/assets/img/${props.row.receiver_country?.iso?.toLowerCase?.() ?? ''}.svg`"
              style="width: 20px; height: 20px; border-radius: 50%; object-fit: cover"
            />
            {{ props.row.receiver_country?.default_name }}
          </div>
        </q-td>
      </template>
      <template #body-cell-more="props">
        <q-td :props="props" class="more">
          <q-btn dense flat round style="color:#1f2124;" class="more-action" field="edit" icon="more_horiz">
            <shipvagoo-menu>
              <shipvagoo-menu-item
                clickable
                @click="onClickDeletePersonalizedMessage(props.row.message_code)"
              >
                Delete
              </shipvagoo-menu-item>
            </shipvagoo-menu>
          </q-btn>
        </q-td>
      </template>
    </shipvagoo-table>
  </div>

  <q-dialog v-model="isPersonalizedMessageModalOpen" @before-hide="onCloseFormModal">
    <shipvagoo-modal :title="getPersonalizedMessageModalTitle()" @close="onCloseFormModal">
      <shipvagoo-card-section style="display: flex; flex-direction: column; gap: 20px">
        <shipvagoo-select
          v-model="form.trigger"
          label="Trigger"
          :options="messageHooks"
          option-label="content"
          option-value="message_hook_code"
          required
          :error="$v.trigger.$error"
          :error-message="$v.trigger.$errors?.[0]?.$message.toString()"
          @update:model-value="onChangeTrigger"
        />
        <shipvagoo-input-group v-if="form.trigger" responsive>
          <shipvagoo-select
            v-model="form.isReceiverCountrySpecific"
            label="Receiver country specific"
            :options="[{label:'Yes',value:true},{label:'No',value:false}]"
            emit-value
            option-label="label"
            option-value="value"
            map-options
            required
            :error="$v.isReceiverCountrySpecific.$error"
            :error-message="$v.isReceiverCountrySpecific.$errors?.[0]?.$message.toString()"
            @update:model-value="onChangeIsReceiverCountrySpecific"
          />
          <shipvagoo-select
            v-model="form.receiverCountry"
            :disable="!form.isReceiverCountrySpecific"
            label="Country"
            :required="form.isReceiverCountrySpecific"
            :options="countries"
            option-label="default_name"
            option-value="country_code"
            :error="$v.receiverCountry.$error"
            :error-message="$v.receiverCountry.$errors?.[0]?.$message.toString()"
          />
        </shipvagoo-input-group>
        <shipvagoo-input-group v-if="form.trigger" responsive>
          <shipvagoo-select
            v-model="form.isIntegrationSpecific"
            label="Integration Specific"
            :options="yesNoOptions"
            emit-value
            option-label="label"
            option-value="value"
            map-options
            required
            :disable="form.trigger.message_id=='fulfillment_created'"
            :error="$v.isIntegrationSpecific.$error"
            :error-message="$v.isIntegrationSpecific.$errors?.[0]?.$message.toString()"
            @update:model-value="onChangeIsIntegrationSpecific"
          />
          <shipvagoo-select
            v-model="form.integration"
            :disable="!form.isIntegrationSpecific"
            label="Order Integration"
            :options="orderIntegrations"
            :required="form.isIntegrationSpecific"
            option-label="name"
            option-value="integration_code"
            :error="$v.integration.$error"
            :error-message="$v.integration.$errors?.[0]?.$message.toString()"
          />
        </shipvagoo-input-group>
        <shipvagoo-input-group v-if="form.trigger" responsive>
          <shipvagoo-select
            v-model="form.when"
            label="When"
            :options="whenOptions"
            option-label="label"
            option-value="value"
            emit-value
            map-options
            required
            disable
            :error="$v.when.$error"
            :error-message="$v.when.$errors?.[0]?.$message.toString()"
            @update:model-value="onChangeWhen"
          />
          <!-- <shipvagoo-input
             v-model="form.specificTime"
             :required="form.when === 'SPECIFIC_TIME'"
             :disable="form.when !== 'SPECIFIC_TIME'"
             label="Time"
             mask="##:##"
             :error="$v.specificTime.$error"
             :error-message="$v.specificTime.$errors?.[0]?.$message.toString()"
           />-->
        </shipvagoo-input-group>
      </shipvagoo-card-section>
      <shipvagoo-separator v-if="form.trigger" />
      <shipvagoo-card-section
        v-if="form.trigger"
        style="display: flex; flex-direction: column; gap: 20px"
      >
        <shipvagoo-textarea
          v-if="selectedMessageType === 'SMS'"
          v-model="form.messageContent"
          label="Message content"
          required
          :error="$v.messageContent.$error"
          :error-message="$v.messageContent.$errors?.[0]?.$message.toString()"
        />
        <shipvagoo-input
          v-if="selectedMessageType === 'EMAIL'"
          v-model="form.subject"
          label="Subject"
          required
          :error="$v.subject.$error"
          :error-message="$v.subject.$errors?.[0]?.$message.toString()"
        />
        <!-- <shipvagoo-input
           v-if="selectedMessageType === 'EMAIL'"
           v-model="form.bcc"
           label="Bcc"
           :error="$v.bcc.$error"
           :error-message="$v.bcc.$errors?.[0]?.$message.toString()"
         />-->
        <div>
          <shipvagoo-label v-if="selectedMessageType === 'EMAIL'" label="Message content*" />
          <shipvagoo-wysiwyg
            v-if="selectedMessageType === 'EMAIL'"
            v-model="form.messageContent"
            :error="$v.messageContent.$error"
            :error-message="$v.messageContent.$errors?.[0]?.$message.toString()"
          />
        </div>
      </shipvagoo-card-section>
      <shipvagoo-separator />
      <shipvagoo-card-actions>
        <shipvagoo-button
          v-if="personalizedMessageModalType === 'create'"
          min-width="120px"
          @click="onClickCreate"
        >
          Create
        </shipvagoo-button>
        <shipvagoo-button
          v-if="personalizedMessageModalType === 'edit'"
          min-width="120px"
          @click="onClickSave"
        >
          Save
        </shipvagoo-button>
      </shipvagoo-card-actions>
    </shipvagoo-modal>
  </q-dialog>

  <!-- <q-fab class="personalized_message-fab" direction="up">
     <q-fab-action
       icon="email"
       class="personalized_message-fab&#45;&#45;item"
       @click="onClickAdd('EMAIL')"
     />
     &lt;!&ndash; <q-fab-action icon="sms" class="personalized_message-fab&#45;&#45;item" @click="onClickAdd('SMS')"/>&ndash;&gt;
   </q-fab>-->
</template>

<script setup lang="ts">
import { onBeforeMount, Ref, ref } from "vue";
import { QTableProps } from "quasar";
import useVuelidate, { ValidationArgs } from "@vuelidate/core";
import { helpers, required, requiredIf } from "@vuelidate/validators";
import { Pagination } from "../model/common.model";
import {
  loadingService,
  notificationService,
  loggingService,
  personalizedMessageService,
  orderIntegrationService
} from "../services/_singletons";
import {
  IMessageHook,
  IPersonalizedMessage,
  TMessagingType
} from "../model/personalized-message.model";
import { ICountry } from "../model/country.model";
import { useStore } from "../store";
import { IOrderIntegration } from "../model/order-integration.model";
import { IRequestParam } from "../model/http.model";
import HelperService from "../services/helper.service";

interface YesNo {
  label: string,
  value: boolean
}

const $store = useStore();
const yesNoOptions = ref<YesNo[]>([
  { label: "Yes", value: true },
  { label: "No", value: false }
]);

const whenOptions = [
  { label: "Immediately", value: "IMMEDIATELY" }
  // { label: 'Specific Time', value: 'SPECIFIC_TIME' },
];

// const search: Ref<string> = ref<string>('');
const countries = ref<ICountry[]>([]);
const orderIntegrations = ref<IOrderIntegration[]>([]);
const messageHooks: Ref<IMessageHook[]> = ref<IMessageHook[]>([]);
const rows = ref<IPersonalizedMessage[]>([]);
const isPersonalizedMessageModalOpen: Ref<boolean> = ref<boolean>(false);
const selectedMessageType = ref<TMessagingType | null>(null);
const personalizedMessageModalType = ref<"create" | "edit" | null>(null);
const selectedPersonalizedMessageCode = ref<number | null>(null);

interface IPersonalizedMessageForm {
  trigger: IMessageHook | null;
  isReceiverCountrySpecific: boolean;
  receiverCountry: ICountry | null;
  isIntegrationSpecific: boolean;
  integration: IOrderIntegration | null;
  when: "IMMEDIATELY" | "SPECIFIC_TIME";
  specificTime: string | null;
  subject: string | null;
  bcc: string | null;
  messageContent: string;
}

const form = ref<IPersonalizedMessageForm>({
  trigger: null,
  isReceiverCountrySpecific: false,
  receiverCountry: null,
  isIntegrationSpecific: false,
  integration: null,
  when: "IMMEDIATELY",
  specificTime: null,
  subject: null,
  bcc: null,
  messageContent: ""
});

const pagination = ref<Pagination>({
  rowsNumber: 0,
  rowsPerPage: 10,
  page: 1
});

const getPersonalizedMessageModalTitle = () => {
  if (selectedMessageType.value === "EMAIL" && personalizedMessageModalType.value === "create") {
    return "Create tracking email message";
  }

  if (selectedMessageType.value === "EMAIL" && personalizedMessageModalType.value === "edit") {
    return "Edit tracking email message";
  }

  if (selectedMessageType.value === "SMS" && personalizedMessageModalType.value === "create") {
    return "Create tracking SMS message";
  }

  if (selectedMessageType.value === "SMS" && personalizedMessageModalType.value === "edit") {
    return "Edit tracking SMS message";
  }

  return "";
};

const hydrateCountries = () => {
  if (countries.value.length > 0) {
    return;
  }
  console.log("Countries ", $store.state.countryStore.countries);
  countries.value = $store.state.countryStore.countries;
  /*countries.value = $store.state.countryStore.countries.filter(
    (country) => country.iso === 'DK' || country.iso === 'SE' || country.iso === 'DE',
  );*/
};

const onCloseFormModal = () => {
  isPersonalizedMessageModalOpen.value = false;
  selectedPersonalizedMessageCode.value = null;
  form.value = {
    trigger: null,
    isReceiverCountrySpecific: false,
    receiverCountry: null,
    isIntegrationSpecific: false,
    integration: null,
    when: "IMMEDIATELY",
    specificTime: null,
    messageContent: "",
    subject: null,
    bcc: null
  };
};

const onClickAdd = (type: TMessagingType) => {
  hydrateCountries();
  selectedMessageType.value = type;
  personalizedMessageModalType.value = "create";
  isPersonalizedMessageModalOpen.value = true;
};

const $v = useVuelidate<IPersonalizedMessageForm, ValidationArgs<IPersonalizedMessageForm>>(
  {
    trigger: {
      required: helpers.withMessage("This field is required", required)
    },
    isReceiverCountrySpecific: {
      required: helpers.withMessage("This field is required", required)
    },
    receiverCountry: {
      required: helpers.withMessage(
        "This field is required",
        requiredIf(() => form.value.isReceiverCountrySpecific)
      )
    },
    isIntegrationSpecific: {
      required: helpers.withMessage("This field is required", required)
    },
    integration: {
      required: helpers.withMessage(
        "This field is required",
        requiredIf(() => form.value.isIntegrationSpecific)
      )
    },
    when: {
      required: helpers.withMessage("This field is required", required)
    },
    specificTime: {
      required: helpers.withMessage(
        "This field is required",
        requiredIf(() => form.value.when === "SPECIFIC_TIME")
      ),
      isValidTime: helpers.withMessage("Invalid time", (value: string) => {
        if (form.value.when !== "SPECIFIC_TIME") {
          return true;
        }

        return /^([0-1]?[0-9]|2[0-4]):([0-5][0-9])(:[0-5][0-9])?$/.test(value);
      })
    },
    subject: {
      required: helpers.withMessage(
        "This field is required",
        requiredIf(() => selectedMessageType.value === "EMAIL")
      )
    },
    bcc: {},
    messageContent: {
      required: helpers.withMessage("This field is required", required)
    }
  },
  form
);

const columns: QTableProps["columns"] = [
  {
    name: "type",
    field: (row: IPersonalizedMessage) => row.type,
    required: true,
    label: "Type",
    align: "left",
    sortable: false
  },
  {
    name: "trigger",
    field: (row: IPersonalizedMessage) => row.message_hook?.content,
    required: true,
    label: "Trigger",
    align: "left",
    sortable: false
  },
  {
    name: "country",
    field: "country",
    required: true,
    label: "Country",
    align: "left",
    sortable: false
  },
  {
    name: "order_integration",
    field: (row: IPersonalizedMessage) => row.integration?.name,
    required: true,
    label: "Order Integration",
    align: "left",
    sortable: false
  },
  { name: "more", field: "more", label: "More", sortable: false }
];

const onChangeIsReceiverCountrySpecific = (value: boolean) => {
  if (value) {
    return;
  }

  form.value.receiverCountry = null;
};

const onChangeIsIntegrationSpecific = (value: boolean) => {
  if (value) {
    return;
  }

  form.value.integration = null;
};

const onChangeWhen = (value: "IMMEDIATELY" | "SPECIFIC_TIME") => {
  if (value === "SPECIFIC_TIME") {
    return;
  }

  form.value.specificTime = null;
};

const getPersonalizedMessages = async (tablePagination: Pagination | null) => {
  try {
    loadingService.startLoading("main-loader:personalized-messages");

    const page: number = tablePagination?.page || 1;
    const limit: number = tablePagination?.rowsPerPage || 10;

    const params: IRequestParam[] = [];

    if (tablePagination?.sortBy) {
      const sortingOrder = tablePagination.descending ? "desc" : "asc";
      params.push({ key: `sorting[${tablePagination.sortBy}]`, value: sortingOrder });
    } else {
      params.push({ key: "sorting[created_at]", value: "desc" });
    }

    const response = await personalizedMessageService.getPersonalizedMessages(
      page,
      limit,
      params
    );

    pagination.value.page = page;
    pagination.value.rowsPerPage = limit;
    pagination.value.rowsNumber = response.total;
    pagination.value.sortBy = tablePagination?.sortBy;
    pagination.value.descending = tablePagination?.descending;
    rows.value = response.entities;
  } catch (e) {
    loggingService.error("Error while loading personalized messages", e);
    notificationService.showError("Error while loading personalized messages");
  } finally {
    loadingService.stopLoading("main-loader:personalized-messages");
  }
};

const getMessageHooks = async () => {
  try {
    loadingService.startLoading("main-loader:get-message-hooks");
    const response = await personalizedMessageService.getMessageHooks();
    messageHooks.value = response.slice(0, 2);
  } catch (e) {
    notificationService.showError("Error while getting message hooks");
    loggingService.error("Error while getting message hooks", e);
  } finally {
    loadingService.stopLoading("main-loader:get-message-hooks");
  }
};

const getOrderIntegrations = async () => {
  try {
    loadingService.startLoading("main-loader:get-order-integrations");
    const response = await orderIntegrationService.getOrderIntegrations(1, 1000);
    orderIntegrations.value = response.entities;
  } catch (e) {
    notificationService.showError("Error while getting order integrations");
    loggingService.error("Error while getting order integrations", e);
  } finally {
    loadingService.stopLoading("main-loader:get-order-integrations");
  }
};

const onChangeTrigger = async (messageHook: IMessageHook | null) => {
  if (!messageHook || !selectedMessageType.value) {
    return;
  }

  try {
    loadingService.startLoading("main-loader:get-message-templates");
    const messageTemplate = await personalizedMessageService.getPersonalizedMessageTemplate(
      selectedMessageType.value,
      messageHook.message_hook_code
    );

    form.value.isReceiverCountrySpecific = Boolean(messageTemplate.is_receiver_country_specific);

    if (form.value.isReceiverCountrySpecific) {
      form.value.receiverCountry = $store.state.countryStore.countries.find(
        (country) => country.country_code === messageTemplate.receiver_country_code
      ) as ICountry;
    }
    if (messageHook.message_id == "fulfillment_created") {
      yesNoOptions.value = [{ label: "Yes", value: true }];
      form.value.isIntegrationSpecific = true;
    } else {
      yesNoOptions.value = [{ label: "Yes", value: true }, { label: "No", value: false }];
      form.value.isIntegrationSpecific = Boolean(messageTemplate.is_integration_specific);
    }

    if (form.value.isIntegrationSpecific) {
      form.value.integration = orderIntegrations.value.find(
        (o) => o.integration_code === messageTemplate.integration_code
      ) as IOrderIntegration;
    }

    form.value.when = messageTemplate.send_option;
    form.value.specificTime = messageTemplate.send_time;

    form.value.subject = messageTemplate.subject;
    form.value.bcc = messageTemplate.bcc;

    form.value.messageContent = messageTemplate.message_content.replaceAll("\n", "<br>");
  } catch (e) {
    notificationService.showError("Error while getting message templates");
    loggingService.error("Error while getting message templates", e);
  } finally {
    loadingService.stopLoading("main-loader:get-message-templates");
  }
};

const onClickCreate = async () => {
  await $v.value.$validate();

  if ($v.value.$error || !form.value.trigger || !selectedMessageType.value) {
    return;
  }

  try {
    loadingService.startLoading("main-loader:save-personalized-message");

    await personalizedMessageService.createPersonalizedMessage({
      message_hook_code: form.value.trigger.message_hook_code,
      type: selectedMessageType.value,
      is_receiver_country_specific: form.value.isReceiverCountrySpecific,
      receiver_country_code: form.value.receiverCountry?.country_code ?? undefined,
      is_integration_specific: form.value.isIntegrationSpecific,
      integration_code: form.value.integration?.integration_code ?? undefined,
      send_option: form.value.when,
      send_time: form.value.specificTime ?? null,
      subject: form.value.subject,
      bcc: form.value.bcc,
      message_content: form.value.messageContent
    }).then(response => {
      notificationService.showSuccess("Tracking message saved successfully.");
    }).catch(error => {
      throw new Error(error.response.data?.error);
    });

    onCloseFormModal();
    getPersonalizedMessages(null);
  } catch (e) {
    if (typeof e === "string") {
      notificationService.showError(e);
    } else if (e instanceof Error) {
      notificationService.showError(e.message);
    } else {
      notificationService.showError("Error while saving personalized message");
    }
    loggingService.error("Error while saving personalized message", e);
  } finally {
    loadingService.stopLoading("main-loader:save-personalized-message");
  }
};

const onClickSave = async () => {
  await $v.value.$validate();

  if (
    $v.value.$error ||
    !form.value.trigger ||
    !selectedMessageType.value ||
    !selectedPersonalizedMessageCode.value
  ) {
    return;
  }

  try {
    loadingService.startLoading("main-loader:save-personalized-message");

    await personalizedMessageService.updatePersonalizedMessage(
      selectedPersonalizedMessageCode.value,
      {
        message_hook_code: form.value.trigger.message_hook_code,
        type: selectedMessageType.value,
        is_receiver_country_specific: form.value.isReceiverCountrySpecific,
        receiver_country_code: form.value.receiverCountry?.country_code ?? undefined,
        is_integration_specific: form.value.isIntegrationSpecific,
        integration_code: form.value.integration?.integration_code ?? undefined,
        send_option: form.value.when,
        send_time: form.value.specificTime ?? null,
        subject: form.value.subject,
        bcc: form.value.bcc,
        message_content: form.value.messageContent
      }
    ).then(response => {
      notificationService.showSuccess("Tracking message saved successfully.");
    }).catch(error => {
      throw new Error(error.response.data?.error);
    });

    onCloseFormModal();
    getPersonalizedMessages(pagination.value);
  } catch (e) {
    if (typeof e === "string") {
      notificationService.showError(e);
    } else if (e instanceof Error) {
      notificationService.showError(e.message);
    } else {
      notificationService.showError("Error while saving personalized message");
    }
    loggingService.error("Error while saving personalized message", e);
  } finally {
    loadingService.stopLoading("main-loader:save-personalized-message");
  }
};

const onClickRow = (event: Event, row: IPersonalizedMessage) => {
  if (HelperService.containsIgnoredRowElements(event)) {
    return;
  }

  hydrateCountries();
  selectedMessageType.value = row.type;
  personalizedMessageModalType.value = "edit";
  selectedPersonalizedMessageCode.value = row.message_code;

  form.value.trigger = messageHooks.value.find(
    (messageHook) => messageHook.message_hook_code === row.message_hook_code
  ) as IMessageHook;
  form.value.isReceiverCountrySpecific = Boolean(row.is_receiver_country_specific);

  if (form.value.isReceiverCountrySpecific) {
    form.value.receiverCountry = $store.state.countryStore.countries.find(
      (country) => country.country_code === row.receiver_country_code
    ) as ICountry;
  }

  form.value.isIntegrationSpecific = Boolean(row.is_integration_specific);

  if (form.value.isIntegrationSpecific) {
    form.value.integration = orderIntegrations.value.find(
      (o) => o.integration_code === row.integration_code
    ) as IOrderIntegration;
  }

  form.value.when = row.send_option;

  if (form.value.when === "SPECIFIC_TIME") {
    form.value.specificTime = row.send_time;
  }

  if (row.type === "EMAIL") {
    form.value.subject = row.subject;
    form.value.bcc = row.bcc;
  }

  form.value.messageContent = row.message_content;

  isPersonalizedMessageModalOpen.value = true;
};

const onClickDeletePersonalizedMessage = async (messageCode: number) => {
  if (!messageCode) {
    return;
  }

  try {
    loadingService.startLoading("main-loader:delete-personalized-message");
    await personalizedMessageService.deletePersonalizedMessage(messageCode);
    const page = pagination.value.page ?? 1;

    if (page > 1 && rows.value.length === 1) {
      pagination.value.page = page - 1;
    }

    getPersonalizedMessages(pagination.value);
  } catch (e) {
    notificationService.showError("Error while deleting personalized message");
    loggingService.error("Error while deleting personalized message", e);
  } finally {
    loadingService.stopLoading("main-loader:delete-personalized-message");
  }
};

onBeforeMount(() => {
  getMessageHooks();
  getOrderIntegrations();
  getPersonalizedMessages(null);
});
</script>

<style scoped lang="scss">
.personalized_message_page {
  padding: 0 40px 10px 40px;

  @media (max-width: 1024px) {
    padding: 0 20px 10px 20px;
  }

  &-actions {
    display: flex;
    flex-direction: row;

    & > *:not(:first-child) {
      margin-left: 10px;

      @media (max-width: 600px) {
        margin-left: unset;
      }
    }

    .big_search-rows_per_page-group {
      display: flex;
      flex: 1;
      flex-direction: row;

      & > *:not(:first-child) {
        margin-left: 10px;
      }

      .rows_per_page {
        display: none;

        @media (max-width: $tablet) {
          display: block;
        }
      }
    }

    &-big_search {
      flex: 1;
    }

    @media (max-width: 600px) {
      flex-direction: column;

      &-big_search {
        margin-bottom: 10px;
      }
    }
  }

  &-iframe {
    width: 100%;
    height: 100%;
    min-height: 500px;
    border: none;
  }
}

.personalized_message-fab {
  position: fixed;
  right: 40px;
  bottom: 40px;
  width: 60px;
  height: 60px;
  background: #131393;
  border-radius: 50%;

  &.q-fab--opened {
    color: #131393;
    background: #e1e1e1;
  }

  &--item {
    width: 60px;
    height: 60px;
    color: #e1e1e1;
    background: #131393;
    border-radius: 50%;
  }
}
</style>