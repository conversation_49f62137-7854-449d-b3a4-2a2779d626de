/* eslint-disable no-param-reassign */
import { Module } from 'vuex';
import { IPickSettings } from '../model/pick-settings.model';
import { IRootState } from '../model/store.model';
import { loadingService, loggingService, pickSettingsService } from '../services/_singletons';

const pickSettingsStore: Module<IPickSettings, IRootState> = {
  state: () => ({
    pick_setting_code: 0,
    company_code: 0,
    primary_action: 'CreatePickPathList',
    pick_sorting: 'Bin',
    language_code: 0,
    capture_payment: false,
    choose_packaging: false,
    confirm_pick_amount_obtained: false,
    create_fulfillment: false,
    create_shipment: false,
    finish_when_items_picked: false,
    go_to_next_item: false,
    play_sound: false,
    print_packing_slip: false,
    print_proforma_invoice: false,
    print_shipping_label: false,
    say_item_name: false,
    show_item_image: false,
    skip_summary: false,
    start_at_first_item: false,
    use_camera_on_mobile_devices: false,
    vibrate_when_scanning: false,
    warn_partial_pick: false,
    user_camera_on_mobile_devices: false,
  }),
  mutations: {
    setPickSettings(state: IPickSettings, payload: IPickSettings): void {
      state.pick_setting_code = payload.pick_setting_code;
      state.company_code = payload.company_code;
      state.language_code = payload.language_code;
      state.primary_action = payload.primary_action;
      state.pick_sorting = payload.pick_sorting;
      state.start_at_first_item = Boolean(payload.start_at_first_item);
      state.warn_partial_pick = Boolean(payload.warn_partial_pick);
      state.confirm_pick_amount_obtained = Boolean(payload.confirm_pick_amount_obtained);
      state.go_to_next_item = Boolean(payload.go_to_next_item);
      state.finish_when_items_picked = Boolean(payload.finish_when_items_picked);
      state.show_item_image = Boolean(payload.show_item_image);
      state.play_sound = Boolean(payload.play_sound);
      state.use_camera_on_mobile_devices = Boolean(payload.use_camera_on_mobile_devices);
      state.vibrate_when_scanning = Boolean(payload.vibrate_when_scanning);
      state.skip_summary = Boolean(payload.skip_summary);
      state.choose_packaging = Boolean(payload.choose_packaging);
      state.say_item_name = Boolean(payload.say_item_name);
      state.create_fulfillment = Boolean(payload.create_fulfillment);
      state.create_shipment = Boolean(payload.create_shipment);
      state.print_shipping_label = Boolean(payload.print_shipping_label);
      state.print_proforma_invoice = Boolean(payload.print_proforma_invoice);
      state.print_packing_slip = Boolean(payload.print_packing_slip);
      state.capture_payment = Boolean(payload.capture_payment);
    },
  },
  actions: {
    async syncPickSettings(context): Promise<void> {
      try {
        loadingService.startLoading('main-loader:sync-pick-settings');
        const pickSettings: IPickSettings = await pickSettingsService.getPickSettings();
        context.commit('setPickSettings', pickSettings);
      } catch (e) {
        loggingService.error('Error fetching pick settings data', e);
      } finally {
        loadingService.stopLoading('main-loader:sync-pick-settings');
      }
    },
  },
  getters: {},
  namespaced: true,
};

export default pickSettingsStore;
