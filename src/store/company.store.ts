/* eslint-disable no-param-reassign */
import { Module } from "vuex";
import { ICompany, ICompanyState } from "../model/company.model";
import { IRootState } from "../model/store.model";
import { companyService, loadingService, loggingService } from "../services/_singletons";

const companyStore: Module<ICompanyState, IRootState> = {
  state: () => ({
    address: "",
    city: "",
    company_code: 0,
    company_name: "",
    contact_person: "",
    country_code: 0,
    created_at: "",
    created_by: 0,
    deleted_by: 0,
    deleted_at: null,
    language_code: 0,
    phone_no: "",
    updated_at: "",
    updated_by: 0,
    vat_no: "",
    zipcode: "",
    attn: "",
    website: "",
    country: null,
    language: null,
    balance: 0,
    credit_enabled: 0
  }),
  mutations: {
    setCompany(state: ICompanyState, payload: ICompany): void {
      state.address = payload.address;
      state.city = payload.city;
      state.company_code = payload.company_code;
      state.company_name = payload.company_name;
      state.contact_person = payload.contact_person;
      state.country_code = payload.country_code;
      state.country = payload.country;
      state.language_code = payload.language_code;
      state.phone_no = payload.phone_no;
      state.vat_no = payload.vat_no;
      state.zipcode = payload.zipcode;
      state.balance = payload.balance ?? 0;
      state.website = payload.website;
      state.credit_enabled = payload.credit_enabled ?? 0;
    }
  },
  actions: {
    async syncCompanyData(context): Promise<void> {
      try {
        //loadingService.startLoading('main-loader:company');
        const company: ICompany = await companyService.getCurrentCompany();
        context.commit("setCompany", company);
      } catch (e) {
        loggingService.error("Error fetching company data", e);
      } finally {
        loadingService.stopLoading("main-loader:company");
      }
    }
  },
  getters: {
    formattedBalance(state): string {
      return Intl.NumberFormat("de-DE", { style: "currency", currency: "DKK" }).format(
        state.balance
      );
    }
  },
  namespaced: true
};

export default companyStore;
