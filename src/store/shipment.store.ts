/* eslint-disable no-param-reassign */
import { noop } from "lodash-es";
import { Mo<PERSON><PERSON> } from "vuex";
import { ICreateNewShipmentForm, IShipmentStoreState } from "../model/shipment.model";
import { IRootState } from "../model/store.model";

const shipmentStore: Module<IShipmentStoreState, IRootState> = {
  state: () => ({
    createShipmentPredefinedValues: null,
    isCreateShipmentModalOpen: false,
    shipmentCode: "",
    fulfillmentCode: ""
  }),
  mutations: {
    setIsCreateShipmentModalOpen(state: IShipmentStoreState, payload: boolean): void {
      state.isCreateShipmentModalOpen = payload;

      if (!payload && state.createShipmentPredefinedValues) {
        state.createShipmentPredefinedValues = null;
      }
    },
    setShipmentCode(state: IShipmentStoreState, payload: number | string): void {
      state.shipmentCode = payload;
    },
    setFulfillmentCode(state: IShipmentStoreState, payload: number | string): void {
      state.fulfillmentCode = payload;
    },
    setCreateShipmentData(state: IShipmentStoreState, payload: ICreateNewShipmentForm | null): void {
      state.createShipmentPredefinedValues = payload;
    }
  },
  actions: {
    openCreateShipmentModal(context): void {
      context.commit("setIsCreateShipmentModalOpen", true);
    },
    closeCreateShipmentModal(context): void {
      context.commit("setIsCreateShipmentModalOpen", false);
    },
    setCreateShipmentData(context, payload: ICreateNewShipmentForm | null): void {
      context.commit("setCreateShipmentData", payload);
    },
    setShipmentCode(context, payload: number | string): void {
      context.commit("setShipmentCode", payload);
    },
    setFulfillmentCode(context, payload: number | string): void {
      context.commit("setFulfillmentCode", payload);
    },
    refreshDrafts: noop,
    refreshBookings: noop
  },
  getters: {},
  namespaced: true
};

export default shipmentStore;
