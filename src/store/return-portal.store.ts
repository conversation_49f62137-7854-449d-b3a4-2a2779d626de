/* eslint-disable no-param-reassign */
import { <PERSON><PERSON><PERSON> } from "vuex";
import { IRootState } from "../model/store.model";
import { returnPortalService } from "../services/_singletons";
import { IReturnPortalStoreState } from "../model/return-portal.model";


const returnPortalStore: Module<IReturnPortalStoreState, IRootState> = {
  state: () => ({
    count: 0
  }),
  mutations: {
    setCount(state, payload: number): void {
      state.count = payload;
    }
  },
  actions: {
    async getReturnPortals(context) {
      const response = await returnPortalService.getReturnPortals(1, 1000, []);
      context.commit("setCount", response.entities.length);
    }
  },
  getters: {},
  namespaced: true
};

export default returnPortalStore;
