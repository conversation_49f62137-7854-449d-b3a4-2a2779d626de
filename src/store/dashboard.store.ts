/* eslint-disable no-param-reassign */
import { Modu<PERSON> } from 'vuex';
import { IDashboardOverview, IDashboardStoreState } from '../model/dashboard.model';
import { IRootState } from '../model/store.model';
import {
  dashboardService,
  loadingService,
  loggingService,
  notificationService,
} from '../services/_singletons';

const dashboardStore: Module<IDashboardStoreState, IRootState> = {
  state: () => ({
    thisWeekInfo: null,
    overviewInfo: {
      dispatched_orders: [],
      imported_orders: [],
      picked_orders: [],
      shipments: [],
    },
    thisWeekTotalShipmentCount: 0,
  }),
  mutations: {
    setOverviewInformation(state, payload: IDashboardOverview) {
      if (!state.thisWeekInfo) {
        state.thisWeekInfo = payload;
        state.thisWeekTotalShipmentCount = payload.shipments.reduce((prev, current) => {
          return prev + current.shipmentCount;
        }, 0);
      }

      state.overviewInfo = payload;
    },
  },
  actions: {
    async getDashboardOverview({ commit }, days: number = 7) {
      try {
        loadingService.startLoading('main-loader:shipment-statistics');
        const response = await dashboardService.getDashboardOverview(days);
        commit('setOverviewInformation', response);
      } catch (e) {
        notificationService.showError('Error loading shipment statistics');
        loggingService.error('Error loading shipment statistics', e);
      } finally {
        loadingService.stopLoading('main-loader:shipment-statistics');
      }
    },
  },
  getters: {},
  namespaced: true,
};

export default dashboardStore;
