/* eslint-disable no-param-reassign */
import { Modu<PERSON> } from 'vuex';
import { ICountry, ICountryState } from '../model/country.model';
import { IRootState } from '../model/store.model';
import { countryService, loadingService, loggingService } from '../services/_singletons';

const countryStore: Module<ICountryState, IRootState> = {
  state: () => ({
    countries: [],
    countriesIndexedByIso: {},
    countriesIndexedByCountryCode: {},
  }),
  mutations: {
    setCountries(state: ICountryState, payload: ICountry[]): void {
      state.countries = payload;

      const countriesIndexedByIso: { [key: string]: ICountry } = {};

      payload.forEach((country: ICountry) => {
        countriesIndexedByIso[country.iso] = country;
      });

      state.countriesIndexedByIso = countriesIndexedByIso;

      const countriesIndexedByCountryCode: { [key: number]: ICountry } = {};

      payload.forEach((country: ICountry) => {
        countriesIndexedByCountryCode[country.country_code] = country;
      });

      state.countriesIndexedByCountryCode = countriesIndexedByCountryCode;
    },
  },
  actions: {
    async syncCountries(context): Promise<void> {
      try {
        loadingService.startLoading('main-loader:countries');
        const countries: ICountry[] = await countryService.getCountries([
          { key: 'range', value: 'active' },
        ]);
        context.commit('setCountries', countries);
      } catch (e) {
        loggingService.error('Error while syncing countries data', e);
      } finally {
        loadingService.stopLoading('main-loader:countries');
      }
    },
  },
  getters: {},
  namespaced: true,
};

export default countryStore;
