import { createStore, Store } from "vuex";
import { IRootState } from "../model/store.model";
import companyStore from "./company.store";
import countryStore from "./country.store";
import courierStore from "./courier.store";
import dashboardStore from "./dashboard.store";
import languageStore from "./language.store";
import orderStore from "./order.store";
import pickSettingsStore from "./pick-settings.store";
import printerStore from "./printer.store";
import shipmentStore from "./shipment.store";
import userStore from "./user.store";
import paymentSettingsStore from "./payment-settings.store";
import integrationStore from "./integration.store";
import returnPortalStore from "./return-portal.store";

const store: Store<IRootState> = createStore<IRootState>({
  modules: {
    userStore,
    companyStore,
    pickSettingsStore,
    countryStore,
    courierStore,
    shipmentStore,
    orderStore,
    languageStore,
    dashboardStore,
    printerStore,
    paymentSettingsStore,
    integrationStore,
    returnPortalStore
  }
});

export const useStore = () => store;

export default store;
