/* eslint-disable no-param-reassign */
import { Modu<PERSON> } from 'vuex';
import { ICarrier, ICourierStoreState, IShippingProduct } from '../model/carrier.model';
import { IRootState } from '../model/store.model';
import {
  carrierService,
  loadingService,
  loggingService,
  notificationService,
} from '../services/_singletons';

const courierStore: Module<ICourierStoreState, IRootState> = {
  state: () => ({
    carriers: [],
    carrierProducts: [],
  }),
  mutations: {
    setCouriers(state, carriers: ICarrier[]) {
      state.carriers = carriers;
    },
    setCarrierProducts(state, carrierProducts: IShippingProduct[]) {
      state.carrierProducts = carrierProducts;
    },
  },
  actions: {
    async getCarriers(context) {
      try {
        loadingService.startLoading('main-loader:loading-carriers');
        const carriers = await carrierService.getCarriers();
        context.commit('setCouriers', carriers);
      } catch (e) {
        notificationService.showError('Error loading carriers');
        loggingService.error('Error loading carriers', e);
      } finally {
        loadingService.stopLoading('main-loader:loading-carriers');
      }
    },
    async getCarrierProducts(context) {
      try {
        loadingService.startLoading('main-loader:loading-carrier-products');
        const carrierProducts = await carrierService.getCarrierProducts();
        context.commit('setCarrierProducts', carrierProducts);
      } catch (e) {
        notificationService.showError('Error loading carrier products');
        loggingService.error('Error loading carrier products', e);
      } finally {
        loadingService.stopLoading('main-loader:loading-carrier-products');
      }
    },
  },
  getters: {},
  namespaced: true,
};

export default courierStore;
