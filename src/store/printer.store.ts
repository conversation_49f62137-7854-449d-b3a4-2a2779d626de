/* eslint-disable no-param-reassign */
import { Module } from "vuex";
import { IPrinter, IPrinterStoreState } from "../model/printer.model";
import { IRootState } from "../model/store.model";
import {
  loadingService,
  loggingService,
  notificationService,
  printerService
} from "../services/_singletons";
import { IPdfSettingsStoreState } from "~/model/pdf-settings.model";

const printerStore: Module<IPrinterStoreState, IRootState> = {
  state: (): IPrinterStoreState => ({
    printers: [],
    defaultPrinterForLabel: null,
    defaultPrinterForOther: null,
    defaultPrinterForPickDocuments: null,
    isPdfSettingsModalOpen: false,
    isApiAccessModalOpen: false
  }),
  mutations: {
    setPrinters(state, payload: IPrinter[]) {
      const defaultPrinterForLabel = payload.find((p) => p.is_default_label);
      const defaultPrinterForOther = payload.find((p) => p.is_default_other);
      const defaultPrinterForPickDocuments = payload.find((p) => p.is_default_pick_documents);

      state.printers = payload;
      state.defaultPrinterForLabel = defaultPrinterForLabel || null;
      state.defaultPrinterForOther = defaultPrinterForOther || null;
      state.defaultPrinterForPickDocuments = defaultPrinterForPickDocuments || null;

    },
    setPdfSettingModalOpen(state: IPdfSettingsStoreState, payload: boolean): void {
      state.isPdfSettingsModalOpen = payload;
    },
    setApiAccessModalOpen(state: IPdfSettingsStoreState, payload: boolean): void {
      state.isApiAccessModalOpen = payload;
    }
  },
  actions: {
    async syncPrinters(context) {
      try {
        loadingService.startLoading("main-loader:loading-printers");
        const response = await printerService.getPrinters(1, 1000, []);
        context.commit("setPrinters", response.entities);
      } catch (e) {
        notificationService.showError("Error loading printers");
        loggingService.error("Error loading printers", e);
      } finally {
        loadingService.stopLoading("main-loader:loading-printers");
      }
    },
    openPdfSettingModal(context): void {
      context.commit("setPdfSettingModalOpen", true);
    },
    closePdfSettingModal(context): void {
      context.commit("setPdfSettingModalOpen", false);
    },
    openApiAccessModalOpen(context): void {
      context.commit("setApiAccessModalOpen", true);
    },
    closeApiAccessModalOpen(context): void {
      context.commit("setApiAccessModalOpen", false);
    }
  },
  getters: {},
  namespaced: true
};

export default printerStore;
