/* eslint-disable no-param-reassign */
import {Modu<PERSON>} from 'vuex';
import {IRootState} from '../model/store.model';
import {cardService, loadingService, loggingService} from '../services/_singletons';
import {IPaymentSetting} from "../model/payment.model";

const paymentSettingsStore: Module<IPaymentSetting, IRootState> = {
  state: () => ({
    payment_setting_code: 0,
    company_code: 0,
    payment_method: 'BALANCE',
    cards: null
  }),
  mutations: {
    setPaymentSettings(state: IPaymentSetting, payload: IPaymentSetting): void {
      state.company_code = payload.company_code;
      state.payment_setting_code = payload.payment_setting_code;
      state.payment_method = payload.payment_method;
      state.cards = payload.cards;
    }
  },
  actions: {
    async syncPaymentSettings(context): Promise<void> {
      try {
        loadingService.startLoading('main-loader:user');
        const payload: IPaymentSetting = await cardService.getPaymentSettings();
        context.commit('setPaymentSettings', payload);
      } catch (e) {
        loggingService.error('Error while syncing user data', e);
      } finally {
        loadingService.stopLoading('main-loader:user');
      }
    },
  },
  getters: {},
  namespaced: true,
};

export default paymentSettingsStore;
