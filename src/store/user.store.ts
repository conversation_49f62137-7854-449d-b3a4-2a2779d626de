/* eslint-disable no-param-reassign */
import {<PERSON><PERSON><PERSON>} from 'vuex';
import {IRootState} from '../model/store.model';
import {IUser, IUserState} from '../model/user.model';
import {loadingService, loggingService, userService} from '../services/_singletons';

const userStore: Module<IUserState, IRootState> = {
  state: () => ({
    id: 0,
    user_code: 0,
    company_code: 0,
    name: '',
    email: '',
    email_verified_at: null,
    role: 'COMPANY_USER',
    permitted_modules: [],
    created_at: '',
    updated_at: '',
    deleted_at: null,
    version: '',
  }),
  mutations: {
    setUser(state: IUserState, payload: IUser): void {
      state.name = payload.name;
      state.email = payload.email;
      state.user_code = payload.user_code;
      state.created_at = payload.created_at;
      state.updated_at = payload.updated_at;
      state.version = payload.version;
    },
    resetUser(state: IUserState): void {
      state.id = 0;
      state.user_code = 0;
      state.company_code = 0;
      state.name = '';
      state.email = '';
      state.email_verified_at = null;
      state.role = 'COMPANY_USER';
      state.permitted_modules = [];
      state.created_at = '';
      state.updated_at = '';
      state.deleted_at = null;
    },
  },
  actions: {
    async syncUser(context): Promise<void> {
      try {
        loadingService.startLoading('main-loader:user');
        const payload: IUser = await userService.getCurrentUser();
        context.commit('setUser', payload);
      } catch (e) {
        loggingService.error('Error while syncing user data', e);
      } finally {
        loadingService.stopLoading('main-loader:user');
      }
    },
    resetUser(context): void {
      context.commit('resetUser');
    },
  },
  getters: {},
  namespaced: true,
};

export default userStore;
