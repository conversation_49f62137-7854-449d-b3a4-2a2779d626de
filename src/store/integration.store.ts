/* eslint-disable no-param-reassign */
import { Modu<PERSON> } from 'vuex';
import { IRootState } from '../model/store.model';
import { IOrderIntegration, IOrderIntegrationState } from '../model/order-integration.model';
import { orderIntegrationService } from '../services/_singletons';

const integrationStore: Module<IOrderIntegrationState, IRootState> = {
  state: () => ({
    count: 0,
    integrations: [],
  }),
  mutations: {
    setIntegrationsCount(state, payload: number): void {
      state.count = payload;
    },
    setIntegrations(state, payload: IOrderIntegration[]): void {
      state.integrations = payload;
    },
  },
  actions: {
    async getIntegrations(context) {
      const response = await orderIntegrationService.getOrderIntegrations(1, 1000, []);
      context.commit('setIntegrationsCount', response.entities.length);
      context.commit('setIntegrations', response.entities);
    },
  },
  getters: {},
  namespaced: true,
};

export default integrationStore;
