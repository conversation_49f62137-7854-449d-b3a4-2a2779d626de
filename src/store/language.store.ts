import { Module } from 'vuex';
import { ILanguage, ILanguageStoreState } from '../model/language.model';
import { IRootState } from '../model/store.model';
import {
  languageService,
  loadingService,
  loggingService,
  notificationService,
} from '../services/_singletons';

const languageStore: Module<ILanguageStoreState, IRootState> = {
  state: () => ({
    languages: [],
  }),
  mutations: {
    setLanguages(state, languages: ILanguage[]) {
      // eslint-disable-next-line no-param-reassign
      state.languages = languages;
    },
  },
  actions: {
    async syncLanguages(context) {
      try {
        loadingService.startLoading('main-loader:loading-languages');
        const languages = await languageService.getLanguages();
        context.commit('setLanguages', languages);
      } catch (e) {
        notificationService.showError('Error loading languages');
        loggingService.error('Error loading languages', e);
      } finally {
        loadingService.stopLoading('main-loader:loading-languages');
      }
    },
  },
  getters: {},
  namespaced: true,
};

export default languageStore;
