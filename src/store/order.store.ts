/* eslint-disable no-param-reassign */
import {Modu<PERSON>} from 'vuex';
import {IOrderStoreState} from '../model/order.model';
import {IRootState} from '../model/store.model';
import {
  loadingService,
  loggingService,
  notificationService,
  orderService,
} from '../services/_singletons';
import {noop} from "lodash-es";

const orderStore: Module<IOrderStoreState, IRootState> = {
  state: () => ({
    count: 0,
  }),
  mutations: {
    setCount(state, payload: number): void {
      state.count = payload;
    },
  },
  actions: {
    async getOpenOrderCount(
      context,
      payload: { withoutLoading: boolean } = {withoutLoading: false},
    ): Promise<void> {
      try {
        if (!payload.withoutLoading) {
          loadingService.startLoading('main-loader:order-count');
        }
        const {count} = await orderService.getUnprocessedOrderCount();
        context.commit('setCount', count);
      } catch (e) {
        loggingService.error('Error while getting open order count', e);
        notificationService.showError('Error while getting open order count');
      } finally {
        if (!payload.withoutLoading) {
          loadingService.stopLoading('main-loader:order-count');
        }
      }
    },
    refreshOrders: noop,
  },
  getters: {},
  namespaced: true,
};

export default orderStore;
