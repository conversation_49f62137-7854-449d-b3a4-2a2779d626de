import { DirectiveBinding, ObjectDirective } from 'vue';

type CallbackType = (event: Event) => void;

interface ExtendedHTMLElement extends HTMLElement {
  clickOutsideEvent: CallbackType;
}

const vClickOutside: ObjectDirective<ExtendedHTMLElement, CallbackType> = {
  beforeMount: (el: ExtendedHTMLElement, binding: DirectiveBinding<CallbackType>) => {
    // eslint-disable-next-line no-param-reassign
    el.clickOutsideEvent = (event: Event) => {
      if (!(event.target instanceof Node)) {
        return;
      }

      // here I check that click was outside the el and his children
      if (!(el === event.target || el.contains(event.target))) {
        // and if it did, call method provided in attribute value
        binding.value(event);
      }
    };
    document.addEventListener('click', el.clickOutsideEvent);
  },
  unmounted: (el) => {
    document.removeEventListener('click', el.clickOutsideEvent);
  },
};

export default vClickOutside;
