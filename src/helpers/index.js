export function danishNumberFormat(str) {
  const chars = {
    ',': '.', '.': ','
  };

  let s = thousands_separators(str);
  s = s.replace(/[.,]/g, (m) => chars[m]);
  return s;
}

export function thousands_separators(num) {
  if (num) {
    const num_parts = num.toString().split(".");
    num_parts[0] = num_parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    return num_parts.join(".");
  }
  return '0'
}

export const hash = Math.floor(Math.random() * 90000) + 10000;