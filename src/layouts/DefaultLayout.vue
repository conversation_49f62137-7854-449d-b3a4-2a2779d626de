<script setup lang="ts">
  import { onBeforeMount } from 'vue';
  import HelperService from '../services/helper.service';
  import { useStore } from '../store';

  const $store = useStore();

  onBeforeMount(() => {
    document.title = 'Shipvagoo';
    $store.dispatch('languageStore/syncLanguages');
    HelperService.switchToDarkColorScheme();
  });
</script>

<template>
  <header>
    <img src="/assets/img/logo.svg" alt="" decoding="async" />
  </header>
  <router-view></router-view>
</template>

<style scoped lang="scss">
  header {
    padding: 24px 40px;

    img {
      height: 21px;
    }
  }
</style>
