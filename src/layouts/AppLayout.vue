<template>
  <div class="shipvagoo-app-layout">
    <shipvagoo-header-v1 class="shipvagoo-header" />
    <div class="content-container">
      <router-view key="`app_version_1.0.1`" />
      <div class="version-box">Version:{{ $store.state.userStore.version }}</div>
    </div>
  </div>

  <q-dialog
    @keydown.esc="
      () => {
        $store.state.shipmentStore.isCreateShipmentModalOpen = false;
      }
    "
    position="top"
    @hide="onHideShipmentModal"
    :model-value="$store.state.shipmentStore.isCreateShipmentModalOpen"
    no-backdrop-dismiss
    style="border-radius: 10px !important"
  >
    <create-shipment />
  </q-dialog>
  <q-dialog
    @hide="onHidePdfSettingModal"
    @keydown.esc="
      () => {
        $store.state.printerStore.isPdfSettingsModalOpen = false;
      }
    "
    :model-value="$store.state.printerStore.isPdfSettingsModalOpen"
    no-backdrop-dismiss
  >
    <pdf-settings @close="onHidePdfSettingModal" />
  </q-dialog>
  <q-dialog
    @hide="onHideApiAccessDialog"
    @keydown.esc="
      () => {
        $store.state.printerStore.isApiAccessModalOpen = false;
      }
    "
    :model-value="$store.state.printerStore.isApiAccessModalOpen"
    no-backdrop-dismiss
  >
    <api-access @close="onHideApiAccessDialog" />
  </q-dialog>

  <!-- <q-dialog v-model="update_available" persistent>
    <q-card>
      <q-card-section>
        <div class="text-h6 text-dark">Update Available</div>
      </q-card-section>

      <q-card-section class="q-pt-none text-dark">
        New version of app is available. Please update it now.
      </q-card-section>

      <q-card-actions align="right">
        <q-btn label="Update" size="md" color="primary" @click="updateApp" />
      </q-card-actions>
    </q-card>
  </q-dialog> -->
</template>

<script setup lang="ts">
  import {
    onBeforeMount,
    onBeforeUnmount,
    onBeforeUpdate,
    onMounted,
    //  ref,
    //getCurrentInstance,
  } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { useStore } from '../store';
  import LanguageService from '../services/language.service';
  import HelperService from '../services/helper.service';
  //import {app} from '../config/app'

  //const instance = getCurrentInstance();
  const $store = useStore();
  const { locale } = useI18n();
  // Set type as any to avoid error
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  //let orderCountInterval: any;
  //const update_available = ref<boolean>(false);
  onBeforeMount(() => {
    document.title = 'Shipvagoo';
    HelperService.switchToDarkColorScheme();
    $store.dispatch('userStore/syncUser');
    $store.dispatch('companyStore/syncCompanyData');
    // $store.dispatch('pickSettingsStore/syncPickSettings');
    $store.dispatch('courierStore/getCarriers');
    $store.dispatch('courierStore/getCarrierProducts');
    $store.dispatch('countryStore/syncCountries');
    //$store.dispatch("orderStore/getOpenOrderCount");
    $store.dispatch('languageStore/syncLanguages');
    $store.dispatch('printerStore/syncPrinters');
    $store.dispatch('paymentSettingsStore/syncPaymentSettings');
    $store.dispatch('integrationStore/getIntegrations');
    $store.dispatch('returnPortalStore/getReturnPortals');
    /* orderCountInterval = setInterval(() => {
      $store.dispatch("orderStore/getOpenOrderCount", { withoutLoading: true });
    }, 60000); */
    locale.value = LanguageService.getLanguage();
  });
  const onHideShipmentModal = () => {
    $store.state.shipmentStore.isCreateShipmentModalOpen = false;
    $store.state.shipmentStore.fulfillmentCode = '';
    $store.state.shipmentStore.shipmentCode = '';
  };
  const onHidePdfSettingModal = () => {
    $store.state.printerStore.isPdfSettingsModalOpen = false;
  };
  const onHideApiAccessDialog = () => {
    $store.state.printerStore.isApiAccessModalOpen = false;
  };
  /*  const updateApp = () => {
    self.addEventListener('activate', (event: any) => {
      const cacheAllowlist = ['static-resources', 'api-cache'];
      event.waitUntil(
        caches.keys().then((cacheNames) => {
          return Promise.all(
            cacheNames.map((cacheName) => {
              if (!cacheAllowlist.includes(cacheName)) {
                return caches.delete(cacheName);
              }
            }),
          );
        }),
      );
    });
    instance?.proxy?.$forceUpdate();
    localStorage.clear();
    location.reload();
    localStorage.setItem('installed_version', $store.state.userStore.version);
    update_available.value = false;
  };
  const shouldUpdate = () => {
    const installed_version: string | null = localStorage.getItem('installed_version');
    const version: string = $store.state.userStore.version;
    if (version != '' && installed_version != version) {
      update_available.value = true;
    }
  }; */
  onBeforeUpdate(() => {
    //shouldUpdate();
  });
  onMounted(() => {
    console.log('APP VERSION ', $store.state.userStore.version);
  });
  onBeforeUnmount(() => {
    //clearInterval(orderCountInterval);
  });
</script>

<style lang="scss">
  @use '../assets/scss/_font';
  @use '../assets/scss/shipvagoo';

  #app {
    font-weight: 400;
  }

  body {
    background-color: white !important;
  }

  .shipvagoo-app-layout {
    //height: 100%;
    display: flex;
    flex-direction: column;
    height: 100vh; /* Ensure the layout takes the full viewport height */
  }

  .version-box {
    position: fixed;
    bottom: 0;
    width: 100%;
    padding-right: 15px;
    font-size: 12px;
    color: #aaaaaa;
    text-align: right;
  }

  .shipvagoo-header {
    position: sticky;
    top: 0;
    z-index: 100; /* Ensure the header is above other content */
  }

  .content-container {
    flex: 1;
    overflow-y: auto; /* Allow content to scroll within this container */
  }
</style>
