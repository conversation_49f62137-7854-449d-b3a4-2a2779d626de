<template>
  <div class="shipvagoo-return-container">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onBeforeMount } from "vue";
import HelperService from "../services/helper.service";

onBeforeMount(() => {
  document.title = "Return Portal | Shipvagoo";
  HelperService.switchToLightColorScheme();
});
</script>

<style lang="scss">
.shipvagoo-return-container {
  width: 100%;
  height: 100%;
  color: #000000;
  background-color: white;
}
</style>
