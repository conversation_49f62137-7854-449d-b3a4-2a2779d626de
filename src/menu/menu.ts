import store from "../store";

export interface IMenu {
  href: string;

  /**
   * onClick overrides href
   */
  onClick?: () => unknown | Promise<unknown>;
  title: string;
  child?: IMenu[];
  type?: string,
  key?: string,
  icon?: {
    element: string;
    attributes: {
      src: string;
      hover_src?: string,
      original?: string,
      size?: string
    };
  };
  isHidden?: boolean;
}

const shipmentsSub: IMenu[] = [
  {
    href: "",
    onClick: () => store.dispatch("shipmentStore/openCreateShipmentModal"),
    title: "New Shipment"
  },
  {
    href: "/shipped",
    title: "Shipments"
  },
  {
    href: "/drafts",
    title: "Drafts"
  },
  {
    href: "/contacts",
    title: "Contacts"
  }
];

const orderSub: IMenu[] = [
  {
    title: "Orders",
    href: "/orders"
  },

  {
    title: "Order Invoice",
    href: "/order-invoices"
  }
];
const paymentSub: IMenu[] = [
  {
    title: "Transactions",
    href: "/transactions"
  },

  {
    title: "Billing",
    href: "/billing"
  }, {
    title: "Balance",
    href: "/add-funds"
  }
];

const settingsSub: IMenu[] = [
  {
    title: "Account",
    href: "/company"
  },

  {
    title: "Integration",
    href: "/order-integration"
  },
  {
    title: "Payment",
    href: "/payment-settings"
  },
  {
    title: "Tracking Messages",
    href: "/personalized-messages"
  },
  {
    title: "Return Portal",
    href: "/return-portals"
  },
  {
    title: "Printer",
    href: "/printers"
  },
  {
    title: "Shipping Rules",
    href: "/templates"
  },
  {
    title: "Agreement",
    href: "/agreement"
  },
];
const menu: IMenu[] = [
  {
    href: "/",
    title: "Dashboard",
    icon: {
      element: "img",
      attributes: {
        src: "/assets/img/dashboard.svg",
        hover_src: "/assets/img/dashboard_hover.svg",
        original: "/assets/img/dashboard.svg",
        size: "12px"
      }
    }
  },
  {
    href: "",
    title: "Shipments",
    icon: {
      element: "img",
      attributes: {
        src: "/assets/img/shipments.svg",
        hover_src: "/assets/img/shipments_hover.svg",
        original: "/assets/img/shipments.svg",
        size: "20px"
      }
    },
    child: shipmentsSub
  },
  {
    href: "",
    title: "Orders",
    icon: {
      element: "img",
      attributes: {
        src: "/assets/img/order.svg",
        hover_src: "/assets/img/order_hover.svg",
        original: "/assets/img/order.svg",
        size: "14px"
      }
    },
    child: orderSub
  },
  {
    href: "/returns",
    title: "Returns",
    icon: {
      element: "img",
      attributes: {
        src: "/assets/img/returns.svg",
        hover_src: "/assets/img/returns.svg",
        original: "/assets/img/returns.svg",
        size: "15px"
      }
    }
  },
  {
    href: "",
    title: "Payments",
    icon: {
      element: "img",
      attributes: {
        src: "/assets/img/billing.svg",
        hover_src: "/assets/img/billing_hover.svg",
        original: "/assets/img/billing.svg",
        size: "15px"
      }
    },
    child: paymentSub
  },
  {
    href: "",
    title: "Settings",
    icon: {
      element: "img",
      attributes: {
        src: "/assets/img/settings.svg",
        hover_src: "/assets/img/settings.svg",
        original: "/assets/img/settings.svg",
        size: "15px"
      }
    },
    child: settingsSub
  }
];

const iconMenu: IMenu[] = [

  {
    href: "/help",
    title: "common.help",
    type: "page",
    icon: {
      element: "img",
      attributes: {
        src: "/assets/img/help.svg"
      }
    }
  },
  {
    href: "",
    title: "common.settings",
    type: "dropdown",
    key: "settings",
    icon: {
      element: "img",
      attributes: {
        src: "/assets/img/settings.svg"
      }
    },
    child: settingsSub
  },
  {
    href: "",
    title: "Add Funds",
    type: "dialog",
    key: "add_funds",
    icon: {
      element: "img",
      attributes: {
        src: "/assets/img/add_funds.svg"
      }
    }
  },
  {
    href: "",
    title: "Logout",
    type: "dialog",
    key: "logout",
    icon: {
      element: "img",
      attributes: {
        src: "/assets/img/power_off.svg"
      }
    }
  }
];

const removeHiddenMenuItems = (_menu: IMenu[]) =>
  _menu.filter((item) => {
    if (item.child) {
      // eslint-disable-next-line no-param-reassign
      item.child = removeHiddenMenuItems(item.child);
    }

    return !item.isHidden;
  });
const _my_menu = removeHiddenMenuItems(menu);
const _my_menu1 = removeHiddenMenuItems(iconMenu);
export { _my_menu, _my_menu1 };