// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AddBalance: typeof import('./components/billing/add-balance.vue')['default']
    AddCard: typeof import('./components/add-card.vue')['default']
    AdditionalServiceCheck: typeof import('./components/shipment/additional-service-check.vue')['default']
    AdditionalServices: typeof import('./components/shipment/additional-services.vue')['default']
    AddNewBox: typeof import('./components/add-new-box.vue')['default']
    AdvanceDialogLoader: typeof import('./components/orders/v1/advance-dialog-loader.vue')['default']
    Alert: typeof import('./components/alert.vue')['default']
    ApiAccess: typeof import('./components/api-access.vue')['default']
    BarChartOverview: typeof import('./components/bar-chart-overview.vue')['default']
    BigSearch: typeof import('./components/big-search.vue')['default']
    BillingInvoicesList: typeof import('./components/billing-invoices-list.vue')['default']
    BulkOrdersCapturePayment: typeof import('./components/orders/bulk-orders-capture-payment.vue')['default']
    CancelShipment: typeof import('./components/shipment/cancel-shipment.vue')['default']
    CapturePayment: typeof import('./components/orders/capture-payment.vue')['default']
    ChangeShipmentTemplate: typeof import('./components/orders/change-shipment-template.vue')['default']
    ChatBot: typeof import('./components/Chat-bot.vue')['default']
    CheckoutRuleList: typeof import('./components/v1/integrations/shipping-methods/checkout-rule-list.vue')['default']
    ConfirmLogout: typeof import('./components/v1/confirm-logout.vue')['default']
    ConfirmPopup: typeof import('./components/confirm-popup.vue')['default']
    ConfirmRepurchase: typeof import('./components/shipment/confirm-repurchase.vue')['default']
    ConfirmReturn: typeof import('./components/shipment/confirm-return.vue')['default']
    copy: typeof import('./components/monthly-invoices-list copy.vue')['default']
    CreateFulfillmentsAndShipments: typeof import('./components/orders/create-fulfillments-and-shipments.vue')['default']
    CreateFulfillmentsShipmentsPackingSlips: typeof import('./components/orders/create-fulfillments-shipments-packing-slips.vue')['default']
    CreatePickLists: typeof import('./components/orders/create-pick-lists.vue')['default']
    CreatePickListsFulfillmentsAndShipments: typeof import('./components/orders/create-pick-lists-fulfillments-and-shipments.vue')['default']
    CreateShipment: typeof import('./components/create-shipment.vue')['default']
    CreditInvoiceSettlement: typeof import('./components/billing/credit-invoice-settlement.vue')['default']
    CreditNotes: typeof import('./components/credit-notes.vue')['default']
    CreditNotesList: typeof import('./components/credit-notes-list.vue')['default']
    DaoRequirements: typeof import('./components/shipment/dao-requirements.vue')['default']
    DashboardTaskAlert: typeof import('./components/v1/dashboard/DashboardTaskAlert.vue')['default']
    DashboardTile: typeof import('./components/v1/dashboard/DashboardTile.vue')['default']
    DisconnectPopup: typeof import('./components/v1/integrations/disconnect-popup.vue')['default']
    EcommerceIntegrationItem: typeof import('./components/v1/integrations/ecommerce-integration-item.vue')['default']
    EditBillingAddress: typeof import('./components/customers/edit-billing-address.vue')['default']
    EditReceiver: typeof import('./components/orders/edit-receiver.vue')['default']
    ErrorAlertV1: typeof import('./components/ui-kit/v1/error-alert-v1.vue')['default']
    FilterChip: typeof import('./components/filter-chip.vue')['default']
    FulfillmentModal: typeof import('./components/orders/v1/modals/fulfillment-modal.vue')['default']
    GeneralPopup: typeof import('./components/general-popup.vue')['default']
    GlsRequirements: typeof import('./components/shipment/gls-requirements.vue')['default']
    InfoBar: typeof import('./components/info-bar.vue')['default']
    InfoBarV1: typeof import('./components/v1/info-bar-v1.vue')['default']
    InvoicesList: typeof import('./components/invoices-list.vue')['default']
    Loader: typeof import('./components/loader.vue')['default']
    MakePayment: typeof import('./components/v1/payments/make-payment.vue')['default']
    MiniShipmentChargeTable: typeof import('./components/shipment/mini-shipment-charge-table.vue')['default']
    MonthlyCreditNotes: typeof import('./components/monthly-credit-notes.vue')['default']
    MonthlyCreditNotesList: typeof import('./components/monthly-credit-notes-list.vue')['default']
    MonthlyInvoicesList: typeof import('./components/monthly-invoices-list.vue')['default']
    OrderActionResultItem: typeof import('./components/orders/v1/order-action-result-item.vue')['default']
    OrderDetail: typeof import('./components/orders/v1/order-detail.vue')['default']
    OrderDetailTimeline: typeof import('./components/orders/order-detail-timeline.vue')['default']
    OrderFinancialStatus: typeof import('./components/orders/order-financial-status.vue')['default']
    OrderFulfillment: typeof import('./components/orders/v1/ordr-detail-cards/order-fulfillment.vue')['default']
    OrderFulfillmentRefundModal: typeof import('./components/orders/v1/modals/order-fulfillment-refund-modal.vue')['default']
    OrderFulfillmentReturn: typeof import('./components/orders/v1/ordr-detail-cards/order-fulfillment-return.vue')['default']
    OrderFulfillmentReturnModal: typeof import('./components/orders/v1/modals/order-fulfillment-return-modal.vue')['default']
    OrderIntegrationItem: typeof import('./components/v1/integrations/order-integration-item.vue')['default']
    OrderOnholdItems: typeof import('./components/orders/v1/ordr-detail-cards/order-onhold-items.vue')['default']
    OrderUnfulfilledItems: typeof import('./components/orders/v1/ordr-detail-cards/order-unfulfilled-items.vue')['default']
    ParcelTable: typeof import('./components/parcel-table.vue')['default']
    PaymentCard: typeof import('./components/v1/payments/payment-card.vue')['default']
    PaymentMethod: typeof import('./components/v1/payments/payment-method.vue')['default']
    PaymentReceipt: typeof import('./components/v1/pdfs/PaymentReceipt.vue')['default']
    PayWithCard: typeof import('./components/pay-with-card.vue')['default']
    PdfFooter: typeof import('./components/v1/pdfs/PdfFooter.vue')['default']
    PdfSettings: typeof import('./components/pdf-settings.vue')['default']
    PrinterDetail: typeof import('./components/printers/printer-detail.vue')['default']
    PrinterItem: typeof import('./components/v1/printer-item.vue')['default']
    ProductDetail: typeof import('./components/products/product-detail.vue')['default']
    QuickAnswer: typeof import('./components/help/quick-answer.vue')['default']
    RefundSummary: typeof import('./components/orders/v1/ordr-detail-cards/refund-summary.vue')['default']
    ReturnMethod: typeof import('./components/return/return-method.vue')['default']
    ReturnPortalDetail: typeof import('./components/return/return-portal-detail.vue')['default']
    ReturnPortalItem: typeof import('./components/return-portal-item.vue')['default']
    ReturnReason: typeof import('./components/return/return-reason.vue')['default']
    ReturnResolution: typeof import('./components/return/return-resolution.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ServicePointMap: typeof import('./components/service-point-map.vue')['default']
    ShipmentCard: typeof import('./components/shipment/shipment-card.vue')['default']
    ShipmentDetail: typeof import('./components/shipment/shipment-detail.vue')['default']
    ShippingMethodDetail: typeof import('./components/shipping-method/shipping-method-detail.vue')['default']
    ShippingMethodDetailV1: typeof import('./components/shipping-method/shipping-method-detail-v1.vue')['default']
    ShippingRuleListingModal: typeof import('./components/v1/integrations/shipping-rule-listing-modal.vue')['default']
    ShipvagooBookingStatus: typeof import('./components/ui-kit/shipvagoo-booking-status.vue')['default']
    ShipvagooButton: typeof import('./components/ui-kit/shipvagoo-button.vue')['default']
    ShipvagooButtonDropdown: typeof import('./components/ui-kit/shipvagoo-button-dropdown.vue')['default']
    ShipvagooButtonDropdownV1: typeof import('./components/ui-kit/v1/shipvagoo-button-dropdown-v1.vue')['default']
    ShipvagooCardActions: typeof import('./components/ui-kit/shipvagoo-card-actions.vue')['default']
    ShipvagooCardSection: typeof import('./components/ui-kit/shipvagoo-card-section.vue')['default']
    ShipvagooCheckbox: typeof import('./components/ui-kit/shipvagoo-checkbox.vue')['default']
    ShipvagooCheckboxCard: typeof import('./components/ui-kit/shipvagoo-checkbox-card.vue')['default']
    ShipvagooCheckboxV1: typeof import('./components/ui-kit/v1/shipvagoo-checkbox-v1.vue')['default']
    ShipvagooCountryIcon: typeof import('./components/ui-kit/shipvagoo-country-icon.vue')['default']
    ShipvagooExpansionItem: typeof import('./components/ui-kit/shipvagoo-expansion-item.vue')['default']
    ShipvagooFab: typeof import('./components/ui-kit/shipvagoo-fab.vue')['default']
    ShipvagooFilepicker: typeof import('./components/ui-kit/shipvagoo-filepicker.vue')['default']
    ShipvagooHeader: typeof import('./components/shipvagoo-header.vue')['default']
    ShipvagooHeaderV1: typeof import('./components/v1/shipvagoo-header-v1.vue')['default']
    ShipvagooHyperlink: typeof import('./components/ui-kit/shipvagoo-hyperlink.vue')['default']
    ShipvagooInput: typeof import('./components/ui-kit/shipvagoo-input.vue')['default']
    ShipvagooInputGroup: typeof import('./components/ui-kit/shipvagoo-input-group.vue')['default']
    ShipvagooInputV1: typeof import('./components/ui-kit/v1/shipvagoo-input-v1.vue')['default']
    ShipvagooLabel: typeof import('./components/ui-kit/shipvagoo-label.vue')['default']
    ShipvagooList: typeof import('./components/ui-kit/shipvagoo-list.vue')['default']
    ShipvagooMenu: typeof import('./components/ui-kit/shipvagoo-menu.vue')['default']
    ShipvagooMenuItem: typeof import('./components/ui-kit/shipvagoo-menu-item.vue')['default']
    ShipvagooModal: typeof import('./components/ui-kit/shipvagoo-modal.vue')['default']
    ShipvagooModalV1: typeof import('./components/ui-kit/v1/shipvagoo-modal-v1.vue')['default']
    ShipvagooMultiSelect: typeof import('./components/ui-kit/shipvagoo-multi-select.vue')['default']
    ShipvagooPlaceholder: typeof import('./components/ui-kit/shipvagoo-placeholder.vue')['default']
    ShipvagooRadio: typeof import('./components/ui-kit/shipvagoo-radio.vue')['default']
    ShipvagooRadioCard: typeof import('./components/ui-kit/shipvagoo-radio-card.vue')['default']
    ShipvagooSelect: typeof import('./components/ui-kit/shipvagoo-select.vue')['default']
    ShipvagooSelectV1: typeof import('./components/ui-kit/v1/shipvagoo-select-v1.vue')['default']
    ShipvagooSeparator: typeof import('./components/ui-kit/shipvagoo-separator.vue')['default']
    ShipvagooSidebarMenu: typeof import('./components/shipvagoo-sidebar-menu.vue')['default']
    ShipvagooStatusTag: typeof import('./components/ui-kit/v1/shipvagoo-status-tag.vue')['default']
    ShipvagooStepper: typeof import('./components/ui-kit/shipvagoo-stepper.vue')['default']
    ShipvagooStepperV1: typeof import('./components/ui-kit/v1/shipvagoo-stepper-v1.vue')['default']
    ShipvagooTabfilter: typeof import('./components/ui-kit/shipvagoo-tabfilter.vue')['default']
    ShipvagooTabfilterV1: typeof import('./components/ui-kit/v1/shipvagoo-tabfilter-v1.vue')['default']
    ShipvagooTable: typeof import('./components/ui-kit/shipvagoo-table.vue')['default']
    ShipvagooTableV1: typeof import('./components/ui-kit/v1/shipvagoo-table-v1.vue')['default']
    ShipvagooTabs: typeof import('./components/ui-kit/shipvagoo-tabs.vue')['default']
    ShipvagooTextarea: typeof import('./components/ui-kit/shipvagoo-textarea.vue')['default']
    ShipvagooTooltip: typeof import('./components/ui-kit/shipvagoo-tooltip.vue')['default']
    ShipvagooWysiwyg: typeof import('./components/ui-kit/shipvagoo-wysiwyg.vue')['default']
    ShipvagooWysiwygV1: typeof import('./components/ui-kit/v1/shipvagoo-wysiwyg-v1.vue')['default']
    ShopifyInstall: typeof import('./components/v1/integrations/shopify-install.vue')['default']
    StartPickPath: typeof import('./components/orders/start-pick-path.vue')['default']
    StatisValues: typeof import('./components/v1/dashboard/StatisValues.vue')['default']
    StoreModal: typeof import('./components/v1/integrations/store-modal.vue')['default']
    TemplateModal: typeof import('./components/shipment/template-modal.vue')['default']
    TitleBar: typeof import('./components/title-bar.vue')['default']
    TrackingMessageItem: typeof import('./components/tracking-message-item.vue')['default']
    TrackingMessageModal: typeof import('./components/tracking-message-modal.vue')['default']
  }
}
