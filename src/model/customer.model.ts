import { ICompany } from './company.model';
import { ICountry } from './country.model';
import { IOrderIntegration } from './order-integration.model';

export interface IAddress {
  id: number;
  zip: `${number}`;
  city: string;
  name: string;
  phone: string | null;
  country: string;
  company: string | null;
  default: boolean;
  address1: string;
  address2: string | null;
  province: string | null;
  last_name: string;
  first_name: string;
  customer_id: number;
  country_code: string;
  province_code: string | null;
  country_name: string;
}

export interface ICustomer {
  id: number;
  customer_code: number;
  integration_code: number;
  company_code: number;
  country_code: number;
  customer_id: number;
  email: string;
  first_name: string;
  last_name: string;
  accepts_marketing: 0 | 1;
  state: 'disabled';
  tags: '';
  note: null;
  verified_email: 1;
  phone: string | null;
  address1: string | null;
  address2: string | null;
  city: string | null;
  zipcode: string | null;
  mobile: string | null;
  currency: 'DKK';
  addresses: IAddress[];
  default_address: IAddress;
  company: ICompany | null;
  integration: IOrderIntegration | null;
  country: ICountry | null;
  created_at: string;
  updated_at: string;
}

export interface ICustomerRequest {
  first_name: string;
  last_name: string;
  email: string | null;
  phone: string;
  address1: string;
  address2: string | null;
  zipcode: string;
  city: string;
  country_code: number;
}