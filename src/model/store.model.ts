import { IShipmentStoreState } from "./shipment.model";
import { ICompanyState } from "./company.model";
import { ICountryState } from "./country.model";
import { IPickSettingsState } from "./pick-settings.model";
import { IUserState } from "./user.model";
import { ICourierStoreState } from "./carrier.model";
import { IOrderStoreState } from "./order.model";
import { ILanguageStoreState } from "./language.model";
import { IDashboardStoreState } from "./dashboard.model";
import { IPrinterStoreState } from "./printer.model";
import { IPaymentSetting } from "./payment.model";
import { IOrderIntegrationState } from "./order-integration.model";
import { IReturnPortalStoreState } from "./return-portal.model";

export interface IRootState {
  userStore: IUserState;
  companyStore: ICompanyState;
  pickSettingsStore: IPickSettingsState;
  countryStore: ICountryState;
  shipmentStore: IShipmentStoreState;
  courierStore: ICourierStoreState;
  orderStore: IOrderStoreState;
  languageStore: ILanguageStoreState;
  dashboardStore: IDashboardStoreState;
  printerStore: IPrinterStoreState;
  paymentSettingsStore: IPaymentSetting;
  integrationStore: IOrderIntegrationState;
  returnPortalStore: IReturnPortalStoreState;
}
