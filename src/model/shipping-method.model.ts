// import { ICompany } from './company.model';

import {ICountry} from "~/model/country.model";
import {ICarrier, IShippingProduct} from "~/model/carrier.model";

export interface IShippingMethod {
  currency_code: number;
  title: string;
  description: string | null;
  price: number;
  order_amount_based: boolean;
  order_amount_minimum: number | null;
  order_amount_maximum: number | null;
  order_weight_based: boolean;
  order_weight_minimum: number | null;
  order_weight_maximum: number | null;
  item_quantity_based: boolean;
  item_quantity_minimum: number | null;
  item_quantity_maximum: number | null;
  zipcode_based: boolean;
  zipcode_from: number | null;
  zipcode_to: number | null;
  country: ICountry | null;
  carrier: ICarrier | null;
  shippingProduct: IShippingProduct | null;
  number_of_pickup_points: number;
  integration_code: number;
  deleted_at: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface IShippingMethodResponse {
  id: number;
  currency_code: number;
  title: string;
  description: string | null;
  price: number;
  order_amount_based: boolean;
  order_amount_minimum: number | null;
  order_amount_maximum: number | null;
  order_weight_based: boolean;
  order_weight_minimum: number | null;
  order_weight_maximum: number | null;
  item_quantity_based: boolean;
  item_quantity_minimum: number | null;
  item_quantity_maximum: number | null;
  zipcode_based: boolean;
  zipcode_from: number | null;
  zipcode_to: number | null;
  country_code: number | null;
  carrier_code: number | null;
  carrier_product_code: number | null;
  number_of_pickup_points: number;
  integration_code: number;
  deleted_at: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface IShippingMethodCreate {
  currency_code: number;
  title: string;
  description: string | null;
  price: string;
  order_amount_based: boolean;
  order_amount_minimum: string | null;
  order_amount_maximum: string | null;
  order_weight_based: boolean;
  order_weight_minimum: number | null;
  order_weight_maximum: number | null;
  item_quantity_based: boolean;
  item_quantity_minimum: number | null;
  item_quantity_maximum: number | null;
  zipcode_based: boolean;
  zipcode_from: number | null;
  zipcode_to: number | null;
  country_code: number;
  carrier_code: number;
  carrier_product_code: number;
  number_of_pickup_points: number;
  integration_code: string | number | null;
}

export interface ICurrency {
  currency: string;
  currency_code: number;
}