import { ICompany } from "./company.model";
import { ICountry } from "./country.model";
import { ILanguage } from "./language.model";
import { IOrderIntegration } from "./order-integration.model";
import { IOrder } from "./order.model";
import { ITemplate } from "./template.model";
import { IShipment } from "~/model/shipment.model";

/**
 * TODO: Not finalized yet
 */
export interface IReturnPortal {
  zipcode: string;
  return_portal_code: number;
  company_code: number;
  portal_name: string;
  is_active: 0 | 1;
  confirmation_note: string | null;
  sender_country_code: number;
  language_code: number;
  sender_country: ICountry | null;
  integration_code: number;
  integration: IOrderIntegration | null;
  language: ILanguage | null;
  company: ICompany | null;
  company_name: string;
  country_code: number;
  attn: string;
  phone: string;
  email: string;
  address: string;
  logo: string | undefined;
  city: string;
  country: ICountry | null;
  page_title: string;
  content: string;
  meta: any;
  created_at: string;
  updated_at: string;
}

export interface IReturnPortalCreate {
  portal_name: string;
  sender_country_code: number;
  language_code: number;
  integration_code: number;
  confirmation_note: string | null;
  is_active: boolean;
}

export interface IReturnPortalUpdate {
  portal_url: string;
}

export interface IReturnPortalReason {
  return_reason_code: number;
  return_portal_code: number;
  description: string;
  enable_comment: 0 | 1;
  comment_required: 0 | 1;
  available_days: number;
  status: 0 | 1;
}

export interface IReturnPortalReasonCreate {
  available_days: number | null;
  enable_comment: boolean | null;
  comment_required: boolean | null;
  description: string;
}

export interface IReturnPortalReasonUpdate {
  available_days?: number | null;
  enable_comment?: boolean | null;
  comment_required?: boolean | null;
  description?: string;
  status?: boolean;
}

export interface IReturnPortalResolution {
  resolution_code: number;
  return_portal_code: number;
  description: string;
  name: string;
  available_days: number;
  status: boolean;
}

export interface IReturnPortalResolutionCreate {
  description: string;
  name: string;
  available_days: number;
}

export interface IReturnPortalResolutionUpdate {
  description?: string;
  name?: string;
  available_days?: number;
  status?: boolean;
}

export interface IReturnPortalMethodCreate {
  template_code: number;
  description: string;
  name: string;
  parcelshop_code: string | null | undefined;
  price_type: string;
  currency_code: number;
  price: number;
}

export interface IReturnPortalMethod {
  method_code: number;
  name: string;
  description: string | null;
  price: number;
  currency_code: number;
  template_code: number;
  parcelshop_code: string | number | null;
  price_type: string;
  template: ITemplate | null;
  // TODO
  currency: null;
  created_at: string;
  updated_at: string;
}

export interface IReturnPortalReceiverUpdate {
  company_name: string | null;
  attn: string | null;
  // first_name: string;
  // last_name: string;
  zipcode: string;
  address: string;
  country_code: number;
  city: string;
  email: string;
  phone: string;
  logo: string | null;
  company_logo_url?: string | null;
}

export interface IReturnPortalTextUpdate {
  page_title: string;
  content: string;
}

export interface IVerifyPortalRequest {
  portal_name: string;
}

export interface IVerifyPortal {
  id: number;
  return_portal_code: number;
  company_code: number;
  portal_name: string;
  integration_code: number;
  sender_country_code: number;
  language_code: number;
  confirmation_note: string | null;
  is_active: 1 | 0;
  logo: string | undefined;
  attn: string;
  first_name: string;
  last_name: string;
  company_name: string;
  address: string;
  zipcode: string;
  city: string;
  country_code: number;
  phone: string;
  email: string;
  page_title: string;
  content: string;
  deleted_at: string | null;
  created_at: string;
  updated_at: string;
  access_token: string;
  user_access_token: string;
  company: any,
  logo_full_path?: string | null,
  meta: any
}

export interface IReturnOrder {
  return_order_code: number;
  order_code: number;
  return_reason_code?: number;
  method_code: number;
  resolution_code: number;
  resolution: IReturnPortalResolution | null;
  company_code: number;
  return_portal_code: number;
  transaction_status: string;
  updated_at: string;
  created_at: string;
  return_portal: IReturnPortal | null;
  order: IOrder | null;
  method: IReturnPortalMethod | null;
  shipment: IShipment;
  payment_url: string;
  return_order_items: IReturnOrderItem[] | null;
  payment_id?: string | null;
  is_return_fulfilled?: 0 | 1;
}

export interface IReturnOrderItem {
  return_order_item_code: number;
  return_order_code: number;
  line_item_code: number;
  quantity: number;
  return_reason?: string | null,
  comments?: string | null
}

export interface IReturnOrderRequest {
  order_code: number;
  method_code: number;
  return_reason_code?: number;
  resolution_code: number;
  transaction_status: string;
  comments?: string | null;
  line_items: {
    line_item_code: number;
    quantity: number;
    reason?: string | null,
    comment?: string | null
  }[];
}

export interface IReturnPortalStoreState {
  count: number;
}