import { ICompany } from "./company.model";

export interface IPrinter {
  id: number;
  name: string;
  printer_code: number;
  printer_app_code: number;
  company_code: number;
  linked_printer: string;
  format: "A4" | "10x19" | "ZPL";
  is_default_label: boolean;
  is_default_other: boolean;
  is_default_pick_documents: boolean;
  is_auto_print_pick_list: boolean;
  deleted_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface IPrinterCreate {
  name: string;
  printer_app_code: number;
  linked_printer: string;
  format: "A4" | "10x19" | "ZPL";
  is_default_label: boolean;
  is_default_other: boolean;
  is_default_pick_documents: boolean;
  is_auto_print_pick_list: boolean;
}

export interface IPrinterApp {
  id: number;
  printer_app_code: number;
  company_code: number;
  hostname: string;
  instance: number;
  printers: string[];
  platform: string;
  company: ICompany | null;
  deleted_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface IPrinterStoreState {
  printers: IPrinter[];
  defaultPrinterForLabel: IPrinter | null;
  defaultPrinterForOther: IPrinter | null;
  defaultPrinterForPickDocuments: IPrinter | null;
  isApiAccessModalOpen: boolean,
  isPdfSettingsModalOpen: boolean
}