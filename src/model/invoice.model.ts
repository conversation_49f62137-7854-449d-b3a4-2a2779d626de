import { IDbInfo } from '../model/http.model';
import { ITransaction } from '../model/transactions.model';
import { IShipment } from '../model/shipment.model';

export interface IInvoiceItem {
  type: string;
  items: [
    {
      name: string;
      count: 1;
      unit_price: string;
      weight_class: string;
    },
  ];
  country_pair: string;
}

export interface IInvoice extends IDbInfo {
  id: number;
  invoice_code: number;
  company_code: number;
  shipment_code: number;
  invoice_items: IInvoiceItem[];
  status: string;
  tax_percent: string;
  amount: number | null;
  shipment?: IShipment | null;
  company: null;
  transaction?: ITransaction;
  description?: string;
}

export interface IBillingInvoice extends IDbInfo {
  id: number;
  start_date: string;
  created_at: string;
  economic_invoice_code: number;
  economic_invoice_id: string;
  end_date: string;
  due_date: string;
  month: string;
  year: string;
  payment_status: string;
  total_amount: number;
}
