export interface IPricesResponse {
  carriers: ICarrier[];
  productWithClientIds: string;
}

export interface ICarrier {
  id: number;
  carrier_code: number;
  name: string;
  carrier_id: string;
  tracking_url: string;
  created_at: string;
  updated_at: string | null;
  products: IShippingProduct[];
  icon?: string
}

export interface IShippingProduct {
  id: number;
  carrier_product_code: number;
  carrier_code: number;
  name: string;
  note: string | null;
  deleted_at: string | null;
  hasMultiParcelShop: boolean;
  has_multi_parcel_shop: boolean;
  created_at: string;
  updated_at: string;
  product_id: string;
  rates: IRate[];
}

export interface IWeightClass {
  id: number;
  weight_class_code: number;
  min_weight: string | number;
  unit: string;
  max_weight: string | number;
  deleted_at: string | null;
  created_at: string;
  updated_at: string | null;
}

export interface IRate {
  id: number;
  price_code: number;
  cp_product_code: number;
  weight_class_code: number;
  price: string;
  deleted_at: string | null;
  created_at: string;
  updated_at: string;
  weight_class: IWeightClass;
}

export interface ICourierStoreState {
  carriers: ICarrier[];
  carrierProducts: IShippingProduct[];
}