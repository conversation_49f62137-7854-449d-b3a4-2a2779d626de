import { ICompany } from './company.model';
import { IShipment } from './shipment.model';

export interface ICreditNoteItem {
  count: string;
  unit_price: string;
  weight_class: string;
}

export interface ICreditNoteItemGroup {
  type: string;
  items: ICreditNoteItem[];
  country_pair: string;
}

export interface ICreditNote {
  id: number;
  credit_note_code: number;
  company_code: number;
  shipment_code: number;
  credit_note_items: ICreditNoteItemGroup[];
  tax_percent: string;
  amount: null;
  shipment: IShipment | null;
  company: ICompany | null;
  created_at: string;
  updated_at: string;
}
