import { IPackaging } from './packaging.model';
import { IShipment } from './shipment.model';

export interface IFulfillmentReturnRequestSingle {
  line_item_code: number;
  quantity: number;
  comment: string;
  reason: string;
  fulfillment_type: string;
  reference: number;
  name: string;
  return_order_code?: any;
}

export interface IFulfillmentRefundRequestSingle {
  line_item_code: number;
  quantity: number;
  price: string;
  fulfillment_code: number;
  restock_type: string;
}

export interface IFulfillmentReturnRequest {
  fulfillments: IFulfillmentReturnRequestSingle[];
  packaging_code: number | null;
  // resolution: string | null;
}

export interface IFulfillmentRefundRequest {
  fulfillments: IFulfillmentRefundRequestSingle[];
  shipping_charges: string | '0';
  tax_amount: string | number | '0';
}

export interface IFulfillment {
  fulfillment_code: number;
  refunded: number;
  name: string;
  order_code: number;
  packaging_code: null;
  line_item_code: number;
  shipment_code: number | null;
  shipment: IShipment | null;
  quantity: number;
  returned_quantity: number;
  is_cancelled: 0 | 1;
  packaging: IPackaging | null;
  created_at: string;
  updated_at: string;
  return_order_code?: number;
}

export interface ICancelFulfillReturnRequestSingle {
  line_item_code: number;
}

export interface ICancelFulfillReturnRequest {
  fulfillmentReturns: ICancelFulfillReturnRequestSingle[];
}
