import {IDbInfo} from './http.model';

export interface IPaymentGateway extends IDbInfo {
  payment_gateway_code: number;
  gateway_name: string;
}

export interface IProcessPayment {
  transaction_code: number;
}

export interface IProcessingPayment {
  link: string;
}

export interface ICardDetail {
  card_number: string,
  card_owner: string,
  expiry: string,
  card_type: 'master_card' | 'visa' | 'dk',
  status: boolean
}

export interface ICardResponse {
  accepted: boolean,
  bin: string,
  brand: 'master_card' | 'visa' | 'dk',
  card_code: number,
  exp_month: string,
  exp_year: string,
  holder_name: string,
  is_default: boolean,
  last4: string,
  qp_card_id: number
}

export interface IAddCardLinkResposne {
  link: string
}

export interface IPaymentSetting{
  payment_setting_code: number,
  company_code: number,
  payment_method: 'BALANCE' | 'SAVED_CARD' | 'CARD',
  cards: ICardResponse[] | null
}
