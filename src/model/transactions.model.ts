import { IDbInfo } from "./http.model";

interface IShipment {
  [key: string]: any;
}

interface IPaymentMethodCard {
  brand: string,
  bin: string,
  last4: string
}

interface ITransactionPaymentMethod {
  type: string,
  details: null | IPaymentMethodCard
}

export interface ITransaction extends IDbInfo {
  id: number;
  transaction_code: number;
  company_code: number;
  payment_gateway_code: number;
  payment_id: number;
  description: string;
  amount: number;
  transaction_status: string;
  transaction_type: string;
  balance: string;
  status: boolean;
  is_deleted: boolean;
  shipment?: IShipment | null,
  payment_method: ITransactionPaymentMethod | null,
  shipment_code?: number,
  invoice_items?: any,
  tax_percent?: any,
  meta?: {
    [key: string]: any
  }
}

export interface ICreatedTransaction {
  amount: string;
  payment_gateway_code: number;
  company_code: number;
  transaction_code: number;
}

export interface ICreateTransactionRequest {
  amount: number;
  payment_gateway_code: number;
}

export interface ITransactionState {
  transactions: ITransaction[];
}

export interface IAddFundRequest {
  amount: number | string;
}

export interface IVerifyPayment {
  payment_id: number | string,
  type?: string | null
}

export interface IAddFundResponse {
  link: string;
  payment_id: number;
  transaction_code: number;
}

export interface IAddFundWithSavedCardResponse {
  message: string;
  data: {
    payment_id: string | number
    transaction_code: string | number
  },
  status: string
}

export interface IVerifyPaymentResponse {
  message: string;
  data: {
    payment_id: string | number
    transaction_code: string | number
  },
  p_status: string
}
