import { IShippingProduct } from './carrier.model';
import {ICountry} from './country.model';
import {IParcel} from './shipment.model';

export interface ITemplate {
  id: number;
  template_code: number;
  name: string;
  carrier_product_code: number;
  sender_country_code: number;
  receiver_country_code: number;
  sender_country: ICountry | null;
  receiver_country: ICountry | null;
  parcels: IParcel[];
  is_default: 0 | 1;
  additional_services: number[] | null;
  carrier_product?:IShippingProduct,
  created_at: string;
  updated_at: string;
  ubsend_client_id?: number | null
}

export interface ITemplateRequest {
  ubsend_client_id: string;
  name: string;
  is_default: 0 | 1;
  carrier_product_code: number;
  sender_country_code: number;
  receiver_country_code: number;
  parcels: IParcel[];
  additional_services: number[];
}
