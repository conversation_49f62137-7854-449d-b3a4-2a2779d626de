import { IPackaging } from "./packaging.model";
import { IShipment } from "./shipment.model";

export interface IFulfillmentRequestSingle {
  line_item_code: number;
  quantity: number;
}

export interface IFulfillmentRequest {
  fulfillments: IFulfillmentRequestSingle[];
  packaging_code: number | null;
}

export interface IFulfillment {
  fulfillment_code: number;
  name: string;
  order_code: number;
  packaging_code: null;
  line_item_code: number;
  shipment_code: number | null;
  calculated_returned_quantity?: number;
  returned_quantity: number;
  reference: number | null;
  shipment: IShipment | null;
  quantity: number;
  is_cancelled: 0 | 1;
  refunded: number;
  packaging: IPackaging | null;
  created_at: string;
  updated_at: string;
  return_order_code?: any,
  reason?: string
  comment?: string
}

export interface ICancelFulfillRequestSingle {
  line_item_code: number;
}

export interface ICancelFulfillRequest {
  fulfillments: ICancelFulfillRequestSingle[];
}
