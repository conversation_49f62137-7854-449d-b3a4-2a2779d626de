interface ShippingProducts {
  name: string | number | '',
  count: string | number | 0
}

interface TopSellingProducts {
  product: string | number | '',
  count: string | number | 0
}

interface WeightClasses {
  weightClass: string | number | '',
  count: string | number | 0
}

export interface IDashboardOrderStats {
  total_orders?: number | string | 0,
  ongoing_orders?: number | string | 0,
  total_return?: number | string | 0,
  unfilled_return?: number | string | 0
}

export interface IDashboardSalesStats {
  shipment_cost?: number | string | 0,
  total_shipments?: number | string | 0,
  sales_invoice?: number | string | 0,
  avg_sale_price?: number | string | 0,
  shipping_products?: ShippingProducts[] | [],
  top_selling_products?: TopSellingProducts[] | [],
  weight_classes?: WeightClasses[] | []
}

export interface IDashboardTasks {
  type: string,
  description: string,
  label: string
}export interface IDashboardCarriers {
  name: string,
  url: string,
}