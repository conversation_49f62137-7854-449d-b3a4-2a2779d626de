export interface IDashboardOverview {
  shipments: {
    theDate: string;
    shipmentCount: number;
  }[];
  picked_orders: [];
  imported_orders: {
    theDate: string;
    orderCount: number;
  }[];
  dispatched_orders: [];
}

export interface IIntegrationIssues {
  inactive_integrations: number;
  delivery_methods_need_setup: number;
}

export interface IDashboardStoreState {
  thisWeekInfo: IDashboardOverview | null;
  overviewInfo: IDashboardOverview;
  thisWeekTotalShipmentCount: number;
}