export interface IAddressComponent {
  long_name: string;
  short_name: string;
  types: string[];
}

// export interface IMapInfo {
//   address_components: IAddressComponent[];
//   formatted_address: string;
//   geometry: {
//     bounds: {
//       northeast: {
//         lat: number;
//         lng: number;
//       };
//       southwest: {
//         lat: number;
//         lng: number;
//       };
//     };
//     location: {
//       lat: number;
//       lng: number;
//     };
//     location_type: 'APPROXIMATE';
//     viewport: {
//       northeast: {
//         lat: number;
//         lng: number;
//       };
//       southwest: {
//         lat: number;
//         lng: number;
//       };
//     };
//   };
//   place_id: string;
//   postcode_localities: string[];
//   types: string[];
// }

// export interface IMapInfoResponse {
//   results: IMapInfo[];
//   status: string;
// }

export interface IZipCodeBaseResult {
  query: {
    codes: string[];
    country: string | null;
  };
  results: {
    [key: string]: [
      {
        postal_code: string;
        country_code: string;
        latitude: string;
        longitude: string;
        city: string;
        state: string;
        city_en: string;
        state_en: string;
        state_code: string;
        province: string;
        province_code: string;
      },
    ];
  };
}
