import {QTableProps} from 'quasar';

export interface IOption<L = number | string, V = number | string> {
    label: L;
    value: V;
}

export type ICheckable<T> = T & { isChecked: boolean };

export interface IBase64File {
    file: string;
}

export type TAlphabet =
    | 'A'
    | 'B'
    | 'C'
    | 'D'
    | 'E'
    | 'F'
    | 'G'
    | 'H'
    | 'I'
    | 'J'
    | 'K'
    | 'L'
    | 'M'
    | 'N'
    | 'O'
    | 'P'
    | 'Q'
    | 'R'
    | 'S'
    | 'T'
    | 'U'
    | 'V'
    | 'W'
    | 'X'
    | 'Y'
    | 'Z';

export type Pagination = Exclude<QTableProps['pagination'], undefined>;

export interface ITabOption {
    label: string;
    value: string;
    onClick?: (value?: any) => void;
    subtitle?: string,
    icon?: string,
    activeIcon?: string,
    stylingClass?: string
}

export interface IServicePointSearch {
    address: string | null;
    zipCode: string | null;
    city: string | null;
}