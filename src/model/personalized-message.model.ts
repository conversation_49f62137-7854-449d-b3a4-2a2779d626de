import { ICountry } from "./country.model";
import { IOrderIntegration } from "./order-integration.model";

export interface IMessageHook {
  message_hook_code: number;
  message_id: string;
  content: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export type TMessagingType = "SMS" | "EMAIL";

export interface IPersonalizedMessage {
  message_code: number;
  type: TMessagingType;
  message_hook_code: number;
  is_receiver_country_specific: 0 | 1;
  receiver_country_code: null;
  is_integration_specific: 0 | 1;
  integration_code: number | null;
  send_option: "IMMEDIATELY" | "SPECIFIC_TIME";
  send_time: string | null;
  bcc: string | null;
  subject: string | null;
  message_content: string;
  integration: IOrderIntegration | null;
  receiver_country: ICountry | null;
  message_hook: IMessageHook | null;
  created_at: string;
  updated_at: string;
}

export interface IPersonalizedMessageTemplate extends Omit<IPersonalizedMessage, "message_code"> {
  message_template_code: number;
}

export interface IPersonalizedMessageRequest {
  message_hook_code: number;
  type: TMessagingType;
  is_receiver_country_specific: boolean;
  receiver_country_code?: number;
  is_integration_specific: boolean;
  integration_code?: number;
  send_option: "IMMEDIATELY" | "SPECIFIC_TIME";
  send_time: string | null;
  bcc: string | null;
  subject: string | null;
  message_content: string;
}


export interface ITrackingMessageForm {
  one: {
    trigger: IMessageHook | null;
    isReceiverCountrySpecific: boolean;
    receiverCountry: ICountry | null;
    isIntegrationSpecific: boolean;
    integration: IOrderIntegration | null;
    when: "IMMEDIATELY" | "SPECIFIC_TIME";
  };
  two: {
    subject: string | null;
    messageContent: string;
  };
}
