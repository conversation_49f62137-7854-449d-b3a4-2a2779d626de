export interface IPickSettings {
  pick_setting_code: number;
  company_code: number;
  language_code: number | null;
  primary_action: 'CreatePickPathList' | 'StartPickPath';
  pick_sorting: 'Bin' | null;
  start_at_first_item: boolean;
  warn_partial_pick: boolean;
  confirm_pick_amount_obtained: boolean;
  go_to_next_item: boolean;
  finish_when_items_picked: boolean;
  show_item_image: boolean;
  play_sound: boolean;
  use_camera_on_mobile_devices: boolean;
  vibrate_when_scanning: boolean;
  skip_summary: boolean;
  choose_packaging: boolean;
  say_item_name: boolean;
  create_fulfillment: boolean;
  create_shipment: boolean;
  print_shipping_label: boolean;
  print_proforma_invoice: boolean;
  print_packing_slip: boolean;
  capture_payment: boolean;
  user_camera_on_mobile_devices: boolean;
}

export type IPickSettingsSaveRequest = Omit<IPickSettings, 'pick_setting_code' | 'company_code'>;

export type IPickSettingsState = IPickSettings;
