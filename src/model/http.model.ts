export interface IStdResponse<T> {
  status: boolean;
  code: number;
  cache: boolean;
  isAvailableData: boolean;
  client: string;
  env: string;
  responseCode: number;
  errorMessage?: string;
  resource: T;
}

export interface IDbInfo {
  created_by: number;
  created_at: string;
  updated_at: string;
}

export interface IPagination<T> {
  total: number;
  current_page: number;
  last_page: number;
  per_page: number;
  to: number;
  entities: T[];
  data: T[];
}

export interface IUbsendResponse<T> {
  hits: T;
  total: number;
  nextPageToken: string;
}

export interface IRequestParam {
  key: string;
  value: string | number;
}
