import { ICustomer } from './customer.model';
import { IFulfillment } from './fulfillment.model';
import { IProduct, IProductVariant } from './product.model';
import { ISalesInvoice } from './sales-invoice.model';
import { ITemplate } from './template.model';
import { IReturnOrder } from './return-portal.model';

export interface IPriceSet {
  shop_money: {
    amount: string;
    currency_code: string;
  };
  presentment_money: {
    amount: string;
    currency_code: string;
  };
}

export interface IFulfillmentHolds {
  id: number;
  fulfillment_order_id: string;
  line_item_id: number;
  fulfillment_order_line_item: string;
  quantity: number;
  order_code: number;
  created_at: string; // ISO 8601 datetime string
  updated_at: string; // ISO 8601 datetime string
  deleted_at: string | null; // nullable datetime
}
export interface IOrderProduct {
  order_code: number;
  line_item_code: number;
  quantity: number;
  total_quantity?: number;
  product: IProduct | null;
  lineItem: IProduct | null;
  returned_quantity: number;
  calculated_returned_quantity?: number | null;
  grams: string | null;
  created_at: string;
  updated_at: string;
  price_set?: IPriceSet | null;
  total_discount_set?: IPriceSet[] | null;
  name?: string;
  title?: string;
  variant_title?: string;
  sku?: string;
  variant?: IProductVariant | null;
  fulfillable_quantity: number;
  discount_allocations: IDiscountAllocation[];
  tax_lines: ITaxLine[];
  taxable: number;
  fulfillment_holds: IFulfillmentHolds[];
}

export interface IOrderEvent {
  event_code: number;
  id: number;
  body: null;
  path: string;
  verb: string;
  author: string;
  message: string;
  arguments: [];
  created_at: string;
  subject_id: number;
  description: string;
  subject_type: string;
}

export interface IOrderAddress {
  id: number;
  zip: string;
  city: string;
  name: string;
  mobile: string;
  phone: string;
  email: string;
  company: string;
  country: string;
  default: true;
  address1: string;
  address2: string | null;
  province: string;
  last_name: string;
  first_name: string;
  customer_id: number;
  country_code: string;
  country_name: string;
  province_code: string;
  dialCode?: string;

  latitude?: string | null;
  longitude?: string | null;
}

export interface IShippingLine {
  id: number;
  code: string;
  phone: null;
  price: string;
  title: string;
  source: string;
  price_set: IPriceSet;
  tax_lines: ITaxLine[];
  discounted_price: string;
  delivery_category: null;
  carrier_identifier: string;
  discount_allocations: IDiscountAllocation[];
  discounted_price_set: {
    shop_money: {
      amount: string;
      currency_code: string;
    };
    presentment_money: {
      amount: string;
      currency_code: string;
    };
  };
  requested_fulfillment_service_id: null;
  delivery_method_title?: null | string;
}
/**
 * Unfinished interface
 * Feel free to add/change
 */

export interface IOrderTransaction {
  order_transaction_code: number;
  order_code: number;
  order_transaction_id: number;
  amount: string;
  authorization: string | null;
  created_at: string; // ISO 8601 date string
  currency: string;
  gateway: string;
  kind: string;
  message: string | null;
  payment_details: any[]; // Can be refined if you know the shape
  receipt: string | null;
  source_name: string | null;
  status: string;
  test: number; // 1 for true, 0 for false
  updated_at: string; // ISO 8601 date string
}
export interface IOrder {
  order_code: number;
  name?: string;
  order_number: number;
  fulfillment_status: string;
  order_status:
    | 'OPEN'
    | 'CANCELLED'
    | 'PROCESSING'
    | 'CAPTURED_PAYMENT'
    | 'CREATED_FULFILLMENT'
    | 'PARTIAL_FULFILLMENT'
    | 'CREATED_SHIPMENT_LABEL'
    | 'READY_FOR_PICKUP'
    | 'PICKED'
    | 'PACKED'
    | 'BEING_DELIVERED'
    | 'SENT'
    | 'CLOSED'
    | 'PARTIALLY_REFUNDED'
    | 'REFUNDED';
  financial_status: string;
  template_status: string;
  notes: string | null;
  note_attributes: string[] | null;
  events: IOrderEvent[];
  line_items: IOrderProduct[];
  subtotal_price: number;
  total_discounts: null;
  total_price: number;
  refund_amount: number;
  current_total_tax: number;
  total_price_set: {
    shop_money: {
      amount: string;
      currency_code: string;
    };
    presentment_money: {
      amount: string;
      currency_code: string;
    };
  };
  current_total_tax_set: {
    shop_money: {
      amount: string;
      currency_code: string;
    };
    presentment_money: {
      amount: string;
      currency_code: string;
    };
  };
  current_total_price_set: {
    shop_money: {
      amount: string;
      currency_code: string;
    };
    presentment_money: {
      amount: string;
      currency_code: string;
    };
  };
  customer: ICustomer | null;
  billing_address: IOrderAddress;
  cancelled_fulfillments: IFulfillment[] | null;
  fulfillments: IFulfillment[] | null;
  fulfillment_returns: IFulfillment[] | null;
  shipping_address: IOrderAddress;
  shipping_lines: IShippingLine[];
  subtotal_price_set: {
    shop_money: {
      amount: string;
      currency_code: string;
    };
    presentment_money: {
      amount: string;
      currency_code: string;
    };
  };
  price_set: {
    shop_money: {
      amount: string;
      currency_code: string;
    };
    presentment_money: {
      amount: string;
      currency_code: string;
    };
  };
  taxes_included: boolean;
  tax_lines: ITaxLine[];
  ordered_at: string;
  order_id: number;
  status: boolean;
  is_deleted: boolean;
  created_by: number;
  updated_by: number;
  deleted_by: number;
  deleted_at: string | null;
  created_at: string;
  updated_at: string | null;
  integration_code: number;
  company_code: number;
  delivery_template_code: number | null;
  return_template_code: number | null;
  delivery_template: ITemplate | null;
  return_template: ITemplate | null;
  sales_invoices: ISalesInvoice[] | null;
  payment_gateway_names: string[];
  pick_list_base64: string | null;
  packing_slip_base64: string | null;
  return_orders: IReturnOrder[] | null;
  refunds?: any[];
  total_weight?: number;
  platform?: any;
  returned_quantity?: number | undefined;
  fulfilled_quantity_sum?: number | undefined;
  total_discounts_set: {
    shop_money: {
      amount: string;
      currency_code: string;
    };
    presentment_money: {
      amount: string;
      currency_code: string;
    };
  } | null;
  discount_applications: IDiscountApplication[];
  paid_balance?: {
    amount: number;
    currency: string;
  } | null;
  payment_terms?: IPaymentTerms | null;
  transactions: IOrderTransaction[] | null;
  total_outstanding: number | null;
  authorization_status: 'expiring' | 'expired';
}

export interface IDiscountApplication {
  code?: string; //in case if discount_code
  title?: string; //incase of automatic
  type: 'discount_code' | 'automatic';
  value: string;
  value_type: 'fixed_amount' | 'percentage';
  target_type: 'line_item' | 'shipping_line';
  target_selection: 'entitled' | 'all';
  allocation_method: 'across' | 'each';
  single_item_discount?: number;
}
export interface IUpdateNoteRequest {
  note: string;
}

export interface IRefundPaymentRequest {
  refundPaymentAmount: string;
}
export interface ICapturePaymentRequest {
  amount: string | number;
  currency_code: string;
}

export interface IUpdateReceiverRequest {
  shipping_address: {
    name: string;
    email: string | null;
    phone: string | null;
    mobile: string | null;
    address1: string;
    address2: string | null;
    zip: string;
    city: string;
    country: string;
    country_code: string;
    delivery_introduction: string | null;
    dialCode: string | null;

    first_name: string | null;
    last_name: string | null;
    latitude: string | null;
    longitude: string | null;
    province: string | null;
    province_code: string | null;
  };
}

export interface IUpdateCustomerRequest {
  customer: {
    name: string;
    email: string | null;
    phone: string | null;
    mobile: string | null;
    address1: string;
    address2: string;
    zip: string;
    city: string;
    country: string;
    country_code: string;
  };
}
export interface IUpdateBillingAddress {
  billing_address: {
    name: string;
    first_name: string | null;
    last_name: string | null;
    email: string | null;
    phone: string | null;
    mobile: string | null;
    address1: string;
    address2: string | null;
    zip: string;
    city: string;
    country: string;
    dialCode: string;
    country_code: string;
    latitude: string | null;
    longitude: string | null;
    province: string | null;
    province_code: string | null;
  };
}

export interface IShippingMethodPayload {
  delivery_template_code: number | null;
  return_template_code: number | null;
  parcel_shop?: {
    code: string;
    title: string;
  } | null;
}

export interface IDeliveryTemplatePayload {
  type: string;
}

export interface IOrderStoreState {
  count: number;
}

export interface IOrderMultiActionResult {
  success: boolean;
  description: string;
  order_number?: number | string;
  already?: boolean;
}

export interface IIntCurrency {
  currency: string;
  amount: number;
}

export interface IDiscountAllocation {
  amount: string;
  amount_set: {
    shop_money: {
      amount: string;
      currency_code: string;
    };
    presentment_money: {
      amount: string;
      currency_code: string;
    };
  };
  discount_application?: IDiscountApplication;
  discount_application_index: number;
}

export interface ITaxLine {
  price: number;
  price_set: {
    shop_money: {
      amount: string;
      currency_code: string;
    };
    presentment_money: {
      amount: string;
      currency_code: string;
    };
  };
  rate: number;
  title: string;
  channel_liable: boolean;
}

export interface PaymentSchedule {
  id: number;
  amount: string;
  due_at: string;
  currency: string;
  issued_at: string | null;
  created_at: string;
  updated_at: string;
  completed_at: string | null;
}

export interface IPaymentTerms {
  id: number;
  created_at: string;
  updated_at: string;
  due_in_days: number | null;
  payment_schedules: PaymentSchedule[];
  payment_terms_name: string;
  payment_terms_type: string;
}

/**
 * Enum representing the shipment status of an order
 */
export enum OrderShipmentStatus {
  NOT_CREATED = 'NOT_CREATED', // No shipments created
  PARTIAL = 'PARTIAL', // Some but not all shipments created
  COMPLETE = 'COMPLETE', // All shipments created
}
