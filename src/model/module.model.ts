export interface IModule {
  module_code: number;
  module_key: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;
  name: string;
}

export enum EModukeKey {
  DASHBOARD = 'Dashboard',
  ORDERS = 'Orders',
  CUSTOMERS = 'Customers',
  STATISTICS = 'Statistics',
  PERSONALIZED_MESSAGE = 'PersonalizedMessage',
  INTEGRATION = 'Integration',
  SHIPMENTS = 'Shipments',
  PRODUCTS = 'Products',
  RETURN_PORTAL = 'ReturnPortal',
  TEMPLATES = 'Templates',
  SALES_INVOICES = 'SalesInvoices',
  BILLING = 'Billing',
  TEAM = 'Team',
}
