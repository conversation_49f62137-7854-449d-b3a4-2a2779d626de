import { IDbInfo } from './http.model';
import { IPackaging } from './packaging.model';
import { ITemplate } from './template.model';
import { ISOCountries } from '../model/country.model';
import { ICurrency } from '../model/shipping-method.model';

export interface IOrderIntegration extends IDbInfo {
  integration_code: number;
  country_code: number;
  currency_code: number;
  currency?: string;
  platform_code: number;
  company_code: number;
  name: string;
  email: string;
  address: string;
  city: string;
  phone_no: string;
  logo_image: string | null;
  api_url: string;
  api_username: string;
  api_secret: string;
  accesstoken: string;
  delivery_methods: IDeliveryMethod[] | null;
  last_import: string;
  status?: string | number | null;
}

export interface IOrderIntegrationPlatform {
  platform_code: number;
  name: string;
}

export interface IOrderIntegrationCountry {
  country_code: number;
  default_name: string;
  is_deleted: boolean;
  iso: ISOCountries;
  iso3: string;
  name: string;
  num_code: number;
  phone_code: number;
  status: boolean;
}

export interface IOrderIntegrationCurrency {
  currency_code: number;
  currency: string;
}

export type IOrderIntegrationCreateRequest = Omit<
  IOrderIntegration,
  | 'integration_code'
  | 'logo_image'
  | 'delivery_methods'
  | 'last_import'
  | keyof IDbInfo
  | 'country_code'
  | 'currency_code'
  | 'email'
  | 'address'
  | 'city'
  | 'phone_no'
>;

export type IOrderIntegrationUpdateRequest = Omit<
  IOrderIntegration,
  | 'integration_code'
  | 'logo_image'
  | 'delivery_methods'
  | 'last_import'
  | keyof IDbInfo
  | 'api_url'
  | 'api_username'
  | 'api_secret'
  | 'accesstoken'
  | 'platform_code'
>;

export interface IOrderIntegrationStatus {
  integration_code: number;
  company_code: number;
}

export interface IDeliveryMethod {
  delivery_method_code: number;
  transfer_orders: 0 | 1;
  integration_code: number;
  delivery_template_code: number | null;
  return_template_code: number | null;
  name: string;
  packaging_code: number | null;
  third_party_delivered: 0 | 1;
  skip_shipment: null;
  integration: IOrderIntegration | null;
  delivery_template: ITemplate | null;
  return_template: ITemplate | null;
  packaging: IPackaging | null;
  created_at: string;
  updated_at: string;
}

export interface IUpdateDeliveryMethodRequest {
  transfer_orders: boolean;
  delivery_template_code: number | null;
  return_template_code: number | null;
  packaging_code: number | null;
  third_party_delivered: boolean;
  skip_shipment: boolean;
}

export interface IOrderEcommerceIntegration {
  name: string;
  icon: string;
  url: string;
  is_available: boolean;
  size: string;
}

export interface IStore {
  name: string;
  email: string;
  address: string;
  phoneNo: string;
  city: string;
  country: IOrderIntegrationCountry | null;
  currency: ICurrency | null;
}

export interface IOrderIntegrationState {
  count: number;
  integrations: IOrderIntegration[];
}
