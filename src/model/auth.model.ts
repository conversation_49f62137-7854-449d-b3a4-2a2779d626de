import { ICompany } from './company.model';
import { IUser } from './user.model';

export interface ILoginRequest {
  email: string;
  password: string;
}

export interface ILoginResponse extends IUser {
  token: string;
}

export interface ISignUpRequest {
  company_name: string;
  contact_person: string;
  address: string;
  country_code: number;
  city: string;
  language_code: number;
  zipcode: string;
  vat_no: string;
  phone_no: string;
  email: string;
  password: string;
  password_confirmation: string;
}

export interface ISignUpResponse extends ICompany {
  user: ILoginResponse;
}

export interface ILogoutRequest {
  token: string;
}

export interface IForgotPasswordRequest {
  email: string;
}

export type IForgotPasswordResponse = [false];

export interface IResetPasswordRequest {
  password: string;
  password_confirmation: string;
  token: string;
}

