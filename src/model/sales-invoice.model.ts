import {ICompany} from './company.model';
// eslint-disable-next-line import/no-cycle
import {IOrder} from './order.model';

export interface ISalesInvoiceItem {
  count: number;
  amount: string;
  description: string;
  vat_rate: number;
  tax_price: number;
  taxes_included: boolean;
  price_set: any;
}

export interface ISalesInvoiceShippingLines {
  title: string;
  price: string;
  vat_rate: number;
  taxes_included: boolean;
}

export interface ISalesInvoice {
  id: number;
  sales_invoice_code: number;
  order_code: number;
  company_code: number;
  invoice_items: ISalesInvoiceItem[];
  shipping_lines: ISalesInvoiceShippingLines;
  invoice_type: 'INVOICE' | 'CREDIT_NOTE';
  discount: [];
  tax: number;
  company: ICompany | null;
  order: IOrder;
  created_at: string;
  updated_at: string;
  total_amount?: number
}
