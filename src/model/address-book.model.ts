import {ICountry} from './country.model';

export interface IAddressBookRecord {
  address_book_code: number;
  company_code: number;
  name: string;
  address: string;
  address2: string | null;
  country_code: number | null;
  city: string;
  email: string;
  attn: string | null;
  zipcode: string;
  mobile: string | null;
  is_default: boolean;
  country: ICountry;
}

export type IAddressBookCreate = Omit<IAddressBookRecord,
  'address_book_code' | 'country' | 'company_code'>;

export type IAddressBookUpdate = Omit<IAddressBookRecord, 'address_book_code' | 'country'>;
