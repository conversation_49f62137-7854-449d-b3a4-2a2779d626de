import { ICarrier, IShippingProduct, IWeightClass } from './carrier.model';
import { ICountry } from './country.model';
import { IFulfillment } from './fulfillment.model';
import { IReturnOrder } from '~/model/return-portal.model';

export interface IAdditionalService {
  id: number;
  additional_service_code: number;
  name: string;
  deleted_at: string | null;
  created_at: string;
  updated_at: string | null;
}

export interface IAdditionalServiceCharge {
  as_charge_code: number;
  additional_service_code: number;
  carrier_product_code: number;
  price: string;
  additional_service: IAdditionalService;
  created_at: string;
  updated_at: string;
}

export interface IParcel {
  count: number;
  width: number | null;
  height: number | null;
  length: number | null;
  weight: number;
  min_weight?: number | string | null;
  unit?: string | null;
  max_weight?: number | string | null;
  description: string;
  orderNumber?: null;
  carrierReference?: string;
  cashOnDeliveryAmount?: null;
  carrierIntegrationReference?: string;
}

export interface IShipmentContactInfo {
  dialCode: string;
  telephone: string;
  email: string;
}

export enum EShipmentStatus {
  UNKNOWN = 'UNKNOWN',
  BLOCKED = 'BLOCKED',
  DRAFT = 'DRAFT',
  FAILED_TO_REGISTER = 'FAILED_TO_REGISTER',
  REGISTERED = 'REGISTERED',
  IN_TRANSIT = 'IN_TRANSIT',
  RECEIVED_AT_WAREHOUSE = 'RECEIVED_AT_WAREHOUSE',
  READY_FOR_DISPATCH_FROM_WAREHOUSE = 'READY_FOR_DISPATCH_FROM_WAREHOUSE',
  DELIVERY_ATTEMPTED = 'DELIVERY_ATTEMPTED',
  DELIVERED = 'DELIVERED',
  ERROR = 'ERROR',
  RELABELLED = 'RELABELLED',
  CMR_CLOSED = 'CMR_CLOSED',
  RETURNED_TO_WAREHOUSE = 'RETURNED_TO_WAREHOUSE',
}

export interface IShipmentEvent {
  shipment_event_code: number;
  state: EShipmentStatus;
  parcel_reference: string;
  carrier_reference: string;
  carrier: string;
  event_created_at: string;
  received_at: string;
  created_at: string;
  updated_at: string;
}

export interface IReturningItem {
  name: string;
  quantity: number;
  image: string | null;
  description?: string | null;
}

export interface IShipment {
  id: number;
  shipment_code: number;
  refunded: number;
  carrier_product_code: number;
  order_code: number | null;
  fulfillment_code: number | null;
  company_code: number;
  sender_country_code: number;
  receiver_country_code: number;
  return_order_code: number;
  sender: {
    city: string;
    country: string;
    lastName: string;
    postCode: string;
    firstName: string;
    contactInfo: IShipmentContactInfo;
    addressLine1: string;
    addressLine2?: string | null;
  };
  recipient: {
    city: string;
    country: string;
    lastName: string;
    postCode: string;
    firstName: string;
    contactInfo: IShipmentContactInfo;
    addressLine1: string;
    addressLine2?: string | null;
    parcelShopId?: string;
    company?: string;
    parcelShopTitle?: string | null;
  };
  description: string | null;
  collection_info: {
    type: 'DROP_OFF' | 'PICK_UP';
  };
  shipment_type: 'REGULAR' | 'LAST_INCH';
  parcelshop_id: string | null;
  shipment_id: string | null;
  carrier_reference: string | null;
  parcels: IParcel[];
  returning_items: IReturningItem[] | null;
  return_order: IReturnOrder | null;
  status:
    | 'BOOKED'
    | 'PAID'
    | 'UNKNOWN'
    | 'BLOCKED'
    | 'DRAFT'
    | 'FAILED_TO_REGISTER'
    | 'REGISTERED'
    | 'IN_TRANSIT'
    | 'RECEIVED_AT_WAREHOUSE'
    | 'READY_FOR_DISPATCH_FROM_WAREHOUSE'
    | 'DELIVERY_ATTEMPTED'
    | 'DELIVERED'
    | 'ERROR';
  source_type: 'MANUAL' | 'FULFILLMENT' | 'REPURCHASE' | 'RETURN';
  source_id: string | null;
  label: string | null;
  label_a6: string | null;
  label_zpl: string | null;
  additional_services: number[] | null;
  shipment_events: IShipmentEvent[] | null;
  fulfillments: IFulfillment[] | null;
  stop_monitoring: 0 | 1;
  marked_as_delivered: 0 | 1;
  is_cancelled: 0 | 1;
  is_already_paid?: 1;
  created_at: string;
  updated_at: string;
  type?: string;
  delivery_template_code?: number | null;
}

export interface IPaymentShipment {
  p_status: string;
  message: string;
  data: IShipment;
}

export type IShipmentUpdateRequest = Omit<IShipment, 'shipment_code' | 'created_at' | 'updated_at'> & {
  ubsend_client_id?: number; 
};

export interface IShipmentLabel {
  shipment_label_code: number;
  order_number: string;
  shipment_code: number;
  label: string;
}

export interface ISendShipmentLabelEmailRequest {
  name: string;
  email: string;
  pdf_type: 'A4';
  shipment_id: string;
}

export interface IShipmentLabelEmail {
  name: string;
  email: string;
  pdf_type: 'A4';
  shipment_id: string;
  email_label_code: number;
  company_code: number;
}

export interface ICreateShipmentRequest {
  ubsend_client_id: number;
  carrier_product_code: number;
  sender_country_code: number;
  receiver_country_code: number;
  sender: {
    firstName: string;
    lastName: string;
    addressLine1: string;
    addressLine2?: string | null;
    postCode: string;
    city: string;
    country: string;
    contactInfo: IShipmentContactInfo;
  };
  recipient: {
    firstName: string;
    lastName: string;
    addressLine1: string;
    addressLine2?: string | null;
    postCode: string;
    city: string;
    country: string;
    parcelShopId: string | undefined;
    contactInfo: IShipmentContactInfo;
    parcelShopTitle?: string | null;
  };
  parcels: IParcel[];
  additional_services: number[];
  fulfillment_code?: string | number | null;
}

export interface IParcelShopOpeningTime {
  day: number;
  from: string;
  to: string;
  closedDuring: string | null;
}

export interface IParcelShop {
  id: string;
  name: string;
  type: null;
  latitude: number;
  longitude: number;
  carrier: string;
  addressLine1: string;
  addressLine2?: string | null;
  postCode: string;
  city: string;
  country: string;
  openingTimes: IParcelShopOpeningTime[];
  carrierData: string;
  distance: string | null;
}

export interface IParcelShopSingleResponse {
  parcelShop: IParcelShop;
  distance: number;
}

export interface ICalculateChargeParcel {
  description: '0-1 kg';
  count: 1;
  unit_price: '35.40';
  item_total: 35.4;
  currency: 'DKK';
  weight: '0-1 kg';
}

export interface ICalculateChargeResponse {
  'display-name': 'Shop Delivery';
  parcels: ICalculateChargeParcel[];
  additional_services: { name: string; price: string }[];
  totals: {
    before_vat: {
      amount: number;
      currency: 'DKK';
    };
    vat: {
      amount: number;
      currency: 'DKK';
    };
    after_vat: {
      amount: number;
      currency: 'DKK';
    };
  };
}

export interface IShipmentStoreState {
  createShipmentPredefinedValues: ICreateNewShipmentForm | null;
  isCreateShipmentModalOpen: boolean;
  shipmentCode: number | string;
  fulfillmentCode?: number | string;
}

export interface ICreateShipmentForm {
  one: {
    senderCountry: ICountry | null;
    receiverCountry: ICountry | null;
    carrier: ICarrier | null;
    shippingProduct: IShippingProduct | null;
    shippingAgreement: string;
    parcels: IFormParcel[];
    additionalServices: {
      [key: number]: boolean;
    };
  };
  two: {
    name: string | null;
    email: string | null;
    phone: string | null;
    zipCode: string | null;
    address1: string | null;
    address2: string | null;
    city: string | null;
  };
  three: {
    name: string | null;
    email: string | null;
    phone: string | null;
    zipCode: string | null;
    address1: string | null;
    address2: string | null;
    city: string | null;
    isChooseClosestServicePointChecked: boolean;
    servicePoint: (IParcelShop & { label: string }) | null;
  };
  four: {
    payWith: 0 | 1;
    isTermsAndConditionsChecked: boolean;
  };
}

export interface IFormParcel {
  /**
   * UUID
   */
  id: string;
  quantity: number | null;
  weightClass: IWeightClass | null;
}

interface ISender {
  name: string | null;
  email: string | null;
  phone: string | null;
  zipCode: string | null;
  address1: string | null;
  address2: string | null;
  city: string | null;
  senderCountry: ICountry | null;
}

interface IRecipient {
  name: string | null;
  email: string | null;
  phone: string | null;
  zipCode: string | null;
  address1: string | null;
  address2: string | null;
  city: string | null;
  receiverCountry: ICountry | null;
}

export interface ICreateNewShipmentForm {
  one: {
    sender: ISender;
    recipient: IRecipient;
    //shippingAgreement: string;
  };
  two: {
    carrier: ICarrier | null;
    shippingProduct: IShippingProduct | null;
    weightClass: IWeightClass | null;
    isChooseClosestServicePointChecked: boolean;
    servicePoint: (IParcelShop & { label: string }) | null;
    productWeight?: number | null;
  };
  three: {
    additionalServices: {
      [key: number]: boolean;
    };
  };
  four: {
    payWith: 0 | 1;
    isTermsAndConditionsChecked: boolean;
  };
}

export interface ICheckTrackingEmail {
  additional_services: number[];
}
