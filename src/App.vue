<template>
  <router-view />
  <Loader :status="isLoading" :message="loadingMessage" style="z-index: 9999999999998" />
</template>

<script setup lang="ts">
  import { ref, watch, onMounted, onUnmounted, computed } from 'vue';
  import {
    loadingService,
    loggingService,
    notificationService,
    orderService,
    printJobService,
  } from './services/_singletons';
  import { IOrder } from './model/order.model';
  import OrderService from './services/order.service';
  import { useStore } from './store';

  const isOnline = ref<boolean>(navigator.onLine);
  const isLoading = ref<boolean>(false);
  const loadingMessage = ref<string | null>(null);
  const $store = useStore();

  computed(() => {
    console.log('Loadings ', loadingService.loadings);
    return loadingService.loadings;
  });
  const getToken = () => {
    if (localStorage.getItem('token')) {
      return localStorage.getItem('token') as string;
    }

    if (sessionStorage.getItem('token')) {
      return sessionStorage.getItem('token') as string;
    }

    return null;
  };

  if (getToken() != null) {
    let barcode = '';
    let lastKeystrokeTime = 0;
    const inputSpeedThreshold = 50; // threshold in milliseconds

    document.addEventListener('keydown', (event: KeyboardEvent) => {
      const currentTime = new Date().getTime();
      const timeSinceLastKeystroke = currentTime - lastKeystrokeTime;
      lastKeystrokeTime = currentTime;

      const keyCode = event.which || event.keyCode;
      if (keyCode === 13) {
        // check for newline character
        if (timeSinceLastKeystroke < inputSpeedThreshold) {
          let barCodeValue = barcode.match(/\d+/)?.join('');
          console.log(`Scanned barcode: ${barCodeValue}`);
          if (barCodeValue == undefined) notificationService.showError('Invalid Scanned Value');
          else processBarcode(parseInt(barCodeValue));
        }
        barcode = ''; // reset barcode value
      } else {
        if (timeSinceLastKeystroke > inputSpeedThreshold) {
          barcode = ''; // reset barcode value
        }
        barcode += event.key;
      }
    });

    const processBarcode = async (orderId: number) => {
      try {
        loadingService.startLoading('main-loader:get-order');
        orderService
          .getOrder(orderId)
          .then((order) => {
            createFulfillmentAndShipment(order)
              .then((res) => {
                if (res != null) {
                  loadingService.startLoading('main-loader:send-print-job');
                  printJobService
                    .queueLabelPrintJob(res)
                    .then((printJob) => {
                      notificationService.showSuccess('Label sent to printer');
                      loadingService.stopLoading('main-loader:send-print-job');
                    })
                    .catch((e) => {
                      loggingService.error('Error while creating print job', e);
                      notificationService.showError('Error while creating print job');
                      loadingService.stopLoading('main-loader:send-print-job');
                    });
                }
              })
              .catch((e) => {
                console.log('Error1 ', e.response.data);
                if (e.response.data?.error) {
                  notificationService.showError(e.response.data?.error);
                } else {
                  loggingService.error('Error while creating fulfillment and shipment', e);
                  notificationService.showError('Error while creating fulfillment and shipment');
                }
              });
            loadingService.stopLoading('main-loader:get-order');
          })
          .catch((e) => {
            loggingService.error('Error while getting order', e);
            notificationService.showError('Error while getting order');
            loadingService.stopLoading('main-loader:get-order');
          });
      } catch (e) {
        loggingService.error('Error while processing barcode', e);
        notificationService.showError('Error while processing barcode');
      }
    };

    const createFulfillmentAndShipment = async (order: IOrder) => {
      try {
        loadingService.startLoading('main-loader:create-fulfillment-and-shipment');

        const _unfulfilledItems = OrderService.getUnfulfilledItems(
          order.fulfillments,
          order.line_items,
        );

        const order_fulfilment = await orderService.createFulfillment(order.order_code, null, [
          ..._unfulfilledItems.map((item) => ({
            line_item_code: item.line_item_code,
            quantity: item.quantity,
          })),
        ]);

        // eslint-disable-next-line @typescript-eslint/naming-convention, no-underscore-dangle
        const _fulfillments = order_fulfilment.fulfillments?.filter((fulfillment) =>
          _unfulfilledItems.find(
            (item) =>
              fulfillment.line_item_code === item.line_item_code &&
              fulfillment.quantity === item.quantity,
          ),
        );

        if (!_fulfillments || _fulfillments.length === 0) {
          throw new Error('Could not create fulfillment');
        }
        notificationService.showSuccess('Fulfillment created successfully');

        return await onClickCreateShipment(
          order,
          order.order_code,
          _fulfillments[0].fulfillment_code,
        );
      } catch (e: any) {
        console.log('Error2 ', e.response.data);
        if (e.response.data?.error) {
          notificationService.showError(e.response.data?.error);
        } else {
          loggingService.error('Error while creating fulfillment and shipment', e);
          notificationService.showError('Error while creating fulfillment and shipment');
        }
      } finally {
        loadingService.stopLoading('main-loader:create-fulfillment-and-shipment');
      }
    };

    const onClickCreateShipment = async (
      order: IOrder,
      orderCode: number,
      fulfillmentCode: number,
    ) => {
      if (order.delivery_template_code === null) {
        notificationService.showError(
          `Shipping rule is not set for order ${
            order.name ?? order.order_number
          }. Please set a shipping rule before attempting to create a shipment..`,
        );
        return;
      }

      try {
        loadingService.startLoading('main-loader:create-shipment-from-fulfillment');
        await orderService.createShipmentFromFulfillment(orderCode, fulfillmentCode);
        const order = await orderService.generateLabelForFulfillmentsShipment(fulfillmentCode);
        $store.dispatch('companyStore/syncCompanyData');

        if (order.fulfillments) {
          const [fulfillment] = order.fulfillments.filter(
            (f) => f.fulfillment_code === fulfillmentCode,
          );

          if (fulfillment && fulfillment.shipment_code) {
            notificationService.showSuccess(
              `Shipment created successfully for fulfillment ${fulfillment.name}`,
            );
            return fulfillment.shipment_code;
          }
          return null;
        }
      } catch (e: any) {
        console.log('Error2 ', e.response.data);
        if (e.response.data?.error) {
          notificationService.showError(e.response.data?.error);
        } else {
          loggingService.error('Error while creating fulfillment and shipment', e);
          notificationService.showError('Error while creating fulfillment and shipment');
        }
      } finally {
        loadingService.stopLoading('main-loader:create-shipment-from-fulfillment');
      }
    };
  }

  watch(
    () => loadingService.loadings,
    () => {
      loadingMessage.value = loadingService.loadingMessage();
      console.log('Loadings ', loadingService.loadings);
      isLoading.value = loadingService.isAnyLoadingWithPrefix('main-loader');
    },
    { deep: true },
  );
  watch(
    () => isOnline.value,
    (currentVal) => {
      if (!currentVal) {
        notificationService.showError('Internet is not connected.');
      }
    },
  );
  const updateOnlineStatus = (e: any) => {
    console.log('Update Status ', e);
    const { type } = e;
    isOnline.value = type == 'online';
  };
  onMounted(() => {
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    /** temporarily unregistering **/
    /*navigator.serviceWorker.getRegistrations().then(registrations => {
    for (let registration of registrations) {
      console.log("Unregistering...");
      registration.unregister();
    }
  });*/
  });
  onUnmounted(() => {
    window.removeEventListener('online', updateOnlineStatus);
    window.removeEventListener('offline', updateOnlineStatus);
  });
</script>
<style lang="css">
  @import './assets/css/style.css';
</style>
<style lang="scss">
  @use 'node_modules/modern-normalize/modern-normalize.css';
  //  @use './assets/scss/_font';
  //  @import url('https://fonts.googleapis.com/css2?family=Inter&display=swap');

  *,
  *::before,
  *::after {
    box-sizing: border-box !important;
    text-rendering: optimizeLegibility;

    -webkit-text-size-adjust: 100%;
  }

  /*body {
  font-family: 'Inter', sans-serif;
  font-style: normal;
  font-weight: normal;
}*/

  a {
    text-decoration: none;
  }

  li {
    list-style: none;
  }

  .flex-1 {
    flex: 1;
  }

  .input-double {
    display: flex;
    flex-direction: row;
    gap: 30px;

    &-responsive {
      @media (max-width: 768px) {
        display: flex;
        flex-direction: column;
        gap: 17px;
      }
    }

    & > * {
      flex: 1;
    }
  }

  td {
    font-variant-numeric: tabular-nums !important;
  }

  .q-notifications__list {
    z-index: 9999999999999;
  }
</style>
