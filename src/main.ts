// FILE: main.ts

import { App, createApp } from 'vue';
import { Quasar, Notify, Dialog, Loading, QuasarPluginOptions } from 'quasar';
import quasarLang from 'quasar/lang/en-US';
import quasarIconSet from 'quasar/icon-set/svg-fontawesome-v5';
import VueSidebarMenu from 'vue-sidebar-menu';
import 'vue-sidebar-menu/dist/vue-sidebar-menu.css';
import VueGoogleMaps from '@fawmi/vue-google-maps';
import durationPlugin from 'dayjs/plugin/duration';
import relativeTime from 'dayjs/plugin/relativeTime';
import dayjs from 'dayjs';
import { createI18n } from 'vue-i18n';
import HelperService from './services/helper.service';
import router from './router';
import translations from './assets/json/translations.json';

// Import icon libraries
import '@quasar/extras/material-icons/material-icons.css';
import '@quasar/extras/ionicons-v4/ionicons-v4.css';

import '@fortawesome/fontawesome-pro/css/fontawesome.css';
import '@fortawesome/fontawesome-pro/css/light.css';
import '@quasar/extras/fontawesome-v5/fontawesome-v5.css';

// Import Quasar css
import 'quasar/src/css/index.sass';

import './assets/scss/quasar-palette.scss';

// Assumes your root component is App.vue
// and placed in same folder as main.js
import AppRoot from './App.vue';
import vClickOutside from './directives/v-click-outside.directive';
import store from './store';
import { updateService } from './services/_singletons';

const app: App<Element> = createApp(AppRoot);

Quasar.lang.set(quasarLang);
Notify.setDefaults({ position: 'top-right' });

const quasarConfig: QuasarPluginOptions = {
  plugins: { Notify, Dialog, Loading },
  iconSet: HelperService.overrideQuasarIcons(quasarIconSet),
} as QuasarPluginOptions;

const googleMapsConfig = {
  load: {
    key: import.meta.env.VITE_googleMapsAPIKey,
  },
};

[durationPlugin, relativeTime].forEach((plugin) => dayjs.extend(plugin));

const i18n = createI18n({
  legacy: false,
  locale: 'dk',
  fallbackLocale: 'en',
  messages: translations,
});

app.config.errorHandler = (err) => {
  console.error('App Level Error: ', err);
};

app.config.performance = true;
app
  .use(store)
  .use(Quasar, quasarConfig)
  .use(VueSidebarMenu)
  .use(VueGoogleMaps, googleMapsConfig)
  .use(i18n)
  .use(router)
  .directive('click-outside', vClickOutside)
  .mount('#app');

updateService.initializeAutoUpdater();


// comment
