import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { quasar, transformAssetUrls } from "@quasar/vite-plugin";
import Components from "unplugin-vue-components/vite";
import { QuasarResolver } from "unplugin-vue-components/resolvers";
import viteCompression from "vite-plugin-compression";
import StylelintPlugin from "vite-plugin-stylelint";
import { VitePWA } from "vite-plugin-pwa";

export default defineConfig({
  css: {
    preprocessorOptions: {
      scss: {
        quietDeps: true
      }
    }
  },
  plugins: [
    vue({
      template: { transformAssetUrls },
      reactivityTransform: true
    }),
    viteCompression({ algorithm: "brotliCompress" }),

    quasar({
      autoImportComponentCase: "kebab",
      sassVariables: "src/assets/scss/variables.scss"
    }),

    StylelintPlugin({ quiet: true, emitErrorAsWarning: true }),

    Components({
      extensions: ["vue"],
      include: [/\.vue$/, /\.vue\?vue/],
      resolvers: [QuasarResolver()],
      dts: "src/components.d.ts"
    }),

    VitePWA({
      registerType: "autoUpdate",
      injectRegister: "inline",
      workbox: {
        clientsClaim: true,
        skipWaiting: true,
        cleanupOutdatedCaches: true,
        navigateFallbackDenylist: [/index\.html$/], // Prevent caching index.html
        globPatterns: ['**/*.{js,css,html,ico}'],
        globIgnores: ['**/index.html'], //
        runtimeCaching: [
          {
            urlPattern: ({ url }) => url.pathname.endsWith('.js') || url.pathname.endsWith('.css')|| url.pathname.endsWith('.br'),
            handler: "NetworkFirst", 
            options: {
              cacheName: "static-resources",
              expiration: {
                maxEntries: 20,
                maxAgeSeconds: 20 // 1 day
              }
            }
          },
          {
            urlPattern: ({ url }) => url.pathname.startsWith('/api/'),
            handler: "NetworkFirst",
            options: {
              cacheName: "api-cache", 
              expiration: {
                maxEntries: 50,
                maxAgeSeconds: 20 // 5 minutes
              }
            }
          }
        ]
      },
      manifest: {
        name: "Shipvagoo",
        short_name: "Shipvagoo",
        start_url: "/",
        display: "standalone",
        background_color: "#ffffff",
        theme_color: "#42b883"
      },
      filename: `sw-${Date.now()}.js`
    })
  ],
  server: {
    proxy: {
      "/api": {
        target:
          process.env.ENV === "LOCAL"
            ? "http://localhost/api"
            : "https://development2.api.shipvagoo.com/api",
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api/, "")
      }
    }
  }
});