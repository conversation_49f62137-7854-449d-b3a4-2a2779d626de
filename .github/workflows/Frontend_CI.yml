name: Deploy_to_ECR

on: 
  push:
    branches: [main]
    
jobs:
  build:
    name: Build And Push Image
    runs-on: [self-hosted, stg]
    
    steps:
      - name: Check put Code using Github ACTIONS
        uses: actions/checkout@v3
        
      - name: Configuring AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-north-1

      - name: Login to ECR After Configuring the AWS Credentrials
        id : login-ecr
        uses: aws-actions/amazon-ecr-login@v1
        
      - name: Copying env web
        run: sudo cp /home/<USER>/envs/stg-web.env /home/<USER>/web/_work/shipvagoo-web/shipvagoo-web/.env 
          
      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ${{ secrets.REPO_NAME}}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          sudo docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          sudo docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "::set-output name=image::$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG"

      - name: Register new ECS task definition
        id: register-task
        run: |
          TASK_DEFINITION_JSON=$(aws ecs register-task-definition \
            --family shipvagoo-web \
            --container-definitions '[
              {
                "name": "shipvagoo-web",
                "image": "'${{ steps.build-image.outputs.image }}'",
                "cpu": 0,
                "portMappings": [
                  {
                    "name": "shipvagoo-web-80-tcp",
                    "containerPort": 80,
                    "hostPort": 80,
                    "protocol": "tcp",
                    "appProtocol": "http"
                  }
                ],
                "essential": true,
                "environment": [],
                "environmentFiles": [],
                "mountPoints": [],
                "volumesFrom": [],
                "logConfiguration": {
                  "logDriver": "awslogs",
                  "options": {
                    "awslogs-create-group": "true",
                    "awslogs-group": "/ecs/shipvagoo-web",
                    "awslogs-region": "eu-north-1",
                    "awslogs-stream-prefix": "ecs"
                  },
                  "secretOptions": []
                }
              }
            ]' \
            --task-role-arn arn:aws:iam::933438912987:role/ecsTaskExecutionRole \
            --execution-role-arn arn:aws:iam::933438912987:role/ecsTaskExecutionRole \
            --network-mode awsvpc \
            --requires-compatibilities FARGATE \
            --cpu 2048 \
            --memory 4096 \
            --ephemeral-storage 'sizeInGiB=25' \
            --runtime-platform 'cpuArchitecture=X86_64,operatingSystemFamily=LINUX' \
            --output json)

          # Extract and store "shipvagoo-api:245" from the task definition JSON
          TASK_DEFINITION_NAME_WITH_REVISION=$(echo "$TASK_DEFINITION_JSON" | jq -r '.taskDefinition.family + ":" + (.taskDefinition.revision | tostring)')
          #echo "Task Definition Family with Revision: $TASK_DEFINITION_NAME_WITH_REVISION"
          echo "::set-output name=TaskDefinition::$TASK_DEFINITION_NAME_WITH_REVISION"
           
      - name: Update AWS Service
        run: | 
          echo "Task Definition Family with Revision: $TASK_DEFINITION_NAME_WITH_REVISION"
          aws ecs update-service \
            --cluster Shipvagoo \
            --service shipvagooweb-stg \
            --task-definition ${{ steps.register-task.outputs.TaskDefinition }}
