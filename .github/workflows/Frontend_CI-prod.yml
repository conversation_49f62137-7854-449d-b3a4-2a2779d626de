name: Deploy_to_ECR

on: 
  push:
    branches: [production]
    
jobs:
  build:
    name: Build And Push Image
    runs-on: [self-hosted, prod]
    
    steps:
      - name: Check put Code using Github ACTIONS
        uses: actions/checkout@v3
        
      - name: Configuring AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-north-1

      - name: Login to ECR After Configuring the AWS Credentrials
        id : login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Removing older ENV
        run: rm -rf .env
        
      - name: Copying env web
        run: sudo cp /home/<USER>/envs/prod-web.env ./.env 

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ${{ secrets.REPO_NAME_PROD}}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          sudo docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          sudo docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "::set-output name=image::$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG"

          
