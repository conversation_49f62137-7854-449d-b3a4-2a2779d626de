<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with <PERSON><PERSON><PERSON><PERSON> ("http://www.sodipodi.com/") -->
<!-- /Creative Commons Public Domain -->
<!--
<rdf:RDF xmlns="http://web.resource.org/cc/"
     xmlns:dc="http://purl.org/dc/elements/1.1/"
     xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
<Work rdf:about="">
    <dc:title>New Zealand, Australia, United Kingdom, United States, 
Bosnia and Herzegovina, Azerbaijan, Armenia, Bahamas, Belgium, Benin, 
Bulgaria, Estonia, Finland, Gabon, Gambia, Germany, Greece, Greenland, 
Guinea, Honduras, Israel, Jamaica, Jordan, and Romania Flags</dc:title>
    <dc:rights><Agent>
       <dc:title><PERSON></dc:title>
    </Agent></dc:rights>
    <license rdf:resource="http://web.resource.org/cc/PublicDomain" />
</Work>

<License rdf:about="http://web.resource.org/cc/PublicDomain">
    <permits rdf:resource="http://web.resource.org/cc/Reproduction" />
    <permits rdf:resource="http://web.resource.org/cc/Distribution" />
    <permits rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
</License>
</rdf:RDF>
-->
<svg id="svg609" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="480" viewBox="0 0 512 512" width="640" version="1.0" y="0" x="0" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata3231">
  <rdf:RDF>
   <cc:Work rdf:about="">
    <dc:format>image/svg+xml</dc:format>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs id="defs611">
  <clipPath id="clipPath3133" clipPathUnits="userSpaceOnUse">
   <rect id="rect3135" fill-opacity="0.67" height="512" width="682.67" y="7.1054e-15" x="-85.333"/>
  </clipPath>
 </defs>
 <g id="flag" fill-rule="evenodd" clip-path="url(#clipPath3133)">
  <rect id="rect614" height="512" width="682.67" y="0" x="-85.333" fill="#009"/>
  <path id="path615" d="m-85.333 0 682.67 512v-512h-682.67z" fill="#fc0"/>
  <polygon id="polygon621" transform="matrix(.43691 0 0 .43691 -188.29 47.624)" points="381.84 62.857 326 22.287 270.16 62.857 291.49 -2.7868 235.65 -43.357 304.67 -43.357 326 -109 326 -109 347.33 -43.357 416.35 -43.357 360.51 -2.7868" fill="#fff"/>
  <polygon id="polygon622" transform="matrix(.43691 0 0 .43691 -112.34 102.24)" points="326 22.287 270.16 62.857 291.49 -2.7868 235.65 -43.357 304.67 -43.357 326 -109 326 -109 347.33 -43.357 416.35 -43.357 360.51 -2.7868 381.84 62.857" fill="#fff"/>
  <polygon id="polygon616" transform="matrix(.43691 0 0 .43691 -36.397 156.85)" points="270.16 62.857 291.49 -2.7868 235.65 -43.357 304.67 -43.357 326 -109 326 -109 347.33 -43.357 416.35 -43.357 360.51 -2.7868 381.84 62.857 326 22.287" fill="#fff"/>
  <polygon id="polygon623" transform="matrix(.43691 0 0 .43691 39.551 211.47)" points="291.49 -2.7868 235.65 -43.357 304.67 -43.357 326 -109 326 -109 347.33 -43.357 416.35 -43.357 360.51 -2.7868 381.84 62.857 326 22.287 270.16 62.857" fill="#fff"/>
  <polygon id="polygon624" transform="matrix(.43691 0 0 .43691 115.5 266.08)" points="235.65 -43.357 304.67 -43.357 326 -109 326 -109 347.33 -43.357 416.35 -43.357 360.51 -2.7868 381.84 62.857 326 22.287 270.16 62.857 291.49 -2.7868" fill="#fff"/>
  <polygon id="polygon625" transform="matrix(.43691 0 0 .43691 191.45 320.69)" points="304.67 -43.357 326 -109 326 -109 347.33 -43.357 416.35 -43.357 360.51 -2.7868 381.84 62.857 326 22.287 270.16 62.857 291.49 -2.7868 235.65 -43.357" fill="#fff"/>
  <polygon id="polygon626" transform="matrix(.43691 0 0 .43691 267.39 375.31)" points="326 -109 326 -109 347.33 -43.357 416.35 -43.357 360.51 -2.7868 381.84 62.857 326 22.287 270.16 62.857 291.49 -2.7868 235.65 -43.357 304.67 -43.357" fill="#fff"/>
  <polygon id="polygon627" transform="matrix(.43691 0 0 .43691 343.34 429.92)" points="326 -109 347.33 -43.357 416.35 -43.357 360.51 -2.7868 381.84 62.857 326 22.287 270.16 62.857 291.49 -2.7868 235.65 -43.357 304.67 -43.357" fill="#fff"/>
  <polygon id="polygon628" transform="matrix(.43691 0 0 .43691 419.29 484.54)" points="347.33 -43.357 416.35 -43.357 360.51 -2.7868 381.84 62.857 326 22.287 270.16 62.857 291.49 -2.7868 235.65 -43.357 304.67 -43.357 326 -109 326 -109" fill="#fff"/>
 </g>
</svg>
