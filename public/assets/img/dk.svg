<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with <PERSON><PERSON><PERSON><PERSON> ("http://www.sodipodi.com/") -->
<svg id="svg548" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="480" width="640" version="1.1" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata2993">
  <rdf:RDF>
   <cc:Work rdf:about="">
    <dc:format>image/svg+xml</dc:format>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs id="defs550">
  <clipPath id="clipPath4130" clipPathUnits="userSpaceOnUse">
   <rect id="rect4132" fill-opacity="0.67" height="512" width="682.67" y="-.0000039058" x="-64"/>
  </clipPath>
 </defs>
 <g id="flag" fill-rule="evenodd" clip-path="url(#clipPath4130)" transform="matrix(.9375 0 0 .9375 60 .0000036617)" stroke-width="1pt">
  <rect id="rect551" height="512" width="676.57" y="-.0000039058" x="-64" fill="#fb1b22"/>
  <rect id="rect552" height="73.143" width="676.57" y="219.43" x="-64" fill="#fff"/>
  <rect id="rect553" height="512" width="73.143" y="-.0000039058" x="155.43" fill="#fff"/>
 </g>
</svg>
