<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with <PERSON><PERSON><PERSON><PERSON> ("http://www.sodipodi.com/") -->

<!-- /Creative Commons Public Domain -->

<!--
<rdf:RDF xmlns="http://web.resource.org/cc/"
     xmlns:dc="http://purl.org/dc/elements/1.1/"
     xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
<Work rdf:about="">
    <dc:title>New Zealand, Australia, United Kingdom, United States, 
Bosnia and Herzegovina, Azerbaijan, Armenia, Bahamas, Belgium, Benin, 
Bulgaria, Estonia, Finland, Gabon, Gambia, Germany, Greece, Greenland, 
Guinea, Honduras, Israel, Jamaica, Jordan, and Romania Flags</dc:title>
    <dc:rights><Agent>
       <dc:title><PERSON></dc:title>
    </Agent></dc:rights>
    <license rdf:resource="http://web.resource.org/cc/PublicDomain" />
</Work>

<License rdf:about="http://web.resource.org/cc/PublicDomain">
    <permits rdf:resource="http://web.resource.org/cc/Reproduction" />
    <permits rdf:resource="http://web.resource.org/cc/Distribution" />
    <permits rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
</License>
</rdf:RDF>
-->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   id="svg153"
   sodipodi:version="0.29win"
   width="640"
   height="480"
   sodipodi:docname="_olympic.svg"
   version="1.1"
   inkscape:version="0.91 r13725">
  <sodipodi:namedview
     id="base"
     showgrid="false"
     inkscape:zoom="0.79569286"
     inkscape:cx="805.94408"
     inkscape:cy="406.22005"
     inkscape:window-width="2560"
     inkscape:window-height="1414"
     inkscape:window-x="2560"
     inkscape:window-y="0"
     inkscape:window-maximized="1"
     inkscape:current-layer="svg153" />
  <metadata
     id="metadata3151">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <desc
     id="desc3066">
    The United States of America flag, produced by Daniel McRae
  </desc>
  <defs
     id="defs155">
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath6304">
      <rect
         id="rect6306"
         height="130"
         x="0"
         style="fill:#000000;fill-opacity:0.67000002;stroke:none"
         width="130"
         y="-1.2695313e-06" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath4293">
      <rect
         id="rect4295"
         height="512"
         x="0"
         style="fill:#000000;fill-opacity:0.67000002;stroke:none"
         width="682.66669"
         y="5.2306668e-06" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath4164">
      <rect
         style="opacity:0.744;fill:#000000;fill-opacity:1;stroke:#000000;stroke-width:2.14086294;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
         id="rect4166"
         width="995.30212"
         height="557.94745"
         x="-160.83763"
         y="59.011711" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath4168">
      <rect
         style="opacity:0.744;fill:#000000;fill-opacity:1;stroke:#000000;stroke-width:2.14086294;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
         id="rect4170"
         width="995.30212"
         height="557.94745"
         x="-160.83763"
         y="59.011711" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath4172">
      <rect
         style="opacity:0.744;fill:#000000;fill-opacity:1;stroke:#000000;stroke-width:2.14086294;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
         id="rect4174"
         width="995.30212"
         height="557.94745"
         x="-160.83763"
         y="59.011711" />
    </clipPath>
    <clipPath
       id="clip_white">
      <path
         id="path4629"
         d="M 0,0 l -11,-5.5 v 11 L 0,0 l .2,-11 h 10 z" />
    </clipPath>
    <clipPath
       id="clip_color">
      <path
         id="path4632"
         d="M 0,0 l -11,-11 v 22 L 0,0 v -11 h 11 z" />
    </clipPath>
  </defs>
  <g
     id="g4678"
     transform="matrix(7.1111111,0,0,8,320,200)">
    <rect
       id="rect4634"
       height="60"
       width="90"
       y="-25"
       x="-45"
       style="fill:#ffffff" />
    <g
       id="g4636"
       style="fill:none;stroke:#ffffff;stroke-width:2">
      <circle
         id="circle4638"
         r="9"
         cy="9"
         cx="-11"
         style="stroke-width:3" />
      <circle
         id="circle4640"
         r="9"
         cy="9"
         cx="11"
         style="stroke-width:3" />
      <circle
         id="circle4642"
         r="9"
         cy="9"
         cx="-11"
         style="stroke:#f4c300" />
      <circle
         id="circle4644"
         r="9"
         cy="9"
         cx="11"
         style="stroke:#009f3d" />
      <circle
         id="circle4646"
         r="9"
         cy="0"
         cx="-22"
         style="stroke-width:3" />
      <circle
         id="circle4648"
         r="9"
         cy="0"
         cx="0"
         style="stroke-width:3" />
      <circle
         id="circle4650"
         r="9"
         cy="0"
         cx="22"
         style="stroke-width:3" />
      <circle
         id="circle4652"
         r="9"
         cy="0"
         cx="-22"
         style="stroke:#0085c7" />
      <circle
         id="circle4654"
         r="9"
         cy="0"
         cx="0"
         style="stroke:#000000" />
      <circle
         id="circle4656"
         r="9"
         cy="0"
         cx="22"
         style="stroke:#df0024" />
      <g
         id="g4658"
         transform="translate(-11,9)">
        <circle
           id="circle4660"
           cy="0"
           cx="0"
           clip-path="url(#clip_white)"
           r="9"
           style="stroke-width:3" />
        <circle
           id="circle4662"
           cy="0"
           cx="0"
           clip-path="url(#clip_color)"
           r="9"
           style="stroke:#f4c300" />
      </g>
      <g
         id="g4664"
         transform="translate(11,9)">
        <circle
           id="circle4666"
           cy="0"
           cx="0"
           clip-path="url(#clip_white)"
           r="9"
           style="stroke-width:3" />
        <circle
           id="circle4668"
           cy="0"
           cx="0"
           clip-path="url(#clip_color)"
           r="9"
           style="stroke:#009f3d" />
      </g>
    </g>
  </g>
</svg>
