<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with <PERSON><PERSON><PERSON><PERSON> ("http://www.sodipodi.com/") -->
<!-- /Creative Commons Public Domain -->
<!--
<rdf:RDF xmlns="http://web.resource.org/cc/"
     xmlns:dc="http://purl.org/dc/elements/1.1/"
     xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
<Work rdf:about="">
    <dc:title>New Zealand, Australia, United Kingdom, United States, 
Bosnia and Herzegovina, Azerbaijan, Armenia, Bahamas, Belgium, Benin, 
Bulgaria, Estonia, Finland, Gabon, Gambia, Germany, Greece, Greenland, 
Guinea, Honduras, Israel, Jamaica, Jordan, and Romania Flags</dc:title>
    <dc:rights><Agent>
       <dc:title><PERSON></dc:title>
    </Agent></dc:rights>
    <license rdf:resource="http://web.resource.org/cc/PublicDomain" />
</Work>

<License rdf:about="http://web.resource.org/cc/PublicDomain">
    <permits rdf:resource="http://web.resource.org/cc/Reproduction" />
    <permits rdf:resource="http://web.resource.org/cc/Distribution" />
    <permits rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
</License>
</rdf:RDF>
-->
<svg id="svg611" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="480" width="640" version="1.1" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata3406">
  <rdf:RDF>
   <cc:Work rdf:about="">
    <dc:format>image/svg+xml</dc:format>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs id="defs613">
  <clipPath id="clipPath4935" clipPathUnits="userSpaceOnUse">
   <rect id="rect4937" fill-opacity="0.67" height="90" width="120" y="0" x="0"/>
  </clipPath>
 </defs>
 <g id="flag" fill-rule="evenodd" clip-path="url(#clipPath4935)" transform="scale(5.3333)" stroke-width="1pt">
  <g id="g626">
   <rect id="rect614" height="10" width="135" y="0" x="0" fill="#0061f3"/>
   <rect id="rect615" height="10" width="135" y="10" x="0" fill="#fff"/>
   <rect id="rect616" height="10" width="135" y="20" x="0" fill="#0061f3"/>
   <rect id="rect617" height="10" width="135" y="30" x="0" fill="#fff"/>
   <rect id="rect618" height="10" width="135" y="40" x="0" fill="#0061f3"/>
   <rect id="rect619" height="10" width="135" y="50" x="0" fill="#fff"/>
   <rect id="rect620" height="10" width="135" y="60" x="0" fill="#0061f3"/>
   <rect id="rect621" height="10" width="135" y="70" x="0" fill="#fff"/>
   <rect id="rect622" height="10" width="135" y="80" x="0" fill="#0061f3"/>
  </g>
  <g id="g639">
   <rect id="rect623" height="50" width="50" y="0" x="0" fill="#0061f3"/>
   <g id="g636" fill="#fff">
    <rect id="rect624" height="50" width="10" y="0" x="20"/>
    <rect id="rect625" height="10" width="50" y="20" x="0"/>
   </g>
  </g>
 </g>
</svg>
