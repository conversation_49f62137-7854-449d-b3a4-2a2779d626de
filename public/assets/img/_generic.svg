<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with <PERSON><PERSON><PERSON><PERSON> ("http://www.sodipodi.com/") -->

<!-- /Creative Commons Public Domain -->

<!--
<rdf:RDF xmlns="http://web.resource.org/cc/"
     xmlns:dc="http://purl.org/dc/elements/1.1/"
     xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
<Work rdf:about="">
    <dc:title>New Zealand, Australia, United Kingdom, United States, 
Bosnia and Herzegovina, Azerbaijan, Armenia, Bahamas, Belgium, Benin, 
Bulgaria, Estonia, Finland, Gabon, Gambia, Germany, Greece, Greenland, 
Guinea, Honduras, Israel, Jamaica, Jordan, and Romania Flags</dc:title>
    <dc:rights><Agent>
       <dc:title><PERSON></dc:title>
    </Agent></dc:rights>
    <license rdf:resource="http://web.resource.org/cc/PublicDomain" />
</Work>

<License rdf:about="http://web.resource.org/cc/PublicDomain">
    <permits rdf:resource="http://web.resource.org/cc/Reproduction" />
    <permits rdf:resource="http://web.resource.org/cc/Distribution" />
    <permits rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
</License>
</rdf:RDF>
-->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   id="svg153"
   sodipodi:version="0.29win"
   width="640"
   height="480"
   sodipodi:docname="_generic.svg"
   version="1.1"
   inkscape:version="0.91 r13725">
  <sodipodi:namedview
     id="base"
     showgrid="false"
     inkscape:zoom="0.79569286"
     inkscape:cx="329.00125"
     inkscape:cy="403.70652"
     inkscape:window-width="2560"
     inkscape:window-height="1389"
     inkscape:window-x="0"
     inkscape:window-y="0"
     inkscape:window-maximized="1"
     inkscape:current-layer="g4971" />
  <metadata
     id="metadata3151">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title />
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <desc
     id="desc3066">
    The United States of America flag, produced by Daniel McRae
  </desc>
  <defs
     id="defs155">
    <linearGradient
       inkscape:collect="always"
       id="linearGradient5096">
      <stop
         style="stop-color:#999999;stop-opacity:1;"
         offset="0"
         id="stop5098" />
      <stop
         style="stop-color:#ececec;stop-opacity:1"
         offset="1"
         id="stop5100" />
    </linearGradient>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath6304">
      <rect
         id="rect6306"
         height="130"
         x="0"
         style="fill:#000000;fill-opacity:0.67000002;stroke:none"
         width="130"
         y="-1.2695313e-06" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath4293">
      <rect
         id="rect4295"
         height="512"
         x="0"
         style="fill:#000000;fill-opacity:0.67000002;stroke:none"
         width="682.66669"
         y="5.2306668e-06" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath4164">
      <rect
         style="opacity:0.744;fill:#000000;fill-opacity:1;stroke:#000000;stroke-width:2.14086294;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
         id="rect4166"
         width="995.30212"
         height="557.94745"
         x="-160.83763"
         y="59.011711" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath4168">
      <rect
         style="opacity:0.744;fill:#000000;fill-opacity:1;stroke:#000000;stroke-width:2.14086294;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
         id="rect4170"
         width="995.30212"
         height="557.94745"
         x="-160.83763"
         y="59.011711" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath4172">
      <rect
         style="opacity:0.744;fill:#000000;fill-opacity:1;stroke:#000000;stroke-width:2.14086294;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
         id="rect4174"
         width="995.30212"
         height="557.94745"
         x="-160.83763"
         y="59.011711" />
    </clipPath>
    <clipPath
       id="clip_white">
      <path
         id="path4629"
         d="M 0,0 l -11,-5.5 v 11 L 0,0 l .2,-11 h 10 z" />
    </clipPath>
    <clipPath
       id="clip_color">
      <path
         id="path4632"
         d="M 0,0 l -11,-11 v 22 L 0,0 v -11 h 11 z" />
    </clipPath>
    <circle
       stroke-width="39.030362"
       stroke="#013ba6"
       fill="none"
       r="145.663293"
       id="rb" />
    <circle
       stroke-width="17.346827"
       stroke="#fff"
       fill="none"
       r="145.663293"
       id="rw" />
    <clipPath
       id="o">
      <path
         id="path4724"
         d="M0,-200V200H200V-200zM-200,-200V200H-100V-200z" />
    </clipPath>
    <clipPath
       id="h">
      <path
         id="path4727"
         d="M-200,-200H200V50H-200z" />
    </clipPath>
    <clipPath
       id="i1">
      <path
         id="path4730"
         d="M0,0 -200,-150V150z" />
    </clipPath>
    <clipPath
       id="i2">
      <path
         id="path4733"
         d="M0,0 -200,-200V200z" />
    </clipPath>
    <clipPath
       id="l1">
      <path
         id="path4736"
         d="M0,0 -50,-200H-200V150z" />
    </clipPath>
    <clipPath
       id="l2">
      <path
         id="path4739"
         d="M0,0V-200H-200V200z" />
    </clipPath>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient5096"
       id="linearGradient5102"
       x1="0"
       y1="400"
       x2="1350.812"
       y2="412.56766"
       gradientUnits="userSpaceOnUse" />
  </defs>
  <g
     id="g4971"
     transform="scale(0.53333333,0.6)">
    <rect
       style="fill:url(#linearGradient5102);fill-rule:evenodd;stroke:none;fill-opacity:1"
       x="0"
       y="0"
       width="1200"
       height="800"
       id="rect5" />
  </g>
</svg>
