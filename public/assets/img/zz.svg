<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with <PERSON><PERSON><PERSON><PERSON> ("http://www.sodipodi.com/") -->
<!-- /Creative Commons Public Domain -->
<!--
<rdf:RDF xmlns="http://web.resource.org/cc/"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
    xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
<Work rdf:about="">
   <dc:title>SVG graphic of German flags</dc:title>
   <dc:rights><Agent>
      <dc:title><PERSON></dc:title>
   </Agent></dc:rights>
   <license rdf:resource="http://web.resource.org/cc/PublicDomain" />
</Work>

<License rdf:about="http://web.resource.org/cc/PublicDomain">
   <permits rdf:resource="http://web.resource.org/cc/Reproduction" />
   <permits rdf:resource="http://web.resource.org/cc/Distribution" />
   <permits rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
</License>
</rdf:RDF>
-->
<svg id="svg378" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="480" width="640" version="1.0" y="0" x="0" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata3074">
  <rdf:RDF>
   <cc:Work rdf:about="">
    <dc:format>image/svg+xml</dc:format>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <rect id="rect171" fill-rule="evenodd" height="2" width="640" y="478" x="0" stroke-width="1pt"/>
 <rect id="rect256" fill-rule="evenodd" height="2" width="640" y="159" x="0" stroke-width="1pt"/>
 <rect id="rect255" fill-rule="evenodd" height="2" width="640" y="319" x="0" stroke-width="1pt"/>
 <rect id="rect2988" height="480" width="2" y="1.3101e-14" x="0"/>
 <rect id="rect2990" height="480" width="2" y="1.3101e-14" x="638"/>
 <rect id="rect2992" height="480" width="2" y="1.3101e-14" x="319"/>
 <rect id="rect2994" fill-rule="evenodd" height="2" width="640" y="0" x="0" stroke-width="1pt"/>
 <rect id="rect2996" fill-rule="evenodd" height="2" width="640" y="239" x="0" stroke-width="1pt"/>
</svg>
