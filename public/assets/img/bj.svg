<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with <PERSON><PERSON><PERSON><PERSON> ("http://www.sodipodi.com/") -->
<!-- /Creative Commons Public Domain -->
<!--
<rdf:RDF xmlns="http://web.resource.org/cc/"
     xmlns:dc="http://purl.org/dc/elements/1.1/"
     xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
<Work rdf:about="">
    <dc:title>New Zealand, Australia, United Kingdom, United States, 
Bosnia and Herzegovina, Azerbaijan, Armenia, Bahamas, Belgium, Benin, 
Bulgaria, Estonia, Finland, Gabon, Gambia, Germany, Greece, Greenland, 
Guinea, Honduras, Israel, Jamaica, Jordan, and Romania Flags</dc:title>
    <dc:rights><Agent>
       <dc:title><PERSON></dc:title>
    </Agent></dc:rights>
    <license rdf:resource="http://web.resource.org/cc/PublicDomain" />
</Work>

<License rdf:about="http://web.resource.org/cc/PublicDomain">
    <permits rdf:resource="http://web.resource.org/cc/Reproduction" />
    <permits rdf:resource="http://web.resource.org/cc/Distribution" />
    <permits rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
</License>
</rdf:RDF>
-->
<svg id="svg598" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="480" width="640" version="1.1" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata9">
  <rdf:RDF>
   <cc:Work rdf:about="">
    <dc:format>image/svg+xml</dc:format>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs id="defs600">
  <clipPath id="clipPath3483" clipPathUnits="userSpaceOnUse">
   <rect id="rect3485" height="666" width="666" y="-154" x="67.641" fill="#808080"/>
  </clipPath>
 </defs>
 <g id="flag" transform="matrix(.96096 0 0 .72072 -65 110.99)" clip-path="url(#clipPath3483)">
  <g id="g605" fill-rule="evenodd" stroke-width="1pt" transform="matrix(8.325,0,0,8.325,0,-154)">
   <rect id="rect602" height="80" width="40" y="0" x="0" fill="#319400"/>
   <rect id="rect603" height="40" width="80" y="0" x="40" fill="#ffd600"/>
   <rect id="rect604" height="40" width="80" y="40" x="40" fill="#de2110"/>
  </g>
 </g>
</svg>
