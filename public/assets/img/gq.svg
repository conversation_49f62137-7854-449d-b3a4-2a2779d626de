<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with <PERSON><PERSON><PERSON><PERSON> ("http://www.sodipodi.com/") -->
<svg id="svg548" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="480" width="640" version="1" y="0" x="0" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata3385">
  <rdf:RDF>
   <cc:Work rdf:about="">
    <dc:format>image/svg+xml</dc:format>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs id="defs550">
  <clipPath id="clipPath5347" clipPathUnits="userSpaceOnUse">
   <rect id="rect5349" fill-opacity="0.67" height="512" width="682.67" y="-.000021338" x="0"/>
  </clipPath>
 </defs>
 <g id="flag" clip-path="url(#clipPath5347)" transform="matrix(.93750 0 0 .93750 0 .000020004)">
  <g id="g771" fill-rule="evenodd" stroke-width="1pt" transform="matrix(1.0321 0 0 1.0321 0 .000049354)">
   <rect id="rect757" height="166.54" width="744.09" y="-0.000068" x="0" fill="#3e9a00"/>
   <rect id="rect758" height="162.99" width="744.09" y="166.54" x="0" fill="#fff"/>
   <rect id="rect759" height="166.54" width="744.09" y="329.53" x="0" fill="#e32118"/>
   <path id="path761" d="m0-0.000038147v496.06l265.75-248.03-265.75-248.03z" transform="matrix(.93333 0 0 1 0 -.000015259)" fill="#5567e4"/>
  </g>
  <g id="g699" transform="matrix(.20377 0 0 .18554 287 179.77)">
   <path id="path553" d="m283.46 148.82v442.91c0 35.433 17.716 70.866 53.149 70.866h70.866c17.717 0 35.433 35.433 35.433 35.433s16.938-35.433 35.433-35.433h70.867c35.433 0 53.149-35.433 53.149-70.866v-442.91h-318.9z" fill-rule="evenodd" transform="matrix(1.1698 0 0 1 -85.821 -.000030518)" stroke="#000" stroke-width="3.7907" fill="#ccc"/>
   <g id="g639" transform="matrix(1.108 0 0 1 -59.607 -.000030518)">
    <g id="g582" transform="matrix(1.0612 0 0 .99999 -28.554 -106.29)">
     <path id="path560" d="m215.38 731.79c-18.021 0-35.738-17.717-35.738-35.433v-88.583c15.502 15.502 88.583 70.866 70.867 88.583-17.717 17.716-35.737 0-35.433 0s0.304 35.433 0.304 35.433z" fill-rule="evenodd" transform="translate(-2.4787 1.6768)" stroke="#000" stroke-width="1pt" fill="#ccc"/>
     <path id="path580" d="m218.72 687.78c17.716-17.716 17.716 17.717 35.433 0l-17.717 106.3c-17.189-3.33-17.778-0.001-27.707-17.717 0-17.718 0-74.332 9.991-88.583z" fill-rule="evenodd" transform="translate(-6.1181 10.256)" stroke-width="1.25" fill="#ccc"/>
     <path id="path579" d="m215.38 731.79c-18.021 0-35.738-17.717-35.738-35.433v-88.583c15.502 15.502 88.583 70.866 70.867 88.583-17.717 17.716-35.737 0-35.433 0s0.304 35.433 0.304 35.433z" fill-rule="evenodd" transform="matrix(-1 0 0 1 891.06 1.6765)" stroke="#000" stroke-width="1pt" fill="#ccc"/>
     <path id="path581" d="m218.72 687.78c17.716-17.716 17.716 17.717 35.433 0l-17.717 106.3c-17.189-3.33-17.778-0.001-27.707-17.717 0-17.718 0-74.332 9.991-88.583z" fill-rule="evenodd" transform="matrix(-1 0 0 1 891.95 10.256)" stroke-width="1.25" fill="#ccc"/>
     <path id="path577" d="m513.78 1140.9c-0.001-17.71-35.433-53.14-35.433-70.86s35.433 17.72 106.3 17.72c70.866 0 106.3-88.588 88.582-106.3 17.717 35.434 17.717 70.864 17.717 88.584 0 35.43-35.433 70.86-106.3 70.86h-70.866z" fill-rule="evenodd" transform="translate(-.00011444 -283.46)" stroke="#000" stroke-width="1pt" fill="#ccc"/>
     <path id="path578" d="m513.78 1140.9c-0.001-17.71-35.433-53.14-35.433-70.86s35.433 17.72 106.3 17.72c70.866 0 106.3-88.588 88.582-106.3 17.717 35.434 17.717 70.864 17.717 88.584 0 35.43-35.433 70.86-106.3 70.86h-70.866z" fill-rule="evenodd" transform="matrix(-1 0 0 1 885.83 -283.46)" stroke="#000" stroke-width="1pt" fill="#ccc"/>
     <path id="path572" d="m549.21 1016.9c0-17.712-35.433-35.433-35.433-53.151v88.581h35.433v-35.43z" fill-rule="evenodd" transform="matrix(-1 0 0 1 921.26 -177.16)" stroke-width="1pt" fill="#ccc"/>
     <path id="path571" d="m549.21 1016.9c0-17.712-35.433-35.433-35.433-53.151v88.581h35.433v-35.43z" fill-rule="evenodd" transform="translate(-35.433 -177.16)" stroke-width="1pt" fill="#ccc"/>
     <path id="path565" d="m371.17 1034.6c0 17.71 141.73 17.71 141.73 0v70.87c0 17.71-141.73 17.71-141.73 0v-70.87z" fill-rule="evenodd" transform="translate(.87551 -194.89)" stroke="#000" stroke-width="1pt" fill="#ccc"/>
     <path id="path554" d="m249.63 696.36c-17.717 17.716-17.717-17.717-35.433 0-17.717 17.716 17.717 106.3 88.583 106.3s106.3-35.433 106.3-17.717c0 17.717-35.433 35.433-35.433 53.15s141.73 17.717 141.73 0-35.433-35.433-35.433-53.15c0-17.716 35.433 17.717 106.3 17.717 70.866 0 106.3-88.583 88.583-106.3-17.716-17.717-17.716 17.716-35.433 0" transform="translate(-1.6026 1.6768)" stroke="#000" stroke-width="1.25" fill="none"/>
    </g>
    <path id="path600" transform="matrix(.89701 -0.442 .39159 .79469 -121.72 234)" stroke-width="1pt" d="m497.6 890.01v-49.407h3.761v49.407h-3.761z"/>
    <path id="path601" transform="matrix(.92714 -.37473 .33198 .82138 -81.636 179.07)" stroke-width="1pt" d="m488.56 872.69 3.765 1.657c-0.791 5.366-2.212 9.467-4.268 12.297-2.043 2.813-4.546 4.216-7.509 4.216-3.066 0-5.563-1.081-7.49-3.238-1.913-2.18-3.373-5.326-4.383-9.433-0.997-4.113-1.494-8.53-1.494-13.246 0-5.148 0.563-9.629 1.689-13.448 1.139-3.842 2.748-6.753 4.831-8.731 2.096-1.996 4.397-2.997 6.906-2.997 2.845 0 5.238 1.26 7.179 3.773 1.94 2.519 3.291 6.057 4.052 10.618l-3.705 1.513c-0.659-3.595-1.615-6.212-2.87-7.851-1.255-1.64-2.834-2.456-4.735-2.456-2.185 0-4.016 0.908-5.49 2.726-1.46 1.823-2.489 4.268-3.085 7.345-0.593 3.06-0.891 6.218-0.891 9.473 0 4.199 0.348 7.874 1.046 11.02 0.712 3.124 1.811 5.459 3.298 7.012 1.49 1.547 3.099 2.324 4.831 2.324 2.109 0 3.893-1.059 5.357-3.17 1.46-2.111 2.45-5.245 2.966-9.404z"/>
    <path id="path602" transform="matrix(.95763 -.28801 .25515 .84840 -28.133 116.65)" stroke-width="1pt" d="m457.87 890.01v-49.407h3.761v49.407h-3.761z"/>
    <path id="path603" transform="matrix(.99630 -.085881 .076085 .88266 107.31 -3.2823)" stroke-width="1pt" d="m440.2 890.01v-43.575h-9.37v-5.832h22.541v5.832h-9.41v43.575h-3.761z"/>
    <path id="path604" transform="matrix(1 0 0 .88593 172.34 -41.585)" stroke-width="1pt" d="m405.18 874.14 3.552-0.535c0.166 2.467 0.557 4.503 1.162 6.097 0.623 1.576 1.58 2.852 2.871 3.842 1.295 0.966 2.751 1.449 4.367 1.449 1.434 0 2.702-0.368 3.801-1.11 1.1-0.742 1.914-1.754 2.447-3.037 0.543-1.3 0.815-2.715 0.815-4.245 0-1.547-0.259-2.899-0.779-4.043-0.516-1.168-1.37-2.146-2.559-2.934-0.765-0.517-2.45-1.311-5.062-2.392-2.616-1.099-4.444-2.134-5.493-3.1-1.358-1.237-2.371-2.761-3.046-4.585-0.659-1.84-0.987-3.894-0.987-6.166 0-2.496 0.407-4.819 1.222-6.976 0.814-2.18 2.003-3.831 3.569-4.953 1.563-1.121 3.304-1.685 5.218-1.685 2.109 0 3.963 0.593 5.569 1.783 1.616 1.173 2.857 2.899 3.725 5.194 0.864 2.289 1.331 4.883 1.397 7.782l-3.609 0.472c-0.195-3.123-0.854-5.482-1.98-7.075-1.112-1.599-2.761-2.393-4.946-2.393-2.278 0-3.94 0.731-4.987 2.192-1.033 1.438-1.553 3.175-1.553 5.222 0 1.772 0.371 3.233 1.106 4.383 0.725 1.145 2.613 2.324 5.665 3.538 3.066 1.19 5.169 2.231 6.305 3.134 1.655 1.323 2.88 3.008 3.668 5.056 0.788 2.019 1.182 4.354 1.182 7.006 0 2.628-0.434 5.113-1.298 7.448-0.867 2.318-2.116 4.124-3.745 5.43-1.615 1.277-3.44 1.921-5.469 1.921-2.576 0-4.735-0.656-6.48-1.956-1.735-1.305-3.099-3.261-4.096-5.866-0.983-2.629-1.499-5.591-1.552-8.898z"/>
    <path id="path605" transform="matrix(.99896 .045497 -.040307 .88502 205.14 -59.322)" stroke-width="1pt" d="m396.43 840.6h3.765v28.546c0 4.969-0.325 8.909-0.971 11.831-0.649 2.922-1.817 5.303-3.513 7.144-1.682 1.817-3.893 2.732-6.635 2.732-2.665 0-4.844-0.8-6.536-2.393-1.695-1.599-2.903-3.899-3.628-6.913-0.726-3.032-1.086-7.167-1.086-12.401v-28.546h3.761v28.511c0 4.291 0.228 7.46 0.682 9.508 0.463 2.019 1.251 3.583 2.364 4.682 1.126 1.104 2.496 1.651 4.115 1.651 2.765 0 4.738-1.087 5.917-3.267 1.176-2.18 1.765-6.373 1.765-12.574v-28.511z"/>
    <path id="path606" transform="matrix(1 0 0 .88593 164.85 -44.083)" stroke-width="1pt" d="m355.98 875.99 3.393-0.811c0.093 3.774 0.5 6.362 1.225 7.754 0.722 1.392 1.725 2.088 3.007 2.088 0.943 0 1.758-0.368 2.443-1.11 0.686-0.765 1.159-1.789 1.417-3.072 0.259-1.3 0.388-3.37 0.388-6.2v-34.039h3.764v33.671c0 4.135-0.291 7.333-0.874 9.605-0.569 2.266-1.48 3.998-2.735 5.188-1.241 1.191-2.705 1.789-4.383 1.789-2.497 0-4.411-1.248-5.745-3.744-1.318-2.491-1.953-6.201-1.9-11.119z"/>
    <path id="path607" transform="translate(136.54 -84.922)" stroke-width="1pt" d="m320.32 890.01v-6.068l14.568-31.64c1.033-2.249 2.016-4.205 2.95-5.867h-15.869v-5.832h20.369v5.832l-15.966 34.275-1.728 3.474h18.161v5.826h-22.485z"/>
    <path id="path618" transform="matrix(.98339 .18149 -.18149 .98339 249.72 -160.65)" stroke-width="1pt" d="m164.27 890.01v-49.407h3.761v49.407h-3.761z"/>
    <path id="path619" transform="matrix(.88093 .47324 -.47324 .88093 511.82 -122.63)" stroke-width="1pt" d="m134.9 890.01v-49.407h3.861l14.936 38.79v-38.79h3.609v49.407h-3.861l-14.939-38.824v38.824h-3.606z"/>
    <path id="path620" transform="matrix(.70781 .70641 -.70641 .70781 734.97 -6.5348)" stroke-width="1pt" d="m124.9 840.6h3.764v28.546c0 4.969-0.324 8.909-0.97 11.831-0.649 2.922-1.818 5.303-3.513 7.144-1.682 1.817-3.894 2.732-6.635 2.732-2.662 0-4.844-0.8-6.536-2.393-1.695-1.599-2.904-3.899-3.629-6.913-0.725-3.032-1.086-7.167-1.086-12.401v-28.546h3.765v28.511c0 4.291 0.225 7.46 0.679 9.508 0.463 2.019 1.254 3.583 2.367 4.682 1.122 1.104 2.493 1.651 4.112 1.651 2.768 0 4.738-1.087 5.917-3.267 1.175-2.18 1.765-6.373 1.765-12.574v-28.511z"/>
    <path id="path623" transform="matrix(.99962 .027666 -.027666 .99962 119.76 -146.36)" stroke-width="1pt" d="m174.66 890.01v-49.407h9.798c2.211 0 3.9 0.236 5.065 0.707 1.629 0.656 3.02 1.835 4.169 3.543 1.503 2.198 2.619 5.022 3.357 8.455 0.752 3.417 1.126 7.328 1.126 11.728 0 3.756-0.252 7.081-0.758 9.98-0.503 2.899-1.149 5.303-1.94 7.212-0.788 1.887-1.656 3.382-2.6 4.481-0.93 1.076-2.062 1.898-3.393 2.462-1.321 0.558-2.841 0.839-4.56 0.839h-10.264zm3.765-5.826h6.072c1.874 0 3.344-0.305 4.404-0.915 1.073-0.604 1.927-1.461 2.563-2.559 0.89-1.553 1.582-3.629 2.076-6.235 0.503-2.629 0.755-5.809 0.755-9.536 0-5.171-0.49-9.134-1.474-11.901-0.97-2.784-2.155-4.647-3.553-5.591-1.006-0.673-2.628-1.012-4.867-1.012h-5.976v37.749z"/>
    <path id="path626" transform="translate(95.745 -141.54)" stroke-width="1pt" d="m200.24 890.01 10.923-49.407h4.053l11.641 49.407h-4.288l-3.317-14.96h-11.893l-3.122 14.96h-3.997zm8.208-20.286h9.642l-2.97-13.683c-0.904-4.159-1.576-7.57-2.017-10.244-0.364 3.163-0.874 6.309-1.533 9.433l-3.122 14.494z"/>
    <path id="path629" transform="translate(95.745 -141.54)" stroke-width="1pt" d="m229.86 890.01v-49.407h9.797c2.212 0 3.9 0.236 5.066 0.707 1.629 0.656 3.019 1.835 4.168 3.543 1.5 2.198 2.619 5.022 3.357 8.455 0.752 3.417 1.126 7.328 1.126 11.728 0 3.756-0.251 7.081-0.758 9.98-0.503 2.899-1.149 5.303-1.94 7.212-0.788 1.887-1.656 3.382-2.599 4.481-0.931 1.076-2.063 1.898-3.394 2.462-1.321 0.558-2.841 0.839-4.559 0.839h-10.264zm3.764-5.826h6.072c1.875 0 3.345-0.305 4.404-0.915 1.073-0.604 1.927-1.461 2.56-2.559 0.893-1.553 1.585-3.629 2.075-6.235 0.507-2.629 0.759-5.809 0.759-9.536 0-5.171-0.49-9.134-1.474-11.901-0.97-2.784-2.155-4.647-3.552-5.591-1.007-0.673-2.633-1.012-4.868-1.012h-5.976v37.749z"/>
    <path id="path632" transform="translate(136.54 -84.922)" stroke-width="1pt" d="m269.59 890.01v-49.407h10.731c1.888 0 3.328 0.161 4.324 0.471 1.398 0.409 2.57 1.18 3.513 2.33 0.944 1.122 1.699 2.703 2.268 4.751 0.583 2.042 0.875 4.291 0.875 6.741 0 4.199-0.772 7.759-2.308 10.681-1.54 2.899-4.321 4.348-8.344 4.348h-7.294v20.085h-3.765zm3.765-25.917h7.354c2.43 0 4.158-0.782 5.178-2.358 1.023-1.57 1.533-3.785 1.533-6.638 0-2.07-0.305-3.83-0.91-5.291-0.596-1.484-1.384-2.462-2.368-2.934-0.636-0.293-1.804-0.437-3.513-0.437h-7.274v17.658z"/>
    <path id="path635" transform="translate(136.54 -84.922)" stroke-width="1pt" d="m292.97 890.01 10.923-49.407h4.053l11.641 49.407h-4.287l-3.318-14.96h-11.893l-3.122 14.96h-3.997zm8.205-20.286h9.641l-2.966-13.683c-0.904-4.159-1.58-7.57-2.017-10.244-0.364 3.163-0.874 6.309-1.533 9.433l-3.125 14.494z"/>
    <path id="path638" transform="matrix(.73993 -.54417 .54796 .73482 -179.59 338.23)" stroke-width="1pt" d="m504.88 890.01 10.923-49.407h4.053l11.641 49.407h-4.288l-3.317-14.96h-11.893l-3.123 14.96h-3.996zm8.205-20.286h9.641l-2.966-13.683c-0.908-4.159-1.58-7.57-2.017-10.244-0.364 3.163-0.874 6.309-1.533 9.433l-3.125 14.494z"/>
   </g>
   <path id="path668" d="m419.16 331.78c0.065 32.968 19.893 131.63-37.678 284.94-4.262 11.127 7.064 16.484 16.484 18.839 9.419 2.355 14.129-9.419 14.129-11.774s2.355 18.839 11.774 16.484c9.42-2.355 18.839-16.484 18.839-16.484s4.71 21.194 16.484 21.194 11.774-25.904 11.774-25.904c0 2.355 8.164 22.235 17.583 22.235 9.42 0 23.929-8.76 15.548-23.144-59.832-100.72-56.68-267.54-56.68-286.38 0-18.839 27.784-53.068 55.418-53.889l-10.675-16.756c-6.96 2.135-15.28 3.669-29.409 15.443s-22.398 24.589-22.398 24.589v-80.065h-23.548l2.355 73.001s-8.051-9.439-20.095-19.932c-11.401-9.934-31.972-9.199-44.637-7.118l-1.204 15.989c14.969 3.123 34.067-3.068 43.487 11.061 11.774 11.774 22.414 21.259 22.449 37.677z" fill-rule="evenodd" transform="matrix(.72308 0 0 .8 123.32 119.98)" stroke="#000" stroke-width="5.0292" fill="#682900"/>
   <path id="path670" d="m310.55 312.21v-0.832c0 1.976-0.067 1.101 0.833-2.498 0.777-2.848 2.407-5.523 4.163-7.493 3.04-1.014 7.441 0.053 10.823-0.833 2.278-3.268 5.828 4.267 5.828-0.833 4.343-0.311 4.399-4.559 5.828-6.66 4.054-0.5 4.963-5.44 8.326-2.497 2.756 1.305 0.406-0.833 4.996-0.833 2.713 0.663 4.131 2.32 6.66 0.832 0.62-2.851 1.399-6.268 1.665-9.99-1.177-2.356-0.832-7.184-0.832-10.824 2.274-3.032 6.262 2.476 9.99 1.665 1.018-3.562 4.146-3.466 2.498-6.66-0.308-4.007-1.08-6.503 0-10.823 1.314-3.704 3.228-3.607 6.66-5.828 1.093-3.277-0.122-6.03 0.833-9.991 3.901-0.115 3.832-1.132 4.163-4.995 1.26-2.661 2.858-5.356 4.995-7.493 1.968-2.382 4.303-2.147 5.828-5.828 1.308-3.271 1.311-4.751 4.995-5.828 2.696-1.269 4.833-1.411 6.661-3.331 1.732-3.056 3.174-5.486 5.828-9.158 2.63-1.772 15.237 1.665 19.149 1.665 2.118-2.669 5.397-6.859 7.493-8.326 3.587-1.916 4.961-2.497 9.99-2.497 4.288 1.27 6.082 2.794 10.824 1.665 1.932-2.706 3.816-3.965 8.325-2.498 3.433 2.221 5.347 2.124 6.661 5.828 1.946 4.615 1.508 6.192 6.661 6.661 5.181 0.185 5.709 1.442 9.158 4.162 5.248 2.25 4.995 0.982 4.995 5.828 3.721 0 4.052-0.44 4.163 3.331 2.294 0.713 4.907 2.718 6.661 4.162 2.093 2.416 2.629 3.929 7.493 4.163 3.591-1.508 4.75-1.496 4.995-5.828 2.75-0.982 3.212-3.635 4.163-6.66 2.108-2.885 3.474-3.331 8.325-3.331 4.279 1.773 4.139 3.568 3.331 7.494 2.664 0.551 3.764 0.239 4.163 3.33 1.407 2.346 1.039 5.15-0.833 6.66 2.132 1.568 3.369 4.256 4.163 7.493-1.411 2.352-0.547 5.809 0 8.326 2.912 1.456 4.415 2.809 8.325 4.163 1.983 1.68 6.199 3.441 9.991 4.163 4.481-0.606 5.961-0.568 9.991 1.665 2.084 2.993 2.235 6.771 4.163 8.326 2.175 1.843 5.818 2.649 7.493 4.995 2.778 2.03 3.594 4.281 7.493 4.995 1.471 2.228 4.634 5.896 5.828 8.326 0.941 2.694 1.361 7.413 2.498 10.823v11.656c-0.158 4.304-0.833 6.916-0.833 11.656-0.74 2.384-1.665 3.3-1.665 6.661-1.582 3.526-2.931 6.489-4.995 9.158-0.941 4.235-2.473 6.223-4.163 9.991 0.052 4.369 0.832 6.304 0.832 10.823-1.81 2.69-3.887 4.647-7.493 6.661-2.724 3.187-4.368 4.426-4.995 9.158-1.566 2.705-1.443 2.498-4.995 2.498-4.2 0-7.814 0.375-11.656 0.832-3.378 0-4.64 0.356-6.661-1.665-3.988-1.804-7.345-1.91-11.656-0.833-2.325-1.594-3.258-2.497-7.493-2.497-3.161 0.427-5.965 0.832-9.991 0.832-2.495-2.789-3.897-3.33-8.325-3.33-2.356 1.178-7.184 0.833-10.824 0.833-3.821-1.043-4.313-2.63-5.828-5.828-2.605-1.235-3.635-3.039-7.493-3.331-3.639 0-8.468 0.345-10.823-0.832-2.723-0.327-5.586-1.027-7.493-2.498-1.556-2.288-3.54-4.272-5.828-5.828-3.698 0-5.864 0.355-7.493-1.665-2.732-2.157-4.891-5.564-8.326-7.493-3.062-2.43-5.136-3.916-6.661-8.326 0-3.943 0.78-4.874-3.33-4.995-2.755 2.812-4.009 3.33-9.158 3.33-3.389 0-6.931-0.018-9.158-0.832-3.681-2.09-5.028-3.013-9.158-3.331-2.585 1.645-6.008 3.547-9.991 4.163-3.405 1.966-5.437 2.379-6.661 5.828-3.145 2.568-4.989 4.078-9.158 5.828-2.316 2.209-4.526 2.591-5.828 6.661-0.792 3.366-1.665 5.369-1.665 9.99 0.59 4.368 0.361 6.237-1.665 9.991-1.741 2.486-4.757 3.7-7.493 5.828-4.116 0.494-4.548 0.186-7.493-1.665-2.933-0.977-6.266-1.78-9.991-2.498-4.496-0.866-6.017-1.961-8.326-4.162l8.326 4.162c-4.496-0.866-6.017-1.961-8.326-4.162-5.064-3.583-6.063-5.398-11.656-5.828-5.198 0.061-7.429 0.318-8.325 4.995 2.961 0.789 0.609 2.554-0.833 3.33-2.078 1.852-5.411 3.368-8.326 4.163-1.328 1.328-1.072-1.101-3.33-1.665-3.448-1.842-4.89-3.226-8.326-4.163-1.683-2.971-3.584-5.274-5.828-8.326-1.734-2.818-3.382-3.838-5.828-6.66-1.169-1.754-4.159-1.829-5.827-3.33-1.001-3.002-3.169-8.57-2.498-11.656 1.679-1.586 2.632-7.243 4.995-8.326 3.367-2.525 4.635-0.223 9.159 0 4.259 0-5.866-6.71-1.665-6.66 3.92-0.666 4.387-2.595 5.827-4.996h9.991z" fill-rule="evenodd" transform="matrix(1 0 0 .82325 -6.6605 55.382)" stroke="#000" stroke-width="4.4636" fill="#009200"/>
   <path id="path671" d="m382.98 253.1c0.878 0.713 4.684 2.498 8.326 2.498 3.976 0.728 4.843 2.216 5.828 5.828 2.174 1.304 3.55 1.665 7.493 1.665 4.359 0.871 4.322 2.212 7.493 4.995" transform="translate(-6.6605 -3.3303)" stroke="#006800" stroke-width="4.05" fill="none"/>
   <path id="path672" d="m358.84 299.72c1.812 0.266 8.154 0.832 12.488 0.832 3.333-1.924 4.912-3.24 7.493-4.995 3.943 0 5.32 0.361 7.494 1.665 2.105 0.702 7.052 0.963 9.158 1.665 0 1.11-0.278 0.833 0.832 0.833" transform="translate(-6.6605 -3.3303)" stroke="#006800" stroke-width="4.05" fill="none"/>
   <path id="path673" d="m391.31 286.4c0.065-0.025 3.252-1.946 5.828-2.498 2.835-1.976 5.946-2.939 9.158-4.163 2.117-0.46 7.102-0.448 8.326-0.832" transform="translate(-6.6605 -3.3303)" stroke="#006800" stroke-width="4.05" fill="none"/>
   <path id="path674" d="m432.1 273.91v0.833c0-2.327-0.104-1.091 1.665 3.33 0.874 4.078 1.949 7.526 3.33 10.823 2.455 3.83 4.058 5.547 7.493 7.493 2.939 2.316 5.164 4.724 8.326 5.828 2.202 1.963 5.106 3.584 8.325 4.996 4.271 0 7.558 0.686 11.656 0.832h2.498" transform="translate(-6.6605 -3.3303)" stroke="#006800" stroke-width="4.05" fill="none"/>
   <path id="path676" d="m477.06 227.29v0.833c0-2.247 0.055-1.105-0.832 3.33-1.257 2.654-2.705 5.124-4.163 7.493-3.915 1.419-5.374 4.277-6.661 7.493-1.372 2.231-3.505 5.17-4.995 7.493-2.589 3.211-3.622 4.242-8.326 5.828" transform="translate(-6.6605 -3.3303)" stroke="#006800" stroke-width="4.05" fill="none"/>
   <path id="path677" d="m510.36 244.77c0 0.244-1.537 2.434-2.498 4.996-1.653 2.436-2.075 6.861-2.497 9.99 0 3.618 0.021 7.557 0.832 9.991 2.516 3.121 3.158 4.764 7.493 6.661l-7.49-6.66c2.516 3.121 3.158 4.764 7.493 6.661" transform="translate(-6.6605 -3.3303)" stroke="#006800" stroke-width="4.05" fill="none"/>
   <path id="path678" d="m571.14 273.91v0.833c0-2.61 0.217-1.006-4.163 2.497-3.252 2.999-5.572 5.416-8.325 7.493-4.732 1.255-5.455 1.182-9.991 0.833h-7.493" transform="translate(-6.6605 -3.3303)" stroke="#006800" stroke-width="4.05" fill="none"/>
   <path id="path679" d="m582.8 337.19h-0.832c2.327 0 1.089-0.103-3.331 1.665-3.692 0.923-7.879 0.982-10.823 0-2.966-1.681-5.955-3.055-8.326-5.828-1.241-0.93-1.42-1.306-2.497-1.665" transform="translate(-6.6605 -3.3303)" stroke="#006800" stroke-width="4.05" fill="none"/>
   <path id="path680" d="m553.66 363v-0.833c0 2.61 0.217 1.006-4.163-2.498-1.872-2.753-3.748-3.794-7.493-6.66-3.236-3.913-3.659-5.481-4.995-9.991-0.356-3.323-1.455-6.734 0-9.158" transform="translate(-6.6605 -3.3303)" stroke="#006800" stroke-width="4.05" fill="none"/>
   <path id="path681" d="m492.05 348.84c1.81 0 8.56-0.159 11.656-0.833 2.418-1.643 5.094-2.746 8.326-4.162 1.764-4.36 3.27-5.809 3.33-10.824" transform="translate(-6.6605 -3.3303)" stroke="#006800" stroke-width="4.05" fill="none"/>
   <path id="path682" d="m441.26 323.87c-0.652-1.483-3.544-7.436-4.995-10.823-0.065-3.638-0.833-6.285-0.833-9.991" transform="translate(-6.6605 -3.3303)" stroke="#006800" stroke-width="4.05" fill="none"/>
   <path id="path683" d="m328.86 350.51c0.839 0.126 4.375 0.403 6.66 0 3.635-1.861 7.022-2.205 10.823-2.498 1.933-1.159 5.086-1.031 6.661-2.497 3.474-2.316 3.937-4.645 5.828-8.326 0.167-1.673 0.665-2.49 0.832-4.163" transform="translate(-6.6605 -3.3303)" stroke="#006800" stroke-width="4.05" fill="none"/>
   <path id="path684" d="m383.81 338.02c-1.828 0-8.658 0.125-12.489-0.833-1.967-1.273-3.363-3.364-4.995-4.995 1.11 0 0.833 0.277 0.833-0.833" transform="translate(-6.6605 -3.3303)" stroke="#006800" stroke-width="4.05" fill="none"/>
   <path id="path685" d="m393.8 313.88c0.353-0.106 4.485-1.665 7.493-1.665 1.79-1.158 4.76-2.237 7.493-3.33 1.481-0.797 2.504-1.787 4.163-2.498" transform="translate(-6.6605 -3.3303)" stroke="#006800" stroke-width="4.05" fill="none"/>
   <path id="path686" d="m293.06 347.18h0.833c-2.249 0-1.1 0.053 3.33-0.833 5.18-0.32 8.666-2.111 12.489-3.33 2.496-2.318 5.939-4.635 8.325-5.828 0-1.423-0.341-1.274 0.833-1.665" transform="translate(-6.6605 -3.3303)" stroke="#006800" stroke-width="4.05" fill="none"/>
   <path id="path687" d="m504.54 301.39h0.833c-2.328 0-1.09-0.103 3.33 1.665 3.517 1.346 6.695 1.781 9.991 2.498 2.993 0.731 4.127 1.665 8.325 1.665 4.19 1.826 5.104 2.397 8.326 4.995 2.465 1.168 4.221 1.665 8.326 1.665 2.178-1.206 6.161-2.609 8.325-3.33 1.211-0.908 1.293-0.55 1.665-1.665" transform="translate(-6.6605 -3.3303)" stroke="#006800" stroke-width="4.05" fill="none"/>
   <path id="path688" d="m430.94 469.54v18.839c0 6.017-0.557 11.277-2.355 16.484 0 5.875-0.077 11.554-2.355 15.306-0.401 5.618-1.177 10.479-1.177 16.484-1.705 5.116-1.504 12.044-2.355 17.662-0.738 5.508-1.393 12.06-2.355 16.484-1.754 3.684-4.745 10.349-7.064 12.951-0.288 5.085-1.532 7.588 0 11.775 1.641 4.922 1.177 10.85 1.177 16.484 1.537 4.611 1.178 10.004 1.178 15.306" transform="translate(6.6605 -6.6606)" stroke="#000" stroke-width="1pt" fill="none"/>
   <path id="path689" d="m436.82 493.08c0.358 2.361 2.118 10.873 2.355 16.484v25.904c0 6.299 0.532 11.851 1.178 17.661-0.084 6.943-1.346 9.54-4.71 15.307-0.662 5.559-1.693 10.924-2.355 16.484-1.641 4.923-1.177 10.85-1.177 16.484 1.404 6.322 3.703 7.453 4.709 12.951 0 3.359 0.321 5.066 1.178 7.065" transform="translate(6.6605 -6.6606)" stroke="#000" stroke-width="1pt" fill="none"/>
   <path id="path690" d="m447.42 535.47c0 2.155-0.14 12.532 1.178 16.484 0 6.023 1.106 10.544 1.177 16.484-0.714 5.281-1.453 9.084-4.71 11.774-2.144 4.358-3.314 8.235-3.532 14.129 3.043 2.13 6.349 8.079 9.419 10.597 2.567 5.361 4.956 5.59 5.888 11.774 2.458 2.732-1.733 4.154 0 5.887 1.91 2.548 0.687 1.469 0 3.533" transform="translate(6.6605 -6.6606)" stroke="#000" stroke-width="1pt" fill="none"/>
   <polygon id="polygon693" fill-rule="evenodd" transform="matrix(.79171 0 0 .79284 405.25 101.14)" stroke="#000" points="93.248 -106.57 109.23 -80.974 139.39 -79.928 125.22 -53.286 139.39 -26.644 109.23 -25.599 93.248 -0.0019379 93.248 -0.0019379 77.262 -25.599 47.102 -26.644 61.277 -53.286 47.102 -79.928 77.262 -80.974" stroke-width="3.125" fill="#ffd100"/>
   <polygon id="polygon694" fill-rule="evenodd" transform="matrix(.79171 0 0 .79284 307.7 101.14)" stroke="#000" points="93.248 -106.57 109.23 -80.974 139.39 -79.928 125.22 -53.286 139.39 -26.644 109.23 -25.599 93.248 -0.0019379 93.248 -0.0019379 77.262 -25.599 47.102 -26.644 61.277 -53.286 47.102 -79.928 77.262 -80.974" stroke-width="3.125" fill="#ffd100"/>
   <polygon id="polygon695" fill-rule="evenodd" transform="matrix(.78504 -.10268 .10253 .78617 224.14 128.84)" stroke="#000" points="93.248 -106.57 109.23 -80.974 139.39 -79.928 125.22 -53.286 139.39 -26.644 109.23 -25.599 93.248 -0.0019379 93.248 -0.0019379 77.262 -25.599 47.102 -26.644 61.277 -53.286 47.102 -79.928 77.262 -80.974" stroke-width="3.125" fill="#ffd100"/>
   <polygon id="polygon696" fill-rule="evenodd" transform="matrix(-.78504 -.10268 -.10253 .78617 644.37 126.2)" stroke="#000" points="93.248 -106.57 109.23 -80.974 139.39 -79.928 125.22 -53.286 139.39 -26.644 109.23 -25.599 93.248 -0.0019379 93.248 -0.0019379 77.262 -25.599 47.102 -26.644 61.277 -53.286 47.102 -79.928 77.262 -80.974" stroke-width="3.125" fill="#ffd100"/>
   <polygon id="polygon697" fill-rule="evenodd" transform="matrix(-.74718 -.26215 -.26178 .74825 719.36 181.3)" stroke="#000" points="93.248 -106.57 109.23 -80.974 139.39 -79.928 125.22 -53.286 139.39 -26.644 109.23 -25.599 93.248 -0.0019379 93.248 -0.0019379 77.262 -25.599 47.102 -26.644 61.277 -53.286 47.102 -79.928 77.262 -80.974" stroke-width="3.125" fill="#ffd100"/>
   <polygon id="polygon698" fill-rule="evenodd" transform="matrix(.74718 -.26215 .26178 .74825 146.51 176.02)" stroke="#000" points="93.248 -106.57 109.23 -80.974 139.39 -79.928 125.22 -53.286 139.39 -26.644 109.23 -25.599 93.248 -0.0019379 93.248 -0.0019379 77.262 -25.599 47.102 -26.644 61.277 -53.286 47.102 -79.928 77.262 -80.974" stroke-width="3.125" fill="#ffd100"/>
  </g>
 </g>
</svg>
