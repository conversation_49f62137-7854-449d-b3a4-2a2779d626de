# Shipvagoo Front-end

## Getting Started

### Prerequisites

- Latest stable Node runtime (v18 at the time of writing)
- yarn package manager

### Installation

1. Clone the project

```sh
git clone https://github.com/ShipVagoo/Web
```

2. cd to the cloned project
3. Install the dependencies by executing yarn in the project root

```sh
yarn
```

4. Set-up husky hooks

```sh
yarn prepare
```

5. Create an .env file at the root and have these properties in it:

```
VITE_baseUrl={{ SHIPVAGOO_API_BASE_URL }}
VITE_googleMapsAPIKey={{ VALID_GOOGLE_MAPS_KEY }}
VITE_shopify_callback_url={{Shopify backend callback url}}
VITE_clientId={{clientId from shopify}}
VITE_appUrl={{Frontend APP URL}}
```

6. You're all set!
